package com.somytrip.provider.hotel;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.somytrip.api.service.hotel.*;
import com.somytrip.api.service.hotel.NCNB.NCNBService;
import com.somytrip.api.service.hotel.NCNB.NCNBStaticService;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.CityMappingNCNBEntity;
import com.somytrip.entity.hotel.HotelGeneralEntity;
import com.somytrip.entity.hotel.HotelGeneralSimpleEntity;
import com.somytrip.entity.hotel.HotelMinRateEntity;
import com.somytrip.entity.hotel.NCNB.*;
import com.somytrip.provider.hotel.utils.DataUtil;
import com.somytrip.provider.hotel.utils.request.NCNBRequestUtil;
import com.somytrip.utils.LocalDateTimeUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.BinaryOperator;
import java.util.stream.Collectors;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.hotel
 * @className: HotelTest
 * @author: shadow
 * @description:
 * @date: 2024/4/19 15:46
 * @version: 1.0
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class NCNBTest {

    @Resource
    private NCNBStaticService ncnbStaticService;
    @Resource
    private CityHotHotelService cityHotHotelService;
    @Resource
    private HotelGeneralService hotelGeneralService;
    @Resource
    private NCNBService ncnbService;
    @Resource
    private HotelMinRateService hotelMinRateService;
    @Resource
    private CityMappingNCNBService cityMappingNCNBService;
    @Resource
    private HotelIncrService hotelIncrService;

    private static void mergeMaps(Map<LocalDate, BigDecimal> dest, Map<LocalDate, BigDecimal> src, BinaryOperator<BigDecimal> merger) {
        src.forEach((key, value) -> {
            // 如果dest中已存在key，则合并value；否则，直接添加
            dest.merge(key, value, merger);
        });
    }

    @Test
    public void fixCityCode() {

        List<HotelGeneralEntity> updateList = new ArrayList<>();
        List<String> hotelIds = hotelGeneralService.queryHotelIdsByHotelOrigin(HotelOrigin.NCNB);
        int hotelSize = hotelIds.size();
        int hotelIndex = 0;
        int percentageCompleted = 0;

        for (String hotelId : hotelIds) {
//            log.info("Start hotelIndex: {}/{}", hotelIndex++, hotelSize - 1);
            HotelGeneralEntity entity = hotelGeneralService.queryOneByHotelId(hotelId, HotelOrigin.NCNB);
            String cityId = entity.getCityId();
//            cityMappingNCNBService.queryNCNBCityIdByCityCode()
            LambdaQueryWrapper<CityMappingNCNBEntity> qw = new LambdaQueryWrapper<>();
            qw.eq(CityMappingNCNBEntity::getCityId, cityId);
            CityMappingNCNBEntity mapping = cityMappingNCNBService.getOne(qw);
            if (mapping == null) continue;
            if (StringUtils.isBlank(mapping.getCityCode())) continue;
            entity.setGlobalCityCode(mapping.getCityCode());
            DataUtil.processBatchSaveAsync(updateList, entity, hotelGeneralService);

            ++hotelIndex;
            if ((double) hotelIndex / hotelSize * 100 >= (percentageCompleted + 10)) {
                percentageCompleted += 10;
                if (percentageCompleted <= 100) {
                    log.info("{}% Down", percentageCompleted);
                }
            }
        }
        DataUtil.batchSaveAsync(updateList, hotelGeneralService);
        log.info("100% Down");
    }

    @Test
    public void testUpdateRp() {

        List<String> cityCodes = cityHotHotelService.queryCityCodes();
        List<HotelGeneralSimpleEntity> simpleEntityList = hotelGeneralService.querySimpleListByCityCodes(cityCodes);
        // 按酒店来源分组
        Map<HotelOrigin, List<String>> groupByOrigin = simpleEntityList.stream()
                .collect(Collectors.groupingBy(
                        HotelGeneralSimpleEntity::getHotelOrigin,
                        Collectors.mapping(HotelGeneralSimpleEntity::getHotelId, Collectors.toList())
                ));

        List<String> ncnbList = groupByOrigin.get(HotelOrigin.NCNB);

        boolean result = ncnbStaticService.updateMinRate(ncnbList, LocalDateTime.now(), 1);
        log.info("result: {}", result);
    }

    @Test
    public void testPrice() {
        NCNBRatePlanSearchReq ratePlanSearchReq = new NCNBRatePlanSearchReq();
        ratePlanSearchReq.setHotelId("1029790");
        NCNBHotelListSearchReq.StayDateRange stayDateRange = new NCNBHotelListSearchReq.StayDateRange();
        LocalDateTime checkIn = LocalDateTime.now();
        LocalDateTime checkOut = checkIn.plusDays(30);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        stayDateRange.setCheckIn(formatter.format(checkIn));
        stayDateRange.setCheckOut(formatter.format(checkOut));
        ratePlanSearchReq.setStayDateRange(stayDateRange);
        NCNBReqBody<NCNBRatePlanSearchReq> requestBody = NCNBRequestUtil.getRequestBody(ratePlanSearchReq);
        log.info("ratePlanSearchReq: {}", JSONObject.toJSONString(ratePlanSearchReq));
        NCNBRatePlanSearchResp ratePlanSearchResp = ncnbService.ratePlanSearch(requestBody);
        log.info("ratePlanSearchResp: {}", JSONObject.toJSONString(ratePlanSearchResp));
    }


    @Test
    public void testGetPrice() {

        LocalDateTime today = LocalDateTime.now();
        List<String> hotelIds = Arrays.asList("675561", "673604", "209968", "673607", "126247", "128600", "128718", "131520");
        generalProcess(hotelIds, today);
    }

    private void generalProcess(List<String> hotelIds, LocalDateTime today) {

        CompletableFuture<NCNBRatePlanSearchResp> part1Future = CompletableFuture
                .supplyAsync(() -> processRequest(hotelIds, today, 0));
        CompletableFuture<NCNBRatePlanSearchResp> part2Future = CompletableFuture
                .supplyAsync(() -> processRequest(hotelIds, today, 1));
        CompletableFuture<NCNBRatePlanSearchResp> part3Future = CompletableFuture
                .supplyAsync(() -> processRequest(hotelIds, today, 2));
        CompletableFuture<NCNBRatePlanSearchResp> part4Future = CompletableFuture
                .supplyAsync(() -> processRequest(hotelIds, today, 3));
        CompletableFuture<NCNBRatePlanSearchResp> part5Future = CompletableFuture
                .supplyAsync(() -> processRequest(hotelIds, today, 4));
        CompletableFuture<NCNBRatePlanSearchResp> part6Future = CompletableFuture
                .supplyAsync(() -> processRequest(hotelIds, today, 5));

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                part1Future,
                part2Future,
                part3Future,
                part4Future,
                part5Future,
                part6Future
        );
        try {
            allFutures.get();
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        }

        NCNBRatePlanSearchResp part1Resp = part1Future.join();
        NCNBRatePlanSearchResp part2Resp = part2Future.join();
        NCNBRatePlanSearchResp part3Resp = part3Future.join();
        NCNBRatePlanSearchResp part4Resp = part4Future.join();
        NCNBRatePlanSearchResp part5Resp = part5Future.join();
        NCNBRatePlanSearchResp part6Resp = part6Future.join();

        Map<String, Map<LocalDate, BigDecimal>> part1Map = processData(part1Resp);
        Map<String, Map<LocalDate, BigDecimal>> part2Map = processData(part2Resp);
        Map<String, Map<LocalDate, BigDecimal>> part3Map = processData(part3Resp);
        Map<String, Map<LocalDate, BigDecimal>> part4Map = processData(part4Resp);
        Map<String, Map<LocalDate, BigDecimal>> part5Map = processData(part5Resp);
        Map<String, Map<LocalDate, BigDecimal>> part6Map = processData(part6Resp);

        List<Map<String, Map<LocalDate, BigDecimal>>> maps = Arrays.asList(part1Map, part2Map, part3Map, part4Map, part5Map, part6Map);
        Map<String, Map<LocalDate, BigDecimal>> general = mergeMaps(maps);
        log.info("general: {}", JSONObject.toJSONString(general));

        processUpdateData(general);
    }

    private NCNBRatePlanSearchResp processRequest(List<String> hotelIds, LocalDateTime today, Integer x) {

        int unit = 28;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        NCNBRatePlanSearchReq ratePlanSearchReq = new NCNBRatePlanSearchReq();
        ratePlanSearchReq.setHotelId(String.join(",", hotelIds));
        NCNBHotelListSearchReq.StayDateRange stayDateRange = new NCNBHotelListSearchReq.StayDateRange();
        LocalDateTime checkIn = today.plusDays((long) unit * x);
        LocalDateTime checkOut = checkIn.plusDays(unit);
        stayDateRange.setCheckIn(formatter.format(checkIn));
        stayDateRange.setCheckOut(formatter.format(checkOut));
        ratePlanSearchReq.setStayDateRange(stayDateRange);
        NCNBReqBody<NCNBRatePlanSearchReq> requestBody = NCNBRequestUtil.getRequestBody(ratePlanSearchReq);
        log.info("ratePlanSearchReq: {}", JSONObject.toJSONString(ratePlanSearchReq));
        return ncnbService.ratePlanSearch(requestBody);
    }

    private Map<String, Map<LocalDate, BigDecimal>> processData(NCNBRatePlanSearchResp ratePlanSearchResp) {

        if (ratePlanSearchResp == null) return null;

        Map<String, Map<LocalDate, BigDecimal>> hotelMap = new HashMap<>();

        List<NCNBRatePlanSearchResp.HotelPriceRoomInfo> hotelPriceRoomInfos = ratePlanSearchResp.getHotelPriceRoomInfos();
        if (hotelPriceRoomInfos == null || hotelPriceRoomInfos.isEmpty()) return null;

        for (NCNBRatePlanSearchResp.HotelPriceRoomInfo hotelPriceRoomInfo : hotelPriceRoomInfos) {
            String hotelId = hotelPriceRoomInfo.getHotelId();
            List<NCNBRatePlanSearchResp.Room> rooms = hotelPriceRoomInfo.getRooms();
            if (rooms == null || rooms.isEmpty()) continue;

            Map<LocalDate, BigDecimal> curHotelPriceMap = new HashMap<>();

            for (NCNBRatePlanSearchResp.Room room : rooms) {
                List<NCNBRatePlanSearchResp.Room.Rate> rates = room.getRates();
                if (rates == null || rates.isEmpty()) continue;
                for (NCNBRatePlanSearchResp.Room.Rate rate : rates) {
                    List<NCNBRatePlanSearchResp.Room.Rate.PriceAndStatus> priceAndStatus = rate.getPriceAndStatus();
                    if (priceAndStatus == null || priceAndStatus.isEmpty()) continue;
                    for (NCNBRatePlanSearchResp.Room.Rate.PriceAndStatus priceItem : priceAndStatus) {
                        if (!Objects.equals("026001", priceItem.getStatus())) continue;

                        String price = priceItem.getPrice();
                        String date = priceItem.getDate();
                        if (StringUtils.isAnyBlank(price, date)) continue;

                        BigDecimal priceValue = new BigDecimal(price);
                        LocalDate dateValue = LocalDateTimeUtil.convertStr2LocalDate(date, "yyyy/MM/dd");

                        BigDecimal existingPrice = curHotelPriceMap.get(dateValue);
                        if (existingPrice == null) {
                            curHotelPriceMap.put(dateValue, priceValue);
                        } else {
                            if (existingPrice.compareTo(priceValue) > 0) {
                                curHotelPriceMap.put(dateValue, priceValue);
                            }
                        }
                    }
                }
            }

            hotelMap.put(hotelId, curHotelPriceMap);
        }

        return hotelMap;
    }

    private Map<String, Map<LocalDate, BigDecimal>> mergeMaps(List<Map<String, Map<LocalDate, BigDecimal>>> maps) {

        Map<String, Map<LocalDate, BigDecimal>> general = new HashMap<>();
        for (Map<String, Map<LocalDate, BigDecimal>> map : maps) {
            if (map == null) continue;
//            general.putAll(map);
            map.forEach((key, value) -> {
                general.putIfAbsent(key, new HashMap<>());
                mergeMaps(general.get(key), value, BigDecimal::add);
            });
        }
        return general;
    }

    private void processUpdateData(Map<String, Map<LocalDate, BigDecimal>> map) {

        List<HotelMinRateEntity> dataList = new ArrayList<>();
        HotelMinRateEntity hotelMinRateEntity = new HotelMinRateEntity();
        hotelMinRateEntity.setHotelOrigin(HotelOrigin.NCNB);

        for (Map.Entry<String, Map<LocalDate, BigDecimal>> entry : map.entrySet()) {
            String hotelId = entry.getKey();
            hotelMinRateEntity.setHotelId(hotelId);
            Map<LocalDate, BigDecimal> dateRateMap = entry.getValue();
            for (Map.Entry<LocalDate, BigDecimal> dateEntry : dateRateMap.entrySet()) {
                LocalDate date = dateEntry.getKey();
                BigDecimal rate = dateEntry.getValue();
                hotelMinRateEntity.setDate(date);
                hotelMinRateEntity.setMinRate(rate);

                DataUtil.processBatchSaveAsync(dataList, hotelMinRateEntity, hotelMinRateService);
            }
        }
        DataUtil.batchSaveAsync(dataList, hotelMinRateService);
    }

    @Test
    public void testOrderDetail() {

        NCNBOrderSearchReq orderSearchReq = new NCNBOrderSearchReq();
        orderSearchReq.setOrderId("CN24061211874A");
        NCNBReqBody<NCNBOrderSearchReq> requestBody = NCNBRequestUtil.getRequestBody(orderSearchReq);
        NCNBOrderSearchResp orderSearchResp = ncnbService.orderSearch(requestBody);
        log.info("orderSearchResp: {}", JSONObject.toJSONString(orderSearchResp));
    }

    @Test
    public void testNcnbHotelData() {
        ncnbStaticService.updateStaticHotel();
    }

    @Test
    public void testupdateStaticCity() {
        ncnbStaticService.updateStaticCity();
    }

    @Test
    public void testModifyHotelImage() {
        ncnbStaticService.modifyHotelInfoImage();
    }

    @Test
    public void testinitNCNBHotelThirtyDaysRate() {
        hotelIncrService.initNCNBHotelThirtyDaysRate();
    }

    @Test
    public void testinitNCNBHotelincreDaysRate() {
        hotelIncrService.initNVNBHotelIncrementDaysRate("2025-04-27", 20);
    }

    @Test
    public void testModifyPrice() {
        hotelIncrService.ncnbHotelPriceModify("2024-11-24", 3);
    }
}
