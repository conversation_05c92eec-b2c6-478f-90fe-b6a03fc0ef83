spring:
  application:
    name: hotel-service
  profiles:
    active: &profileActive '@profiles.active@'
  banner:
    location: static/banner/banner.txt
  cloud:
    nacos:
      config:
        file-extension: yaml
        username: &nacosUserName dev
        password: &nacosPassword '@Dev@2023715'
        namespace: *profileActive
        server-addr: &serverAddr '@nacos.server.addr@'
dubbo:
  application:
    name: provider-hotel
    qos-enable: false
    check-serializable: false
    trust-serialize-class-level: 2
    serialize-check-status: DISABLE
    auto-trust-serialize-class: true
  protocol:
    name: tri
    port: -1
    serialization: fastjson2
    prefer-serialization: fastjson2
  registry:
    username: *nacosUserName
    password: *nacosPassword
    parameters:
      register-consumer-url: true
      namespace: '@dubbo.nacos.namespace@'
    address: '@dubbo.nacos.server.addr@'
  consumer:
    timeout: 200000
  #    check: false
  provider:
    timeout: 200000