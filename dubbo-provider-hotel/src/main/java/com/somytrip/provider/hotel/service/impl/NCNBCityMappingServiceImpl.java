package com.somytrip.provider.hotel.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.hotel.NCNB.NCNBCityMappingService;
import com.somytrip.entity.hotel.NCNB.NCNBCityMappingEntity;
import com.somytrip.exception.BusinessException;
import com.somytrip.provider.hotel.mapper.NCNBCityMappingMapper;
import com.somytrip.provider.hotel.service.RedisService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: NCNBCityMappingServiceImpl
 * @Description: 捷旅城市ID映射 Service层实现类
 * @Author: shadow
 * @Date: 2023/11/28 9:52
 */
@Service
public class NCNBCityMappingServiceImpl
        extends ServiceImpl<NCNBCityMappingMapper, NCNBCityMappingEntity> implements NCNBCityMappingService {

    @Resource
    private RedisService redisService;

    /**
     * 根据GlobalCityId获取捷旅城市ID
     *
     * @param globalCityId GlobalCityId
     * @return String 捷旅城市ID
     */
    @Override
    public String getNCNBCityId(Integer globalCityId) {

        if (globalCityId == null) {
            throw new BusinessException("api.hotel.missing-param");
        }

        LambdaQueryWrapper<NCNBCityMappingEntity> mappingQW = new LambdaQueryWrapper<>();
        mappingQW.eq(NCNBCityMappingEntity::getGlobalCityId, globalCityId);
//        NCNBCityMappingEntity mappingEntity = baseMapper.selectOne(mappingQW);
        List<NCNBCityMappingEntity> mappingList = baseMapper.selectList(mappingQW);
        NCNBCityMappingEntity mappingEntity = mappingList.get(0);

        if (mappingEntity == null) {
            throw new BusinessException("api.hotel.not-support-city");
        }

        return mappingEntity.getNCNBCityId();
    }

    /**
     * 根据捷旅城市名称查询GlobalCityId
     *
     * @param NCNBCityName 捷旅城市名称
     * @return GlobalCityId
     */
    @Override
    public String getCityIdByNCNBCityName(String NCNBCityName) {

//        LambdaQueryWrapper<NCNBCityMappingEntity> qw = new LambdaQueryWrapper<>();
//        qw.eq(NCNBCityMappingEntity::getNCNBCityName, NCNBCityName);
//        NCNBCityMappingEntity mapping = baseMapper.selectOne(qw);
//        return String.valueOf(mapping.getGlobalCityId());
        List<NCNBCityMappingEntity> mappingList = getNCNBMappingList();
        NCNBCityMappingEntity target =
                mappingList.stream()
                        .filter(item -> Objects.equals(item.getNCNBCityName(), NCNBCityName))
                        .findFirst()
                        .orElse(null);
        if (target == null) {
            return null;
        }
        return String.valueOf(target.getGlobalCityId());
    }

    /**
     * 根据CityCode查询捷旅城市ID
     *
     * @param cityCode 城市code
     * @return String 捷旅城市ID
     */
    @Override
    public String queryNCNBCityIdByCityCode(String cityCode) {

        LambdaQueryWrapper<NCNBCityMappingEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(NCNBCityMappingEntity::getCityCode, cityCode);
        try {
            NCNBCityMappingEntity mapping = baseMapper.selectOne(qw);
            return mapping.getNCNBCityId();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    private List<NCNBCityMappingEntity> getNCNBMappingList() {

        String redisKey = "CACHE_DATA:NCNB_CITY_MAPPING";
        String redisData = redisService.getString(redisKey);
        if (redisData != null) {
            return JSONArray.parseArray(redisData, NCNBCityMappingEntity.class);
        }

        List<NCNBCityMappingEntity> list = baseMapper.selectList(new QueryWrapper<>());
        redisService.setString(redisKey, JSONArray.toJSONString(list), 30, TimeUnit.DAYS);
        return list;
    }
}
