package com.somytrip.provider.hotel.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.somytrip.api.service.hotel.CityMappingNCNBService;
import com.somytrip.api.service.hotel.HotelGeneralService;
import com.somytrip.api.service.hotel.HotelRoomService;
import com.somytrip.api.service.hotel.NCNB.NCNBHotelService;
import com.somytrip.api.service.hotel.NCNB.NCNBService;
import com.somytrip.api.service.order.OrderBaseService;
import com.somytrip.constant.NcnbHotelStatusConstant;
import com.somytrip.entity.dto.hotel.*;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.enums.hotel.NCNBFacilityType;
import com.somytrip.entity.enums.hotel.orderstatus.HotelOrderStatus;
import com.somytrip.entity.enums.locale.NCNBLocaleMapping;
import com.somytrip.entity.enums.locale.NCNBMealLocale;
import com.somytrip.entity.hotel.HotelOrderEntity;
import com.somytrip.entity.hotel.NCNB.*;
import com.somytrip.entity.resp.ListResp;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.CreateOrderFinanceVo;
import com.somytrip.entity.vo.hotel.*;
import com.somytrip.entity.vo.order.CreateOrderMasterVo;
import com.somytrip.entity.vo.order.CreateOrderProductVo;
import com.somytrip.entity.vo.order.CreateOrderVo;
import com.somytrip.entity.vo.order.OrderBigFiledVo;
import com.somytrip.exception.BusinessException;
import com.somytrip.exception.OrderException;
import com.somytrip.provider.hotel.utils.SnowFlakeUtil;
import com.somytrip.provider.hotel.utils.request.NCNBRequestUtil;
import com.somytrip.utils.BigDecimalUtil;
import com.somytrip.utils.CommonConstants;
import com.somytrip.utils.LocalDateTimeUtil;
import com.somytrip.utils.StaticInfoUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName: NCNBHotelServiceImpl
 * @Description: 捷旅酒店Service实现类
 * @Author: shadow
 * @Date: 2024/3/10 9:52
 */
@Slf4j
@Service
@DubboService
public class NCNBHotelServiceImpl implements NCNBHotelService {

    @Resource
    private NCNBService ncnbService;
    @Resource
    private CityMappingNCNBService cityMappingNCNBService;
    @Resource
    private HotelGeneralService hotelGeneralService;
    @Resource
    private HotelRoomService hotelRoomService;

    @DubboReference
    private OrderBaseService orderBaseService;

    /**
     * 查询酒店列表
     *
     * @param queryHotelListDto 查询酒店列表参数dto
     * @return ListResp<HotelListVo> 酒店列表响应
     */
    @Override
    public ListResp<HotelListVo> queryHotelList(QueryHotelListDtoV2 queryHotelListDto) {

        NCNBHotelListSearchReq hotelListSearchReq = new NCNBHotelListSearchReq();

        // 设置参数
        setHotelListReq(hotelListSearchReq, queryHotelListDto);

        // 封装请求体
        NCNBReqBody<NCNBHotelListSearchReq> reqBody = new NCNBReqBody<>();
        reqBody.setSearchConditions(hotelListSearchReq);
        reqBody.setScrollingInfo(new NCNBReqBody.ScrollingInfo(queryHotelListDto.getPagination()));

        // 请求数据
        NCNBHotelListSearchResp hotelListSearchResp = ncnbService.hotelListSearch(reqBody);
        Long hotelCount = hotelListSearchResp.getHotelCount();
        List<NCNBHotelListSearchResp.HotelListInfo> hotelListInfos = hotelListSearchResp.getHotelListInfos();
        List<HotelListVo> hotelListVoList = hotelListInfos.stream().map(HotelListVo::new).toList();

        return new ListResp<>(hotelListVoList, hotelCount);
    }

    /**
     * 查询酒店详情
     *
     * @param queryHotelDetailDto 查询酒店详情参数dto
     * @return HotelDetailVo 酒店详情vo
     */
    @Override
    public HotelDetailVo queryHotelDetail(QueryHotelDetailDtoV2 queryHotelDetailDto) {

        String hotelId = queryHotelDetailDto.getHotelId();
//        HotelOrigin hotelOrigin = queryHotelDetailDto.getHotelOrigin();

        HotelDetailVo hotelDetailVo = new HotelDetailVo();
//        hotelDetailVo.setHotelId(hotelId);

//        HotelGeneralEntity hotelGeneralEntity = hotelGeneralService.queryOneByHotelId(hotelId, hotelOrigin);
//        if (hotelGeneralEntity != null) {
////            return null;
//            hotelDetailVo.mergeHotelGeneral(hotelGeneralEntity);
//        } else {
//            NCNBHotelListSearchReq hotelListSearchReq = new NCNBHotelListSearchReq();
//            hotelListSearchReq.setHotelId(queryHotelDetailDto.getHotelId());
//            HotelDateRange dateRange = new HotelDateRange(queryHotelDetailDto.getDateRange());
//            hotelListSearchReq.setStayDateRange(dateRange);
//            // 分页参数 详情只查询一条
//            PaginationDto paginationDto = new PaginationDto(1, 1);
//            // 获取请求体
//            NCNBReqBody<NCNBHotelListSearchReq> requestBody = NCNBRequestUtil.getRequestBody(hotelListSearchReq, paginationDto);
//            // 请求数据
//            NCNBHotelListSearchResp hotelListSearchResp = ncnbService.hotelListSearch(requestBody);
//            if (hotelListSearchResp == null) {
//                return null;
//            }
//            Long hotelCount = hotelListSearchResp.getHotelCount();
//            List<NCNBHotelListSearchResp.HotelListInfo> hotelListInfos = hotelListSearchResp.getHotelListInfos();
//            if (hotelCount < 1 || hotelListInfos.isEmpty()) {
//                return null;
//            }
//            // 取出酒店
//            NCNBHotelListSearchResp.HotelListInfo hotelListInfo = hotelListInfos.get(0);
//
//            // 转换为hotelDetailVo
//            hotelDetailVo = convertHotelListInfo2HotelDetailVo(hotelListInfo);
//        }

        // 酒店政策
        HotelPolicyVo hotelPolicy = hotelDetailVo.getHotelPolicy() != null ? hotelDetailVo.getHotelPolicy() : new HotelPolicyVo();

        // 判断是否查询房间
        if (queryHotelDetailDto.isQueryRooms()) {
            // 查询价格库存数据
            QueryRatePlanDto queryRatePlanDto = new QueryRatePlanDto();
            queryRatePlanDto.setHotelId(hotelId);
            queryRatePlanDto.setDateRange(queryHotelDetailDto.getDateRange());
            queryRatePlanDto.setCalculateRate(true);
            List<RoomListVo> roomListVos = queryRatePlanByHotelId(queryRatePlanDto);

            // 设置取消政策
            if (!roomListVos.isEmpty()) {
                if (!roomListVos.get(0).getRatePlans().isEmpty()) {
                    RoomListVo.RoomListRatePlanVo ratePlanVo = roomListVos.get(0).getRatePlans().get(0);
                    hotelPolicy.setCancelPolicy(ratePlanVo.getCancelPolicy().getCancelDescription());
                    hotelDetailVo.setHotelPolicy(hotelPolicy);
                }
            }

            // 查询合并房间基本信息
            queryRoomInfoByRoomListVos(queryHotelDetailDto.getHotelId(), roomListVos);

            // 房间列表按照价格升序
            roomListVos.sort(Comparator.comparing(RoomListVo::getStartPrice));

            hotelDetailVo.setRoomList(roomListVos);
        }

        return hotelDetailVo;
    }

    /**
     * 查询实时房间列表
     *
     * @param queryRatePlanDto 查询价格参数dto
     * @return java.util.List<com.somytrip.entity.vo.hotel.RoomListVo>
     * <AUTHOR>
     * @date 2024/5/31 10:49
     */
    @Override
    public List<RoomListVo> queryRoomList(QueryRatePlanDto queryRatePlanDto) {


        // 查询价格库存数据
        List<RoomListVo> roomListVos = queryRatePlanByHotelId(queryRatePlanDto);

        // 查询合并房间基本信息
        String hotelId = queryRatePlanDto.getHotelId();
        queryRoomInfoByRoomListVos(hotelId, roomListVos);

        // 房间列表按照价格升序
        roomListVos.sort(Comparator.comparing(RoomListVo::getStartPrice));
        return roomListVos;
    }

    /**
     * 查询酒店筛选项
     *
     * @param queryHotelFilterDto 查询酒店筛选项参数dto
     * @return HotelFilterVo 酒店筛选项vo
     */
    @Override
    public HotelFilterVo queryHotelFilter(QueryHotelFilterDto queryHotelFilterDto) {

        HotelFilterVo hotelFilterVo = new HotelFilterVo();

//        String cityCode = queryHotelFilterDto.getCityCode();
        String filterType = queryHotelFilterDto.getFilterType();

        // TODO: 获取捷旅城市ID


        if (StringUtils.isBlank(filterType)) {
            // 查询位置筛选项
            // TODO: 查询位置筛选类别列表配置
            List<String> locationFilterTypeIds = List.of("5");

            // 查询其他筛选项
            // TODO: 查询其他筛选类别列表配置
            List<String> otherFilterTypeIds = List.of("3");

            String filterTypes = Stream.concat(locationFilterTypeIds.stream(), otherFilterTypeIds.stream())
                    .distinct()
                    .collect(Collectors.joining(","));

            // FIXME: 先返回空筛选项
            List<HotelFilterVo.FilterType> locationFilterTypeList = HotelFilterVo.getEmptyFilterTypeList(filterTypes);
            List<HotelFilterVo.FilterType> otherFilterTypeList = HotelFilterVo.getEmptyFilterTypeList(filterTypes);
            hotelFilterVo.setLocation(locationFilterTypeList);
            hotelFilterVo.setFilters(otherFilterTypeList);
        }

        return hotelFilterVo;
    }

    /**
     * 创建订单
     *
     * @param createOrderDto 创建订单参数dto
     * @return CreateOrderResultDto 创建订单结果
     */
    @Override
    public CreateOrderResultDto createOrder(CreateOrderDtoV2 createOrderDto) {

        String hotelId = createOrderDto.getHotelId();
        String roomTypeId = createOrderDto.getRoomTypeId();
        String ratePlanId = createOrderDto.getRatePlanId();
        HotelDateRangeV2 dateRange = createOrderDto.getDateRange();
        Long uid = createOrderDto.getUid();

        // 查询产品信息
        QueryRatePlanDto queryRatePlanDto = new QueryRatePlanDto(createOrderDto);
        queryRatePlanDto.setCalculateRate(false);
        log.info("queryRatePlanDto: {}", JSONObject.toJSONString(queryRatePlanDto));
        List<RoomListVo> roomListVos = queryRatePlanByHotelId(queryRatePlanDto);
//        log.info("roomListVos: {}", JSONArray.toJSONString(roomListVos));
        RoomListVo.RoomListRatePlanVo roomListRatePlanVo = null;
        try {
            roomListRatePlanVo = roomListVos.get(0).getRatePlans().get(0);
        } catch (IndexOutOfBoundsException e) {
            log.warn("[NCNB]RatePlan Not Found, hotelId: {}, roomId: {}, ratePlanId: {}, dateRange: {}",
                    hotelId, roomTypeId, ratePlanId, dateRange);
        }
        if (roomListRatePlanVo == null) {
            // 未找到产品信息
//            throw new BusinessException(ReturnMessageEnum.HOTEL_ORDER_RATEPLAN_NOTFOUND.toString());
            throw new BusinessException("api.hotel.order.ratePlan_notFound");
        }
        BigDecimal rate = roomListRatePlanVo.getRate();
        Integer numberOfRooms = createOrderDto.getNumberOfRooms();
        BigDecimal formTotalPrice = createOrderDto.getTotalPrice();

        // FIXME: 读取酒店加价配置
        BigDecimal ratio = BigDecimal.valueOf(1.01);

        // 原始总价(未加价)
        BigDecimal originalTotalPrice = rate.multiply(new BigDecimal(numberOfRooms));
        createOrderDto.setOriginalTotalPrice(originalTotalPrice);

        // 加价
        roomListRatePlanVo = roomListRatePlanVo.rateMakeup(ratio);
        BigDecimal realTotalPrice = roomListRatePlanVo.getTotalRate();
        if (numberOfRooms > 1) {
            realTotalPrice = realTotalPrice.multiply(BigDecimal.valueOf(numberOfRooms));
        }

        // 对比实际支付总价
        if (realTotalPrice.compareTo(formTotalPrice) != 0) {
            throw new BusinessException("api.hotel.order.price_incorrect");
        }

        // 可订检查
        boolean preBookingCheckResult = preBookingCheck(createOrderDto);
        log.info("preBookingCheckResult: {}", JSONObject.toJSONString(preBookingCheckResult));
        if (!preBookingCheckResult) {
            // 可订检查未通过
//            throw new BusinessException(ReturnMessageEnum.HOTEL_ORDER_BOOKING_CHECK_FAIL.toString());
            throw new BusinessException("api.hotel.order.booking_check_fail");
        }

        // 处理产品取消政策罚金
        // 每晚价格
        BigDecimal perNightPrice = BigDecimalUtil.ceiling(rate.multiply(ratio), 0);
        for (CancelPolicyVo.CancelDetail cancelDetail : roomListRatePlanVo.getCancelPolicy().getCancelDetails()) {
            Integer penaltyType = cancelDetail.getPenaltyType();
            BigDecimal penaltyValue = cancelDetail.getPenaltyValue();
            switch (penaltyType) {
                case 0 -> cancelDetail.setAmount(BigDecimal.ZERO);
                case 1 -> cancelDetail.setAmount(penaltyValue);
                case 2 -> {
                    penaltyValue = penaltyValue.divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP);
                    cancelDetail.setAmount(BigDecimalUtil.ceiling(originalTotalPrice.multiply(penaltyValue), 0));
                }
                case 3 -> cancelDetail.setAmount(perNightPrice.multiply(penaltyValue));
            }
        }

        // 获取初始化订单实体
        HotelOrderEntity orderEntity = getInitialOrderEntity(createOrderDto);
        // 用户ID
        orderEntity.setUid(uid);
        // 产品ID
        orderEntity.setRatePlanId(String.valueOf(ratePlanId));
        // 产品信息
        orderEntity.setRatePlan(roomListRatePlanVo);
        // TODO: 餐食信息
        // 真实支付价格
        orderEntity.setRealPayPrice(formTotalPrice);
        // 真实原始价格
        orderEntity.setRealOriginalPrice(originalTotalPrice);
        // 支付价格(单位 分)
        orderEntity.setPayAmount(formTotalPrice.multiply(BigDecimal.valueOf(100)).intValue());
        // 原始价格(单位 分)
        orderEntity.setOriginalAmount(originalTotalPrice.multiply(BigDecimal.valueOf(100)).intValue());
//        // 每晚价格
//        orderEntity.setPerNightPrice(perNightPrice);

        log.info("orderEntity: {}", JSONObject.toJSONString(orderEntity));
        // nacos创建订单
        CreateOrderVo createOrderVo = getCreateOrderVo(uid, orderEntity, roomListRatePlanVo);
        log.info("createOrderVo: {}", JSONObject.toJSONString(createOrderVo));
        ResponseResult createResult;
        try {
            createResult = orderBaseService.createOrderBaseV2(createOrderVo);
            log.info("createResult: {}", createResult);
            if (!createResult.isSuccess()) {
//                if (Objects.equals(333, createResult.getCode())) {
//                    throw new BusinessException("")
//                }
                throw new BusinessException(createResult.getCode(), createResult.getMessage());
            }
        } catch (OrderException e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("");
        }

        // 订单号
        String orderNo = String.valueOf(createResult.getData());
        CreateOrderResultDto createOrderResultDto = new CreateOrderResultDto();
        createOrderResultDto.setOrderNo(orderNo);

        return createOrderResultDto;
    }

    /**
     * 可订检查
     *
     * @param createOrderDto 创建订单dto
     * @return boolean true: 可以预订, false: 不可预订
     */
    @Override
    public boolean preBookingCheck(CreateOrderDtoV2 createOrderDto) {

        NCNBPreBookingCheckReq preBookingCheckReq = new NCNBPreBookingCheckReq();
        preBookingCheckReq.setHotelId(createOrderDto.getHotelId());
        preBookingCheckReq.setRoomId(createOrderDto.getRoomTypeId());
        preBookingCheckReq.setRateplanId(String.valueOf(createOrderDto.getRatePlanId()));
        HotelDateRange dateRange = new HotelDateRange(createOrderDto.getDateRange());
        preBookingCheckReq.setCheckIn(dateRange.getCheckIn());
        preBookingCheckReq.setCheckOut(dateRange.getCheckOut());
        preBookingCheckReq.setRoomCount(String.valueOf(createOrderDto.getNumberOfRooms()));
        preBookingCheckReq.setOrderAmount(String.valueOf(createOrderDto.getOriginalTotalPrice().multiply(BigDecimal.valueOf(1000))));
        if (StringUtils.isNotBlank(createOrderDto.getNationality())) {
            preBookingCheckReq.setNationality(createOrderDto.getNationality());
        }

        // 获取请求体
        NCNBReqBody<NCNBPreBookingCheckReq> requestBody = NCNBRequestUtil.getRequestBody(preBookingCheckReq);
        // 请求
        List<NCNBPreBookingCheckResp> preBookingCheckResps = ncnbService.preBookingCheck(requestBody);

        // FIXME: 处理订多房情况
        for (NCNBPreBookingCheckResp preBookingCheckResp : preBookingCheckResps) {
            String status = preBookingCheckResp.getStatus();
            if (Objects.equals("026001", status)) {
                continue;
            }
            // 判断房间是否可用
            if (Objects.equals("026002", status)) {
                throw new BusinessException("api.hotel.room.unavailable");
            }
            // 判断房间数量是否足够
            Long count = preBookingCheckResp.getCount();
            if (count < createOrderDto.getNumberOfRooms()) {
                throw new BusinessException("api.hotel.room.insufficient");
            }
        }

        return true;
    }

    /**
     * 支付订单(捷旅创建订单)
     *
     * @param orderNo     订单号
     * @param orderEntity 订单Entity
     * @return CreateOrderResultDto 创建订单结果
     */
    @Override
    public CreateOrderResultDto payOrder(String orderNo, HotelOrderEntity orderEntity) {

        // 酒店信息
        OrderHotelInfo hotelInfo = orderEntity.getHotelInfo();
        // 房间信息
        OrderRoomInfo roomInfo = orderEntity.getRoomInfo();

        NCNBBookingReq bookingReq = new NCNBBookingReq();
        // 酒店ID
        bookingReq.setHotelId(hotelInfo.getHotelId());
        // 房间ID
        bookingReq.setRoomId(roomInfo.getRoomId());
        // 房间数量
        bookingReq.setRoomCount(Long.valueOf(roomInfo.getNumber()));
        // 价格计划ID
        bookingReq.setRatePlanId(orderEntity.getRatePlanId());
        // 价格(分)
//        bookingReq.setOrderAmount(String.valueOf(orderEntity.getRealPrice().intValue()));
        bookingReq.setOrderAmount(String.valueOf(orderEntity.getRealOriginalPrice()));
        // 入住时间
//        HotelDateRange dateRange = processPayOrderDateRange(orderEntity.getDateRange());
//        HotelDateRangeV2 orderDateRange = orderEntity.getOrderDateRange();
        HotelOrderEntity.HotelOrderDateRange orderDateRange = orderEntity.getOrderDateRange();
        bookingReq.setCheckIn(orderDateRange.getCheckIn().toString());
        // 离店时间
        bookingReq.setCheckOut(orderDateRange.getCheckOut().toString());
        // 预定人信息
        bookingReq.setBookInfo(new NCNBBookingReq.BookInfo(orderEntity.getContact()));
        // 入住人信息
        if (orderEntity.getCustomers() != null) {
            List<NCNBBookingReq.GuestInfo> guestInfos = orderEntity.getCustomers().stream()
                    .map(NCNBBookingReq.GuestInfo::new)
                    .toList();
            bookingReq.setGuestInfos(guestInfos);
        }
        // 特殊要求
        bookingReq.setSpecialRemark(orderEntity.getSpecialRemark());
        // 国籍
        bookingReq.setNationality(orderEntity.getNationality());
        // 自定义订单ID
        bookingReq.setCustomerOrderId(orderNo);

        // 组装请求
        NCNBReqBody<NCNBBookingReq> reqBody = new NCNBReqBody<>();
        reqBody.setSearchConditions(bookingReq);

        log.info("[NCNB]Create Order RspItem: {}", JSONObject.toJSONString(reqBody));

        NCNBBookingResp bookingResp = ncnbService.booking(reqBody);
        log.info("[NCNB]Create Order Resp: {}", JSONObject.toJSONString(bookingResp));

        return new CreateOrderResultDto(bookingResp);
    }

    /**
     * 取消订单
     *
     * @param cancelOrderDto 取消订单参数dto
     * @return CancelOrderResultDto 取消订单结果dto
     */
    @Override
    public CancelOrderResultDto cancelOrder(CancelOrderDto cancelOrderDto) {

        JSONObject param = new JSONObject();
        param.put("orderId", cancelOrderDto.getThirdPartyOrderId());

        // 获取请求体
        NCNBReqBody<JSONObject> requestBody = NCNBRequestUtil.getRequestBody(param);
        NCNBBookingCancelResp bookingCancelResp = ncnbService.bookingCancel(requestBody);
        log.info("NCNB Hotel Cancel Resp: {}", bookingCancelResp);
        return new CancelOrderResultDto(bookingCancelResp);
    }

    /**
     * 查询订单详情
     *
     * @param orderId 订单ID
     * @return OrderDetailVoV2 订单详情vo
     */
    @Override
    public OrderDetailVoV2 queryOrderDetail(String orderId) {
        NCNBOrderSearchReq orderSearchReq = new NCNBOrderSearchReq();
        orderSearchReq.setOrderId(orderId);
        PaginationDto pagination = new PaginationDto(1, 1);
        // 获取请求体
        NCNBReqBody<NCNBOrderSearchReq> requestBody = NCNBRequestUtil.getRequestBody(orderSearchReq, pagination);
        // 请求数据
        NCNBOrderSearchResp orderSearchResp = ncnbService.orderSearch(requestBody);
        List<NCNBOrderSearchResp.OrderSearchInfo> orderSearchInfos = orderSearchResp.getOrderSearchInfos();
        if (orderSearchInfos == null || orderSearchInfos.isEmpty()) {
            // 未找到订单
            log.warn("[NCNB]-queryOrderDetail，未找到订单: {}", orderId);
            throw new BusinessException("api.hotel.order.not_found");
        }
        return new OrderDetailVoV2(orderSearchInfos.get(0));
    }

    /**
     * 根据房间列表vo查询合并房间基本信息
     *
     * @param hotelId     酒店ID
     * @param roomListVos 房间列表Vo
     */
    private void queryRoomInfoByRoomListVos(String hotelId, List<RoomListVo> roomListVos) {

        if (roomListVos == null || roomListVos.isEmpty()) {
            return;
        }

        Locale locale = LocaleContextHolder.getLocale();
        String lang = NCNBLocaleMapping.fromLocale(locale);

        for (int i = roomListVos.size() - 1; i >= 0; --i) {
            String roomId = roomListVos.get(i).getRoomId();
            RoomListVo roomInfo = hotelRoomService.queryOne(roomId, hotelId, HotelOrigin.NCNB);
            if (roomInfo == null) {
                NCNBRoomSearchReq roomSearchReq = new NCNBRoomSearchReq();
                roomSearchReq.setHotelId(hotelId);
                roomSearchReq.setRoomId(roomId);
                roomSearchReq.setLang(lang);
                // 获取请求体
                NCNBReqBody<NCNBRoomSearchReq> requestBody = NCNBRequestUtil.getRequestBody(roomSearchReq, true);
                // 请求数据
                NCNBRoomSearchResp roomSearchResp = ncnbService.roomSearch(requestBody);
                if (roomSearchResp == null) {
                    throw new BusinessException("api.hotel.room.not-found");
                }
                List<NCNBRoomSearchResp.HotelRoomInfo> hotelRoomInfos = roomSearchResp.getHotelRoomInfos();
                // 取出当前房间静态信息
                NCNBRoomSearchResp.HotelRoomInfo hotelRoomInfo = hotelRoomInfos.get(0);
                NCNBRoomSearchResp.HotelRoomInfo.RoomInfo searchRoomInfo = hotelRoomInfo.getRoomInfos().get(0);
                if (searchRoomInfo != null && StrUtil.isNotBlank(searchRoomInfo.getRoomName())) {
                    roomListVos.get(i).mergeRoomInfo(searchRoomInfo);
                } else {
                    roomListVos.remove(i);
                }
                continue;
            }
            roomListVos.get(i).mergeRoomInfo(roomInfo);
        }
    }

    /**
     * 根据酒店ID查询房间价格库存数据
     *
     * @param queryRatePlanDto 查询产品(价格、库存)参数dto
     * @return Item<RoomListVo> 房间列表vo列表
     */
    private List<RoomListVo> queryRatePlanByHotelId(QueryRatePlanDto queryRatePlanDto) {

        // TODO: 读取配置获取酒店加价比例
        BigDecimal ratio = BigDecimal.valueOf(1.01);

        List<RoomListVo> roomListVos = new ArrayList<>();

        String hotelId = queryRatePlanDto.getHotelId();
        HotelDateRange dateRange = new HotelDateRange(queryRatePlanDto.getDateRange());
        String roomId = queryRatePlanDto.getRoomId();
        String ratePlanId = queryRatePlanDto.getRatePlanId();

        // 本地化
        Locale locale = LocaleContextHolder.getLocale();
        String lang = NCNBLocaleMapping.fromLocale(locale);

        NCNBRatePlanSearchReq ratePlanSearchReq = new NCNBRatePlanSearchReq();
        ratePlanSearchReq.setHotelId(hotelId);
        ratePlanSearchReq.setStayDateRange(new NCNBHotelListSearchReq.StayDateRange(dateRange));
        ratePlanSearchReq.setLang(lang);

        // 计算入住天数
        LocalDate checkIn = queryRatePlanDto.getDateRange().getCheckIn();
        LocalDate checkOut = queryRatePlanDto.getDateRange().getCheckOut();
        long days = checkIn.until(checkOut, ChronoUnit.DAYS);

        // 获取请求体
        log.info("ratePlanSearchReq: {}", JSONObject.toJSONString(ratePlanSearchReq));
        NCNBReqBody<NCNBRatePlanSearchReq> requestBody = NCNBRequestUtil.getRequestBody(ratePlanSearchReq);
        // 请求数据
        NCNBRatePlanSearchResp ratePlanSearchResp = ncnbService.ratePlanSearch(requestBody);
        log.info("ratePlanSearchResp: {}", JSONObject.toJSONString(ratePlanSearchResp));
        if (ratePlanSearchResp == null) {
            log.warn("NCNB RatePlanSearchResp is Null");
//            throw new BusinessException("api.hotel.order.ratePlan_notFound");
            throw new BusinessException("api.hotel.order.no-rooms");
        }
        List<NCNBRatePlanSearchResp.HotelPriceRoomInfo> hotelPriceRoomInfos = ratePlanSearchResp.getHotelPriceRoomInfos();
        for (NCNBRatePlanSearchResp.HotelPriceRoomInfo hotelPriceRoomInfo : hotelPriceRoomInfos) {
            List<NCNBRatePlanSearchResp.Room> rooms = hotelPriceRoomInfo.getRooms();
            // 遍历捷旅房间列表
            for (NCNBRatePlanSearchResp.Room room : rooms) {
                if (StringUtils.isNotBlank(roomId) && !Objects.equals(roomId, room.getRoomId())) {
                    continue;
                }

                List<NCNBRatePlanSearchResp.Room.Rate> rates = room.getRates();
                List<NCNBRatePlanSearchResp.Room.RatePlan> ratePlans = room.getRatePlans();
                RoomListVo roomListVo = new RoomListVo();
                List<RoomListVo.RoomListRatePlanVo> roomListRatePlanVoList = new ArrayList<>();

                BigDecimal minRate = BigDecimal.valueOf(Double.MAX_VALUE);
                roomListVo.setRoomId(room.getRoomId());
                roomListVo.setRoomName(room.getRoomName());

                // 遍历房间产品列表
                for (int i = 0; i < rates.size(); ++i) {
                    NCNBRatePlanSearchResp.Room.Rate rate = rates.get(i);
                    if (StringUtils.isNotBlank(ratePlanId) && !Objects.equals(ratePlanId, rate.getRatePlanId())) {
                        continue;
                    }
                    List<NCNBRatePlanSearchResp.Room.Rate.PriceAndStatus> priceAndStatus = rate.getPriceAndStatus();
                    // 有库存的才加入产品列表
                    if (priceAndStatus == null || priceAndStatus.isEmpty()) {
                        continue;
                    }
                    // 排除产品长度小于天数的房间
                    if (priceAndStatus.size() < days) {
                        continue;
                    }
                    // 判断房间状态
                    if (!NcnbHotelStatusConstant.BOOKING_STATUS.equals(priceAndStatus.get(0).getStatus())) {
                        continue;
                    }
                    RoomListVo.RoomListRatePlanVo roomListRatePlanVo = new RoomListVo.RoomListRatePlanVo(rate, locale);
                    roomListRatePlanVo.setRoomTypeId(roomId);
                    NCNBRatePlanSearchResp.Room.RatePlan ratePlan = ratePlans.get(i);
                    rate.setRatePlanId(ratePlan.getRatePlanId());
                    // 餐食类型
                    String breakfastCode = ratePlan.getBreakfast();
                    String breakfast = NCNBMealLocale.getLocaleValue(breakfastCode, locale);
                    roomListRatePlanVo.setBoardDesc(breakfast);

                    // 判断是否进行价格计算
                    if (queryRatePlanDto.isCalculateRate()) {
                        // 加价
                        roomListRatePlanVo = roomListRatePlanVo.rateMakeup(ratio);
                    }

                    BigDecimal curRate = roomListRatePlanVo.getRate();
                    // 记录最低价
                    if (minRate.compareTo(curRate) > 0) {
                        minRate = curRate;
                    }

                    roomListRatePlanVoList.add(roomListRatePlanVo);
                }

                if (!roomListRatePlanVoList.isEmpty()) {
                    // 有库存的才加入房间列表
                    roomListVo.setRatePlans(roomListRatePlanVoList);
                    // 起价(最低价)
                    roomListVo.setStartPrice(BigDecimalUtil.halfAdjust(minRate, 0));
                    roomListVos.add(roomListVo);
                }
            }
        }

        return roomListVos;
    }

    /**
     * 获取初始化订单Entity
     *
     * @param createOrderDto 创建订单参数dto
     * @return HotelOrderEntity 订单Entity
     */
    private HotelOrderEntity getInitialOrderEntity(CreateOrderDtoV2 createOrderDto) {

        HotelOrderEntity orderEntity = new HotelOrderEntity();
        // 订单序列号
        SnowFlakeUtil snowFlakeUtil = new SnowFlakeUtil(1, 2);
        String orderSn = "H" + snowFlakeUtil.nextId();
        orderEntity.setOrderSn(orderSn);
        // 酒店来源
        orderEntity.setHotelOrigin(createOrderDto.getHotelOrigin());
        // 订单状态
        orderEntity.setOrderStatus(HotelOrderStatus.WAIT_PAY.getValue());
        // 产品ID
        orderEntity.setRatePlanId(String.valueOf(createOrderDto.getRatePlanId()));
        // 入住时间区间
        HotelDateRangeV2 dateRange = createOrderDto.getDateRange();
//        orderEntity.setOrderDateRange(dateRange);
        orderEntity.setOrderDateRange(new HotelOrderEntity.HotelOrderDateRange(dateRange));
        // 订单创建时间
        LocalDateTime now = LocalDateTime.now();
        orderEntity.setCreateTime(now);
        orderEntity.setUpdateTime(now);
        // 币种
        orderEntity.setCurrency("CNY");
        // 国籍
        orderEntity.setNationality(createOrderDto.getNationality());
        // 特殊要求
        orderEntity.setSpecialRemark(createOrderDto.getSpecialRemark());
        // 联系人信息(预定人)
        orderEntity.setContact(createOrderDto.getContact());
        // 入主任信息
        // FIXME: 拆分姓名  后续优化
        for (CustomerDto customer : createOrderDto.getCustomers()) {
            String name = customer.getName();
            customer.setFirstName(name.substring(1));
            customer.setLastName(name.substring(0, 1));
        }
        orderEntity.setCustomers(createOrderDto.getCustomers());

        // TODO: 获取酒店信息、房间信息
        // 查询酒店信息
        QueryHotelDetailDtoV2 queryHotelDetailDto =
                new QueryHotelDetailDtoV2(createOrderDto.getHotelOrigin(), createOrderDto.getHotelId());
        queryHotelDetailDto.setDateRange(createOrderDto.getDateRange());
        queryHotelDetailDto.setQueryRooms(false);
        HotelDetailVo hotelDetail = queryHotelDetail(queryHotelDetailDto);
        // 酒店信息
        OrderHotelInfo orderHotelInfo = new OrderHotelInfo(hotelDetail);
        orderHotelInfo.setDateRange(new HotelDateRange(dateRange));
        orderEntity.setHotelInfo(orderHotelInfo);
        // 订单名称
        // FIXME: 暂时用酒店名称
        orderEntity.setOrderName(orderHotelInfo.getHotelName());

        // 查询房间信息
        OrderRoomInfo roomInfo = new OrderRoomInfo();
        RoomListVo roomListVo = queryRoomListVoByRoomId(hotelDetail.getHotelId(), createOrderDto.getRoomTypeId());
        if (roomListVo != null) {
            roomInfo = new OrderRoomInfo(roomListVo);
            roomInfo.setNumber(createOrderDto.getNumberOfRooms());
        } else {
            roomInfo.setRoomId(createOrderDto.getRoomTypeId());
        }
        orderEntity.setRoomInfo(roomInfo);

        return orderEntity;
    }

    /**
     * 设置酒店列表请求参数
     *
     * @param hotelListSearchReq 捷旅酒店列表请求体
     * @param queryHotelListDto  请求参数dto
     */
    private void setHotelListReq(NCNBHotelListSearchReq hotelListSearchReq, QueryHotelListDtoV2 queryHotelListDto) {

        // 城市ID
        String cityId = cityMappingNCNBService.queryNCNBCityIdByCityCode(queryHotelListDto.getCityCode());
        if (StringUtils.isBlank(cityId)) {
            throw new BusinessException("api.hotel.not-support-city");
        }
        hotelListSearchReq.setCityId(cityId);

        // 酒店ID
        if (StringUtils.isNotBlank(queryHotelListDto.getHotelId())) {
            hotelListSearchReq.setHotelId(queryHotelListDto.getHotelId());
        }

//        // 酒店ids
//        if (queryHotelListDto.getHotelIds() != null) {
//            hotelListSearchReq.setHotelIds(queryHotelListDto.getHotelIds());
//        }

        // 语言
        hotelListSearchReq.setLang("GB");

        // 币种
        hotelListSearchReq.setCurrency("CNY");

        // 酒店名称
        if (StringUtils.isNotBlank(queryHotelListDto.getQueryText())) {
            hotelListSearchReq.setHotelName(queryHotelListDto.getQueryText());
        }

        // 经纬度
        if (StringUtils.isNotBlank(queryHotelListDto.getLon()) && StringUtils.isNotBlank(queryHotelListDto.getLat())) {
            hotelListSearchReq.setLonAndLat(queryHotelListDto.getLon() + "," + queryHotelListDto.getLat());
            // 距离
            if (queryHotelListDto.getDistance() != null && queryHotelListDto.getDistance() > 0) {
                BigDecimal distance = BigDecimal.valueOf(queryHotelListDto.getDistance())
                        .divide(BigDecimal.valueOf(1000), RoundingMode.HALF_UP);
                hotelListSearchReq.setDistance(String.valueOf(BigDecimalUtil.halfAdjust2Str(distance, 3)));
            }
        }

        // 星级
        if (queryHotelListDto.getStar() != null) {
            List<String> ncnbStarList = new ArrayList<>();
            for (Integer star : queryHotelListDto.getStar()) {
                String ncnbStar = StaticInfoUtil.getNameByCode(CommonConstants.NCNB_STATIC_STAR, String.valueOf(star));
                ncnbStarList.add(ncnbStar);
            }
            hotelListSearchReq.setStar(String.join(",", ncnbStarList));
        }

        // 价格区间
        if (queryHotelListDto.getHighRate() != null) {
            Integer lowRate = queryHotelListDto.getLowRate() != null ? queryHotelListDto.getLowRate() : 0;
            hotelListSearchReq.setPr(lowRate + "-" + queryHotelListDto.getHighRate());
        }

        // 入住时间区间
        if (queryHotelListDto.getDateRange() != null) {
            hotelListSearchReq.setStayDateRange(new HotelDateRange(queryHotelListDto.getDateRange()));
        } else {
            // 设置默认时间 今天-明天
            HotelDateRange dateRange = new HotelDateRange();
            LocalDate checkin = LocalDate.now();
            LocalDate checkout = checkin.plusDays(1);
            dateRange.setCheckIn(checkin.toString());
            dateRange.setCheckOut(checkout.toString());
            hotelListSearchReq.setStayDateRange(dateRange);
        }

        // 排序
        if (StringUtils.isNotBlank(queryHotelListDto.getSort())) {
            String sortCode = queryHotelListDto.getSort();
            String ncnbSortCode = "RE";
            String ncnbSortType = "ASC";
            switch (sortCode) {
                case "12", "13" -> {
                    ncnbSortCode = "SC";
                    ncnbSortType = "DESC";
                }
                case "14" -> ncnbSortCode = "PR";
                case "15" -> {
                    ncnbSortCode = "PR";
                    ncnbSortType = "DESC";
                }
            }
            hotelListSearchReq.setSortCode(ncnbSortCode);
            hotelListSearchReq.setSortType(ncnbSortType);
        }
    }

    /**
     * 转换捷旅酒店列表对象为统一酒店详情vo
     *
     * @param hotelListInfo 捷旅酒店对象
     * @return HotelDetailVo 统一酒店详情vo
     */
    private HotelDetailVo convertHotelListInfo2HotelDetailVo(NCNBHotelListSearchResp.HotelListInfo hotelListInfo) {

        HotelDetailVo detailVo = new HotelDetailVo();
        // 酒店来源
        detailVo.setHotelOrigin(HotelOrigin.NCNB);
        // 酒店ID
        detailVo.setHotelId(hotelListInfo.getHotelId());
        // 酒店名称
        detailVo.setHotelName(hotelListInfo.getHotelName());
        // 酒店地址
        detailVo.setAddress(hotelListInfo.getAddress());
        // 经度
        detailVo.setLon(hotelListInfo.getLon());
        // 纬度
        detailVo.setLat(hotelListInfo.getLat());
        // 星级
        String ncnbStarStr = StaticInfoUtil.getNameByCode(CommonConstants.NCNB_STATIC_STAR, hotelListInfo.getStar());
        detailVo.setStar(StringUtils.isNotBlank(ncnbStarStr) ? Integer.valueOf(ncnbStarStr) : null);
        // 评分
        detailVo.setScore(hotelListInfo.getScore());
        // 介绍
        detailVo.setIntroduction(hotelListInfo.getIntro());
        // 起价
        detailVo.setStartPrice(hotelListInfo.getStartPrice());
        // 图片列表
        List<NCNBBaseEntity.HotelImage> hotelImages = hotelListInfo.getHotelImages();
        if (hotelImages != null && !hotelImages.isEmpty()) {
            // FIXME: 这里的4改为用配置
            detailVo.setImages(
                    hotelImages.subList(0, Math.min(4, hotelImages.size())).stream()
                            .map(NCNBBaseEntity.HotelImage::getImageUrl)
                            .collect(Collectors.toList())
            );
        }
        // 设施
        if (hotelListInfo.getFacilityInfos() != null) {
            List<NCNBBaseEntity.FacilityInfos.Service> services = hotelListInfo.getFacilityInfos().getServices();

            List<HotelFacilityTypeVo> facilityTypeVoList = new ArrayList<>();
            // 按照设施类型分组
            Map<String, List<NCNBBaseEntity.FacilityInfos.Service>> groupByGroupId =
                    services.stream().collect(Collectors.groupingBy(NCNBBaseEntity.FacilityInfos.Service::getGroupId));
            for (Map.Entry<String, List<NCNBBaseEntity.FacilityInfos.Service>> entry : groupByGroupId.entrySet()) {
                // 设施类型ID
                String groupIdStr = entry.getKey();
                List<NCNBBaseEntity.FacilityInfos.Service> curTypeFacilities = entry.getValue();
                // 设施类型名称
                String facilityTypeName = NCNBFacilityType.getTypeNameById(Integer.valueOf(groupIdStr));
                // 转换设施列表
                List<HotelFacilityVo> facilityVoList = curTypeFacilities.stream().map(HotelFacilityVo::new).toList();

                HotelFacilityTypeVo hotelFacilityTypeVo = new HotelFacilityTypeVo();
                hotelFacilityTypeVo.setFacilityTypeName(facilityTypeName);
                hotelFacilityTypeVo.setFacilities(facilityVoList);

                facilityTypeVoList.add(hotelFacilityTypeVo);
            }
            detailVo.setHotelFacilities(facilityTypeVoList);
        }
        // 开业时间
        if (StringUtils.isNotBlank(hotelListInfo.getStartBusinessDate())) {
            detailVo.setEstablishmentDate(LocalDateTimeUtil.convertDateStr2Year(hotelListInfo.getStartBusinessDate()));
        }
        // 最近装修时间
        if (hotelListInfo.getRepairdate() != null) {
            detailVo.setRenovationDate(LocalDateTimeUtil.convertDateStr2Year(hotelListInfo.getRepairdate()));
        }
        // 酒店政策
        HotelPolicyVo hotelPolicyVo = new HotelPolicyVo();
        // 酒店电话
        String hotelTel = hotelListInfo.getHotelTel();
        if (hotelPolicyVo.getTels() != null) {
            hotelPolicyVo.getTels().add(hotelTel);
        } else {
            hotelPolicyVo.setTels(new ArrayList<>(List.of(hotelTel)));
        }
        detailVo.setHotelPolicy(hotelPolicyVo);

        return detailVo;
    }

    /**
     * 根据房间ID查询房间基本信息
     *
     * @param hotelId 酒店ID
     * @param roomId  房间ID
     * @return RoomListVo
     */
    private RoomListVo queryRoomListVoByRoomId(String hotelId, String roomId) {

        return hotelRoomService.queryOne(roomId, hotelId, HotelOrigin.NCNB);
//        NCNBRoomSearchReq roomSearchReq = new NCNBRoomSearchReq();
//        roomSearchReq.setHotelId(hotelId);
//        roomSearchReq.setRoomId(roomId);
//
//        PaginationDto pagination = new PaginationDto(1, 1);
//
//        // 获取请求体
//        NCNBReqBody<NCNBRoomSearchReq> requestBody = NCNBRequestUtil.getRequestBody(roomSearchReq, pagination);
//        // 请求
//        NCNBRoomSearchResp roomSearchResp = ncnbService.roomSearch(requestBody);
//        List<NCNBRoomSearchResp.HotelRoomInfo> hotelRoomInfos = roomSearchResp.getHotelRoomInfos();
//        if (hotelRoomInfos.isEmpty()) {
//            return null;
//        }
//        List<NCNBRoomSearchResp.HotelRoomInfo.RoomInfo> roomInfos = hotelRoomInfos.get(0).getRoomInfos();
//        if (roomInfos.isEmpty()) {
//            return null;
//        }
//        NCNBRoomSearchResp.HotelRoomInfo.RoomInfo roomInfo = roomInfos.get(0);
//        RoomListVo roomListVo = new RoomListVo();
//        roomListVo.mergeRoomInfo(roomInfo);
//        return roomListVo;
    }

    private CreateOrderVo getCreateOrderVo(Long uid, HotelOrderEntity orderEntity, RoomListVo.RoomListRatePlanVo ratePlanVo) {

        CreateOrderVo createOrderVo = new CreateOrderVo();
        CreateOrderMasterVo createOrderMasterVo = new CreateOrderMasterVo();
        createOrderMasterVo.setOrderType("NORMAL");
        createOrderMasterVo.setBusinessType("HOTEL");
        createOrderMasterVo.setUserSign(String.valueOf(uid));
        createOrderMasterVo.setUserType("admin");
        /* ------------- */
        CreateOrderFinanceVo createOrderFinanceVo = this.getCreateOrderFinanceVo(orderEntity);
        CreateOrderProductVo createOrderProductVo = this.getCreateOrderProductVo(orderEntity);
        /* ------------- */
        createOrderVo.setOrderMaster(createOrderMasterVo);
        createOrderVo.setOrderFinance(createOrderFinanceVo);
        createOrderVo.setOrderProduct(createOrderProductVo);

        JSONObject.from(orderEntity);
        OrderBigFiledVo orderBigFiledVo = new OrderBigFiledVo();
        orderBigFiledVo.setThirdServiceType(orderEntity.getHotelOrigin().getServiceType());
        orderBigFiledVo.setField1(JSONObject.from(orderEntity));
        orderBigFiledVo.setField2(JSONObject.from(ratePlanVo));
        createOrderVo.setOrderBigFiled(orderBigFiledVo);
        log.info("订单持久化: {}", JSONObject.toJSONString(createOrderVo));
        return createOrderVo;
    }

    private CreateOrderFinanceVo getCreateOrderFinanceVo(HotelOrderEntity orderEntity) {

        CreateOrderFinanceVo createOrderFinanceVo = new CreateOrderFinanceVo();
        createOrderFinanceVo.setCurrency("CNY");
        createOrderFinanceVo.setEstimatedPrice(orderEntity.getPayAmount());
        createOrderFinanceVo.setPaymentMode("wechat");
        createOrderFinanceVo.setPaymentFrom("applet");
        createOrderFinanceVo.setSettleMode("electronic");
        createOrderFinanceVo.setBeforeDiscountPrice(orderEntity.getPayAmount());
        createOrderFinanceVo.setAfterDiscountPrice(orderEntity.getPayAmount());
        createOrderFinanceVo.setCollectionAgency("yeepay");
        createOrderFinanceVo.setTotalDiscountPrice(0);
        LocalDateTime orderCreateTime = orderEntity.getCreateTime();
        Date paymentDDL = LocalDateTimeUtil.localDateTime2Date(orderCreateTime.plusMinutes(30));
        createOrderFinanceVo.setPaymentDeadline(paymentDDL);
        return createOrderFinanceVo;
    }

    private CreateOrderProductVo getCreateOrderProductVo(HotelOrderEntity orderEntity) {

        CreateOrderProductVo createOrderProductVo = new CreateOrderProductVo();
        createOrderProductVo.setProductCode(orderEntity.getHotelInfo().getHotelId());
        createOrderProductVo.setProductName(orderEntity.getOrderName());
        // 酒店
        createOrderProductVo.setProductType(2);
        createOrderProductVo.setQuantity(1);
        createOrderProductVo.setProductType(3);
        createOrderProductVo.setBeforeDiscountPrice(orderEntity.getPayAmount());
        createOrderProductVo.setAfterDiscountPrice(orderEntity.getPayAmount());
        createOrderProductVo.setCreditExchangeNumber(0);
        createOrderProductVo.setCreditExchangePrice(0);
        return createOrderProductVo;
    }
}
