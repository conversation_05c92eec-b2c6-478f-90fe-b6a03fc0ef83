package com.somytrip.provider.hotel.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.hotel.HotelFacilityTypeService;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import com.somytrip.entity.hotel.HotelFacilityTypeEntity;
import com.somytrip.provider.hotel.mapper.HotelFacilityTypeMapper;
import com.somytrip.provider.hotel.utils.DataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 酒店设施类型表(HotelFacilityTypes)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-02 10:48:45
 */
@Slf4j
@DubboService
@Service("hotelFacilityTypesService")
public class HotelFacilityTypeServiceImpl
        extends ServiceImpl<HotelFacilityTypeMapper, HotelFacilityTypeEntity>
        implements HotelFacilityTypeService {

    /**
     * 更新酒店设施类型(同程艺龙 列表)
     *
     * @param facilityTypes 同程艺龙酒店设施类型列表
     * @return boolean true: 成功, false: 失败
     */
    @Override
    public boolean updateHotelFacilityType(List<ELongStaticHotelInfoResp.FacilityType> facilityTypes) {

        for (ELongStaticHotelInfoResp.FacilityType facilityType : facilityTypes) {
            HotelFacilityTypeEntity facilityTypeEntity = new HotelFacilityTypeEntity(facilityType);
            // 处理更新
            updateHotelFacilityType(facilityTypeEntity);
        }

        return true;
    }

    /**
     * 根据设施类型ID列表查询设施类型名称Map
     *
     * @param typeIds     设施类型ID列表
     * @param hotelOrigin 酒店来源
     * @return java.util.Map<java.lang.Long, java.lang.String>
     * <AUTHOR>
     * @date 2024/4/2 18:42
     */
    @Override
    public Map<Long, String> queryFacilityTypeNameMapByTypeIds(List<Long> typeIds, HotelOrigin hotelOrigin) {

        if (CollUtil.isEmpty(typeIds)) return null;

        LambdaQueryWrapper<HotelFacilityTypeEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(HotelFacilityTypeEntity::getHotelOrigin, hotelOrigin)
                .in(HotelFacilityTypeEntity::getFacilityTypeId, typeIds);
        List<HotelFacilityTypeEntity> facilityTypeEntityList = baseMapper.selectList(qw);

        // 国际化
        Locale locale = LocaleContextHolder.getLocale();

        try {
            Map<Long, String> result = new HashMap<>();
            for (HotelFacilityTypeEntity entity : facilityTypeEntityList) {
                Long facilityTypeId = entity.getFacilityTypeId();
                String facilityTypeName = entity.getFacilityTypeName();
                Map<String, Map<String, String>> localeDict = entity.getLocaleDict();
                if (localeDict != null) {
                    Map<String, String> facilityTypeNameLocaleDict = localeDict.get("facilityTypeName");
                    String facilityTypeNameLocale = facilityTypeNameLocaleDict.get(locale.toString());
                    if (StringUtils.isNotBlank(facilityTypeNameLocale)) {
                        facilityTypeName = facilityTypeNameLocale;
                    }
                }
                result.put(facilityTypeId, facilityTypeName);
            }
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;

//        try {
//            return facilityTypeEntityList.stream()
//                    .collect(Collectors.toMap(
//                            HotelFacilityTypeEntity::getFacilityTypeId,
//                            HotelFacilityTypeEntity::getFacilityTypeName,
//                            (existing, replacement) -> existing,
//                            LinkedHashMap::new
//                    ));
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//        }
//        return null;
    }

    public void updateHotelFacilityType(HotelFacilityTypeEntity newFacilityTypeEntity) {

        // 查询已存在记录
        HotelOrigin hotelOrigin = newFacilityTypeEntity.getHotelOrigin();
        Long facilityTypeId = newFacilityTypeEntity.getFacilityTypeId();
        LambdaQueryWrapper<HotelFacilityTypeEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(HotelFacilityTypeEntity::getHotelOrigin, hotelOrigin).eq(HotelFacilityTypeEntity::getFacilityTypeId, facilityTypeId);
        HotelFacilityTypeEntity existingFacilityTypeEntity = baseMapper.selectOne(qw);

        // 更新数据
        DataUtil.updateData(newFacilityTypeEntity, existingFacilityTypeEntity, baseMapper, "id", "createTime", "updateTime");
    }
}
