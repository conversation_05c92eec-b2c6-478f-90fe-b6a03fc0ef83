package com.somytrip.provider.hotel.utils.request;

import com.alibaba.fastjson2.JSONObject;
import com.somytrip.entity.enums.hotel.method.ELongMethod;
import com.somytrip.entity.hotel.ELong.ELongPublicRequestBody;
import com.somytrip.entity.hotel.ELong.ELongPublicRequestData;
import com.somytrip.entity.hotel.ELong.ELongReqBody;
import com.somytrip.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.context.i18n.LocaleContextHolder;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPInputStream;

/**
 * @ClassName: ELongRequestUtil
 * @Description: 同程艺龙请求工具类
 * @Author: shadow
 * @Date: 2024/1/31 11:45
 */
@Slf4j
public class ELongRequestUtil {

    //    private static final String HOST = "https://api.elong.com";
    private static final String HOST = "http://api.elong.com/rest";

    private static final BigDecimal Version = new BigDecimal("1.65");
    private static final Locale Local = Locale.CHINA;
    private static final List<Locale> availableLocale = new ArrayList<>(List.of(Locale.CHINA, Locale.US));

    private static final String user = "09250e68301e4aafbf33e2fd35220b54";
    private static final String appKey = "59c0ebbba7bd347915a648fc18935e33";
    private static final String secretKey = "669e3884ddf084265b0e8a092adcfe87";
    private static OkHttpClient client = new OkHttpClient.Builder().connectTimeout(60, TimeUnit.SECONDS).readTimeout(60, TimeUnit.SECONDS).build();

    /**
     * 请求方法
     *
     * @param reqBody 请求体
     * @param <T>     请求体类型
     * @return JSONObject 响应json对象
     */
    public static <T> JSONObject generalRequest(ELongReqBody<T> reqBody) {


        JSONObject responseObj = null;
//        ResponseResult response;

        // 时间戳
        String timestamp = getTimestamp();
        // 请求方法
        String method = reqBody.getMethod().getName();
        // 请求参数
        T requestParam = reqBody.getRequest();

        // 公共请求Data
        ELongPublicRequestData<T> publicRequestData = new ELongPublicRequestData<>();
        publicRequestData.setVersion(Version);
        Locale locale = LocaleContextHolder.getLocale();
        if (!availableLocale.contains(locale)) {
            locale = Local;
        }
        publicRequestData.setLocal(locale.toString());
        publicRequestData.setRequest(requestParam);

        // data json字符串
        String dataJson = JSONObject.toJSONString(publicRequestData);
        // 签名
        String sign = getSign(timestamp, dataJson);

        // 公共请求体
        ELongPublicRequestBody publicRequestBody = new ELongPublicRequestBody();
        publicRequestBody.setUser(user);
        publicRequestBody.setFormat("json");
        publicRequestBody.setTimestamp(timestamp);
        publicRequestBody.setData(dataJson);
        publicRequestBody.setMethod(method);
        publicRequestBody.setSignature(sign);

        try {
            HttpUrl httpUrl = HttpUrl.parse(HOST);
            if (httpUrl == null) {
                log.error("httpUrl is null");
                throw new BusinessException("");
            }
            // 添加url参数
            HttpUrl.Builder urlBuilder = httpUrl.newBuilder();
            JSONObject publicRequestBodyObj = JSONObject.from(publicRequestBody);
//            log.info("publicRequestBodyObj: {}", publicRequestBodyObj.toJSONString());
            for (Map.Entry<String, Object> entry : publicRequestBodyObj.entrySet()) {
                urlBuilder.addEncodedQueryParameter(entry.getKey(), String.valueOf(entry.getValue()));
            }
            String url = urlBuilder.build().toString();
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .addHeader("Content-Type", "application/json;charset=utf-8")
                    .addHeader("Accept-Encoding", "gzip")
                    .build();
//
            StringBuilder responseStr = new StringBuilder();

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new IOException("Unexpected code " + response);
                }
                if (response.body() == null) {
                    throw new BusinessException("");
                }

                List<String> contentEncoding = response.headers("content-encoding");
                if (contentEncoding.contains("gzip")) {
                    try (GZIPInputStream gis = new GZIPInputStream(response.body().byteStream())) {
                        // 从gis读取解压缩后的数据
                        BufferedReader reader = new BufferedReader(new InputStreamReader(gis, StandardCharsets.UTF_8));
                        String line;
                        while ((line = reader.readLine()) != null) {
                            responseStr.append(line);
                        }
                    }
                } else {
                    responseStr = new StringBuilder(response.body().string());
                }
//                log.info("responseStr: {}", responseStr);

                responseObj = JSONObject.parseObject(responseStr.toString()).getJSONObject("Result");
                if (responseObj == null) {
                    log.error("Result is Null");
                    log.error("responseStr: {}", responseStr);
                }
            }
        } catch (IOException e) {
            log.error("请求失败");
            log.error(e.getMessage(), e);
            throw new BusinessException("请求失败");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return responseObj;
    }

    /**
     * 转换result对象为同程艺龙响应实体
     *
     * @param resultObj result JSON对象
     * @param clazz     响应实体 class类
     * @param <T>       响应实体类型
     * @return T 转换后的响应实体类
     */
    public static <T> T convertResultObj2ELongResp(JSONObject resultObj, Class<T> clazz) {

        if (resultObj == null) {
            return null;
        }

        try {
            return resultObj.toJavaObject(clazz);
        } catch (Exception e) {
            log.error("Parse 2 {} Error", clazz);
            log.info("resultObj: {}", resultObj);
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取signature
     *
     * @param timestamp 时间戳
     * @param data      data
     * @return String signature
     */
    public static String getSign(String timestamp, String data) {

        return DigestUtils.md5Hex(timestamp + DigestUtils.md5Hex(data + appKey) + secretKey);
    }

    /**
     * 获取时间戳
     *
     * @return String 时间戳字符串
     */
    public static String getTimestamp() {
        long timestamp = System.currentTimeMillis() / 1000;
        return String.valueOf(timestamp);
    }

    /**
     * 获取请求体
     *
     * @param request 请求参数
     * @param method  请求方法
     * @param <T>     请求参数类型
     * @return ELongReqBody<T> 请求体
     */
    public static <T> ELongReqBody<T> getReqBody(T request, ELongMethod method) {

        ELongReqBody<T> reqBody = new ELongReqBody<>();
        reqBody.setRequest(request);
        reqBody.setMethod(method);
        return reqBody;
    }
}
