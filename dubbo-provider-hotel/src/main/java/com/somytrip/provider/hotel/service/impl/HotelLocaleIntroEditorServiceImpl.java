package com.somytrip.provider.hotel.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.hotel.HotelLocaleIntroEditorService;
import com.somytrip.entity.hotel.HotelLocaleIntroEditorEntity;
import com.somytrip.provider.hotel.mapper.HotelLocaleIntroEditorMapper;
import org.springframework.stereotype.Service;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.hotel.service.impl
 * @className: HotelLocaleIntroEditorServiceImpl
 * @author: shadow
 * @description: 酒店介绍本地化Service实现类
 * @date: 2024/10/16 9:42
 * @version: 1.0
 */
@Service
public class HotelLocaleIntroEditorServiceImpl
        extends ServiceImpl<HotelLocaleIntroEditorMapper, HotelLocaleIntroEditorEntity>
        implements HotelLocaleIntroEditorService {
}
