package com.somytrip.provider.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.somytrip.entity.dto.hotel.HotelQueryWordDTO;
import com.somytrip.entity.dto.hotel.PaginationDto;
import com.somytrip.entity.dto.hotel.QueryHotelListDtoV2;
import com.somytrip.entity.dto.hotel.QueryInputTipsDTO;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.HotelGeneralEntity;
import com.somytrip.entity.vo.hotel.HotelDetailVo;
import com.somytrip.entity.vo.hotel.HotelInfoVo;
import com.somytrip.entity.vo.hotel.HotelListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * @ClassName: HotelGeneralMapper
 * @Description: 酒店基本信息Mapper
 * @Author: shadow
 * @Date: 2024/1/31 14:48
 */
@Mapper
public interface HotelGeneralMapper extends BaseMapper<HotelGeneralEntity> {

    /**
     * 根据dto查询酒店列表
     *
     * @param dto 查询酒店列表参数dto
     * @return java.util.Item<com.somytrip.entity.vo.hotel.HotelListVo>
     * <AUTHOR>
     * @date 2024/4/8 16:45
     */
    List<HotelListVo> queryHotelListByDto(@Param("dto") QueryHotelListDtoV2 dto);

    /**
     * 根据城市code查询和日期区间查询酒店列表
     *
     * @param cityCode       城市code
     * @param rateGeneralIds 有价主键ID列表
     * @param lang           语言
     * @return java.util.List<com.somytrip.entity.vo.hotel.HotelListVo>
     * <AUTHOR>
     * @date 2024/10/9 9:55
     */
    List<HotelListVo> queryHotelListByCityCodeV2(
            @Param("cityCode") String cityCode,
            @Param("rateGeneralIds") List<Long> rateGeneralIds,
            @Param("lang") String lang
    );

    /**
     * 根据酒店主键ID列表查询酒店列表
     *
     * @param generalIds 酒店主键ID列表
     * @param lang       语言
     * @return java.util.List<com.somytrip.entity.vo.hotel.HotelListVo>
     * <AUTHOR>
     * @date 2025/2/8 16:32
     */
    List<HotelListVo> queryHotelListByIds(@Param("generalIds") List<Long> generalIds,
                                          @Param("lang") String lang);

    @ResultMap("mybatis-plus_HotelGeneralEntity")
    @Select("select * from hotel_general where hotel_id = #{hotelId}")
    HotelGeneralEntity queryOneById(String hotelId);

    @Select("SELECT t1.hotel_origin, " +
            "       t1.hotel_id, " +
            "       t1.hotel_name, " +
            "       t1.address, " +
            "       t1.google_lon AS lon, " +
            "       t1.google_lat AS lat, " +
            "       t1.score, " +
            "       t1.star_rate  AS star, " +
            "       t1.category, " +
            "       t1.review_count, " +
            "       t1.cover, " +
            "       t1.business_zone_name, " +
            "       t1.business_zone_distance, " +
            "       t2.min_rate   AS start_price " +
            "FROM hotel_general t1 " +
            "         INNER JOIN hotel_min_rate t2 ON t1.hotel_id = t2.hotel_id AND t1.hotel_origin = t2.hotel_origin " +
            "WHERE t1.is_del = FALSE " +
            "  AND global_city_code = #{dto.cityCode} " +
            "  AND (star_rate IS NOT NULL OR category IS NOT NULL) " +
            "ORDER BY t2.min_rate, t1.score DESC")
    Page<HotelListVo> queryHotelListByDto(Page<HotelListVo> page, @Param("dto") QueryHotelListDtoV2 dto);

    List<HotelListVo> queryHotelListByOriginIdList(@Param("originIdList") List<String> hotelOriginIdList,
                                                   @Param("cityCode") String cityCode);

    List<HotelGeneralEntity> queryEntityListByOriginIdList(@Param("originIdList") List<String> hotelOriginIdList);

    List<HotelGeneralEntity> queryEntityList(@Param("pagination") PaginationDto pagination);

    HotelGeneralEntity queryOneByHotelId(@Param("hotelId") String hotelId,
                                         @Param("hotelOrigin") HotelOrigin hotelOrigin,
                                         @Param("lang") String lang);

    @Select("SELECT t1.hotel_origin, " +
            "       t1.hotel_id, " +
            "       t1.hotel_name, " +
            "       t1.address, " +
            "       t1.business_zone_name, " +
            "       t1.google_lon   AS lon, " +
            "       t1.google_lat   AS lat, " +
            "       t1.star_rate    AS star, " +
            "       t1.category, " +
            "       t1.intro_editor AS introduction, " +
            "       t1.establishment_date, " +
            "       t1.renovation_date " +
            "FROM hotel_general t1 " +
            "WHERE t1.is_del = FALSE " +
            "  AND t1.hotel_id = #{hotelId} " +
            "  AND t1.hotel_origin = #{hotelOrigin}")
    HotelDetailVo queryHotelDetail(@Param("hotelId") String hotelId, @Param("hotelOrigin") HotelOrigin hotelOrigin);

    @Select("select hotel_id from hotel_general")
    List<String> queryHotelIds();

    @Select("SELECT city_id cnt " +
            "FROM hotel_general " +
            "WHERE hotel_origin = 'ELong' " +
            "GROUP BY city_id " +
            "ORDER BY COUNT(id) DESC")
    List<String> queryCityIdsOrderByCount();

    /**
     * 通过酒店来源查询酒店id信息列表
     *
     * @param hotelOrigin
     * @return
     */
    @Select("select id,hotel_id from hotel_general  WHERE is_del = FALSE AND hotel_origin = #{hotelOrigin} ")
    List<HotelInfoVo> queryHotelIdsByhotelOriginWebbeds(@Param("hotelOrigin") HotelOrigin hotelOrigin);

    @Select("select id,hotel_id from hotel_general  WHERE is_del = FALSE AND global_city_code =#{cityCode} AND hotel_origin = #{hotelOrigin} ")
    List<HotelInfoVo> queryHotelIdsByhotelOrigin(@Param("hotelOrigin") HotelOrigin hotelOrigin, @Param("cityCode") String cityCode);

    /**
     * 通过统一城市code+来源 返回酒店id列表
     *
     * @param cityCode
     * @param hotelOrigin
     * @return
     */
    @Select("select hotel_id from hotel_general  WHERE is_del = FALSE AND global_city_code =#{cityCode} AND hotel_origin = #{hotelOrigin} ")
    List<String> queryHotelIdsByCityCodeHotelOrigin(@Param("cityCode") String cityCode, @Param("hotelOrigin") HotelOrigin hotelOrigin);

    /**
     * 酒店搜索提示
     *
     * @param cityCode
     * @param checkIn
     * @param queryText
     * @return
     */
    List<QueryInputTipsDTO> hotelInputTips(@Param("cityCode") String cityCode,
                                           @Param("checkIn") LocalDate checkIn,
                                           @Param("queryText") String queryText,
                                           @Param("lang") String lang);

    /**
     * 酒店城市景点
     *
     * @param cityCode
     * @return
     */
    List<HotelQueryWordDTO> hotelSearchScenic(@Param("cityCode") String cityCode);

    /**
     * 酒店城市品牌
     *
     * @param cityCode
     * @return
     */
    List<HotelQueryWordDTO> hotelSearchBrand(@Param("cityCode") String cityCode);
}
