package com.somytrip.provider.hotel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.hotel.HotelCheckInPolicyService;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import com.somytrip.entity.hotel.HotelCheckInPolicyEntity;
import com.somytrip.provider.hotel.mapper.HotelCheckInPolicyMapper;
import com.somytrip.provider.hotel.utils.DataUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 酒店入住政策表(HotelCheckInPolicy)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-02 10:26:52
 */
@Service("hotelCheckInPolicyService")
public class HotelCheckInPolicyServiceImpl
        extends ServiceImpl<HotelCheckInPolicyMapper, HotelCheckInPolicyEntity>
        implements HotelCheckInPolicyService {

    /**
     * 更新酒店入住政策(同程艺龙 列表)
     *
     * @param checkinPolicies 同程艺龙酒店入住政策列表
     * @param hotelId         酒店ID
     * @return boolean true: 成功, false: 失败
     */
    @Override
    public boolean updateHotelCheckInPolicy(List<ELongStaticHotelInfoResp.Detail.CheckinPolicy> checkinPolicies, String hotelId) {

        for (ELongStaticHotelInfoResp.Detail.CheckinPolicy checkinPolicy : checkinPolicies) {
            HotelCheckInPolicyEntity checkInPolicyEntity = new HotelCheckInPolicyEntity(checkinPolicy, hotelId);
            updateHotelCheckInPolicy(checkInPolicyEntity);
        }

        return true;
    }

    /**
     * 根据酒店主键ID查询入住方式Entity
     *
     * @param hotelPid 酒店主键ID
     * @return Item<HotelCheckInPolicyEntity> 入住方式Entity
     */
    @Override
    public List<HotelCheckInPolicyEntity> queryListByHotelPid(Long hotelPid) {

        LambdaQueryWrapper<HotelCheckInPolicyEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(HotelCheckInPolicyEntity::getHotelPid, hotelPid);
        try {
            return baseMapper.selectList(qw);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public void updateHotelCheckInPolicy(HotelCheckInPolicyEntity newCheckInPolicyEntity) {

        // 查询已存在记录
        String hotelId = newCheckInPolicyEntity.getHotelId();
        HotelOrigin hotelOrigin = newCheckInPolicyEntity.getHotelOrigin();
        LambdaQueryWrapper<HotelCheckInPolicyEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(HotelCheckInPolicyEntity::getHotelId, hotelId).eq(HotelCheckInPolicyEntity::getHotelOrigin, hotelOrigin);
        HotelCheckInPolicyEntity existingCheckInPolicyEntity = baseMapper.selectOne(qw);

        // 更新数据
        DataUtil.updateData(
                newCheckInPolicyEntity,
                existingCheckInPolicyEntity,
                baseMapper,
                "id", "createTime", "updateTime"
        );
    }
}
