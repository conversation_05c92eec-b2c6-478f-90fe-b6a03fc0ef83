package com.somytrip.provider.hotel.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.hotel.HotelSwlService;
import com.somytrip.api.service.hotel.v2.HotelV2Service;
import com.somytrip.entity.dto.hotel.QueryHotelDetailDtoV2;
import com.somytrip.entity.hotel.swl.*;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.hotel.HotelDetailVo;
import com.somytrip.provider.hotel.config.SwlConfig;
import com.somytrip.provider.hotel.config.WebClientConfig;
import com.somytrip.provider.hotel.mapper.HotelSwlMapper;
import com.somytrip.provider.hotel.request.HotelSwlRequestService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.LocaleUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.hotel.service.impl
 * @className: HotelSwlServiceImpl
 * @author: shadow
 * @description: 深文旅Service实现类
 * @date: 2024/12/12 15:34
 * @version: 1.0
 */
@Slf4j
@Service
@DubboService
public class HotelSwlServiceImpl extends ServiceImpl<HotelSwlMapper, HotelSwlEntity> implements HotelSwlService {

    private static final String RSA_ALGORITHM = "RSA";
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    @Resource
    private HotelV2Service hotelV2Service;
    @Resource
    private SwlConfig swlConfig;
    @Resource
    private WebClientConfig webClientConfig;
    private HotelSwlRequestService hotelSwlRequestService;

    public static String sendPost(String urlString, String param, Map<String, String> headers) {
        HttpURLConnection conn = null;
        try {
            URL url = new URL(urlString);
            conn = (HttpURLConnection) url.openConnection();

            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    conn.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }

            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);

            // 设置超时时间
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);

            try (OutputStream os = conn.getOutputStream();
                 PrintWriter out = new PrintWriter(new OutputStreamWriter(os, StandardCharsets.UTF_8))) {
                out.print(param);
                out.flush();
            }

            int responseCode = conn.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                    return in.lines().collect(Collectors.joining("\n"));
                }
            } else {
                throw new IOException("Failed to connect: HTTP error code : " + responseCode);
            }
        } catch (IOException e) {
            // 更加具体的异常处理逻辑
            log.error("Error during POST request: " + e.getMessage());
            log.error(e.getMessage(), e);
            return null;
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
    }

    private static String signData(String data, String privateKeyStr) throws Exception {

        byte[] encodedPrivateKey = Base64.getDecoder().decode(privateKeyStr);
        PKCS8EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(encodedPrivateKey);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        PrivateKey privateKey = keyFactory.generatePrivate(privateKeySpec);
        Signature sig = Signature.getInstance(SIGNATURE_ALGORITHM);
        sig.initSign(privateKey);
        sig.update(data.getBytes(StandardCharsets.UTF_8));
        byte[] signedBytes = sig.sign();
        return Base64.getEncoder().encodeToString(signedBytes);
    }

    @PostConstruct
    void init() {
        this.hotelSwlRequestService = webClientConfig.createService(HotelSwlRequestService.BASE_URL,
                HotelSwlRequestService.class);
    }

    /**
     * 查询酒店详情
     *
     * @param req 查询参数
     * @return com.somytrip.entity.response.ResponseResult<com.somytrip.entity.vo.hotel.HotelDetailVo>
     * <AUTHOR>
     * @date 2024/12/13 9:58
     */
    @Override
    public ResponseResult<HotelDetailVo> queryDetail(HotelSwlDetailReq req) {

        log.info("Swl Query Detail, req: {}", JSON.toJSONString(req));

        // 语言处理
        String lang = req.getLang();
        if (StrUtil.isNotBlank(lang)) {
            Locale locale = LocaleUtils.toLocale(lang);
            LocaleContextHolder.setLocale(locale);
        }

        if (!StrUtil.isNumeric(req.getHotelId())) {
            return ResponseResult.fail(201, "酒店ID不规范");
        }

        if (!checkSign(req)) {
            return ResponseResult.fail(201, "验签失败");
        }

        LambdaQueryWrapper<HotelSwlEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(HotelSwlEntity::getId, req.getHotelId()).eq(HotelSwlEntity::getDel, false);
        HotelSwlEntity hotelSwlEntity = baseMapper.selectOne(qw);
        if (hotelSwlEntity == null) {
            return ResponseResult.fail(201, "酒店不存在");
        }

        HotelDetailVo hotelDetail = null;
        QueryHotelDetailDtoV2 dto = new QueryHotelDetailDtoV2();
        dto.setHotelOrigin(hotelSwlEntity.getHotelOrigin());
        dto.setHotelId(hotelSwlEntity.getHotelId());
        dto.setDateRange(req.getDateRange());
        dto.setNumberOfAdults(req.getNumberOfAdults());
        dto.setNumberOfRooms(req.getNumberOfRooms());
        ResponseResult responseResult = hotelV2Service.queryHotelDetail(dto);
        if (responseResult != null) {
            JSONObject jsonObject = JSONObject.from(responseResult.getData());
            if (jsonObject != null && jsonObject.getJSONObject("hotelDetail") != null) {
                hotelDetail = jsonObject.getJSONObject("hotelDetail").toJavaObject(HotelDetailVo.class);
                hotelDetail.setHotelPid(String.valueOf(hotelSwlEntity.getId()));
            }
        }

        return ResponseResult.ok(hotelDetail);
    }

    /**
     * 推送酒店列表
     *
     * @return boolean
     * <AUTHOR>
     * @date 2024/12/12 15:47
     */
    @Override
    public boolean pushHotelList() {

        // 查询酒店数据
        LocalDate today = LocalDate.now();
        List<HotelSwlListVo> hotelSwlListVos = baseMapper.queryList(today);

        HotelSwlListPushData hotelSwlListPushData = new HotelSwlListPushData();
        if (hotelSwlListVos != null) {
            hotelSwlListPushData.setTotalNum(hotelSwlListVos.size());
            hotelSwlListPushData.setHotelData(hotelSwlListVos);
        }
        log.info("Push total: {}", hotelSwlListPushData.getTotalNum());

        try {
            String body = JSONObject.from(hotelSwlListPushData).toString();

            String timestamp = Calendar.getInstance().getTimeInMillis() + "";
            log.info("timestamp: {}", timestamp);
            String sign = signData(body + timestamp, swlConfig.getPrivateKey());
            log.info("sign: {}", sign);

            HttpResponse response = HttpUtil.createPost(swlConfig.getPushUrl())
                    .header("Content-Type", "")
                    .header("sign", sign)
                    .header("timestamp", timestamp)
                    .body(body)
                    .execute();
            String result = response.body();
            int status = response.getStatus();
            log.info("response status: {}", status);
            log.info("Push result: {}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    /**
     * 推送酒店状态变更
     *
     * @param hotelStatusVos 酒店状态列表
     * @return boolean
     * <AUTHOR>
     * @date 2024/12/20 14:51
     */
    @Override
    public boolean pushHotelChange(List<HotelStatusVo> hotelStatusVos) {

        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("hotelStatusList", hotelStatusVos);
            log.info("jsonObject: {}", JSON.toJSONString(jsonObject));
            String body = jsonObject.toString();

            String timestamp = Calendar.getInstance().getTimeInMillis() + "";
            String sign = signData(body + timestamp, swlConfig.getPrivateKey());

            HttpResponse response = HttpUtil.createPost(swlConfig.getPushStatusChangeUrl())
                    .header("Content-Type", "")
                    .header("sign", sign)
                    .header("timestamp", timestamp)
                    .body(body)
                    .execute();
            String result = response.body();
            int status = response.getStatus();
            log.info("status: {}", status);
            log.info("Push result: {}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    /**
     * 检查签名
     *
     * @param req 查询参数
     * @return boolean
     * <AUTHOR>
     * @date 2024/12/13 10:43
     */
    private boolean checkSign(HotelSwlDetailReq req) {

        String timestamp = req.getTimestamp();
        String sign = req.getSign();

        String secretKey = swlConfig.getSecretKey();
        String secretIdConfig = swlConfig.getSecretId();

        String targetSign = DigestUtil.md5Hex(DigestUtil.md5Hex(secretKey + secretIdConfig).toLowerCase() + timestamp).toLowerCase();
        return Objects.equals(targetSign, sign);
    }
}
