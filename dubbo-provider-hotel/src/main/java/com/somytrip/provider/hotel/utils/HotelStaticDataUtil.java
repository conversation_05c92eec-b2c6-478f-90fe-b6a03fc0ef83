package com.somytrip.provider.hotel.utils;

import com.somytrip.api.service.hotel.HotelMinRateService;
import com.somytrip.entity.enums.RedisKey;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.HotelMinRateEntity;
import com.somytrip.provider.hotel.service.RedisService;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BinaryOperator;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.hotel.utils
 * @className: HotelStaticDataUtil
 * @author: shadow
 * @description: 酒店静态数据工具类
 * @date: 2024/5/29 10:00
 * @version: 1.0
 */
@Slf4j
public class HotelStaticDataUtil {

    /**
     * 缓存无效酒店
     * 无价格数据的酒店
     *
     * @param redisService redisService
     * @param hotelId      酒店ID
     * @param hotelOrigin  酒店来源
     * <AUTHOR>
     * @date 2024/5/29 14:48
     */
    public static void cacheInvalidHotel(RedisService redisService, String hotelId, HotelOrigin hotelOrigin) {

        log.info("Cache InValidHotel, HotelId: {}, HotelOrigin: {}", hotelId, hotelOrigin);
        RedisKey hotelInvalidListRedisKey = RedisKey.HOTEL_INVALID_LIST;
        String redisKey = hotelInvalidListRedisKey.appendKey(hotelOrigin);
        redisService.lSet(redisKey, hotelId, hotelInvalidListRedisKey.getTimeout());
    }

    /**
     * 处理更新数据
     *
     * @param map 酒店日期价格map
     * <AUTHOR>
     * @date 2024/5/24 15:47
     */
    public static void processUpdateData(Map<String, Map<LocalDate, BigDecimal>> map,
                                         HotelOrigin hotelOrigin,
                                         HotelMinRateService hotelMinRateService) {

        List<HotelMinRateEntity> dataList = new ArrayList<>();

        for (Map.Entry<String, Map<LocalDate, BigDecimal>> entry : map.entrySet()) {
            String hotelId = entry.getKey();
            Map<LocalDate, BigDecimal> dateRateMap = entry.getValue();
            for (Map.Entry<LocalDate, BigDecimal> dateEntry : dateRateMap.entrySet()) {
                HotelMinRateEntity hotelMinRateEntity = new HotelMinRateEntity();
                hotelMinRateEntity.setHotelOrigin(hotelOrigin);
                hotelMinRateEntity.setHotelId(hotelId);
                LocalDate date = dateEntry.getKey();
                BigDecimal rate = dateEntry.getValue();
                hotelMinRateEntity.setDate(date);
                hotelMinRateEntity.setMinRate(rate);

                DataUtil.processBatchSaveAsync(dataList, hotelMinRateEntity, hotelMinRateService);
            }
        }
        DataUtil.batchSaveAsync(dataList, hotelMinRateService);
    }

    /**
     * 合并解析出来的酒店日期价格map
     *
     * @param maps 酒店日期价格map
     * @return java.util.Map<java.lang.String, java.util.Map < java.time.LocalDate, java.math.BigDecimal>>
     * <AUTHOR>
     * @date 2024/5/24 15:45
     */
    public static Map<String, Map<LocalDate, BigDecimal>> mergeMaps(List<Map<String, Map<LocalDate, BigDecimal>>> maps) {

        Map<String, Map<LocalDate, BigDecimal>> general = new HashMap<>();
        for (Map<String, Map<LocalDate, BigDecimal>> map : maps) {
            if (map == null) {
                continue;
            }
//            general.putAll(map);
            map.forEach((key, value) -> {
                general.putIfAbsent(key, new HashMap<>());
                mergeMaps(general.get(key), value, BigDecimal::add);
            });
        }
        return general;
    }

    /**
     * 合并酒店日期价格map字方法
     * 合并子map
     *
     * @param dest   子map
     * @param src    子map
     * @param merger merger
     * <AUTHOR>
     * @date 2024/5/24 15:45
     */
    private static void mergeMaps(Map<LocalDate, BigDecimal> dest,
                                  Map<LocalDate, BigDecimal> src,
                                  BinaryOperator<BigDecimal> merger) {
        src.forEach((key, value) -> {
            // 如果dest中已存在key，则合并value；否则，直接添加
            dest.merge(key, value, merger);
        });
    }
}
