package com.somytrip.provider.hotel.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.somytrip.api.service.hotel.ELong.ELongDataService;
import com.somytrip.entity.enums.hotel.method.ELongMethod;
import com.somytrip.entity.hotel.ELong.ELongReqBody;
import com.somytrip.entity.hotel.ELong.offline.ELongHotelDataBookingReq;
import com.somytrip.entity.hotel.ELong.offline.ELongHotelDataBookingResp;
import com.somytrip.provider.hotel.utils.request.ELongRequestUtil;
import org.springframework.stereotype.Service;

/**
 * @ClassName: ELongDataServiceImpl
 * @Description: 同程艺龙数据相关Service实现类
 * @Author: shadow
 * @Date: 2024/2/24 17:58
 */
@Service
public class ELongDataServiceImpl implements ELongDataService {

    /**
     * 查询预定数据
     *
     * @param bookingReq 同程艺龙查询预定数据请求参数
     * @return ELongHotelDataBookingResp 预订数据响应
     */
    @Override
    public ELongHotelDataBookingResp queryBookingData(ELongHotelDataBookingReq bookingReq) {

        ELongReqBody<ELongHotelDataBookingReq> reqBody =
                ELongRequestUtil.getReqBody(bookingReq, ELongMethod.HOTEL_DATA_BOOKING);
        JSONObject result = ELongRequestUtil.generalRequest(reqBody);
//        log.info("result: {}", result);
        return ELongRequestUtil.convertResultObj2ELongResp(result, ELongHotelDataBookingResp.class);
    }
}
