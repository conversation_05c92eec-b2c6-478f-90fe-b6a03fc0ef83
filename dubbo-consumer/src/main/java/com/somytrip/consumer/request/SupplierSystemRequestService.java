package com.somytrip.consumer.request;

import com.somytrip.entity.dto.hotel.QueryHotelDetailDtoV2;
import com.somytrip.entity.dto.hotel.QueryHotelListDtoV2;
import com.somytrip.entity.dto.hotel.QueryRatePlanDto;
import com.somytrip.entity.dto.hotel.SpBookingOrderDTO;
import com.somytrip.entity.vo.hotel.SupplierSystemResponseResult;
import com.somytrip.entity.vo.hotel.sms.HotelSMSDetailVo;
import com.somytrip.entity.vo.hotel.sms.HotelSMSListVo;
import com.somytrip.entity.vo.hotel.sms.HotelSearchReteInfoVo;
import com.somytrip.entity.vo.hotel.sms.HotelSearchRoomVo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.request
 * @interfaceName: SupplierSystemRequestService
 * @author: shadow
 * @description: 供应商系统请求Service
 * @date: 2024/10/11 15:46
 * @version: 1.0
 */
@HttpExchange
public interface SupplierSystemRequestService {

    String BASE_URL = "";

    @PostExchange("/internal/hotel-search/list")
    SupplierSystemResponseResult<List<HotelSMSListVo>> hotelList(@RequestBody QueryHotelListDtoV2 dto);

    @PostExchange("/internal/hotel-search/detail")
    SupplierSystemResponseResult<HotelSMSDetailVo> hotelDetail(@RequestBody QueryHotelDetailDtoV2 dto);

    @PostExchange("/internal/hotel-search/roomList")
    SupplierSystemResponseResult<List<HotelSearchRoomVo>> roomList(@RequestBody QueryRatePlanDto dto);

    @PostExchange("/internal/hotel-search/ratePlanList")
    SupplierSystemResponseResult<List<HotelSearchReteInfoVo>> ratePlanList(@RequestBody QueryRatePlanDto dto);

    @PostExchange("/internal/order/booking-order")
    SupplierSystemResponseResult<Object> bookingOrder(@RequestBody SpBookingOrderDTO spBookingOrderDTO);

    @PostExchange("/internal/order/cancel-order")
    SupplierSystemResponseResult<Object> cancelOrder(@RequestParam("orderNo") String orderNo);

    @PostExchange("/internal/order/payment-order")
    SupplierSystemResponseResult<Object> paymentOrder(@RequestParam("orderNo") String orderNo);
}
