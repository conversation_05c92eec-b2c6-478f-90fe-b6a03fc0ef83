package com.somytrip.consumer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.dto.HotEntity;
import com.somytrip.entity.vo.HotScenicVo;
import com.somytrip.entity.vo.HotVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName: HotMapper
 * @Description: 热度Mapper
 * @Author: shadow
 * @Date: 2023/12/23 14:31
 */
@Mapper
public interface HotMapper extends BaseMapper<HotEntity> {

    /**
     * 查询热门城市列表
     *
     * @param dimension   维度
     * @param dimensionId 维度ID
     * @param limit       总数
     * @return Item<ScenicSimpleVo> 热门景区列表
     */
    List<HotScenicVo> queryHotScenicList(@Param("dimension") Integer dimension,
                                         @Param("dimensionId") Integer dimensionId,
                                         @Param("limit") Integer limit);

    @Select("SELECT city_id AS id, city_name AS name, SUM(search_count) AS search_count " +
            "FROM hot " +
            "WHERE DATEDIFF(CURRENT_DATE, search_date) <= 7 " +
            "GROUP BY city_id, city_name " +
            "ORDER BY search_count DESC " +
            "LIMIT 10")
    List<HotVo> queryListGroupByCity();

    @Select("SELECT country_id AS id, country_name AS name, SUM(search_count) AS search_count " +
            "FROM hot " +
            "WHERE DATEDIFF(CURRENT_DATE, search_date) <= 7 " +
            "GROUP BY country_id, country_name " +
            "ORDER BY search_count DESC " +
            "LIMIT 10")
    List<HotVo> queryListGroupByCountry();
}
