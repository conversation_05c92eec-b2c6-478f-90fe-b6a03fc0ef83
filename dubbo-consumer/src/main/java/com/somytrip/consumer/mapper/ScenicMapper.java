package com.somytrip.consumer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.somytrip.bean.dto.HighlightDTO;
import com.somytrip.bean.dto.ScenicOrCityNameXiaoqiDTO;
import com.somytrip.entity.dto.Scenic;
import com.somytrip.entity.dto.tourism.QueryScenicDto;
import com.somytrip.entity.itinerary.ItineraryScenicEntity;
import com.somytrip.entity.itinerary.ScenicQueryListParam;
import com.somytrip.entity.vo.ScenicVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @ClassName: ScenicMapper
 * @Description:
 * @Author: shadow
 * @Date: 2023/12/26 14:53
 */
@Mapper
public interface ScenicMapper extends BaseMapper<Scenic> {

    /**
     * 查询景区详情
     *
     * @param id   景区ID
     * @param lang 语言
     * @return com.somytrip.entity.dto.Scenic
     * <AUTHOR>
     * @date 2024/5/22 10:22
     */
    Scenic queryScenicDetail(@Param("id") String id, @Param("lang") String lang);

    /**
     * 分页查询景点列表
     *
     * @param page  分页组件
     * @param param 查询参数
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.somytrip.entity.dto.Scenic>
     * <AUTHOR>
     * @date 2025/1/19 9:20
     */
    Page<Scenic> queryScenicList(IPage<Scenic> page, @Param("param") ScenicQueryListParam param);

    /**
     * 查询景点列表(攻略用)
     *
     * @param param 查询参数
     * @return java.util.List<com.somytrip.entity.itinerary.ItineraryScenicEntity>
     * <AUTHOR>
     * @date 2025/1/17 15:38
     */
    List<ItineraryScenicEntity> queryItineraryScenicList(@Param("param") ScenicQueryListParam param);

    /**
     * 查询景点列表总数(攻略用)
     *
     * @param param 查询参数
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2025/1/17 16:36
     */
    Long queryItineraryScenicTotal(@Param("param") ScenicQueryListParam param);

    /**
     * 根据景点ID查询景点数据(攻略用)
     *
     * @param scenicId 景点ID
     * @param lang     语言
     * @return com.somytrip.entity.itinerary.ItineraryScenicEntity
     * <AUTHOR>
     * @date 2024/6/6 16:43
     */
    ItineraryScenicEntity queryItineraryScenicById(@Param("scenicId") String scenicId, @Param("lang") String lang);

    /**
     * 根据城市ID查询景区列表(攻略用)
     *
     * @param cityId 城市ID
     * @param lang   语言
     * @param limit  查询数量
     * @return java.util.List<com.somytrip.entity.itinerary.ItineraryScenicEntity>
     * <AUTHOR>
     * @date 2024/4/12 14:59
     */
    List<ItineraryScenicEntity> queryItineraryScenicListByCityId(@Param("cityId") Integer cityId,
                                                                 @Param("lang") String lang,
                                                                 @Param("limit") Integer limit);


    /* ======================================= 以下废弃 ======================================= */

    /**
     * 查询景区列表
     *
     * @param dto 查询景区列表dto
     * @return Item<ScenicVo> 景区列表
     */
    List<ScenicVo> queryList(@Param("dto") QueryScenicDto dto,
                             @Param("env") Integer env);

    /**
     * 查询景区列表总数
     *
     * @param dto 查询景区列表dto
     * @return Item<ScenicVo> 景区列表总数
     */
    Long queryTotal(@Param("dto") QueryScenicDto dto,
                    @Param("env") Integer env);

    /**
     * 查询附近景区
     *
     * @param dto 查询景区列表dto
     * @return Item<ScenicVo> 附近景区列表
     */
    List<ScenicVo> queryNearByList(@Param("dto") QueryScenicDto dto,
                                   @Param("env") Integer env);

    /**
     * 查询附近景区列表总数
     *
     * @param dto 查询景区列表dto
     * @return Item<ScenicVo> 附近景区列表总数
     */
    Long queryNearByTotal(@Param("dto") QueryScenicDto dto,
                          @Param("env") Integer env);

    ScenicVo queryOneById(@Param("scenicId") long scenicId,
                          @Param("env") Integer env);

    Set<ScenicVo> selectTopThiScenic(@Param("cityIds") List<Integer> cityIds,
                                     @Param("themeIds") List<Integer> themeIds,
                                     @Param("favScenicIds") List<Long> favScenicIds,
                                     @Param("limit") int limit,
                                     @Param("env") Integer env);

    List<ScenicVo> queryScenicByXYZ(@Param("dayScenic") ScenicVo dayScenic,
                                    @Param("levelValue") Integer levelValue,
                                    @Param("limit") Integer limit,
                                    @Param("env") Integer env);

    /**
     * 查询景点信息-AI小奇查询
     *
     * @param cityCode
     * @param scenicNames
     * @return
     */
    List<HighlightDTO> queryHighlightList(@Param("cityCode") String cityCode, @Param("scenicNames") List<String> scenicNames);

    /**
     * 获取景区内容数据
     *
     * @return
     */
    List<ScenicOrCityNameXiaoqiDTO> getScenicContenData();

    /**
     * 通过经纬度获取附件景点
     *
     * @param cityCode
     * @param lng
     * @param lat
     * @param radius
     * @return
     */
    List<ScenicOrCityNameXiaoqiDTO> getScenicByLngAndLat(@Param("cityCode") String cityCode,
                                                         @Param("lng") String lng,
                                                         @Param("lat") String lat,
                                                         @Param("radius") Integer radius);
}
