package com.somytrip.consumer.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.dto.Scenic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-20
 */
@Mapper
public interface ScenicV1Mapper extends BaseMapper<Scenic> {

    @Select("select s.id, gc.country, s.city, s.city_id cityId, " +
            "ifnull(s.gps_lat, ifnull(s.g_lat, s.lat)) AS lat, " +
            "ifnull(s.gps_lng, ifnull(s.g_lng, s.lng)) AS lng, " +
            "s.name title, TRIM(s.address) address, " +
            "s.play_time_v, s.thi_index, s.pic, " +
            "'view' type, '' subtitle, false isHighlight " +
            "from scenic s, global_city gc " +
            "where s.city = gc.city_name " +
            "and s.id = #{id}" +
            "and (s.gps_lat IS NOT NULL OR s.g_lat IS NOT NULL OR s.lat IS NOT NULL)")
    Map<String, Object> queryScenic4Detail(@Param("id") String id);

    @Select("select s.id, gc.country, s.city, s.lat, s.lng, s.gps_lat, gps_lng, s.name, TRIM(s.address) address, " +
            "s.play_time_v, s.thi_index, s.pic " +
            "from scenic s, global_city gc " +
            "where s.city = gc.city_name " +
            "and s.id = #{id}")
    JSONObject queryById(@Param("id") String id);

    @Select("<script>" +
            "select s.id, gc.country, s.city, s.lat, s.lng, s.name, s.play_time_v, s.thi_index " +
            "from scenic s, global_city gc " +
            "where s.city = gc.city_name " +
            "and s.id in (<foreach collection='list' item='l' separator=','>#{l}</foreach>)" +
            "</script>")
    List<JSONObject> queryScenic(@Param("list") String[] list);

    @Select("<script>" +
            "select s.id, gc.country, s.city, s.lat, s.lng, s.name, s.play_time_v, s.thi_index " +
            "from scenic s, global_city gc " +
            "where s.city = gc.city_name " +
            "and s.id in (<foreach collection='list' item='l' separator=','>#{l}</foreach>)" +
            "</script>")
    List<JSONObject> queryScenicByIds(@Param("list") List<Integer> list);

    @Select("select name, if(brief is null or brief = '', '暂无简介', brief) brief, " +
            "if(price is null or price = '', '暂无价格', price) price " +
            "from scenic where id = #{id}")
    JSONObject queryExportData(@Param("id") Integer id);
}
