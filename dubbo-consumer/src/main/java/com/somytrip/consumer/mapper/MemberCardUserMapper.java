package com.somytrip.consumer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.dto.MemberCardUser;
import org.apache.ibatis.annotations.*;

import java.util.Date;

/**
 * 用户会员的数据关联
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberCardUserMapper extends BaseMapper<MemberCardUser> {

    @Override
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insert(MemberCardUser memberCardUser);

    /**
     * 查询用户会员的数据
     *
     * @return
     */
    @Select("select * from member_card_user where user_id=#{userid} and card_type=#{cardType}")
    MemberCardUser queryMemberCardUser(@Param("userid") int userid, @Param("cardType") int cardType);

    /**
     * 扣除1次次数
     *
     * @param id
     */
    @Update("update member_card_user set surplus_ct=surplus_ct-1 where id=#{id}")
    void deductSurplusCt(@Param("id") int id);

    @Insert("insert into member_card_user (user_id, card_type, card_name, surplus_ct, start_time, end_time, create_time)" +
            " values(#{userId}, #{cardType}, #{cardName}, #{surplusCt}, #{startTime}, #{endTime}, #{createTime})")
    int insertCard(@Param("userId") Integer userId,
                   @Param("cardType") Integer cardType,
                   @Param("surplusCt") Integer surplusCt,
                   @Param("cardName") String cardName,
                   @Param("startTime") String startTime,
                   @Param("endTime") String endTime,
                   @Param("createTime") String createTime);


    @Insert("update member_card_user set card_name=#{cardName}, end_time=#{endTime} where id=#{id}")
    int updateCard(@Param("id") Integer id,
                   @Param("endTime") String endTime,
                   @Param("cardName") String cardName);

    @Select("SELECT end_time FROM member_card_user WHERE user_id = ${uid}")
    Date queryEndDateByUid(@Param("uid") Integer uid);
}
