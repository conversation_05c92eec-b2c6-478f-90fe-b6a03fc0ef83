package com.somytrip.consumer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.itinerary.ItineraryGuidebookEntity;
import com.somytrip.entity.itinerary.ItineraryGuidebookVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.mapper
 * @interfaceName: ItineraryGuidebookMapper
 * @author: shadow
 * @description: 攻略指南Mapper
 * @date: 2024/8/6 15:01
 * @version: 1.0
 */
@Mapper
public interface ItineraryGuidebookMapper extends BaseMapper<ItineraryGuidebookEntity> {

    ItineraryGuidebookEntity queryOneByCityId(@Param("cityId") Integer cityId, @Param("lang") String lang);

    ItineraryGuidebookVo queryVoByCityId(@Param("cityId") Integer cityId, @Param("lang") String lang);

    /**
     * 根据城市ID列表查询攻略指南列表
     *
     * @param cityIds 城市ID列表
     * @param lang    语言
     * @return java.util.List<com.somytrip.entity.itinerary.ItineraryGuidebookVo>
     * <AUTHOR>
     * @date 2025/4/28 16:17
     */
    List<ItineraryGuidebookVo> queryVosByCityIds(@Param("cityIds") List<Integer> cityIds, @Param("lang") String lang);
}
