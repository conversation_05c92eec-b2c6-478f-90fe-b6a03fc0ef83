package com.somytrip.consumer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.community.FeedbackPlatform;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.mapper
 * @interfaceName: FeedbackPlatformMapper
 * @author: lijunqi
 * @description: 反馈平台Mapper接口
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
@Mapper
public interface FeedbackPlatformMapper extends BaseMapper<FeedbackPlatform> {

    /**
     * 增加支持数
     *
     * @param feedbackId 反馈ID
     * @return 影响行数
     */
    @Update("UPDATE feedback_platform SET support_count = support_count + 1, update_time = NOW() WHERE id = #{feedbackId} AND del_flag = 0")
    int increaseSupportCount(@Param("feedbackId") Integer feedbackId);

    /**
     * 更新反馈状态
     *
     * @param feedbackId 反馈ID
     * @param status     状态
     * @return 影响行数
     */
    @Update("UPDATE feedback_platform SET status = #{status}, update_time = NOW() WHERE id = #{feedbackId} AND del_flag = 0")
    int updateFeedbackStatus(@Param("feedbackId") Integer feedbackId, @Param("status") Integer status);

    /**
     * 设置反馈为已采纳
     *
     * @param feedbackId 反馈ID
     * @return 影响行数
     */
    @Update("UPDATE feedback_platform SET is_adopted = 1, update_time = NOW() WHERE id = #{feedbackId} AND del_flag = 0")
    int adoptFeedback(@Param("feedbackId") Integer feedbackId);
}
