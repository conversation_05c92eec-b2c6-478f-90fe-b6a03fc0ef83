package com.somytrip.consumer.mapper;

import com.alibaba.fastjson2.JSONObject;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FileMapper {

    /**
     * 添加图片
     *
     * @param name
     * @param type
     */
    @Insert("insert into somytrip_site.picture (name, type) values (#{name}, #{type})")
    void upload(@Param("name") String name, @Param("type") String type);

    /**
     * 删除图片
     *
     * @param name
     */
    @Update("update somytrip_site.picture set is_delete = 1 where name = #{name} and type = #{type}")
    void delete(@Param("name") String name, @Param("type") String type);

    @Select("select type, name, create_time createTime from somytrip_site.picture " +
            "where is_delete = 0 and type like concat('%', #{type}, '%') " +
            "order by ${prop} ${order} " +
            "limit #{page}, #{size}")
    List<JSONObject> list(@Param("type") String type,
                          @Param("page") Integer page,
                          @Param("size") Integer size,
                          @Param("prop") String prop,
                          @Param("order") String order);

    @Select("select count(*) from somytrip_site.picture " +
            "where is_delete = 0 and type like concat('%', #{type}, '%')")
    Integer listTotal(@Param("type") String type);

    @Update("update somytrip_site.picture set is_use = 1 where name = #{name}")
    void useFile(@Param("name") String name);

    @Update("update somytrip_site.picture set is_delete = 1 " +
            "where date_add(create_time, interval 3 day) < sysdate() and is_use = 0 and is_delete = 0")
    Integer clearTmp();

    @Select("select name, type from somytrip_site.picture " +
            "where date_add(create_time, interval 3 day) < sysdate() and is_use = 0 and is_delete = 0")
    List<JSONObject> queryDelete();
}
