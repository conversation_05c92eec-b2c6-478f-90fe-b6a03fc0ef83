package com.somytrip.consumer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.itinerary.ItineraryUserConditionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.mapper
 * @interfaceName: ItineraryUserConditionMapper
 * @author: shadow
 * @description: 攻略用户查询条件Mapper
 * @date: 2024/11/12 14:53
 * @version: 1.0
 */
@Mapper
public interface ItineraryUserConditionMapper extends BaseMapper<ItineraryUserConditionEntity> {

    /**
     * 获取指定用户最新的查询条件
     *
     * @param uid 用户ID
     * @return com.somytrip.entity.itinerary.ItineraryUserConditionEntity
     * <AUTHOR>
     * @date 2024/11/12 15:19
     */
    ItineraryUserConditionEntity getLatestByUid(@Param("uid") String uid);
}
