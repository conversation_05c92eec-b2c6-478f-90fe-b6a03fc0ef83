package com.somytrip.consumer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.vo.visa.VisaUploadFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
@Mapper
public interface VisaUploadFileMapper extends BaseMapper<VisaUploadFile> {
    @Select("select *" +
            " from somytrip_site.somytrip_third_with_visa_file" +
            " where user_id=#{userId} and visa_order_id=#{orderId}")
    VisaUploadFile queryByUserIdAndOrderId(@Param("userId") Integer userId, @Param("orderId") Integer orderId);

    @Select("select *" +
            " from somytrip_site.somytrip_third_with_visa_file" +
            " where id=#{id}")
    VisaUploadFile queryById(@Param("id") Integer id);
}
