package com.somytrip.consumer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.LocaleDictEntity;
import com.somytrip.entity.enums.locale.LocaleDictType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.mapper
 * @interfaceName: LocaleDictMapper
 * @author: shadow
 * @description: 国际化Mapper
 * @date: 2024/9/19 10:57
 * @version: 1.0
 */
@Mapper
public interface LocaleDictMapper extends BaseMapper<LocaleDictEntity> {

    List<LocaleDictEntity> queryListFromType(@Param("type") LocaleDictType localeDictType,
                                             @Param("locale") String locale);
}
