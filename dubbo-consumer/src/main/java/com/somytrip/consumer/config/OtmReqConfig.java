package com.somytrip.consumer.config;

import cn.hutool.crypto.digest.DigestAlgorithm;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: OTM req config
 * @author: pigeon
 * @created: 2024-07-19 17:28
 */
@Data
@Configuration
public class OtmReqConfig {
    @Value("${config.otm.version}")
    private String version;
    @Value("${config.otm.distributorId}")
    private String distributorId;
    @Value("${config.otm.secretKey}")
    private String secretKey;
    @Value("${config.otm.privateKey}")
    private String privateKey;
    @Value("${config.otm.publicKey}")
    private String publicKey;
    @Value("${config.otm.url}")
    private String url;
    @Value("${config.otm.bodySign}")
    private String bodySign;
    @Value("config.otm.des-key")
    private String desKey;
    @Value("${config.otm.header-sign-alg:SHA256}")
    private DigestAlgorithm headerSignAlg;
}
