package com.somytrip.consumer.config;

import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.TimeZone;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.config
 * @className: JacksonConfig
 * @author: shadow
 * @description:
 * @date: 2024/5/25 14:55
 * @version: 1.0
 */
@Configuration
public class JacksonConfig {

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonObjectMapperCustomization() {
//        BaseEnumSerializer baseEnumSerializer = new BaseEnumSerializer();
//        BaseEnumDeserializer baseEnumDeserializer = new BaseEnumDeserializer();
        return jacksonObjectMapperBuilder -> jacksonObjectMapperBuilder.timeZone(TimeZone.getDefault());
//                .serializerByType(BaseEnum.class, baseEnumSerializer)
//                .deserializerByType(BaseEnum.class, baseEnumDeserializer);
    }
}
