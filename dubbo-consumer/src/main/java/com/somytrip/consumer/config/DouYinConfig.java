package com.somytrip.consumer.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Description: 抖音相关配置
 * @author: pigeon
 * @created: 2024-08-16 15:11
 */
@Configuration
public class DouYinConfig {
    @Value("${douyin.prod.appid}")
    private String PROD_APPID;
    @Value("${douyin.sandbox.appid}")
    private String SANDBOX_APPID;
    @Value("${douyin.prod.salt}")
    private String PROD_SALT;
    @Value("${douyin.sandbox.salt}")
    private String SANDBOX_SALT;
    @Value("${douyin.prod.token}")
    private String PROD_TOKEN;
    @Value("${douyin.sandbox.token}")
    private String SANDBOX_TOKEN;
    @Value("${douyin.pay-notify}")
    private String PAY_NOTIFY_URL;
    @Value("${douyin.refund-notify}")
    private String REFUND_NOTIFY_URL;
    @Value("${douyin.prod.api-secret:822c3603387711982c3ade2e5c48231d27bf7b37}")
    private String PROD_API_SECRET;
    @Value("${douyin.sandbox.api-secret:822c3603387711982c3ade2e5c48231d27bf7b37}")
    private String SANDBOX_API_SECRET;
    @Value("${douyin.prod-enabled}")
    private Boolean PROD_ENABLED;

    @Bean
    public ConfigDTO douYinConfigDTO() {
        if (PROD_ENABLED) {
            return new ConfigDTO(PROD_APPID, PROD_SALT, PROD_TOKEN, PROD_API_SECRET, PAY_NOTIFY_URL, REFUND_NOTIFY_URL);
        } else {
            return new ConfigDTO(SANDBOX_APPID, SANDBOX_SALT, SANDBOX_TOKEN, SANDBOX_API_SECRET, PAY_NOTIFY_URL, REFUND_NOTIFY_URL);
        }
    }

    public record ConfigDTO(String appid, String salt,
                            String token, String apiSecret,
                            String payNotifyUrl, String refundNotifyUrl) {
    }
}
