package com.somytrip.consumer.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.*;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.consumer.config.WechatConfig;
import com.somytrip.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;

import java.nio.charset.StandardCharsets;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-13 10:54
 */
@Slf4j
public class WechatUtils {
    private final String BASE_URL = "https://api.weixin.qq.com";
    private final WechatConfig wechatConfig;

    public WechatUtils(WechatConfig wechatConfig) {
        this.wechatConfig = wechatConfig;
    }

    public String getUserOpenIdFormCode(String code) {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(BASE_URL + "/sns/jscode2session")
                .newBuilder();
        urlBuilder.addQueryParameter("appid", wechatConfig.getAppAppletId());
        urlBuilder.addQueryParameter("secret", wechatConfig.getAppSecret());
        urlBuilder.addQueryParameter("js_code", code);
        urlBuilder.addQueryParameter("grant_type", "authorization_code");
        HttpRequest httpRequest = HttpUtil
                .createPost(urlBuilder.build().toString())
                .header(Header.CONTENT_TYPE, ContentType.JSON.getValue())
                .body("");
        try (HttpResponse response = httpRequest.execute()) {
            if (response.isOk()) {
                String res = response.body();
                log.info("code: {} to openIdInfo: {}", code, res);
                return res;
            }
        } catch (Exception e) {
            log.warn("获取user openid error:{}", e.getMessage());
            throw new RuntimeException("获取user openid error");
        }
        return null;
    }

    public JSONObject getUserPhone(String code) {
        HttpRequest httpRequest = HttpUtil
                .createPost(BASE_URL +
                        "/wxa/business/getuserphonenumber?access_token=" + getAccessToken())
                .header(Header.CONTENT_TYPE, ContentType.JSON.getValue())
                .body(String.format("{\"code\":\"%s\"}", code));
        try (HttpResponse response = httpRequest.execute()) {
            if (!response.isOk()) {
                log.error("get user phone error: {}", response.body());
                throw new BusinessException("get user phone error");
            }
            return JSON.parseObject(response.body());
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    public PhoneCode2Result phoneCode2Result(String code) {
        String url = UrlBuilder.of(BASE_URL, StandardCharsets.UTF_8)
                .addPath("/wxa/business/getuserphonenumber")
                .addQuery("access_token", getAccessToken())
                .build();
        String response = HttpUtil.createPost(url)

                .body(StrUtil.format("{\"code\": \"{}\"}", code))
                .execute()
                .body();
        PhoneCode2Result result = JSON.parseObject(response, PhoneCode2Result.class);
        if (result.errCode() != 0) {
            throw new BusinessException(result.errMsg());
        }
        return result;
    }

    public ApplicationUserInfo appAccessToken2Result(String accessToken, String openid) {
        String url = UrlBuilder.of(BASE_URL, StandardCharsets.UTF_8)
                .addPath("/sns/userinfo")
                .addQuery("access_token", accessToken)
                .addQuery("openid", openid)
                .build();
        String response = HttpUtil.get(url);
        log.info("openid: {}, token: {}, response: {}", openid, accessToken, response);
        ApplicationUserInfo result = JSON.parseObject(response, ApplicationUserInfo.class);
        if (result.errCode() != null) {
            throw new BusinessException(result.errMsg());
        }
        return result;
    }

    public AppCode2SessionResult appCode2Result(String code) {
        String url = UrlBuilder.of(BASE_URL, StandardCharsets.UTF_8)
                .addPath("/sns/oauth2/access_token")
                .addQuery("appid", wechatConfig.getAppApplicationId())
                .addQuery("secret", wechatConfig.getAppApplicationSecret())
                .addQuery("code", code)
                .addQuery("grant_type", "authorization_code")
                .build();
        String response = HttpUtil.createPost(url).execute().body();
        AppCode2SessionResult result = JSON.parseObject(response, AppCode2SessionResult.class);
        log.info("app code:{} to result: {}", code, result);
        if (result.errCode() != null && result.errCode() != 0) {
            throw new BusinessException(500, result.errMsg());
        }
        return result;
    }

    public JsCode2SessionResult appletCode2Result(String code) {
        String url = UrlBuilder.of(BASE_URL, StandardCharsets.UTF_8)
                .addPath("/sns/jscode2session")
                .addQuery("appid", wechatConfig.getAppAppletId())
                .addQuery("secret", wechatConfig.getAppSecret())
                .addQuery("js_code", code)
                .addQuery("grant_type", "authorization_code")
                .build();
        String response = HttpUtil.createPost(url).execute().body();
        JsCode2SessionResult result = JSON.parseObject(response, JsCode2SessionResult.class);
        log.info("applet code: {} to result: {}", code, result);
        if (result.errCode() != null && result.errCode() != 0) {
            log.warn("applet code:{} to result: {}", code, result);
            if (result.errCode == -1) {
                throw new BusinessException(500, "api.user.applet.wechat-system-busy");
            } else if (result.errCode == 40029) {
                throw new BusinessException(400, "api.user.applet.wechat-code-fail");
            } else if (result.errCode == 45011) {
                throw new BusinessException(500, "api.user.applet.wechat-system-busy");
            } else if (result.errCode == 40013) {
                throw new BusinessException(400, "api.user.applet.wechat-code-fail");
            }
            throw new BusinessException(500, result.errMsg());
        }
        return result;
    }

    public String getCodeUnLimitBase64(CodeUnLimitRequest request) {
        HttpRequest httpRequest = HttpUtil
                .createPost(UrlBuilder.of(BASE_URL, StandardCharsets.UTF_8)
                        .addPath("/wxa/getwxacodeunlimit")
                        .addQuery("access_token", getAccessToken())
                        .build())
                .body(JSON.toJSONString(request));
        try (HttpResponse response = httpRequest.execute()) {
            if (StrUtil.contains(response.header(Header.CONTENT_TYPE), ContentType.JSON.getValue())) {
                log.error("get code unlimit error: {}", response.body());
                throw new BusinessException("生成小程序二维码失败");
            }
            log.info("{}", response.header(Header.CONTENT_TYPE));
            return Base64.encode(response.bodyStream());
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    private String getAccessToken() {
        AccessTokenRequest accessTokenRequest = new AccessTokenRequest(wechatConfig.getAppAppletId(), wechatConfig.getAppSecret());
        HttpResponse response = HttpUtil.createPost(BASE_URL + "/cgi-bin/stable_token")
                .body(JSON.toJSONString(accessTokenRequest))
                .execute();
        log.info("access token: {}", response.body());
        AccessTokenResponse accessTokenResponse = JSON.parseObject(response.body(), AccessTokenResponse.class);
        if (StrUtil.isBlank(accessTokenResponse.accessToken())) {
            throw new BusinessException("get wechat access token error");
        }
        return accessTokenResponse.accessToken();
    }

    public record ApplicationUserInfo(
            @JSONField(name = "errmsg")
            String errMsg,
            @JSONField(name = "errcode")
            Integer errCode,
            String openid,
            String nickname,
            Integer sex,
            String province,
            String city,
            String country,
            @JSONField(name = "headimgurl")
            String headImgUrl,
            String privilege,
            @JSONField(name = "unionid")
            String unionId
    ) {
    }

    public record PhoneInfoWatermark(
            Long timestamp,
            String appid
    ) {
    }

    public record PhoneInfo(
            String phoneNumber,
            String purePhoneNumber,
            String countryCode,
            PhoneInfoWatermark watermark
    ) {
    }

    public record PhoneCode2Result(
            @JSONField(name = "errmsg")
            String errMsg,
            @JSONField(name = "errcode")
            Integer errCode,
            @JSONField(name = "phone_info")
            PhoneInfo phoneInfo

    ) {
    }

    public record AppCode2SessionResult(
            @JSONField(name = "access_token")
            String accessToken,
            @JSONField(name = "expires_in")
            Integer expiresIn,
            @JSONField(name = "refresh_token")
            String refreshToken,
            String openid,
            String scope,
            @JSONField(name = "unionid")
            String unionId,
            @JSONField(name = "errmsg")
            String errMsg,
            @JSONField(name = "errcode")
            Integer errCode
    ) {
    }

    public record JsCode2SessionResult(
            @JSONField(name = "session_key")
            String sessionKey,
            @JSONField(name = "unionid")
            String unionId,
            @JSONField(name = "errmsg")
            String errMsg,
            String openid,
            @JSONField(name = "errcode")
            Integer errCode
    ) {
    }

    public record AccessTokenRequest(
            @JSONField(name = "grant_type")
            String grantType,
            @JSONField(name = "appid")
            String appid,
            @JSONField(name = "secret")
            String secret,
            @JSONField(name = "force_refresh")
            Boolean forceRefresh
    ) {
        public AccessTokenRequest(String appid, String secret) {
            this("client_credential", appid, secret, false);
        }
    }

    public record AccessTokenResponse(
            @JSONField(name = "access_token")
            String accessToken,
            @JSONField(name = "expires_in")
            Integer expiresIn
    ) {
    }

    public record CodeUnLimitRequest(
            String scene,
            String page,
            @JSONField(name = "check_path")
            Boolean checkPath,
            @JSONField(name = "env_version")
            String envVersion,
            Integer width,
            @JSONField(name = "auto_color")
            Boolean autoColor
    ) {
        public CodeUnLimitRequest(String scene, String page, Boolean checkPath, String envVersion) {
            this(scene, page, checkPath, envVersion, 430, false);
        }

    }

    public record CodeUnLimitResponse(
            byte[] buffer,
            @JSONField(name = "errmsg")
            String errCode,
            @JSONField(name = "errmsg")
            String errMsg
    ) {
    }
}
