package com.somytrip.consumer.util;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 * @创建时间 2023/9/5 16:15
 */
@Slf4j
public class AESUtils {
    private static final String KEY = "1234567890123456"; // 16个字符，128位
    private static final String IV = "abcdefghijklmnop"; // 16个字符，128位
    private static final String CIPHER_INSTANCE = "AES/CBC/PKCS5Padding";

    public static String encrypt(String strToEncrypt) {
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_INSTANCE);
            SecretKeySpec secretKey = new SecretKeySpec(KEY.getBytes(), "AES");
            IvParameterSpec iv = new IvParameterSpec(IV.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);
            return Base64.getEncoder().encodeToString(cipher.doFinal(strToEncrypt.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            log.warn("Error while encrypting: {}", e.toString());
        }
        return null;
    }

    public static String encrypt(String strToEncrypt, String key, String iv) {
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_INSTANCE);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(key.getBytes(), "AES"), new IvParameterSpec(iv.getBytes()));
            return Base64.getEncoder().encodeToString(cipher.doFinal(strToEncrypt.getBytes(StandardCharsets.UTF_8)));
        } catch (Exception e) {
            log.warn("Error while encrypting: {}", e.toString());
        }
        return null;
    }

    public static String decrypt(String strToDecrypt, String key, String iv) {
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_INSTANCE);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(key.getBytes(), "AES"), new IvParameterSpec(iv.getBytes()));
            return new String(cipher.doFinal(Base64.getDecoder().decode(strToDecrypt)));
        } catch (Exception e) {
            log.warn("Error while decrypting: {}", e.toString());
        }
        return null;
    }

    public static String decrypt(String strToDecrypt) {
        try {
            Cipher cipher = Cipher.getInstance(CIPHER_INSTANCE);
            SecretKeySpec secretKey = new SecretKeySpec(KEY.getBytes(), "AES");
            IvParameterSpec iv = new IvParameterSpec(IV.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, secretKey, iv);
            return new String(cipher.doFinal(Base64.getDecoder().decode(strToDecrypt)));
        } catch (Exception e) {
            log.warn("Error while decrypting: {}", e.toString());
        }
        return null;
    }


}
