package com.somytrip.consumer.util;

import cn.hutool.core.util.StrUtil;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.itextpdf.text.pdf.BaseFont;
import com.somytrip.entity.enums.itinerary.ItineraryBudgetLevel;
import com.somytrip.entity.tourism.excel.ItineraryExcelActivityVo;
import com.somytrip.entity.tourism.excel.ItineraryExcelDayVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.List;

@Component
@Slf4j
public class PdfExporter {
    private final String TITLE_PATTERN = "%s【%s】";

    public static void main(String[] args) {
        PdfExporter pdfExporter = new PdfExporter();

        File file = new File("西安3日游【经济型】.pdf");
        try (PdfWriter writer = new PdfWriter(file);
             PdfDocument pdf = new PdfDocument(writer);
             Document doc = new Document(pdf, PageSize.A4.rotate())) {
            // 加载中文字体（请确保你有该字体文件）
            String fontPath = "fonts/SourceHanSansCN-Normal.ttf";
            PdfFont fontNormal = PdfFontFactory.createFont(fontPath, BaseFont.IDENTITY_H);
            String fontBoldPath = "fonts/SourceHanSansCN-Bold.ttf";
            PdfFont fontBold = PdfFontFactory.createFont(fontBoldPath);
            // 设置默认字体为中文字体
            doc.setFont(fontNormal);
            pdfExporter.addTitleAndLogo(doc, "西安3日游【经济型】", fontBold);
            Table table = addTableAndTd();
            // 合并行的例子：创建一个跨越两行的单元格，第一个参数表示跨行数，第二个参数表示跨列数
            Cell mergedCell = new Cell(2, 1)
                    .add(new Paragraph("1"))
                    .setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setHorizontalAlignment(HorizontalAlignment.CENTER);

            Cell mergedHotelCell = new Cell(2, 1) // 第一个参数表示跨行数，第二个参数表示跨列数
                    .add(new Paragraph("雅舒商务宾馆（西安永松路店）"))
                    .setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 添加数据行

            table.addCell(mergedCell)
                    .addCell("2024/12/12 星期四")
                    .addCell("西安 Xi'an")
                    .addCell("火车 D9631")
                    .addCell("09:00 - 12:00")
                    .addCell("西安城墙")
                    .addCell("08:00-22:00 开放（22:00停止售票，22:00停止入园）")
                    .addCell("54CNY")
                    .addCell(new Cell().setWidth(UnitValue.createPointValue(150)).add(new Paragraph("阿贡盖提草原位于新疆阿勒泰地区的布尔津县，蒙古语阿贡盖提的意思gdfgfdgdfgdfgfd是阳光普照的地⽅”，有很多古⽼的⾃然形成的⽯⼈，在这⾥可以看到古⽼的草原游牧⽂化遗存。洁⽩的毡房")))
                    .addCell(mergedHotelCell);
// 添加数据行
            table
                    .addCell("2024/12/12 星期四")
                    .addCell("西安 Xi'an")
                    .addCell("")
                    .addCell("09:00 - 12:00")
                    .addCell("西安城墙")
                    .addCell("08:00-22:00 开放（22:00停止售票，22:00停止入园）")
                    .addCell("54CNY")
                    .addCell(new Cell().setWidth(UnitValue.createPointValue(150)).add(new Paragraph("阿贡盖提草原位于新疆阿勒泰地区的布尔津县，蒙古语阿贡盖提的意思gdfgfdgdfgdfgfd是阳光普照的地⽅”，有很多古⽼的⾃然形成的⽯⼈，在这⾥可以看到古⽼的草原游牧⽂化遗存。洁⽩的毡房")));

            table
                    .addCell("2")
                    .addCell("2024/12/12 星期四")
                    .addCell("西安 Xi'an")
                    .addCell("")
                    .addCell("09:00 - 12:00")
                    .addCell("西安城墙")
                    .addCell("08:00-22:00 开放（22:00停止售票，22:00停止入园）")
                    .addCell("54CNY")
                    .addCell(new Cell().setWidth(UnitValue.createPointValue(150)).add(new Paragraph("阿贡盖提草原位于新疆阿勒泰地区的布尔津县，蒙古语阿贡盖提的意思gdfgfdgdfgdfgfd是阳光普照的地⽅”，有很多古⽼的⾃然形成的⽯⼈，在这⾥可以看到古⽼的草原游牧⽂化遗存。洁⽩的毡房")))
                    .addCell("雅舒商务宾馆（西安永松路店）");

            table
                    .addCell("3")
                    .addCell("2024/12/12 星期四")
                    .addCell("西安 Xi'an")
                    .addCell("")
                    .addCell("09:00 - 12:00")
                    .addCell(new Cell().add(new Paragraph("西安城墙")).setFont(fontBold))
                    .addCell("08:00-22:00 开放（22:00停止售票，22:00停止入园）")
                    .addCell("54CNY")
                    .addCell(new Cell().setWidth(UnitValue.createPointValue(150)).add(new Paragraph("阿贡盖提草原位于新疆阿勒泰地区的布尔津县，蒙古语阿贡盖提的意思gdfgfdgdfgdfgfd是阳光普照的地⽅”，有很多古⽼的⾃然形成的⽯⼈，在这⾥可以看到古⽼的草原游牧⽂化遗存。洁⽩的毡房")))
                    .addCell("雅舒商务宾馆（西安永松路店）");
            pdfExporter.addCompanyInfo(table, doc);
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 创建表格以及添加表头
     *
     * @return
     */
    private static Table addTableAndTd() {
        // 创建表格
        Table table = new Table(10)
                //.useAllAvailableWidth()
                .setFontSize(8)
                .setHorizontalAlignment(HorizontalAlignment.CENTER);
        DeviceRgb headBackGroupCss = new DeviceRgb(73, 182, 195);
        DeviceRgb headFontCss = new DeviceRgb(255, 255, 255);
        // 添加表头
        float fontSize = 10;
        table.addCell(new Cell().setWidth(UnitValue.createPointValue(20)).add(new Paragraph("Day")).setFontSize(fontSize).setTextAlignment(TextAlignment.CENTER).setFontColor(headFontCss).setBackgroundColor(headBackGroupCss))
                .addCell(new Cell().setWidth(UnitValue.createPointValue(30)).add(new Paragraph("日期")).setFontSize(fontSize).setTextAlignment(TextAlignment.CENTER).setFontColor(headFontCss).setBackgroundColor(headBackGroupCss))
                .addCell(new Cell().setWidth(UnitValue.createPointValue(30)).add(new Paragraph("城市")).setFontSize(fontSize).setTextAlignment(TextAlignment.CENTER).setFontColor(headFontCss).setBackgroundColor(headBackGroupCss))
                .addCell(new Cell().add(new Paragraph("交通")).setFontSize(fontSize).setTextAlignment(TextAlignment.CENTER).setFontColor(headFontCss).setBackgroundColor(headBackGroupCss))
                .addCell(new Cell().setWidth(UnitValue.createPointValue(50)).add(new Paragraph("停留时间")).setFontSize(fontSize).setTextAlignment(TextAlignment.CENTER).setFontColor(headFontCss).setBackgroundColor(headBackGroupCss))
                .addCell(new Cell().setWidth(UnitValue.createPointValue(80)).add(new Paragraph("活动")).setFontSize(fontSize).setTextAlignment(TextAlignment.CENTER).setFontColor(headFontCss).setBackgroundColor(headBackGroupCss))
                .addCell(new Cell().add(new Paragraph("开放时间")).setFontSize(fontSize).setTextAlignment(TextAlignment.CENTER).setFontColor(headFontCss).setBackgroundColor(headBackGroupCss))
                .addCell(new Cell().setWidth(UnitValue.createPointValue(50)).add(new Paragraph("门票")).setFontSize(fontSize).setTextAlignment(TextAlignment.CENTER).setFontColor(headFontCss).setBackgroundColor(headBackGroupCss))
                .addCell(new Cell().setWidth(UnitValue.createPointValue(150)).add(new Paragraph("简介与说明")).setFontSize(fontSize).setTextAlignment(TextAlignment.CENTER).setFontColor(headFontCss).setBackgroundColor(headBackGroupCss))
                .addCell(new Cell().add(new Paragraph("住宿")).setFontSize(fontSize).setTextAlignment(TextAlignment.CENTER).setFontColor(headFontCss).setBackgroundColor(headBackGroupCss));
        return table;
    }

    /**
     * 转成pdf流输出
     *
     * @param title
     * @param travelBudget
     * @param itineraryExcelDayVos
     * @return
     */
    public ByteArrayInputStream convertPdfInputStream(String title, Integer travelBudget, List<ItineraryExcelDayVo> itineraryExcelDayVos) {
        // 创建输出流以捕获PDF内容
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PdfDocument pdf = new PdfDocument(new PdfWriter(baos));
        Document document = new Document(pdf, PageSize.A4.rotate());
        ItineraryBudgetLevel itineraryBudgetLevel = ItineraryBudgetLevel.fromValue(travelBudget);
        // 表格标题
        String pdfTitle = TITLE_PATTERN.formatted(title, itineraryBudgetLevel.getName());
        try {
            // 加载中文字体（请确保你有该字体文件）
            String fontPath = "fonts/SourceHanSansCN-Normal.ttf";
            PdfFont fontNormal = PdfFontFactory.createFont(fontPath, BaseFont.IDENTITY_H);
            String fontBoldPath = "fonts/SourceHanSansCN-Bold.ttf";
            PdfFont fontBold = PdfFontFactory.createFont(fontBoldPath);
            // 设置默认字体为中文字体
            document.setFont(fontNormal);
            //添加标题+logo
            addTitleAndLogo(document, pdfTitle, fontBold);
            //创建表格+表头
            Table table = addTableAndTd();
            //填充表格数据
            for (ItineraryExcelDayVo dayVo : itineraryExcelDayVos) {
                List<ItineraryExcelActivityVo> activityVoList = dayVo.getActivities();
                int activityNum = activityVoList.size();
                String dayNum = dayVo.getDayNum() == null ? "" : dayVo.getDayNum() + "";
                String date = StrUtil.isBlank(dayVo.getDate()) ? "" : dayVo.getDate();
                String cityName = StrUtil.isBlank(dayVo.getCity()) ? "" : dayVo.getCity();
                String tp = StrUtil.isBlank(dayVo.getTp()) ? "" : dayVo.getTp();
                String hotelName = StrUtil.isBlank(dayVo.getHotel()) ? "" : dayVo.getHotel();
                int row = 1;
                if (activityNum > 0) {
                    Cell dayNumCell = new Cell(activityNum, 1)
                            .add(new Paragraph(dayNum))
                            .setVerticalAlignment(VerticalAlignment.MIDDLE)
                            .setHorizontalAlignment(HorizontalAlignment.CENTER);
                    Cell dateCell = new Cell(activityNum, 1)
                            .add(new Paragraph(date))
                            .setVerticalAlignment(VerticalAlignment.MIDDLE)
                            .setHorizontalAlignment(HorizontalAlignment.CENTER);
                    Cell cityCell = new Cell(activityNum, 1)
                            .add(new Paragraph(cityName))
                            .setVerticalAlignment(VerticalAlignment.MIDDLE)
                            .setHorizontalAlignment(HorizontalAlignment.CENTER);
                    Cell tpCell = new Cell(activityNum, 1)
                            .add(new Paragraph(tp))
                            .setVerticalAlignment(VerticalAlignment.MIDDLE)
                            .setHorizontalAlignment(HorizontalAlignment.CENTER);
                    Cell hotelCell = new Cell(activityNum, 1)
                            .add(new Paragraph(hotelName))
                            .setVerticalAlignment(VerticalAlignment.MIDDLE)
                            .setHorizontalAlignment(HorizontalAlignment.CENTER);
                    for (ItineraryExcelActivityVo excelActivityVo : activityVoList) {
                        if (row == 1) {
                            table
                                    .addCell(dayNumCell)
                                    .addCell(dateCell)
                                    .addCell(cityCell)
                                    .addCell(tpCell)
                                    .addCell(excelActivityVo.getStayTimePeriod() != null ? excelActivityVo.getStayTimePeriod() : "")
                                    .addCell(new Cell().add(new Paragraph(excelActivityVo.getActivityName() != null ? excelActivityVo.getActivityName() : "")).setFont(fontBold))
                                    .addCell(excelActivityVo.getBusinessTime() != null ? excelActivityVo.getBusinessTime() : "")
                                    .addCell(excelActivityVo.getTicket() != null ? excelActivityVo.getTicket() : "")
                                    .addCell(new Cell().setWidth(UnitValue.createPointValue(150)).add(new Paragraph(excelActivityVo.getIntro() != null ? excelActivityVo.getIntro() : "")))
                                    .addCell(hotelCell);
                        } else {
                            table
                                    .addCell(excelActivityVo.getStayTimePeriod() != null ? excelActivityVo.getStayTimePeriod() : "")
                                    .addCell(new Cell().add(new Paragraph(excelActivityVo.getActivityName() != null ? excelActivityVo.getActivityName() : "")).setFont(fontBold))
                                    .addCell(excelActivityVo.getBusinessTime() != null ? excelActivityVo.getBusinessTime() : "")
                                    .addCell(excelActivityVo.getTicket() != null ? excelActivityVo.getTicket() : "")
                                    .addCell(new Cell().setWidth(UnitValue.createPointValue(150)).add(new Paragraph(excelActivityVo.getIntro() != null ? excelActivityVo.getIntro() : "")));
                        }
                        row++;
                    }
                } else {
                    table.addCell(dayNum)
                            .addCell(date)
                            .addCell(cityName)
                            .addCell(tp)
                            .addCell("")
                            .addCell("")
                            .addCell("")
                            .addCell("")
                            .addCell("")
                            .addCell(hotelName);
                }
            }
            //添加公司信息
            addCompanyInfo(table, document);
            // 关闭文档
            document.close();
            // 获取字节数组
            byte[] pdfBytes = baos.toByteArray();
            // 使用字节数组创建 ByteArrayInputStream
            return new ByteArrayInputStream(pdfBytes);
        } catch (Exception e) {
            log.error("导出pdf异常，异常：{}", e.getMessage(), e);
            return null;
        } finally {
            // 确保关闭输出流
            try {
                baos.close();
            } catch (IOException e) {
                log.error("导出pdf 关闭流异常，异常：{}", e.getMessage(), e);
            }
        }
    }

    /**
     * 添加标题+logo
     *
     * @param doc
     * @param titleName
     */
    private void addTitleAndLogo(Document doc, String titleName, PdfFont fontBold) {
        // 创建一个表格用于放置Logo和标题
        Table headerTable = new Table(10)
                //.useAllAvailableWidth()
                .setTextAlignment(TextAlignment.CENTER)
                .setHorizontalAlignment(HorizontalAlignment.CENTER);
        headerTable.setBorder(Border.NO_BORDER);
        // 加载Logo图片
        URL url = PdfExporter.class.getClassLoader().getResource("logo.png");
        ImageData imageData = ImageDataFactory.create(url);
        Image logoImage = new Image(imageData)
                // 设置Logo宽度，根据需要调整
                .setMarginLeft(-265)
                .setWidth(50);
        // 添加Logo到表格的第一列
        Cell logoCell = new Cell().add(logoImage);
        logoCell.setBorder(Border.NO_BORDER);
        headerTable.addCell(logoCell);
        // 添加标题
        Paragraph title = new Paragraph(titleName);
        title.setHorizontalAlignment(HorizontalAlignment.CENTER);
        title.setFontSize(16);
        title.setFont(fontBold);
        Cell titleCell = new Cell(1, 9).add(title).setBorder(Border.NO_BORDER);
        headerTable.addCell(titleCell);
        // 将包含Logo和标题的表格添加到文档
        doc.add(headerTable);
    }

    /**
     * 添加公司信息
     *
     * @param table
     * @param doc
     */
    private void addCompanyInfo(Table table, Document doc) {
        //添加公司信息
        String companyInfo = """
                公司名称：深圳搜旅智慧科技有限公司
                公司地址：深圳宝安区福园二路10号濠和智创B栋625
                公司官网：www.tripai.net
                联系电话：4001-520-999
                邮    箱：<EMAIL>
                """;
        // 第一个参数表示跨行数，第二个参数表示跨列数
        Cell footcell = new Cell(1, 8)
                .add(new Paragraph(companyInfo));
        footcell.setBorderRight(Border.NO_BORDER);
        table.addCell(footcell);
        // 加载applet图片
        ImageData imageAppletData = ImageDataFactory.create(PdfExporter.class.getClassLoader().getResource("applet.jpg"));
        Image appletImage = new Image(imageAppletData)
                .setMarginLeft(100)
                //.setFixedPosition(660,190)
                .setMarginTop(20)
                // 设置宽度，根据需要调整
                .setWidth(50);
        // 添加Logo到表格的第一列
        Cell appletImageCell = new Cell().add(appletImage);
        appletImageCell.setBorderRight(Border.NO_BORDER);
        appletImageCell.setBorderLeft(Border.NO_BORDER);
        table.addCell(appletImageCell);
        // 加载qrcode图片
        ImageData imageQrcodeData = ImageDataFactory.create(PdfExporter.class.getClassLoader().getResource("qrcode.jpg"));
        Image qrcodeImage = new Image(imageQrcodeData)
                .setMarginTop(20)
                //.setMarginLeft(-40)
                // 设置宽度，根据需要调整
                .setWidth(50);
        // 添加qrcode到表格的第一列
        Cell qrcodeImageCell = new Cell().add(qrcodeImage);
        qrcodeImageCell.setBorderLeft(Border.NO_BORDER);
        table.addCell(qrcodeImageCell);
        doc.add(table);
    }
}