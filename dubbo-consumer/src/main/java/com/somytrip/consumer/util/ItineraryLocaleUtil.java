package com.somytrip.consumer.util;

import com.somytrip.entity.enums.itinerary.ItineraryActivityType;
import com.somytrip.utils.LocaleUtil;

import java.util.Locale;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.util
 * @className: ItineraryLocaleUtil
 * @author: shadow
 * @description: 攻略多语言工具类
 * @date: 2025/5/29 15:45
 * @version: 1.0
 */
public class ItineraryLocaleUtil {


    /**
     * 攻略活动类型多语言转换
     *
     * @param type   活动类型
     * @param locale 语言
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/5/29 15:49
     */
    public static String formatActivityTypeName(ItineraryActivityType type, Locale locale) {
        return LocaleUtil.getLocalizedString("text.itinerary.activity.type.restaurant", new Object[]{type.getName()}, locale);
    }
}
