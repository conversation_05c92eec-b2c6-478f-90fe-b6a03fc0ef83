package com.somytrip.consumer.interceptor;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.somytrip.constant.HttpHeadConstant;
import com.somytrip.consumer.util.CommonUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-02-02 9:55
 */
@Slf4j
@Order(1)
public class LogRequestInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        // 生成唯一请求 ID（或从请求头中获取）
        String requestId = request.getHeader(HttpHeadConstant.REQUEST_ID);
        if (StrUtil.isBlank(requestId)) {
            requestId = UUID.randomUUID().toString();
        }
        MDC.put(HttpHeadConstant.REQUEST_ID, requestId);
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, OPTIONS, DELETE");
        response.setHeader("Access-Control-Allow-Headers",
                "Content-Type, Access-Control-Allow-Headers, Authorization, token, X-Requested-With, " +
                        "Access-Control-Allow-Methods, Access-Control-Allow-Origin");
        String ip = CommonUtils.getIpByHttpServletRequest(request);
        log.info("[token={}],[appType={}],[deviceNo={}],[versionCode={}], request from ip:{}, Method:{}, URI:{}", request.getHeader(HttpHeadConstant.AUTHORIZATION),
                request.getHeader(HttpHeadConstant.APPTYPE), request.getHeader(HttpHeadConstant.DEVICE_NO), request.getHeader(HttpHeadConstant.VERSION_CODE),
                ip, request.getMethod(),
                request.getRequestURI());
        request.setAttribute("ip", ip);
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }

    @Override
    public void postHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) throws Exception {
        // 清除 MDC，避免内存泄漏
        MDC.remove(HttpHeadConstant.REQUEST_ID);
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }
}
