package com.somytrip.consumer.advice;

import com.somytrip.consumer.service.RedisService;
import com.somytrip.target.CacheableHash;
import jakarta.annotation.Resource;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * @ClassName: CacheableHashAspect
 * @Description:
 * @Author: shadow
 * @Date: 2024/1/11 16:01
 */
@Aspect
@Component
public class CacheableHashAspect {

    @Resource
    private RedisService redisService;

    @Around("@annotation(cacheableHash)")
    public Object around(ProceedingJoinPoint pjp, CacheableHash cacheableHash) throws Throwable {
        // 获取方法的参数
        Object[] args = pjp.getArgs();
        // 生成key
        String key = cacheableHash.value();

        // 尝试从Redis中获取数据
        Object fieldKey = args[0]; // 使用方法的第一个参数作为哈希结构的字段键
        Object cachedValue = redisService.hget(key, String.valueOf(fieldKey));
        if (cachedValue != null) {
            // 如果Redis中有数据，那么直接返回这些数据
            return cachedValue;
        }

        // 如果Redis中没有数据，那么执行方法并获取结果
        Object result = pjp.proceed(args);

        // 将结果存储到Redis中
        redisService.hset(key, String.valueOf(fieldKey), result);

        return result;
    }
}
