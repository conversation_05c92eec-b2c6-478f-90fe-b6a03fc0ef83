package com.somytrip.consumer.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.itinerary.ItineraryHotService;
import com.somytrip.consumer.mapper.ItineraryHotMapper;
import com.somytrip.consumer.service.ConfigService;
import com.somytrip.consumer.service.RedisService;
import com.somytrip.entity.enums.ConfigEnum;
import com.somytrip.entity.enums.RedisKey;
import com.somytrip.entity.itinerary.ItineraryHotEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.service.impl
 * @className: ItineraryHotServiceImpl
 * @author: shadow
 * @description: 热门攻略Service实现类
 * @date: 2024/5/30 14:36
 * @version: 1.0
 */
@Slf4j
@Service
public class ItineraryHotServiceImpl
        extends ServiceImpl<ItineraryHotMapper, ItineraryHotEntity>
        implements ItineraryHotService {

    @Resource
    private ConfigService configService;
    @Resource
    private RedisService redisService;

    /**
     * 查询热门ID列表
     *
     * @return java.util.List<java.lang.Long>
     * <AUTHOR>
     * @date 2024/5/30 15:07
     */
    @Override
    public List<Long> queryHotIdList() {

        RedisKey itineraryHotIdsRedisKey = RedisKey.ITINERARY_HOT_IDS;
        String redisKey = itineraryHotIdsRedisKey.getKey();
        String redisData = redisService.getString(redisKey);
        if (StringUtils.isNotBlank(redisData)) {
            List<Long> cacheHotIds = JSON.parseArray(redisData, Long.class);
            if (cacheHotIds != null && !cacheHotIds.isEmpty()) {
                return cacheHotIds;
            }
        }

        // 读取查询条数配置
        String limitValue = configService.getConfigValue(ConfigEnum.ITINERARY_HOT_LIMIT);
        List<Long> hotIds = baseMapper.queryHotIdList(Integer.valueOf(limitValue));
        redisService.setString(redisKey, JSON.toJSONString(hotIds), itineraryHotIdsRedisKey.getTimeout());

        return hotIds;
    }

    /**
     * 根据攻略ID判断是否为热门攻略
     *
     * @param itineraryId 攻略ID
     * @return boolean
     * <AUTHOR>
     * @date 2024/6/7 9:47
     */
    @Override
    public boolean isHot(Long itineraryId) {

        List<Long> hotIds = queryHotIdList();
        log.info("hotIds: {}", JSONArray.toJSONString(hotIds));
        log.info("itineraryId: {}", itineraryId);
        return hotIds != null && hotIds.contains(itineraryId);
    }
}
