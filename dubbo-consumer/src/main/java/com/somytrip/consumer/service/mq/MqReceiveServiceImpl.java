package com.somytrip.consumer.service.mq;

import com.alibaba.fastjson2.JSON;
import com.somytrip.api.service.kafka.MqReceiveService;
import com.somytrip.api.service.user.TripCommonService;
import com.somytrip.entity.dto.AdCampaignData;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

/**
 * @Description: kafka消费者
 * @author: pigeon
 * @created: 2025-03-31 17:12
 */
@Component
@Slf4j
public class MqReceiveServiceImpl implements MqReceiveService {

    @Resource
    private TripCommonService tripCommonService;

    @KafkaListener(topics = "${config.mq.kafka.consumer.ad-topic:ad-topic-local}", groupId = "ad-group")
    @Override
    public void consumer(ConsumerRecord<String, String> record) {
        log.info("receive msg: {}, {}, {}", record.topic(), record.key(), record.value());
        if (tripCommonService.saveAdData(JSON.parseObject(record.value(), AdCampaignData.class)) > 0) {
            log.info("mq receiver save ad data: {}", record.value());
            return;
        }
        log.error("mq receiver ad data error: {}, {}, {}", record.topic(), record.key(), record.value());
    }


    @Override
    public void consumer(ConsumerRecord<String, String> record, Acknowledgment ack) {

    }
}
