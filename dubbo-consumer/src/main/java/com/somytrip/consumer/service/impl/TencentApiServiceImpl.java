package com.somytrip.consumer.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.somytrip.api.service.TencentApiService;
import com.somytrip.consumer.config.TencentApiConfig;
import com.somytrip.entity.dto.yy.Asr;
import com.somytrip.entity.tencent.AsrFastParams;
import com.tencentcloudapi.asr.v20190614.AsrClient;
import com.tencentcloudapi.asr.v20190614.models.SentenceRecognitionRequest;
import com.tencentcloudapi.asr.v20190614.models.SentenceRecognitionResponse;
import com.tencentcloudapi.captcha.v20190722.CaptchaClient;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-07-16 9:47
 */
@Slf4j
@Service
public class TencentApiServiceImpl implements TencentApiService {

    @Resource
    private TencentApiConfig tencentApiConfig;

    private OkHttpClient okHttpClient;
    private AsrClient asrClient;
    private CaptchaClient captchaClient;

    @PostConstruct
    public void init() {
        okHttpClient = new OkHttpClient();
        initAsrClient();
        initCaptchaClient();
    }

    public void initCaptchaClient() {
        Credential cred = new Credential(tencentApiConfig.getCaptcha().secretId(),
                tencentApiConfig.getCaptcha().secretKey());
        captchaClient = new CaptchaClient(cred, "");
    }

    public void initAsrClient() {
        Credential cred = new Credential(tencentApiConfig.getYySecretId(), tencentApiConfig.getYySecretKey());
        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("asr.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        asrClient = new AsrClient(cred, "", clientProfile);
    }


    @Override
    public String asrFast(AsrFastParams params, byte[] data) {
        params.setTimestamp("" + Instant.now().getEpochSecond());
        log.info("params: {}", params);
        HttpUrl.Builder httpUrlBuilder = new HttpUrl.Builder();
        final String host = "asr.cloud.tencent.com";
        final String path = "asr/flash/v1/" + tencentApiConfig.getYyAppId();
        params.setSecretId(tencentApiConfig.getYySecretId());
        httpUrlBuilder.scheme("https");
        httpUrlBuilder.host(host);
        httpUrlBuilder.addPathSegments(path);
        JSONObject requestParams = JSONObject.from(params);
        requestParams.keySet().stream().sorted().forEach(key -> {
            httpUrlBuilder.addQueryParameter(key, requestParams.getString(key));
        });
        RequestBody requestBody = RequestBody.create(data);
        Request request = new Request.Builder()
                .post(requestBody)
                .url(httpUrlBuilder.build())
                .addHeader("Host", host)
                .addHeader("Content-Type", "application/octet-stream")
                .addHeader("Authorization", getSha1Signature(StrUtil.concat(true, "POST", StrUtil.subSuf(httpUrlBuilder.toString(), 8))))
                .build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                assert response.body() != null;
                return response.body().string();
            }
            log.warn("response not success: {}, code: {}", params, response.code());
        } catch (IOException e) {
            log.error("io error: {}", params, e);
        }
        return "";
    }

    private String getSha1Signature(String text) {
        log.info("Sha1Signature: {}", text);
        return new HMac(HmacAlgorithm.HmacSHA1, tencentApiConfig.getYySecretKey().getBytes()).digestBase64(text, false);
    }


    @Override
    public String asrOne(Asr.Req req) {
        try {
            SentenceRecognitionRequest request = new SentenceRecognitionRequest();
            request.setEngSerViceType(req.getEngineType());
            request.setSourceType(1L);
            request.setVoiceFormat(req.getVoiceFormat().name());
            request.setData(Base64.encode(req.getFile().getBytes()));
            SentenceRecognitionResponse resp = asrClient.SentenceRecognition(request);
            return resp.getResult();
        } catch (TencentCloudSDKException | IOException e) {
            log.error("asrOne error", e);
        }
        return "";
    }

    @Override
    public String asrOne(Asr.ReqB64 req) {
        try {
            SentenceRecognitionRequest request = new SentenceRecognitionRequest();
            request.setEngSerViceType(req.getEngineType());
            request.setSourceType(1L);
            request.setVoiceFormat(req.getVoiceFormat().name());
            request.setData(req.getBase64File());
            SentenceRecognitionResponse resp = asrClient.SentenceRecognition(request);
            return resp.getResult();
        } catch (TencentCloudSDKException e) {
            log.error("asrOne error: {}", JSON.toJSONString(req), e);
        }
        return "";
    }
}
