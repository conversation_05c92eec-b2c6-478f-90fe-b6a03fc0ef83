package com.somytrip.consumer.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.FlightV2Service;
import com.somytrip.api.service.LocaleDictService;
import com.somytrip.api.service.TencentCosService;
import com.somytrip.api.service.flight.ManyFlightService;
import com.somytrip.api.service.tourism.GlobalCityService;
import com.somytrip.consumer.mapper.AirLineCodeDtoMapper;
import com.somytrip.consumer.mapper.flight.AirportCodeNameDTOMapper;
import com.somytrip.consumer.service.RedisService;
import com.somytrip.entity.dto.city.CityDto;
import com.somytrip.entity.dto.city.CityListResponse;
import com.somytrip.entity.dto.city.GlobalCityIATADto;
import com.somytrip.entity.dto.flight.AirLineCodeDto;
import com.somytrip.entity.dto.flight.AirportCodeNameDTO;
import com.somytrip.entity.dto.flight.AirportTimezone;
import com.somytrip.entity.enums.locale.LocaleDictType;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.flight.AirLineCodeVo;
import com.somytrip.entity.vo.flight.CancelFlightOrderVo;
import com.somytrip.entity.vo.flight.FlightConfigItem;
import com.somytrip.model.flight.fr24.FR24NotifyResponse;
import com.somytrip.model.flight.fr24.OrderChangeInfoNotify;
import com.somytrip.model.flight.tc.TCNotifyBody;
import com.somytrip.model.flight.vo.NotifyBody;
import com.somytrip.model.flight.vo.NotifyResult;
import com.somytrip.utils.JSONResultUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-09 16:40
 */

@Slf4j
@Service
@DubboService
public class FlightV2ServiceImpl extends ServiceImpl<AirLineCodeDtoMapper,
        AirLineCodeDto> implements FlightV2Service {
    private static final Integer REDIS_TIMEOUT = 7200;
    private static final String REDIS_KEY_FLIGHT = "TOURISM:TRANSPORTATION:DATA:";
    @Resource
    private AirportCodeNameDTOMapper airportCodeNameDTOMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private GlobalCityService globalCityService;
    @Resource
    private TencentCosService tencentCosService;
    @DubboReference(timeout = 80000)
    private ManyFlightService manyFlightService;
    @Resource
    private LocaleDictService localeDictService;

    @Override
    public List<AirportCodeNameDTO> getAirportList() {
        LambdaQueryWrapper<AirportCodeNameDTO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(AirportCodeNameDTO::getId);
        return airportCodeNameDTOMapper.selectList(queryWrapper);
    }

    @Override
    public ResponseResult<List<AirLineCodeVo>> getAirlineData() {
        final String bucket = "default";
        List<AirLineCodeDto> airLineCodeDtoList = baseMapper.selectList(new QueryWrapper<>());
        List<AirLineCodeVo> result = airLineCodeDtoList.stream().map(d -> {
            if (StrUtil.isNotBlank(d.getLogo())) {
                d.setLogo(tencentCosService.getUrl(bucket, d.getLogo()));
            }
            return new AirLineCodeVo(d);
        }).toList();
        return ResponseResult.ok(result);
    }

    @Override
    public ResponseResult<Map<String, AirLineCodeVo>> getAirlineDataV2() {
        final String bucket = "default";
        List<AirLineCodeDto> airLineCodeDtoList = baseMapper.selectList(new QueryWrapper<>());
        Map<String, AirLineCodeVo> result = new HashMap<>(airLineCodeDtoList.size());
        airLineCodeDtoList.forEach(d -> {
            if (StrUtil.isNotBlank(d.getLogo())) {
                d.setLogo(tencentCosService.getUrl(bucket, d.getLogo()));
            }
            result.put(d.getCode(), new AirLineCodeVo(d));
        });
        return ResponseResult.ok(result);
    }


    @Override
    public String getAirlineLogo(String airlineCode) {
        final String bucket = "default";
        AirLineCodeDto one = lambdaQuery().eq(AirLineCodeDto::getCode, airlineCode).one();
        if (one != null) {
            return tencentCosService.getUrl(bucket, one.getLogo());
        }
        log.error("airlineCode:{} not found", airlineCode);
        return null;
    }

    @Override
    public String getAirlineName(String airlineCode) {
        AirLineCodeDto one = lambdaQuery().eq(AirLineCodeDto::getCode, airlineCode).one();
        if (one != null) {
            return one.getNameCn();
        }
        log.error("airlineCode:{} not found", airlineCode);
        return null;
    }

    @Override
    public List<AirLineCodeVo> getAirlineList() {
        List<AirLineCodeDto> airLineCodeDtoList = baseMapper.selectList(new QueryWrapper<>());
        return airLineCodeDtoList.stream().map(AirLineCodeVo::new).toList();
    }

    @Override
    public ResponseResult cancelOrder(CancelFlightOrderVo vo) {
        log.info("request: {}", vo);
        if (manyFlightService.notPayCancelOrder(vo.getOrderNo(), vo.getReason())) {
            return JSONResultUtils.success();
        }
        return JSONResultUtils.fail();
    }

    @Override
    public String tcNotify(JSONObject body) {
        log.info("TC notify body: {}", body);
        NotifyResult<String> notifyResult = manyFlightService.tcNotify(new NotifyBody<>(body.toJavaObject(TCNotifyBody.class)));
        log.info("TC notify result: {}", notifyResult);
        return notifyResult.getResult();
    }

    @Override
    public ResponseResult flightSystemConfig() {
        FlightConfigItem.Type[] values = FlightConfigItem.Type.values();
        return JSONResultUtils.success(formatFlightConfigItemType(values));
    }

    @Override
    public ResponseResult flightSystemConfigV2() {
        Locale locale = LocaleContextHolder.getLocale();
        Map<String, String> localeMap = localeDictService.getMapFromType(LocaleDictType.FLIGHT_CONFIG, locale);

        FlightConfigItem.Type[] screen = {
                FlightConfigItem.Type.style,
                FlightConfigItem.Type.time,
                FlightConfigItem.Type.sort,
                FlightConfigItem.Type.cabin
        };
        FlightConfigItem.Type[] flightInfo = {
                FlightConfigItem.Type.cabin,
                FlightConfigItem.Type.passenger,
                FlightConfigItem.Type.productTag,
                FlightConfigItem.Type.wightType
        };
        Map<String, List<FlightConfigItem>> result = new HashMap<>(16);
//        result.put("screen", formatFlightConfigItemType(screen));
//        result.put("flightInfo", formatFlightConfigItemType(flightInfo));
        result.put("screen", formatFlightConfigItemTypeLocale(screen, localeMap));
        result.put("flightInfo", formatFlightConfigItemTypeLocale(flightInfo, localeMap));
        return JSONResultUtils.success(result);
    }

    @Override
    public ResponseResult<CityListResponse> flightCityDataV2() {
        return ResponseResult.ok(globalCityService.allIataCityV2());
    }

    @Override
    public ResponseResult cityCodeToIataCode(String cityCode) {
        CityDto cityDto = globalCityService.queryOneByCode(cityCode);
        if (Optional.ofNullable(cityDto).isEmpty()) {
            return JSONResultUtils.fail("not support city: " + cityCode);
        }
        LambdaQueryWrapper<AirportCodeNameDTO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByAsc(AirportCodeNameDTO::getId);
        lambdaQueryWrapper.eq(AirportCodeNameDTO::getCityId, cityDto.getCityId());
        List<AirportCodeNameDTO> airportCodes = airportCodeNameDTOMapper.selectList(lambdaQueryWrapper);
        if (airportCodes.isEmpty()) {
            return JSONResultUtils.fail("not support city: " + cityCode);
        }
        GlobalCityIATADto.GlobalCityIATAVo vo = new GlobalCityIATADto.GlobalCityIATAVo();
        vo.setCityCode(cityDto.getCityCode());
        vo.setCityName(cityDto.getCityName());
        vo.setCode(airportCodes.get(0).getCode());
        vo.setContinent(cityDto.getCountry());
        vo.setCountry(cityDto.getCountry());
        vo.setLogoPic(cityDto.getPic());
        return JSONResultUtils.success(vo);
    }

    @Override
    public ResponseResult cityCodeToIataCodeV3(String cityCode) {
        CityDto cityDto = globalCityService.queryOneByCode(cityCode);
        if (Optional.ofNullable(cityDto).isEmpty()) {
            return JSONResultUtils.fail("not support city: " + cityCode);
        }
//        LambdaQueryWrapper<AirportCodeNameDTO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(AirportCodeNameDTO::getCityId, cityDto.getCityId())
//                .eq(AirportCodeNameDTO::getActive, true);
//        List<AirportCodeNameDTO> airportCodes = airportCodeNameDTOMapper.selectList(lambdaQueryWrapper);
        Locale locale = LocaleContextHolder.getLocale();
        List<AirportCodeNameDTO> airportCodes = airportCodeNameDTOMapper.queryListByCityId(cityDto.getCityId(), locale.toString());
        log.info("airportCodes: {}", JSON.toJSONString(airportCodes));
        if (airportCodes.isEmpty()) {
            return JSONResultUtils.fail("not support city: " + cityCode);
        }
        List<GlobalCityIATADto.GlobalCityIATAVo> result = airportCodes.stream().map((airport) -> {
            GlobalCityIATADto.GlobalCityIATAVo vo = new GlobalCityIATADto.GlobalCityIATAVo();
            vo.setCityCode(cityDto.getCityCode());
            vo.setCityName(cityDto.getCityName());
            vo.setCode(airport.getCode());
            vo.setAirportName(airport.getName());
            vo.setContinent(cityDto.getCountry());
            vo.setCountry(cityDto.getCountry());
            vo.setLogoPic(cityDto.getPic());
            return vo;
        }).toList();
        return JSONResultUtils.success(result);
    }

    @Override
    public GlobalCityIATADto.GlobalCityIATAVo cityCodeToIataCodeV2(String cityCode) {
        CityDto cityDto = globalCityService.queryOneByCode(cityCode);
        if (Optional.ofNullable(cityDto).isEmpty()) {
            return null;
        }
        LambdaQueryWrapper<AirportCodeNameDTO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByAsc(AirportCodeNameDTO::getId);
        lambdaQueryWrapper.eq(AirportCodeNameDTO::getCityId, cityDto.getCityId());
        List<AirportCodeNameDTO> airportCodes = airportCodeNameDTOMapper.selectList(lambdaQueryWrapper);
        if (airportCodes.isEmpty()) {
            return null;
        }
        GlobalCityIATADto.GlobalCityIATAVo vo = new GlobalCityIATADto.GlobalCityIATAVo();
        vo.setCityCode(cityDto.getCityCode());
        vo.setCityName(cityDto.getCityName());
        vo.setCode(airportCodes.get(0).getCode());
        vo.setContinent(cityDto.getCountry());
        vo.setCountry(cityDto.getCountry());
        vo.setLogoPic(cityDto.getPic());
        return vo;
    }

    @Override
    public FR24NotifyResponse fr24Notify(String body) {
        log.info("FR24 notify: {}", body);
        NotifyBody<OrderChangeInfoNotify> orderChangeInfoNotifyNotifyBody = new NotifyBody<>(JSON.parseObject(body, OrderChangeInfoNotify.class));
        NotifyResult<FR24NotifyResponse> fr24NotifyResponseNotifyResult = manyFlightService.fr24Notify(orderChangeInfoNotifyNotifyBody);
        log.info("fr24 notify response: {}", fr24NotifyResponseNotifyResult);
        return fr24NotifyResponseNotifyResult.getResult();
    }

    @Override
    public Map<Integer, AirportCodeNameDTO.Po> getAirportNameMap() {
        return cacheIaTaCode();
    }

    @Override
    public List<List<String>> airportOrderInfo(Collection<String> airportCodeList) {
        List<Map<String, String>> airportCityList = airportCodeNameDTOMapper.queryAirportNameByAirportCode(airportCodeList.stream().toList());
        List<String> airports = new ArrayList<>();
        List<String> cities = new ArrayList<>();
        airportCityList.forEach(item -> {
            String airport = item.get("airport");
            if (StrUtil.isNotBlank(airport)) {
                if (!airports.contains(airport)) {
                    if (StrUtil.isNotBlank(item.get("city"))) {
                        cities.add(item.get("city") + airport);
                    }
                    airports.add(airport);
                }
            }
        });
        log.info("airports: {}, cities: {}", airports, cities);
        return ListUtil.of(airports, cities);
    }

    @Override
    public Map<String, String> getAirportTimezone() {
        List<AirportTimezone> list = airportCodeNameDTOMapper.queryAirportTimezone();
        log.info("results: {}", JSON.toJSONString(list));
        Map<String, String> result = new HashMap<>(list.size());
        list.forEach(item -> {
            result.put(item.code(), item.timezone());
        });
        return result;
    }


    /**
     * 缓存 CITY<->IATA
     *
     * @return
     */
    private Map<Integer, AirportCodeNameDTO.Po> cacheIaTaCode() {
        List<AirportCodeNameDTO.Po> pos = airportCodeNameDTOMapper.selectAirportCodePoListByCityId();
        Map<Integer, AirportCodeNameDTO.Po> poMap = new HashMap<>(pos.size());
        for (AirportCodeNameDTO.Po po : pos) {
            poMap.put(po.getCityId(), po);
        }
        final String key = REDIS_KEY_FLIGHT + "PO";
        redisService.hmset(key, JSONObject.from(poMap), REDIS_TIMEOUT);
        return poMap;
    }

    private List<FlightConfigItem> formatFlightConfigItemType(FlightConfigItem.Type[] types) {
        List<FlightConfigItem> result = new ArrayList<>();
        FlightConfigItem flightConfigItem;
        List<FlightConfigItem.NameValue> nameValues;
        for (FlightConfigItem.Type type : types) {
            flightConfigItem = new FlightConfigItem();
            flightConfigItem.setType(type);
            flightConfigItem.setName(type.getName());
            nameValues = new ArrayList<>();
            List<String> fieldNames = EnumUtil.getNames(type.getValue());
            List<Object> displays = EnumUtil.getFieldValues(type.getValue(), "display");
            log.info("enums: {}", fieldNames);
            log.info("displays: {}", displays);
            for (int i = 0; i < fieldNames.size(); i++) {
                nameValues.add(new FlightConfigItem.NameValue(StrUtil.str(displays.get(i),
                        StandardCharsets.UTF_8), fieldNames.get(i)));
            }
            flightConfigItem.setValue(nameValues);
            result.add(flightConfigItem);
        }
        return result;
    }

    private List<FlightConfigItem> formatFlightConfigItemTypeLocale(
            FlightConfigItem.Type[] types,
            Map<String, String> localeMap
    ) {
        List<FlightConfigItem> result = new ArrayList<>();
        FlightConfigItem flightConfigItem;
        List<FlightConfigItem.NameValue> nameValues;
        for (FlightConfigItem.Type type : types) {
            flightConfigItem = new FlightConfigItem();
            flightConfigItem.setType(type);
//            flightConfigItem.setName(type.getName());
            String typeName = localeMap.getOrDefault(type.name(), type.getName());
            flightConfigItem.setName(typeName);
            nameValues = new ArrayList<>();
            List<String> fieldNames = EnumUtil.getNames(type.getValue());
            List<Object> displays = EnumUtil.getFieldValues(type.getValue(), "display");
            log.info("enums: {}", fieldNames);
            log.info("displays: {}", displays);
            for (int i = 0; i < fieldNames.size(); i++) {
                FlightConfigItem.NameValue nameValue = new FlightConfigItem.NameValue(
                        localeMap.getOrDefault(fieldNames.get(i), StrUtil.str(displays.get(i), StandardCharsets.UTF_8)),
                        fieldNames.get(i)
                );
                nameValues.add(nameValue);
            }
            flightConfigItem.setValue(nameValues);
            result.add(flightConfigItem);
        }
        return result;
    }
}
