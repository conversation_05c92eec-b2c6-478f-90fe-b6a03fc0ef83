package com.somytrip.consumer.service.impl;

import cn.hutool.core.util.StrUtil;
import com.somytrip.api.service.common.IpLocationService;
import com.somytrip.entity.dto.common.IpLocationInfo;
import com.somytrip.utils.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.regex.Pattern;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.service.impl
 * @className: IpLocationServiceImpl
 * @author: lijunqi
 * @description: IP地址位置解析服务实现类
 * @date: 2025-01-04 16:00:00
 * @Version: 1.0
 */
@Slf4j
@DubboService
public class IpLocationServiceImpl implements IpLocationService {

    /**
     * IP地址正则表达式
     */
    private static final Pattern IP_PATTERN = Pattern.compile(
            "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$"
    );

    /**
     * 根据IP地址获取位置信息
     *
     * @param ipAddress IP地址
     * @return IP位置信息
     */
    @Override
    public IpLocationInfo getLocationByIp(String ipAddress) {
        if (!isValidIp(ipAddress)) {
            log.warn("无效的IP地址: {}", ipAddress);
            return new IpLocationInfo(ipAddress, "未知", "未知", "未知", "未知");
        }

        try {
            // 使用工具类解析IP地址
            IpLocationInfo locationInfo = IpUtils.parseIpLocation(ipAddress);
            log.debug("IP地址 {} 解析结果: {}", ipAddress, locationInfo);
            return locationInfo;
        } catch (Exception e) {
            log.error("解析IP地址失败: {}", ipAddress, e);
            return new IpLocationInfo(ipAddress, "解析失败", "解析失败", "未知", "未知");
        }
    }

    /**
     * 批量获取IP地址位置信息
     *
     * @param ipAddresses IP地址数组
     * @return IP位置信息数组
     */
    @Override
    public IpLocationInfo[] getLocationsByIps(String[] ipAddresses) {
        if (ipAddresses == null || ipAddresses.length == 0) {
            return new IpLocationInfo[0];
        }

        IpLocationInfo[] results = new IpLocationInfo[ipAddresses.length];
        for (int i = 0; i < ipAddresses.length; i++) {
            results[i] = getLocationByIp(ipAddresses[i]);
        }
        return results;
    }

    /**
     * 检查IP地址是否有效
     *
     * @param ipAddress IP地址
     * @return 是否有效
     */
    @Override
    public boolean isValidIp(String ipAddress) {
        if (StrUtil.isBlank(ipAddress)) {
            return false;
        }

        // 检查是否为IPv4格式
        if (IP_PATTERN.matcher(ipAddress).matches()) {
            return true;
        }

        // 检查是否为IPv6格式（简单检查）
        if (ipAddress.contains(":") && ipAddress.length() <= 39) {
            return true;
        }

        // 检查特殊情况
        return "localhost".equalsIgnoreCase(ipAddress) || 
               "unknown".equalsIgnoreCase(ipAddress);
    }
}
