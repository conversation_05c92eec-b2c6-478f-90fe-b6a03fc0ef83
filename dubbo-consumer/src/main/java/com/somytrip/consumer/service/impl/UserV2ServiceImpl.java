package com.somytrip.consumer.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.jwt.JWT;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.somytrip.api.service.TencentCosService;
import com.somytrip.api.service.community.CommunityUserInfoService;
import com.somytrip.api.service.pay.DouYinService;
import com.somytrip.api.service.user.UserV2Service;
import com.somytrip.common.util.AesUtil;
import com.somytrip.constant.AppTypeEnum;
import com.somytrip.consumer.config.IDGenConfig;
import com.somytrip.consumer.config.UniConfig;
import com.somytrip.consumer.config.WebClientConfig;
import com.somytrip.consumer.mapper.SomytripUserChannelMapper;
import com.somytrip.consumer.mapper.SomytripUsersMapper;
import com.somytrip.consumer.request.AppleRequestService;
import com.somytrip.consumer.service.LoginService;
import com.somytrip.consumer.service.RedisService;
import com.somytrip.consumer.util.CommonUtils;
import com.somytrip.consumer.util.ConstantUtil;
import com.somytrip.consumer.util.WeiXinUtil;
import com.somytrip.entity.dto.douyin.Code2SessionReq;
import com.somytrip.entity.dto.douyin.Code2SessionRsp;
import com.somytrip.entity.dto.douyin.DecryptData;
import com.somytrip.entity.dto.user.DouYinCodeRsp;
import com.somytrip.entity.dto.user.SomytripUserChannel;
import com.somytrip.entity.dto.user.SomytripUsers;
import com.somytrip.entity.enums.user.LoginMethodEnum;
import com.somytrip.entity.enums.user.UserLoginChannel;
import com.somytrip.entity.enums.user.UserLoginChannelType;
import com.somytrip.entity.po.uni.UniPhoneNumberRequest;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.user.LoginResultVo;
import com.somytrip.entity.vo.user.UserVo;
import com.somytrip.exception.BusinessException;
import com.somytrip.exception.LoginException;
import com.somytrip.utils.JSONResultUtils;
import com.somytrip.utils.SqlAesUtil;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-23 11:57
 */
@DubboService
@Slf4j
@Service
public class UserV2ServiceImpl implements UserV2Service {
    private final String DOU_YIN_CODE2SESSION_KEY = "user:loginOrRegister:douyin:code2session:";
    @Resource
    private IDGenConfig idGenConfig;
    @Resource
    private SomytripUsersMapper somytripUsersMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private TencentCosService tencentCosService;
    @Resource
    private LoginService loginService;
    @Resource
    private SomytripUserChannelMapper somytripUserChannelMapper;
    @Resource
    private WeiXinUtil weiXinUtil;
    @Resource
    private UniConfig uniConfig;
    @Resource
    private WebClientConfig webClientConfig;
    @Resource
    private DouYinService douYinService;
    @Value("${config.user.not-verification-phone}")
    private String notVerificationPhone;
    private AppleRequestService appleRequestService;
    @DubboReference(check = false)
    private CommunityUserInfoService communityUserInfoService;

    /**
     * 用户id加密key
     */
    @Value("${community.userId.secretKey}")
    private String userIdSecretKey;

    @PostConstruct
    public void init() {
        this.appleRequestService = webClientConfig.createService(AppleRequestService.BASE_URL, AppleRequestService.class);
    }

    @Override
    public ResponseResult<Boolean> deleteUserByUserId(String userId, String accessToken) {
        log.info("deleteUserID:{}", userId);
        SomytripUsers user = somytripUsersMapper.selectById(userId);
        log.info("deleteUser:{}", user);
        if (Optional.ofNullable(user).isPresent()) {
            if (user.isDel()) {
                return ResponseResult.fail("api.user.login.user-not-exist");
            } else {
                user.setDel(true);
                if (somytripUsersMapper.updateById(user) > 0) {
                    try {
                        StpUtil.logout(user.getId());
                    } catch (Exception e) {
                        log.error("注销会话报错: {}， {}", userId, accessToken, e);
                    }
                    return ResponseResult.ok("api.user.unsubscribe.success", true);
                }
                return ResponseResult.ok("api.user.unsubscribe.error", true);
            }
        } else {
            return ResponseResult.fail("api.user.login.user-not-exist");
        }
    }

    @Override
    public boolean updateById(SomytripUsers somytripUsers) {
        return somytripUsersMapper.updateById(somytripUsers) > 0;
    }

    @Override
    @Transactional(rollbackFor = {LoginException.class})
    public ResponseResult login(SomytripUsers.LoginEntity loginEntity, String loginChannelType, String ip) {
        log.info("{} ip:{}", loginEntity, ip);
        SomytripUsers somytripUsers = null;
        try {
            if (loginEntity.checkChannel()) {
                // 第三方登陆
                UserLoginChannel channel = loginEntity.getChannel();
                boolean validChannel = switch (channel) {
                    case wechat -> loginEntity.checkChannelWeChatCode();
                    case apple -> loginEntity.checkChannelAppleToken();
                    case phone -> loginEntity.checkChannelUniPhoneLogin();
                    case douyin -> loginEntity.checkChannelDouYinCode();
                    default -> false;
                };
                log.info("是否是支持的第三方渠道: {}, {}", channel, validChannel);
                if (!validChannel) {
                    return JSONResultUtils.fail(400, "api.common.params.error");
                }
                log.info("第三方渠道类型: {}", loginEntity.getChannelType());
                UserLoginChannelType channelType = loginEntity.getChannelType();
                if (channelType != UserLoginChannelType.applet
                        && channelType != UserLoginChannelType.application
                        && channelType != UserLoginChannelType.uni) {
                    return JSONResultUtils.fail(400, "api.common.params.error");
                }

                if (channel == UserLoginChannel.wechat) {
                    somytripUsers = this.loginWeChatChannel(loginEntity);
                } else if (channel == UserLoginChannel.phone) {
                    somytripUsers = this.loginUniPhone(loginEntity);
                } else if (channel == UserLoginChannel.apple) {
                    somytripUsers = this.loginAppleChannel(loginEntity);
                } else if (channel == UserLoginChannel.douyin) {
                    // TODO 抖音login
                    String voucher = loginEntity.getChannelObject().getString("voucher");
                    if (StrUtil.isBlank(voucher)) {
                        DouYinCodeRsp douYinCodeRsp = this.loginDouYinChannelNotCacheCode(loginEntity);
                        if (douYinCodeRsp.isRegister()) {
                            somytripUsers = JSON.parseObject(douYinCodeRsp.getVoucher(), SomytripUsers.class);
                            log.info("douyin register user loginOrRegister: {}", somytripUsers);
                        } else {
                            log.info("douyin not register user loginOrRegister: {}", JSON.toJSONString(douYinCodeRsp));
                            return JSONResultUtils.success(306, "api.user.login.douying.not-register", douYinCodeRsp);
                        }
                    }
                    if (Optional.ofNullable(somytripUsers).isEmpty()) {
                        somytripUsers = loginDouYinChannel(loginEntity, voucher);
                    }

                } else {
                    return JSONResultUtils.fail(400, "api.common.params.error");
                }

                if (Optional.ofNullable(somytripUsers).isPresent()) {
                    return JSONResultUtils.success("api.user.login.success", formatLoginResult(somytripUsers, loginChannelType, ip));
                }
            } else {
                // 普通短信验证码登陆
                log.info("普通短信验证码登陆");
                if (StrUtil.contains(notVerificationPhone, loginEntity.getMobile().getMobile())) {
                    log.info("苹果开发者账号登陆");
                    somytripUsers = login(loginEntity.getMobile());
                    assert somytripUsers != null;
                    return JSONResultUtils.success("api.user.login.success", formatLoginResult(somytripUsers, loginChannelType, ip));
                }
                if (loginEntity.checkMobile()) {
                    throw new LoginException(307, "api.user.login.user-mobile-irregularity");
                }
                String key = String.format(ConstantUtil.Sms.BASE_CODE_REDIS_KEY,
                        ConstantUtil.Sms.TYPE_MAP.get("1")) + loginEntity.getMobile().getMobile();
                if (loginEntity.checkVerificationCode(redisService.getString(key))) {
                    return JSONResultUtils.fail(400, "api.user.login.verification-code-error");
                }
                somytripUsers = login(loginEntity.getMobile());
                if (Optional.ofNullable(somytripUsers).isPresent()) {
                    redisService.del(key);
                    return JSONResultUtils.success("api.user.login.success", formatLoginResult(somytripUsers, loginChannelType, ip));
                }
                // 用户不存在
                Date date = new Date();
                somytripUsers = new SomytripUsers();
                somytripUsers.setUniqueId(this.smtId());
                somytripUsers.setMobileEntity(loginEntity.getMobile());
                somytripUsers.setCreateTime(date);
                somytripUsers.setUpdateTime(date);
                if (this.register(somytripUsers) > 0) {
                    somytripUsers = somytripUsersMapper.selectById(somytripUsers.getId());
                    redisService.del(key);
                    return JSONResultUtils.success("api.user.login.success", formatLoginResult(somytripUsers, loginChannelType, ip));
                } else {
                    throw new LoginException("api.user.register.error");
                }
            }
        } catch (LoginException e) {
            log.warn("LoginException error: {}", e.getMsg());
            return JSONResultUtils.fail(e.getCode(), e.getMsg());
        } catch (Exception e) {
            log.error("", e);
        }
        return JSONResultUtils.fail();
    }

    private SomytripUsers loginDouYinChannel(SomytripUsers.LoginEntity loginEntity, String voucher) {
        JSONObject channelObject = loginEntity.getChannelObject();
        String key = DOU_YIN_CODE2SESSION_KEY + voucher;
        String jsonValue = redisService.getString(key);
        Code2SessionRsp code2SessionRsp = JSON.parseObject(jsonValue, Code2SessionRsp.class);
        if (StrUtil.isBlank(jsonValue)) {
            throw new LoginException(400, "api.common.params.error");
        }
        if (loginEntity.getChannelType() == UserLoginChannelType.applet) {
            String iv = channelObject.getString("iv");
            String encryptedData = channelObject.getString("encryptedData");
            String decodeData = Base64.decodeStr(encryptedData);
            String decodeSecret = Base64.decodeStr(code2SessionRsp.getData().getSessionKey());
            String decodeIv = Base64.decodeStr(iv);
            AES aes = new AES(Mode.CBC, Padding.NoPadding, decodeSecret.getBytes(), decodeIv.getBytes());
            String decryptData = aes.decryptStr(decodeData);
            log.info("decryptData:{}", decryptData);
            DecryptData decryptResult = JSON.parseObject(decodeData, DecryptData.class);
            Date date = new Date();
            SomytripUserChannel somytripUserChannel = new SomytripUserChannel();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code2SessionRsp", code2SessionRsp);
            jsonObject.put("decryptResult", decryptResult);
            somytripUserChannel.setChannelUserInfo(jsonObject);
            somytripUserChannel.setChannel(loginEntity.getChannel());
            somytripUserChannel.setChannelType(loginEntity.getChannelType());
            somytripUserChannel.setCreateTime(date);
            somytripUserChannel.setUpdateTime(date);
            somytripUserChannel.setChannelId(code2SessionRsp.getData().getOpenid());
            somytripUserChannel.setChannelUniqueId(code2SessionRsp.getData().getUnionid());

            SomytripUsers.Mobile mobile = new SomytripUsers.Mobile();
            mobile.setMobile(decryptResult.getPurePhoneNumber());
            mobile.setCountryCode("+" + decryptResult.getCountryCode());
            SomytripUsers somytripUsers = login(mobile);
            if (Optional.ofNullable(somytripUsers).isEmpty()) {
                somytripUsers = new SomytripUsers();
                somytripUsers.setUniqueId(smtId());
                somytripUsers.setUpdateTime(date);
                somytripUsers.setCreateTime(date);
                somytripUsers.setNickname("smt_user");
                somytripUsers.setAuthImage("");
                somytripUsers.setMobileEntity(mobile);
                if (this.register(somytripUsers) <= 0) {
                    log.warn("注册+绑定第三方失败 register:{}", loginEntity);
                    throw new LoginException("api.user.login.third-channel-bind-error");
                }
                somytripUsers = somytripUsersMapper.selectById(somytripUsers.getId());
            }
            somytripUserChannel.setUserId(somytripUsers.getId());
            somytripUserChannel.setUserUniqueId(somytripUsers.getUniqueId());
            if (register(somytripUserChannel) > 0) {
                return somytripUsers;
            } else {
                log.warn("注册+绑定第三方失败 channel:{}", loginEntity);
                throw new LoginException(302, "api.user.login.third-channel-bind-error");
            }
        } else if (loginEntity.getChannelType() == UserLoginChannelType.application) {
            String verificationCodeKey = String.format(ConstantUtil.Sms.BASE_CODE_REDIS_KEY,
                    ConstantUtil.Sms.TYPE_MAP.get("1")) + loginEntity.getMobile().getMobile();
            if (loginEntity.checkVerificationCode(redisService.getString(verificationCodeKey))) {
                throw new LoginException(400, "api.user.login.verification-code-error");
            }
            SomytripUsers somytripUsers = login(loginEntity.getMobile());
            Date date = new Date();
            if (somytripUsers == null) {
                somytripUsers = new SomytripUsers();
                somytripUsers.setUniqueId(smtId());
                somytripUsers.setUpdateTime(date);
                somytripUsers.setCreateTime(date);
                somytripUsers.setNickname("smt_user");
                somytripUsers.setAuthImage("");
                somytripUsers.setMobileEntity(loginEntity.getMobile());
                if (this.register(somytripUsers) <= 0) {
                    log.warn("注册+绑定第三方失败 register user:{}", loginEntity);
                    throw new LoginException("api.user.login.third-channel-bind-error");
                }
                somytripUsers = somytripUsersMapper.selectById(somytripUsers.getId());
            }
            SomytripUserChannel somytripUserChannel = somytripUserChannelMapper.getChannelByOpenIdOrUnionId(
                    loginEntity.getChannel().name(),
                    loginEntity.getChannelType().name(),
                    code2SessionRsp.getData().getOpenid(),
                    code2SessionRsp.getData().getUnionid()
            );
            log.info("查询到渠道绑定的情况 channel: {}", somytripUserChannel);
            if (Optional.ofNullable(somytripUserChannel).isEmpty()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("code2SessionRsp", code2SessionRsp);
                somytripUserChannel = new SomytripUserChannel();
                somytripUserChannel.setChannelUserInfo(jsonObject);
                somytripUserChannel.setChannel(loginEntity.getChannel());
                somytripUserChannel.setChannelType(loginEntity.getChannelType());
                somytripUserChannel.setCreateTime(date);
                somytripUserChannel.setUpdateTime(date);
                somytripUserChannel.setChannelId(code2SessionRsp.getData().getOpenid());
                somytripUserChannel.setChannelUniqueId(code2SessionRsp.getData().getUnionid());
                somytripUserChannel.setUserId(somytripUsers.getId());
                somytripUserChannel.setUserUniqueId(somytripUsers.getUniqueId());
                if (register(somytripUserChannel) <= 0) {
                    log.warn("注册+绑定第三方失败 register channel:{}", loginEntity);
                    throw new LoginException(302, "api.user.login.third-channel-bind-error");
                }
            }

            if (!ObjectUtil.equal(somytripUsers.getId(), somytripUserChannel.getUserId())) {
                somytripUserChannel.setUserId(somytripUsers.getId());
                somytripUserChannel.setUserUniqueId(somytripUsers.getUniqueId());
                somytripUserChannel.setUpdateTime(date);
                somytripUserChannelMapper.updateById(somytripUserChannel);
                log.info("更新渠道登陆success: {}", JSON.toJSONString(somytripUserChannel));
            }
            redisService.del(verificationCodeKey);
            return somytripUsers;
        } else {
            log.error("dou yin not support channel type:{}", loginEntity.getChannelType());
            return null;
        }
    }

    private DouYinCodeRsp loginDouYinChannelNotCacheCode(SomytripUsers.LoginEntity loginEntity) {
        JSONObject channelObject = loginEntity.getChannelObject();
        Code2SessionReq req = new Code2SessionReq();
        req.setCode(channelObject.getString("code"));
        Code2SessionRsp code2SessionRsp = douYinService.code2Session(req);
        String jsonRsp = JSON.toJSONString(code2SessionRsp);
        if (code2SessionRsp.getErrNo() != 0) {
            log.error("dou yin code2session: {}", jsonRsp);
            throw new LoginException(code2SessionRsp.getErrNo(), code2SessionRsp.getErrTips());
        }
        SomytripUserChannel somytripUserChannel = somytripUserChannelMapper.getChannelByOpenIdOrUnionId(
                loginEntity.getChannel().name(),
                loginEntity.getChannelType().name(),
                code2SessionRsp.getData().getOpenid(),
                code2SessionRsp.getData().getUnionid()
        );
        if (Optional.ofNullable(somytripUserChannel).isPresent() && Optional.ofNullable(somytripUserChannel.getUserId()).isPresent()) {
            log.info("channel register: {}", JSON.toJSONString(somytripUserChannel));
            QueryWrapper<SomytripUsers> usersQueryWrapper = new QueryWrapper<>();
            usersQueryWrapper.eq("id", somytripUserChannel.getUserId());
            usersQueryWrapper.eq("unique_id", somytripUserChannel.getUserUniqueId());
            usersQueryWrapper.eq("is_del", false);
            SomytripUsers somytripUsers = somytripUsersMapper.selectOne(usersQueryWrapper);
            log.info("channel register user: {}", somytripUsers);
            if (Optional.ofNullable(somytripUsers).isPresent()) {
                DouYinCodeRsp douYinCodeRsp = new DouYinCodeRsp();
                douYinCodeRsp.setVoucher(JSON.toJSONString(somytripUsers));
                douYinCodeRsp.setRegister(true);
                return douYinCodeRsp;
            }
        }

        String voucher = SecureUtil.sha256(jsonRsp);
        String key = DOU_YIN_CODE2SESSION_KEY + voucher;
        redisService.setString(key, jsonRsp, 7200);
        DouYinCodeRsp douYinCodeRsp = new DouYinCodeRsp();
        douYinCodeRsp.setRegister(false);
        douYinCodeRsp.setOpenid(StrUtil.hide(code2SessionRsp.getData().getOpenid(), 3, 7));
        douYinCodeRsp.setUnionid(StrUtil.hide(code2SessionRsp.getData().getUnionid(), 3, 7));
        douYinCodeRsp.setVoucher(voucher);
        return douYinCodeRsp;
    }

    @Override
    public UserVo getUserByUserId(String userId) {
        SomytripUsers somytripUsers = somytripUsersMapper.selectById(userId);
        UserVo userVo = new UserVo();
        if (Optional.ofNullable(somytripUsers).isPresent() && !somytripUsers.isDel()) {
            somytripUsers.setAuthImage(tencentCosService.getAuthImage(somytripUsers.getAuthImage()));
            userVo.setUserVo(somytripUsers);
        }
        return userVo;
    }

    @Override
    public UserVo getUserByUserIdNotDesensitized(String userId) {
        SomytripUsers somytripUsers = somytripUsersMapper.selectById(userId);
        UserVo userVo = new UserVo();
        if (Optional.ofNullable(somytripUsers).isPresent() && !somytripUsers.isDel()) {
            somytripUsers.setAuthImage(tencentCosService.getAuthImage(somytripUsers.getAuthImage()));
            userVo.setUserVo(somytripUsers, false);
        }
        return userVo;
    }

    @Override
    public SomytripUsers getById(String userId) {
        return somytripUsersMapper.selectById(userId);
    }

    @Override
    public String getUserChannelOpenId(UserVo userVo, UserLoginChannel channel, UserLoginChannelType type) {
        QueryWrapper<SomytripUserChannel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userVo.getId());
        queryWrapper.eq("channel", channel);
        queryWrapper.eq("channel_type", type);
        List<SomytripUserChannel> somytripUserChannel = somytripUserChannelMapper.selectList(queryWrapper);
        if (Optional.ofNullable(somytripUserChannel).isPresent() && !somytripUserChannel.isEmpty()) {
            return somytripUserChannel.get(0).getChannelId();
        }
        return null;
    }

    @Override
    public int register(SomytripUsers somytripUsers) {
        log.info("mobile register user:{}", somytripUsers);
        somytripUsers.setUniqueId(smtId());
        int insertCount = somytripUsersMapper.insert(somytripUsers);
        if (insertCount > 0) {
            //同步用户到社区
            communityUserInfoService.communityUserRegister(somytripUsers.getId());
        }
        return insertCount;
    }

    private SomytripUsers loginUniPhone(SomytripUsers.LoginEntity loginEntity) {
        SomytripUsers somytripUsers = null;
        UniPhoneNumberRequest params = loginEntity.getChannelObject().toJavaObject(UniPhoneNumberRequest.class);
        if (StrUtil.isBlank(params.getOpenid()) || StrUtil.isBlank(params.getAccessToken())) {
            throw new LoginException("chanel params is blank");
        }
        String[] signArr = new String[loginEntity.getChannelObject().size()];
        int index = 0;
        for (String key : loginEntity.getChannelObject().keySet()) {
            signArr[index++] = StrUtil.concat(false, key, "=", loginEntity.getChannelObject().getString(key));
        }

        String signStr = Arrays.stream(signArr).sorted().collect(Collectors.joining("&"));
        log.info("signArr: {}", signStr);
        params.setSign(uniConfig.getSha256Hmac().digestHex(signStr.getBytes(StandardCharsets.UTF_8)));
        JSONObject response = uniConfig.request().getPhoneFormAccessTokenAndOpenid(params.getOpenid(), params.getAccessToken(), params.getSign());
        log.info("uni get phone response: {}", response);
        if (response.getBooleanValue("success") && StrUtil.isNotBlank(response.getString("phoneNumber"))) {
            log.info("get data success");
            loginEntity.setMobile(new SomytripUsers.Mobile("+86", response.getString("phoneNumber")));
            somytripUsers = login(loginEntity.getMobile());
            if (Optional.ofNullable(somytripUsers).isPresent()) {
                return somytripUsers;
            }
            // 用户不存在
            Date date = new Date();
            somytripUsers = new SomytripUsers();
            somytripUsers.setUniqueId(this.smtId());
            somytripUsers.setMobileEntity(loginEntity.getMobile());
            somytripUsers.setCreateTime(date);
            somytripUsers.setUpdateTime(date);
            if (this.register(somytripUsers) > 0) {
                somytripUsers = somytripUsersMapper.selectById(somytripUsers.getId());
                return somytripUsers;
            } else {
                throw new LoginException("api.user.register.error");
            }
        }
        throw new LoginException(response.getString("message"));
    }

    /**
     * 微信渠道登陆
     *
     * @param loginEntity 登陆实体
     * @return SomytripUsers
     */
    private SomytripUsers loginWeChatChannel(SomytripUsers.LoginEntity loginEntity) {
        log.info("loginWeChat {}", loginEntity);
        SomytripUsers somytripUsers;
        try {
            JSONObject jsonObject = null;
            if (loginEntity.getChannelType() == UserLoginChannelType.applet) {
                jsonObject = JSONObject.parseObject(weiXinUtil
                        .getUserOpenIdFormCode(loginEntity
                                .getChannelObject()
                                .getString("code")));
            } else if (loginEntity.getChannelType() == UserLoginChannelType.application) {
                jsonObject = this.loginWeChatChannelApplication(loginEntity
                                .getChannelObject()
                                .getString("code"), loginEntity.getChannel().name(),
                        loginEntity.getChannelType().name());
                if (Optional.ofNullable(jsonObject).isEmpty()) {
                    log.info("第二次: {}", jsonObject);
                    jsonObject = JSONObject.parseObject(weiXinUtil
                            .getUserByAppOpenId(loginEntity
                                    .getChannelObject()
                                    .getString("code")));
                    this.loginSetWeChatChannelApplication(loginEntity
                                    .getChannelObject()
                                    .getString("code"), loginEntity.getChannel().name(),
                            loginEntity.getChannelType().name(), jsonObject);
                }

            } else {
                log.warn("还不支持");
                throw new LoginException("api.user.login.third-channel-not-found");
            }
            log.info("WeChatUtil response:{}", jsonObject);
            SomytripUserChannel somytripUserChannel = somytripUserChannelMapper.getChannelByOpenIdOrUnionId(
                    loginEntity.getChannel().name(),
                    loginEntity.getChannelType().name(),
                    jsonObject.getString("openid"),
                    jsonObject.getString("unionid")
            );
            log.info("查询到渠道绑定的情况: {}", somytripUserChannel);
            if (Optional.ofNullable(somytripUserChannel).isPresent()) {
                log.info("已经注册绑定");
                // 已经注册绑定
                QueryWrapper<SomytripUsers> usersQueryWrapper = new QueryWrapper<>();
                usersQueryWrapper.eq("id", somytripUserChannel.getUserId());
                usersQueryWrapper.eq("unique_id", somytripUserChannel.getUserUniqueId());
                usersQueryWrapper.eq("is_del", false);
                somytripUsers = somytripUsersMapper.selectOne(usersQueryWrapper);
                log.info("已经注册绑定-1:{}", somytripUsers);
                if (Optional.ofNullable(somytripUsers).isPresent()) {
                    return somytripUsers;
                }
                // 用户不存在
                if (loginEntity.checkMobile()) {
                    throw new LoginException(307, "api.user.login.user-mobile-irregularity");
                }
                log.info("校验手机号通过");
                somytripUsers = login(loginEntity.getMobile());
                log.info("已经注册绑定-2:{}", somytripUsers);
                if (Optional.ofNullable(somytripUsers).isPresent()) {
                    somytripUserChannel.setUserId(somytripUsers.getId());
                    somytripUserChannel.setUserUniqueId(somytripUsers.getUniqueId());
                    somytripUserChannel.setUpdateTime(new Date());
                    if (somytripUserChannelMapper.updateById(somytripUserChannel) > 0) {
                        log.info("修改渠道成功: {}", somytripUserChannel);
                        return somytripUsers;
                    } else {
                        log.warn("修改渠道未成功: {}", somytripUserChannel);
                        log.warn("修改渠道未成功: {}", somytripUsers);
                        throw new LoginException(500, "api.user.login.third-channel-update");
                    }
                }
                Date date = new Date();
                somytripUsers = new SomytripUsers();
                somytripUsers.setUniqueId(smtId());
                somytripUsers.setMobileEntity(loginEntity.getMobile());
                somytripUsers.setCreateTime(date);
                somytripUsers.setUpdateTime(date);
                if (this.register(somytripUsers) > 0) {
                    somytripUsers = somytripUsersMapper.selectById(somytripUsers.getId());
                    somytripUserChannel.setUserId(somytripUsers.getId());
                    somytripUserChannel.setUserUniqueId(somytripUsers.getUniqueId());
                    somytripUserChannel.setUpdateTime(date);
                    somytripUserChannelMapper.updateById(somytripUserChannel);
                    return somytripUsers;
                } else {
                    log.warn("绑定第三方失败 register:{}", loginEntity);
                    throw new LoginException("api.user.login.third-channel-bind-error");
                }
            } else {
                log.info("未注册绑定");
                if (loginEntity.checkMobile()) {
                    throw new LoginException(307, "api.user.login.user-mobile-irregularity");
                }
                if (loginEntity.getChannelType() == UserLoginChannelType.application) {
                    String key = String.format(ConstantUtil.Sms.BASE_CODE_REDIS_KEY,
                            ConstantUtil.Sms.TYPE_MAP.get("1")) + loginEntity.getMobile().getMobile();
                    if (loginEntity.checkVerificationCode(redisService.getString(key))) {
                        throw new LoginException(400, "api.user.login.verification-code-error");
                    }
                    redisService.del(key);
                }
                Date date = new Date();
                somytripUserChannel = new SomytripUserChannel();
                somytripUserChannel.setChannelUserInfo(jsonObject);
                somytripUserChannel.setChannel(loginEntity.getChannel());
                somytripUserChannel.setChannelType(loginEntity.getChannelType());
                somytripUserChannel.setCreateTime(date);
                somytripUserChannel.setUpdateTime(date);
                somytripUserChannel.setChannelId(jsonObject.getString("openid"));
                somytripUserChannel.setChannelUniqueId(jsonObject.getString("unionid"));
                log.info("init somytrip user channel:{}", JSON.toJSONString(somytripUserChannel));
                somytripUsers = login(loginEntity.getMobile());
                if (somytripUsers == null) {
                    somytripUsers = new SomytripUsers();
                    somytripUsers.setUniqueId(smtId());
                    somytripUsers.setUpdateTime(date);
                    somytripUsers.setCreateTime(date);
                    if (StrUtil.isNotBlank(jsonObject.getString("nickname"))) {
                        somytripUsers.setNickname(jsonObject.getString("nickname"));
                    }
                    if (StrUtil.isNotBlank(jsonObject.getString("headimgurl"))) {
                        somytripUsers.setAuthImage(uploadWeChatApplicationAuthImage(jsonObject
                                .getString("headimgurl")));
                    }

                    somytripUsers.setMobileEntity(loginEntity.getMobile());
                    if (this.register(somytripUsers) <= 0) {
                        log.warn("注册+绑定第三方失败 register:{}", loginEntity);
                        throw new LoginException("api.user.login.third-channel-bind-error");
                    }
                    somytripUsers = somytripUsersMapper.selectById(somytripUsers.getId());
                }
                somytripUserChannel.setUserId(somytripUsers.getId());
                somytripUserChannel.setUserUniqueId(somytripUsers.getUniqueId());
                if (register(somytripUserChannel) > 0) {
                    return somytripUsers;
                } else {
                    log.warn("注册+绑定第三方失败 channel:{}", loginEntity);
                    throw new LoginException(302, "api.user.login.third-channel-bind-error");
                }
            }
        } catch (Exception e) {
            if (e instanceof LoginException) {
                log.error("LoginException loginWeChat error: {}", loginEntity, e);
                throw e;
            }
            log.error("otherException loginWeChat error: {}", loginEntity, e);
            throw new LoginException("api.user.login.third-channel-bind-error");
        }
    }

    /**
     * 格式化用户数据
     *
     * @param somytripUsers 用户数据
     * @param ip            ip
     * @return 数据
     */
    private Map<String, Object> formatLoginResult(SomytripUsers somytripUsers, String loginChannelType, String ip) {
        Map<String, Object> map = new HashMap<>(8);
        somytripUsers.setAuthImage(tencentCosService.getAuthImage(somytripUsers.getAuthImage()));
        UserVo userVo = new UserVo();
        userVo.setUserVo(somytripUsers);
        map.put("user", userVo);
        map.put("ip", ip);
        try {
            String userSecretId = AesUtil.encrypt(somytripUsers.getId() + "", userIdSecretKey);
            map.put("userSecretId", userSecretId);
        } catch (Exception e) {
            log.error("userId encrypt error :{}", e.getMessage(), e);
        }
        map.put("token", loginService.login(StrUtil.str(somytripUsers.getId(), StandardCharsets.UTF_8),
                AppTypeEnum.getAppTypeEunm(loginChannelType).name()));
        log.info("格式化后的登陆数据: {}", map);
        return map;
    }

    @Override
    public LoginResultVo formatLoginResult(String uid, String loginMethod, String ip) {
        LoginResultVo resultVo = new LoginResultVo();
        SomytripUsers somytripUsers = somytripUsersMapper.selectById(uid);
        if (somytripUsers == null || somytripUsers.isDel()) {
            throw new BusinessException(306, "not found user");
        }
        long between = DateUtil.between(somytripUsers.getCreateTime(), DateUtil.date(), DateUnit.MINUTE);
        if (between < 1) {
            resultVo.setShowFirstLogin(true);
        }
        somytripUsers.setAuthImage(tencentCosService.getAuthImage(somytripUsers.getAuthImage()));
        UserVo userVo = new UserVo();
        if (StrUtil.isNotBlank(somytripUsers.getMobile())) {
            somytripUsers.setMobile(SqlAesUtil.decrypt(somytripUsers.getMobile()));
        }
        userVo.setUserVo(somytripUsers);
        if (somytripUsers.getEmailForeignId() != null && somytripUsers.getEmailForeignId() > 0) {
            userVo.setEmail(StrUtil.utf8Str(somytripUsers.getEmailForeignId()));
        }
        resultVo.setUser(userVo);
        resultVo.setIp(ip);
        try {
            resultVo.setUserSecretId(AesUtil.encrypt(uid, userIdSecretKey));
        } catch (Exception e) {
            log.error("userId encrypt error :{}", e.getMessage(), e);
        }
        resultVo.setToken(loginService.login(uid, loginMethod));
        log.info("formatLoginResult: {}", resultVo);
        return resultVo;
    }

    @Override
    public Map<String, Boolean> bindingOauth2Map(String uid) {
        LambdaQueryWrapper<SomytripUserChannel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SomytripUserChannel::getUserId, uid);
        List<SomytripUserChannel> somytripUserChannels = somytripUserChannelMapper.selectList(queryWrapper);
        Map<String, Boolean> result = new HashMap<>(somytripUserChannels.size());
        somytripUserChannels.forEach(channel -> {
            if (channel.getChannel() == UserLoginChannel.apple
                    && channel.getChannelType() == UserLoginChannelType.application) {
                result.put(LoginMethodEnum.APPLE_APPLICATION.getLow(), true);
            } else if (channel.getChannel() == UserLoginChannel.google
                    && channel.getChannelType() == UserLoginChannelType.application) {
                result.put(LoginMethodEnum.GOOGLE_APPLICATION.getLow(), true);
            } else if (channel.getChannel() == UserLoginChannel.wechat
                    && channel.getChannelType() == UserLoginChannelType.application) {
                result.put(LoginMethodEnum.WX_APPLICATION.getLow(), true);
            } else if (channel.getChannel() == UserLoginChannel.wechat
                    && channel.getChannelType() == UserLoginChannelType.applet) {
                result.put(LoginMethodEnum.WX_APPLET.getLow(), true);
            }
        });
        return result;
    }

    private SomytripUsers login(SomytripUsers.Mobile mobile) {
        QueryWrapper<SomytripUsers> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_del", false);
        queryWrapper.eq("mobile", SqlAesUtil.encrypt(mobile.toMobile()));
        try {
            List<SomytripUsers> somytripUsersList = somytripUsersMapper.selectList(queryWrapper);
            if (somytripUsersList.isEmpty()) {
                return null;
            }
            SomytripUsers somytripUsers = somytripUsersList.get(somytripUsersList.size() - 1);
            if (Optional.ofNullable(somytripUsers).isPresent()) {
                return somytripUsers;
            }
        } catch (Exception e) {
            log.warn("login error", e);
            if (e instanceof LoginException) {
                throw e;
            }
            throw new LoginException("api.user.login.error");
        }
        return null;
    }

    private String smtId() {
        return "smt" + idGenConfig.getUserSnowflake().nextIdStr();
    }

    /**
     * 苹果渠道登陆
     *
     * @param loginEntity 登陆数据
     * @return 登陆用户
     */
    private SomytripUsers loginAppleChannel(SomytripUsers.LoginEntity loginEntity) {
        String token = loginEntity.getChannelObject().getString("token");
        if (!(loginEntity.getChannel() == UserLoginChannel.apple)) {
            throw new LoginException(400, "api.user.login.params-error-channel-apple");
        }
        if (!(loginEntity.getChannelType() == UserLoginChannelType.application)) {
            throw new LoginException(400, "api.user.login.params-error-channel-type-apple");
        }
        JSONObject jsonObject = appleRequestService.getPublicKey();
        log.info("apple token parse: {}", jsonObject);
        JWT jwt = CommonUtils.verifyAppleToken(token, jsonObject);
        if (!jwt.verify()) {
            throw new LoginException(401, "api.user.login.params-error-apple-token-empty");
        }
        log.info("{}, {}", jwt.getHeaders(), jwt.getPayloads());
        SomytripUserChannel somytripUserChannel = somytripUserChannelMapper.getChannelByOpenIdOrUnionId(
                loginEntity.getChannel().name(),
                loginEntity.getChannelType().name(),
                StrUtil.str(jwt.getPayload("email"), StandardCharsets.UTF_8),
                StrUtil.str(jwt.getPayload("sub"), StandardCharsets.UTF_8)
        );
        SomytripUsers somytripUsers = null;
        if (Optional.ofNullable(somytripUserChannel).isPresent()) {
            log.info("已经注册绑定");
            // 已经注册绑定
            QueryWrapper<SomytripUsers> usersQueryWrapper = new QueryWrapper<>();
            usersQueryWrapper.eq("id", somytripUserChannel.getUserId());
            usersQueryWrapper.eq("unique_id", somytripUserChannel.getUserUniqueId());
            usersQueryWrapper.eq("is_del", false);
            somytripUsers = somytripUsersMapper.selectOne(usersQueryWrapper);
            log.info("已经注册绑定-1:{}", somytripUsers);
            // 用户存在
            if (Optional.ofNullable(somytripUsers).isPresent()) {
                return somytripUsers;
            }
            // 用户不存在
            if (loginEntity.checkMobile()) {
                throw new LoginException(307, "api.user.login.user-mobile-irregularity");
            }
            somytripUsers = login(loginEntity.getMobile());
            log.info("已经注册绑定-2:{}", somytripUsers);
            if (Optional.ofNullable(somytripUsers).isPresent()) {
                somytripUserChannel.setUserId(somytripUsers.getId());
                somytripUserChannel.setUserUniqueId(somytripUsers.getUniqueId());
                somytripUserChannel.setUpdateTime(new Date());
                somytripUserChannelMapper.updateById(somytripUserChannel);
                return somytripUsers;
            }
            Date date = new Date();
            somytripUsers = new SomytripUsers();
            somytripUsers.setUniqueId(smtId());
            somytripUsers.setMobileEntity(loginEntity.getMobile());
            somytripUsers.setCreateTime(date);
            somytripUsers.setUpdateTime(date);
            if (this.register(somytripUsers) > 0) {
                somytripUserChannel.setUserId(somytripUsers.getId());
                somytripUserChannel.setUserUniqueId(somytripUsers.getUniqueId());
                somytripUserChannel.setUpdateTime(date);
                somytripUserChannelMapper.updateById(somytripUserChannel);
                somytripUsers = somytripUsersMapper.selectById(somytripUsers.getId());
                return somytripUsers;
            } else {
                log.warn("绑定第三方失败 register:{}", loginEntity);
                throw new LoginException("api.user.login.third-channel-bind-error");
            }
        } else {
            log.info("未注册绑定");
            if (loginEntity.checkMobile()) {
                throw new LoginException(307, "api.user.login.user-mobile-irregularity");
            }
            if (loginEntity.getChannelType() == UserLoginChannelType.application) {
                String key = String.format(ConstantUtil.Sms.BASE_CODE_REDIS_KEY,
                        ConstantUtil.Sms.TYPE_MAP.get("1")) + loginEntity.getMobile().getMobile();
                if (loginEntity.checkVerificationCode(redisService.getString(key))) {
                    throw new LoginException(400, "api.user.login.verification-code-error");
                }
                redisService.del(key);
            }
            Date date = new Date();
            somytripUserChannel = new SomytripUserChannel();
            somytripUserChannel.setChannelUserInfo(jsonObject);
            somytripUserChannel.setChannel(loginEntity.getChannel());
            somytripUserChannel.setChannelType(loginEntity.getChannelType());
            somytripUserChannel.setCreateTime(date);
            somytripUserChannel.setUpdateTime(date);
            somytripUserChannel.setChannelId(jsonObject.getString("email"));
            somytripUserChannel.setChannelUniqueId(jsonObject.getString("sub"));

            somytripUsers = login(loginEntity.getMobile());
            if (Optional.ofNullable(somytripUsers).isEmpty()) {
                somytripUsers = new SomytripUsers();
                somytripUsers.setUniqueId(smtId());
                somytripUsers.setUpdateTime(date);
                somytripUsers.setCreateTime(date);

                somytripUsers.setMobileEntity(loginEntity.getMobile());
                if (this.register(somytripUsers) <= 0) {
                    log.warn("注册+绑定第三方失败 register:{}", loginEntity);
                    throw new LoginException("api.user.login.third-channel-bind-error");
                }
                somytripUsers = somytripUsersMapper.selectById(somytripUsers.getId());
            }
            somytripUserChannel.setUserId(somytripUsers.getId());
            somytripUserChannel.setUserUniqueId(somytripUsers.getUniqueId());
            if (register(somytripUserChannel) > 0) {
                return somytripUsers;
            } else {
                log.warn("注册+绑定第三方失败 channel:{}", loginEntity);
                throw new LoginException(302, "api.user.login.third-channel-bind-error");
            }
        }
    }

    private int register(SomytripUserChannel somytripUserChannel) {
        log.info("channel register:{}", JSON.toJSONString(somytripUserChannel));
        return somytripUserChannelMapper.insert(somytripUserChannel);
    }

    private String uploadWeChatApplicationAuthImage(String url) {
        try {
            if (StrUtil.isNotBlank(url)) {
                String imageName = idGenConfig.getImageSnowFlake().nextIdStr() + ".jpg";
                tencentCosService.uploadFile("user", imageName, url);
                return imageName;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.warn("error uploadImage url: {}", url);
        }
        return "";
    }

    /**
     * 设置微信app登陆code缓存，保证过期时间内code不需要重复获取
     *
     * @param code        value
     * @param channel     渠道
     * @param channelType 渠道来源
     * @param jsonObject  数据
     */
    private void loginSetWeChatChannelApplication(String code, String channel,
                                                  String channelType, JSONObject jsonObject
    ) {
        String key = getLoginUserInfoRedisKey(channel, channelType) + ":" + code;
        redisService.setString(key, jsonObject.toJSONString(), 24 * 60 * 60 * 30);
    }

    /**
     * 获取登陆信息的 redis md5Key
     *
     * @param channel     wechat 渠道
     * @param channelType applet 来源
     * @return md5Key
     */
    private String getLoginUserInfoRedisKey(String channel, String channelType) {
        return "login:" + channel + "_" + channelType + "_userInfo";
    }

    /**
     * 微信app登陆
     *
     * @param code        code
     * @param channel     渠道
     * @param channelType 来源
     * @return 数据
     */
    private JSONObject loginWeChatChannelApplication(String code, String channel, String channelType) {
        String key = getLoginUserInfoRedisKey(channel, channelType) + ":" + code;
        String value = redisService.getString(key);
        if (StrUtil.isNotBlank(value) && value.contains("unionid")) {
            log.info("md5Key:{}, is_Expire", key);
            return JSONObject.parseObject(value);
        }
        log.info("md5Key:{}, is_Not_Expire", key);
        return null;
    }
}
