package com.somytrip.consumer.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.feedback.FeedbackCommentService;
import com.somytrip.consumer.mapper.FeedbackCommentMapper;
import com.somytrip.entity.community.FeedbackComment;
import com.somytrip.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.time.LocalDateTime;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.service.impl
 * @className: FeedbackCommentServiceImpl
 * @author: lijunqi
 * @description: 反馈评论服务实现类
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
@Slf4j
@DubboService
public class FeedbackCommentServiceImpl extends ServiceImpl<FeedbackCommentMapper, FeedbackComment> 
        implements FeedbackCommentService {

    /**
     * 添加评论
     *
     * @param feedbackId 反馈ID
     * @param userId     用户ID
     * @param userType   用户类型
     * @param content    评论内容
     * @param userIp     用户IP
     * @return 是否成功
     */
    @Override
    public Boolean addComment(Integer feedbackId, String userId, Integer userType, String content, String userIp) {
        
        if (feedbackId == null || feedbackId <= 0) {
            throw new BusinessException("反馈ID不能为空");
        }
        if (StrUtil.isBlank(userId)) {
            throw new BusinessException("用户ID不能为空");
        }
        if (StrUtil.isBlank(content)) {
            throw new BusinessException("评论内容不能为空");
        }
        if (content.length() > 300) {
            throw new BusinessException("评论内容不能超过300个字符");
        }

        FeedbackComment comment = new FeedbackComment();
        comment.setFeedbackId(feedbackId);
        comment.setUserId(userId);
        comment.setUserType(userType != null ? userType : 3); // 默认为普通用户
        comment.setContent(content);
        comment.setIp(userIp);
        comment.setCreateTime(LocalDateTime.now());
        comment.setUpdateTime(LocalDateTime.now());
        comment.setDelFlag(0);

        boolean saveResult = this.save(comment);
        if (saveResult) {
            log.info("用户 {} 对反馈 {} 添加评论成功", userId, feedbackId);
        }
        
        return saveResult;
    }

    /**
     * 回复评论
     *
     * @param feedbackId      反馈ID
     * @param userId          用户ID
     * @param userType        用户类型
     * @param content         评论内容
     * @param parentId        父评论ID
     * @param replyToUserId   被回复用户ID
     * @param replyToUserType 被回复用户类型
     * @param userIp          用户IP
     * @return 是否成功
     */
    @Override
    public Boolean replyComment(Integer feedbackId, String userId, Integer userType, String content,
                               Integer parentId, String replyToUserId, Integer replyToUserType, String userIp) {
        
        if (feedbackId == null || feedbackId <= 0) {
            throw new BusinessException("反馈ID不能为空");
        }
        if (StrUtil.isBlank(userId)) {
            throw new BusinessException("用户ID不能为空");
        }
        if (StrUtil.isBlank(content)) {
            throw new BusinessException("回复内容不能为空");
        }
        if (content.length() > 300) {
            throw new BusinessException("回复内容不能超过300个字符");
        }
        if (parentId == null || parentId <= 0) {
            throw new BusinessException("父评论ID不能为空");
        }

        FeedbackComment comment = new FeedbackComment();
        comment.setFeedbackId(feedbackId);
        comment.setUserId(userId);
        comment.setUserType(userType != null ? userType : 3); // 默认为普通用户
        comment.setContent(content);
        comment.setIp(userIp);
        comment.setCreateTime(LocalDateTime.now());
        comment.setUpdateTime(LocalDateTime.now());
        comment.setParentId(parentId);
        comment.setReplyToUserId(replyToUserId);
        comment.setReplyToUserType(replyToUserType);
        comment.setDelFlag(0);

        boolean saveResult = this.save(comment);
        if (saveResult) {
            log.info("用户 {} 回复评论 {} 成功", userId, parentId);
        }
        
        return saveResult;
    }
}
