package com.somytrip.consumer.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.itinerary.ItineraryOperationLogService;
import com.somytrip.consumer.mapper.ItineraryOperationLogMapper;
import com.somytrip.consumer.service.RedisService;
import com.somytrip.entity.enums.RedisKey;
import com.somytrip.entity.itinerary.ItineraryOperationLogDto;
import com.somytrip.entity.itinerary.ItineraryOperationLogEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.service.impl
 * @className: ItineraryOperationLogServiceImpl
 * @author: shadow
 * @description: 攻略操作日志Service实现类
 * @date: 2024/9/24 16:41
 * @version: 1.0
 */
@Slf4j
@Service
public class ItineraryOperationLogServiceImpl
        extends ServiceImpl<ItineraryOperationLogMapper, ItineraryOperationLogEntity>
        implements ItineraryOperationLogService {

    @Resource
    private RedisService redisService;

    /**
     * 记录操作日志
     *
     * @param dto 参数
     * <AUTHOR>
     * @date 2024/9/24 16:50
     */
    @Override
    public void log(ItineraryOperationLogDto dto) {

        if (dto == null) return;

        try {
//            // 缓存
//            RedisKey itineraryOperationLogCurrent = RedisKey.ITINERARY_OPERATION_LOG_CURRENT;
//            String redisKey = itineraryOperationLogCurrent.getKey();
//            long timeout = itineraryOperationLogCurrent.getTimeout();
//            redisService.lSet(redisKey, dto, timeout);

            // 持久化
            baseMapper.insert(new ItineraryOperationLogEntity(dto));
        } catch (Exception e) {
            log.error("Itinerary Operation Log Error, dto: {}", JSON.toJSONString(dto));
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 持久化操作日志
     *
     * <AUTHOR>
     * @date 2024/9/24 17:25
     */
    @Override
    public void persistLogs() {

        RedisKey itineraryOperationLogCurrent = RedisKey.ITINERARY_OPERATION_LOG_CURRENT;
        RedisKey itineraryOperationLogPending = RedisKey.ITINERARY_OPERATION_LOG_PENDING;

        String curRedisKey = itineraryOperationLogCurrent.getKey();
        String pendingRedisKey = itineraryOperationLogPending.getKey();

        // 重命名
        redisService.rename(curRedisKey, pendingRedisKey);

        // 持久化
        List<Object> redisData = redisService.lGet(pendingRedisKey);
        if (CollUtil.isEmpty(redisData)) return;

        List<ItineraryOperationLogEntity> list = redisData.stream()
                .map(item -> JSONObject.from(item).toJavaObject(ItineraryOperationLogDto.class))
                .map(ItineraryOperationLogEntity::new)
                .toList();

        for (ItineraryOperationLogEntity entity : list) {
            baseMapper.insert(entity);
        }

        // 删除缓存
        redisService.del(pendingRedisKey);
    }
}
