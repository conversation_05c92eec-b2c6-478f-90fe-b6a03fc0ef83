package com.somytrip.consumer.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.somytrip.api.service.tourism.ScenicV2Service;
import com.somytrip.consumer.mapper.PlanningMapper;
import com.somytrip.consumer.mapper.ScenicV2Mapper;
import com.somytrip.consumer.util.DistanceUtil;
import com.somytrip.consumer.util.RedisStringUtil;
import com.somytrip.entity.tourism.QueryTourismParam;
import com.somytrip.entity.vo.ScenicVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * @ClassName: ScenicV2ServiceImpl
 * @Description:
 * @Author: shadow
 * @Date: 2023/10/10 17:09
 */
@Deprecated
@Slf4j
@Service
public class ScenicV2ServiceImpl implements ScenicV2Service {

    @Resource
    private ScenicV2Mapper scenicV2Mapper;
    @Resource
    private PlanningMapper planningMapper;
    @Resource
    private RedisStringUtil redisStringUtil;

    /**
     * 转换游玩主题
     */
    private static List<String> getRealTheme(String appThemeStr) {
        HashMap<String, List<String>> themeMap = new HashMap<>(16);
        themeMap.put("主题公园", Collections.singletonList("现代娱乐类"));
        themeMap.put("人文历史", Arrays.asList("人文景观类", "博物馆类"));
        themeMap.put("自然野趣", Collections.singletonList("乡村田园类"));
        themeMap.put("美食购物", Arrays.asList("现代娱乐类", "美食购物类"));
        themeMap.put("儿童娱乐", Collections.singletonList("现代娱乐类"));
        themeMap.put("免费景区", Collections.singletonList("免费景区类"));

        return themeMap.getOrDefault(appThemeStr,
                new ArrayList<>(Collections.singletonList(appThemeStr))
        );
    }

    /**
     * 分析游玩主题
     */
    private static List<String> analysisTheme(JSONArray themes) {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < themes.size(); ++i) {
            String item = themes.getString(i);
            if (StrUtil.isNotBlank(item)) {
                List<String> realTheme = getRealTheme(item);
                for (String theme : realTheme) {
                    if (!list.contains(theme)) {
                        list.add(theme);
                    }
                }
            }
        }
        return list;
    }

    /**
     * 分析年龄
     */
    private static List<String> analysisAgeCls(String ageCls) {
        if (StrUtil.isEmpty(ageCls)) {
            return new ArrayList<>();
        }
        return switch (ageCls) {
            case "18岁以下", "0-18" ->
                    Arrays.asList("现代娱乐类", "乡村田园类", "人文景观类", "博物馆类", "美食购物类", "免费景区类");
//            case "18-35岁":
//            case "18-35":
//            case "35-60岁":
//            case "35-60":
//                return new String[]{};
            case "60岁以上", "60+" -> Arrays.asList("人文景观类", "博物馆类", "乡村田园类");
            default -> new ArrayList<>();
        };
    }

    /**
     * 分析游客组合
     */
    private static List<String> analysisVisitors(JSONObject visitMap) {

        List<String> scenicClasses;
        String isOld = visitMap.getString("isOld");
        Integer child = visitMap.getInteger("childCount");

        if ("1".equals(isOld)) {
            scenicClasses = Arrays.asList("人文景观类", "博物馆类", "乡村田园类", "免费景区类");
        } else if (child > 0) {
            scenicClasses = Arrays.asList("现代娱乐类", "乡村田园类", "人文景观类", "博物馆类", "美食购物类", "免费景区类");
        } else {
            scenicClasses = new ArrayList<>();
        }
        return scenicClasses;
    }

    /**
     * 合并两个景区列表
     */
    private void addScenic_removeRepeat(List<Map<String, Object>> list1, List<Map<String, Object>> list2) {
        Set<String> names = new HashSet<>();
        // 遍历list1并记录景区名称
        for (Map<String, Object> sce1 : list1) {
            names.add(sce1.get("name").toString());
        }

        // 遍历list2，如果景区名称不在names集合中，则添加到list1中
        for (Map<String, Object> sce2 : list2) {
            if (!names.contains(sce2.get("name").toString())) {
                list1.add(sce2);
            }
        }

    }

    /**
     * 获取酒店与景区的距离
     */
    private double getDistance(Map<String, Object> hotel, Map<String, Object> scenic) {

        String hotelLng = hotel.get(DistanceUtil.getLatLngName(hotel).get("lngName")).toString();
        String hotelLat = hotel.get(DistanceUtil.getLatLngName(hotel).get("latName")).toString();

        String scenicLng = scenic.get(DistanceUtil.getLatLngName(scenic).get("lngName")).toString();
        String scenicLat = scenic.get(DistanceUtil.getLatLngName(scenic).get("latName")).toString();
//        if ("深圳".equals(city)) {
//            StringBuilder field = new StringBuilder();
//            field.append(hotel.get("id").toString()).append("_").append(2).append("_").append(scenic.get("id").toString()).append("_").append(1);
//            // 在查询距离时，先查询redis的distance_query，若有则直接取出，没有添加到distance_add中
//            Object distanceTemp = this.redisStringUtil.hget("distance_query", field.toString());
//            if (distanceTemp != null) {
//                return Double.parseDouble(distanceTemp.toString());
//            } else {
//                redisStringUtil.hset("distance_add", field.toString(), hotelLng + "," + hotelLat + "_" + scenicLng + "," + scenicLat);
//            }
//        }
        return DistanceUtil.getDistance(
                Double.parseDouble(hotelLng),
                Double.parseDouble(hotelLat),
                Double.parseDouble(scenicLng),
                Double.parseDouble(scenicLat)
        );
    }

//    /**
//     * 分析获取景区类别列表
//     *
//     * @param param 查询攻略参数
//     * @return Item<String> 景区类别列表
//     */
//    private Item<String> getScenicClasses(QueryTourismParam param) {
//
//        Item<String> scenicClasses = new ArrayList<>();
//        // 优先判断游玩主题，次要年龄，次要游客组合
//        scenicClasses = analysisTheme(JSONArray.from(param.getTheme()));
//        if (scenicClasses.isEmpty()) {
//            scenicClasses = analysisAgeCls(param.getAgeCls());
//        }
//        if (scenicClasses.isEmpty()) {
//            scenicClasses = analysisVisitors(param.getVisitMap());
//        }
//        return scenicClasses;
//    }

    /**
     * 分析景区价格
     */
    @Override
    public BigDecimal analysisScenicPrice(Map<String, Object> visitMap, List<Map<String, Object>> scenicList) {

        BigDecimal totalPrice = new BigDecimal(0);
        for (Map<String, Object> scenic : scenicList) {
            BigDecimal childPrice = new BigDecimal(0);
            BigDecimal bigPrice = (scenic.get("bigman_price") == null ? new BigDecimal(0)
                    : new BigDecimal(scenic.get("bigman_price").toString()));
            if (bigPrice.intValue() > 0) {
                childPrice = (scenic.get("childman_price") == null ? new BigDecimal(0)
                        : new BigDecimal(scenic.get("childman_price").toString()));
                if (childPrice.intValue() == 0) {
                    childPrice = bigPrice; // 如果儿童票为0，则将成人票价作为儿童票
                }
            }

            // 每个景点的总人数票价
            BigDecimal scenicPrice = new BigDecimal(visitMap.get("adultCount").toString()).multiply(bigPrice)
                    .add(new BigDecimal(visitMap.get("childCount").toString()).multiply(childPrice));
            totalPrice = totalPrice.add(scenicPrice);
        }
        return totalPrice;
    }

//    /**
//     * 查询好玩指数最高的几个景区
//     */
//    @Override
//    public Item<ScenicVo> selectTopThiScenicCombined(QueryTourismParam param) {
//
//        Set<ScenicVo> tmpSet = new HashSet<>();
//        Item<ScenicVo> topThiScenicList;
//
//        // 必去景区列表
//        JSONArray favScenicList = param.getFavScenicList();
//        // 目的城市id列表
//        JSONArray cityIds = param.getDestinationList();
//        // 天数
//        Integer days = param.getDays();
//
//        // 存在必去景区数据
//        if (favScenicList != null && !favScenicList.isEmpty()) {
//            tmpSet.addAll(scenicV2Mapper.selectTopThiScenic(cityIds, new ArrayList<>(), favScenicList, -1));
//        }
//
//        // 获取景区类别列表
//        Item<String> scenicClasses = getScenicClasses(param);
//        // 天数减去已有列表长度
//        days = days - tmpSet.size();
//
//        // FIXME: 多城市时 可能出现有的城市无数据情况
//
//        // 查询景区
//        tmpSet.addAll(scenicV2Mapper.selectTopThiScenic(cityIds, scenicClasses, new JSONArray(), days * 5));
//
//        // 景区数量不足，不考虑景区类别参数
//        if (tmpSet.size() < days) {
//            tmpSet.addAll(scenicV2Mapper.selectTopThiScenic(cityIds, new ArrayList<>(), new JSONArray(), days * 5));
//        }
//
////        if (tmpSet.size() > days) {
////        }
//        // 打乱景区顺序
//        Item<ScenicVo> tmpList = new ArrayList<>(tmpSet);
//        Collections.shuffle(tmpList);
//        // 取days个景区
//        topThiScenicList = tmpList.subList(0, days);
//        // 按照城市排序
//        topThiScenicList.sort(Comparator.comparing(ScenicVo::getCity));
////        log.info("topThiScenicList: {}", topThiScenicList);
//
//        return topThiScenicList;
//    }
//
//    /**
//     * 景区按定位排序
//     */
//    @Override
//    public Item<ScenicVo> orderTopThiScenicList(Item<ScenicVo> scenicList) {
//
//        if (scenicList.size() < 2) {
//            return scenicList;
//        }
//
//        ScenicVo firstScenic = new ScenicVo();
//        ScenicVo secondScenic = new ScenicVo();
//        double maxDistance = 0.0;
//        Long tmpCityId = scenicList.get(0).getCityId();
////        log.info("tmpCityId: {}", tmpCityId);
//
//        for (ScenicVo currentScenic : scenicList) {
//            if (!Objects.equals(tmpCityId, currentScenic.getCityId())) {
//                break;
//            }
//
//            // 读取坐标
//            String currentLng = currentScenic.getLng();
//            String currentLat = currentScenic.getLat();
//
//            for (ScenicVo otherScenic : scenicList) {
//                if (!Objects.equals(tmpCityId, otherScenic.getCityId())) {
//                    break;
//                }
//                if (currentScenic == otherScenic) {
//                    continue;
//                }
//
//                String otherLng = otherScenic.getLng();
//                String otherLat = otherScenic.getLat();
//
//                double lngDiff = Double.parseDouble(otherLng) - Double.parseDouble(currentLng);
//                double latDiff = Double.parseDouble(otherLat) - Double.parseDouble(currentLat);
//                double distance = Math.sqrt(lngDiff * lngDiff + latDiff * latDiff);
//
//                if (distance > maxDistance) {
//                    maxDistance = distance;
//                    firstScenic = currentScenic;
//                    secondScenic = otherScenic;
//                }
//            }
//        }
//
//        BigDecimal firstThiIndex = firstScenic.getThiIndex();
//        BigDecimal secondThiIndex = secondScenic.getThiIndex();
//
//        if (ObjectUtils.isNotNull(firstThiIndex, secondThiIndex)) {
//            if (firstThiIndex.compareTo(secondThiIndex) < 0) {
//                firstScenic = secondScenic;
//            }
//
//            int firstIndex = scenicList.indexOf(firstScenic);
//            scenicList.set(firstIndex, scenicList.get(0));
//            scenicList.set(0, firstScenic);
//        }
//
//        return scenicList;
//    }

    /**
     * 按照酒店与景区的距离排序
     */
    @Override
    public List<Map<String, Object>> orderTopThiScenicListByHotel(Map<String, Object> hotel,
                                                                  List<Map<String, Object>> scenicList) {

        if (hotel == null) {
            return scenicList;
        }

        Map<String, Object> tempMap = scenicList.get(0);
        double tempDct = getDistance(hotel, tempMap);
        int index = 0;

        for (int i = 1; i < scenicList.size(); i++) {
            Map<String, Object> sce = scenicList.get(i);
            double distance = getDistance(hotel, sce);

            if (distance < tempDct) {
                tempDct = distance;
                tempMap = sce;
                index = i;
            }
        }

        scenicList.set(index, scenicList.get(0));
        scenicList.set(0, tempMap);
        return scenicList;
    }

    /**
     * 查询距离指定景区最近的10个景区
     */
    @Override
    public List<ScenicVo> queryNearByScenic(QueryTourismParam param,
                                            int levelValue,
                                            String cityId,
                                            ScenicVo dayScenic,
                                            List<String> selectedScenicIdList) {


        List<ScenicVo> scenicList = new ArrayList<>();
        int journey = param.getJourney();

        // 查询距离最近的10个景区
        Set<ScenicVo> tmpList = scenicV2Mapper.queryScenicByXYZ(dayScenic.getLat(), dayScenic.getLng(), cityId, levelValue);
//        if (levelValue == 1) {
////            nightList = scenicV2Mapper.queryNightScenicByXYZ_cheap2(lat, lng);
//        } else {
////            nightList = scenicV2Mapper.queryNightScenicByXYZ2(lat, lng);
//        }

        String scenicId = String.valueOf(dayScenic.getId());
        double playTime = ObjectUtils.isNotNull(dayScenic.getPlayTime())
                ? Double.parseDouble(String.valueOf(dayScenic.getPlayTime()))
                : 2.5;

        // 遍历查询到的景区列表筛选符合条件的景区
        for (ScenicVo tmp : tmpList) {
//            String tmpSceId = tmp.get("id").toString();
            String tmpScenicId = String.valueOf(tmp.getId());
            // 若已累积游玩时间超过（行程安排-1），则退出
            if (playTime > journey - 1) {
                break;
            }
            // 过滤传进来的景区
            if (scenicId.equals(tmpScenicId)) {
                continue;
            }
            // 过滤已去过的景区
            if (selectedScenicIdList.contains(tmpScenicId)) {
                continue;
            }
            // 获取游玩时间
            double playTimeTmp = ObjectUtils.isNotNull(tmp.getPlayTime())
                    ? Double.parseDouble(String.valueOf(tmp.getPlayTime()))
                    : 2.5;
            // 如果新景区游玩时间+已累积游玩时间 > 行程安排时间，则过滤
            if (playTime + playTimeTmp > journey) {
                continue;
            }
            // 符合条件，添加到景区列表
            scenicList.add(tmp);
            // 累积游玩时间
            playTime += playTimeTmp;
        }

        return scenicList;
    }
}
