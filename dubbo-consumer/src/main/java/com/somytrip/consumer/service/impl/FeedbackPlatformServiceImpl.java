package com.somytrip.consumer.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.feedback.FeedbackPlatformService;
import com.somytrip.consumer.mapper.FeedbackPlatformMapper;
import com.somytrip.entity.community.FeedbackPlatform;
import com.somytrip.entity.dto.feedback.SubmitFeedbackDto;
import com.somytrip.entity.vo.feedback.SubmitFeedbackVo;
import com.somytrip.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.time.LocalDateTime;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.service.impl
 * @className: FeedbackPlatformServiceImpl
 * @author: lijunqi
 * @description: 反馈平台服务实现类
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
@Slf4j
@DubboService
public class FeedbackPlatformServiceImpl extends ServiceImpl<FeedbackPlatformMapper, FeedbackPlatform> 
        implements FeedbackPlatformService {

    /**
     * 提交反馈建议
     *
     * @param userId 用户ID
     * @param dto    提交反馈建议请求DTO
     * @param userIp 用户IP地址
     * @return 提交反馈建议响应VO
     */
    @Override
    public SubmitFeedbackVo submitFeedback(String userId, SubmitFeedbackDto dto, String userIp) {
        
        if (StrUtil.isBlank(userId)) {
            throw new BusinessException("用户未登录");
        }

        // 处理标题中的空格
        String title = dto.getTitle();
        if (StrUtil.isNotBlank(title)) {
            // 合并多个连续空格为单个空格，并去除首尾空格
            title = title.replaceAll("\\s+", " ").trim();
            dto.setTitle(title);
        }

        // 创建反馈实体
        FeedbackPlatform feedback = new FeedbackPlatform();
        feedback.setUserId(userId);
        feedback.setTitle(dto.getTitle());
        feedback.setContent(dto.getContent());
        feedback.setImages(dto.getImages());
        feedback.setPhone(dto.getPhone());
        feedback.setIp(userIp);
        feedback.setCreateTime(LocalDateTime.now());
        feedback.setUpdateTime(LocalDateTime.now());
        feedback.setSupportCount(0);
        feedback.setStatus(1); // 默认待处理
        feedback.setCategory(dto.getCategory() != null ? dto.getCategory() : 1);
        feedback.setSource(dto.getSource() != null ? dto.getSource() : 1);
        feedback.setDelFlag(0);
        feedback.setIsAdopted(0);

        // 保存到数据库
        boolean saveResult = this.save(feedback);
        if (!saveResult) {
            throw new BusinessException("提交反馈失败，请稍后重试");
        }

        log.info("用户 {} 提交反馈成功，反馈ID: {}", userId, feedback.getId());
        
        return new SubmitFeedbackVo(feedback.getId());
    }

    /**
     * 增加支持数
     *
     * @param feedbackId 反馈ID
     * @return 是否成功
     */
    @Override
    public Boolean increaseSupportCount(Integer feedbackId) {
        if (feedbackId == null || feedbackId <= 0) {
            throw new BusinessException("反馈ID不能为空");
        }

        int result = baseMapper.increaseSupportCount(feedbackId);
        return result > 0;
    }

    /**
     * 更新反馈状态
     *
     * @param feedbackId 反馈ID
     * @param status     状态
     * @return 是否成功
     */
    @Override
    public Boolean updateFeedbackStatus(Integer feedbackId, Integer status) {
        if (feedbackId == null || feedbackId <= 0) {
            throw new BusinessException("反馈ID不能为空");
        }
        if (status == null || status < 1 || status > 4) {
            throw new BusinessException("状态参数错误");
        }

        int result = baseMapper.updateFeedbackStatus(feedbackId, status);
        return result > 0;
    }

    /**
     * 设置反馈为已采纳
     *
     * @param feedbackId 反馈ID
     * @return 是否成功
     */
    @Override
    public Boolean adoptFeedback(Integer feedbackId) {
        if (feedbackId == null || feedbackId <= 0) {
            throw new BusinessException("反馈ID不能为空");
        }

        int result = baseMapper.adoptFeedback(feedbackId);
        return result > 0;
    }
}
