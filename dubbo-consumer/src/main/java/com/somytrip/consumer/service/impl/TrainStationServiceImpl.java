package com.somytrip.consumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.TrainStationService;
import com.somytrip.consumer.mapper.TrainStationMapper;
import com.somytrip.entity.train.TrainStationDto;
import com.somytrip.entity.train.TrainStationEntity;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Locale;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.service.impl
 * @className: TrainStationServiceImpl
 * @author: shadow
 * @description: 火车站Service实现类
 * @date: 2025/5/7 17:24
 * @version: 1.0
 */
@Service
public class TrainStationServiceImpl
        extends ServiceImpl<TrainStationMapper, TrainStationEntity>
        implements TrainStationService {

    /**
     * 根据城市ID列表查询火车站Dto列表
     *
     * @param cityIds 城市ID列表
     * @return java.util.List<com.somytrip.entity.train.TrainStationDto>
     * <AUTHOR>
     * @date 2025/5/12 15:58
     */
    @Override
    public List<TrainStationDto> queryDtoListByCityIds(List<Integer> cityIds) {

        // 语言
        Locale locale = LocaleContextHolder.getLocale();
        return baseMapper.queryDtoListByCityIds(cityIds, locale.toString());
    }
}
