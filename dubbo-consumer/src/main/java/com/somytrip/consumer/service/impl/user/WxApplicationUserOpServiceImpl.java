package com.somytrip.consumer.service.impl.user;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.somytrip.consumer.config.WechatConfig;
import com.somytrip.consumer.mapper.SomytripUserChannelMapper;
import com.somytrip.consumer.mapper.user.UserBlackRecordDtoMapper;
import com.somytrip.consumer.service.RedisService;
import com.somytrip.consumer.service.UserOpService;
import com.somytrip.consumer.util.WechatUtils;
import com.somytrip.entity.dto.user.SomytripUserChannel;
import com.somytrip.entity.dto.user.UserBlackRecordDTO;
import com.somytrip.entity.enums.user.LoginMethodEnum;
import com.somytrip.entity.enums.user.UserBlackTypeEnum;
import com.somytrip.entity.enums.user.UserLoginChannel;
import com.somytrip.entity.enums.user.UserLoginChannelType;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.user.UserOpVo;
import com.somytrip.entity.vo.user.WxApplicationVo;
import com.somytrip.exception.BusinessException;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 微信Application用户操作
 * @author: pigeon
 * @created: 2025-01-03 15:24
 */
@Service
@Slf4j
public class WxApplicationUserOpServiceImpl implements UserOpService {
    private final String BINDING_REDIS_KEY_PREFIX = "user:oauth2:wx:application:";
    @Resource
    private WechatConfig weChatConfig;
    @Resource
    private RedisService redisService;
    @Resource
    private SomytripUserChannelMapper somytripUserChannelMapper;
    @Resource(name = "phoneUserOpService")
    private UserOpService phoneUserOpService;
    @Resource
    private UserBlackRecordDtoMapper userBlackRecordDtoMapper;
    private WechatUtils wechatUtils;

    @PostConstruct
    public void init() {
        wechatUtils = new WechatUtils(weChatConfig);
    }

    @Override
    public boolean support(LoginMethodEnum loginMethod) {
        return getLoginMethod().equals(loginMethod);
    }

    @Override
    public LoginMethodEnum getLoginMethod() {
        return LoginMethodEnum.WX_APPLICATION;
    }

    @Override
    public String login(UserOpVo<?> vo) {
        WxApplicationVo wxApplicationVo = JSON.parseObject(JSON.toJSONString(vo.getUserInfo()), WxApplicationVo.class);
        WechatUtils.AppCode2SessionResult result = wechatUtils.appCode2Result(wxApplicationVo.getCode());
        if (StrUtil.isBlank(result.unionId())) {
            WechatUtils.ApplicationUserInfo userInfo = wechatUtils.appAccessToken2Result(result.accessToken(), result.openid());
            JSONObject jsonObject = JSONObject.from(result);
            jsonObject.put("unionid", userInfo.unionId());
            jsonObject.put("unionId", userInfo.unionId());
            result = jsonObject.toJavaObject(WechatUtils.AppCode2SessionResult.class);
        }
        LambdaQueryWrapper<SomytripUserChannel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SomytripUserChannel::getChannel, UserLoginChannel.wechat)
                .eq(SomytripUserChannel::getChannelType, UserLoginChannelType.application)
                .eq(StrUtil.isNotBlank(result.openid()), SomytripUserChannel::getChannelId, result.openid())
                .eq(StrUtil.isNotBlank(result.unionId()), SomytripUserChannel::getChannelUniqueId, result.unionId())
        ;
        SomytripUserChannel somytripUserChannel = somytripUserChannelMapper.selectOne(queryWrapper);
        String redisKey = StrUtil.concat(true, BINDING_REDIS_KEY_PREFIX, wxApplicationVo.getCode());
        redisService.setString(redisKey, JSON.toJSONString(result), 10, TimeUnit.HOURS);
        if (somytripUserChannel == null) {
            throw new BusinessException(306, "this channel is not register");
        }
        if (somytripUserChannel.getUserId() == null || somytripUserChannel.getUserId() == 0) {
            somytripUserChannel.setChannelUserInfo(JSONObject.from(result));
            somytripUserChannelMapper.updateById(somytripUserChannel);
            throw new BusinessException(306, "this channel is not register");
        }
        isBlackUser(somytripUserChannel.getId().toString());
        return somytripUserChannel.getUserId().toString();
    }

    @Override
    public boolean checkVerifyCode(UserOpVo<?> vo) {
        return false;
    }

    @Override
    public String register(UserOpVo<?> vo) {
        vo.setServiceUse(true);
        String uid = phoneUserOpService.register(vo);
        log.info("register uid: {}", uid);
        return uid;
    }

    @Override
    public boolean exists(UserOpVo<?> vo) {
        return false;
    }

    @Transactional(rollbackFor = {Exception.class, BusinessException.class})
    @Override
    public boolean binding(UserOpVo<?> vo) {
        WxApplicationVo wxAppVo = JSON.parseObject(JSON.toJSONString(vo.getUserInfo()), WxApplicationVo.class);
        String redisKey = StrUtil.concat(true, BINDING_REDIS_KEY_PREFIX,
                wxAppVo.getCode());
        String value = redisService.getString(redisKey);
        if (StrUtil.isBlank(value)) {
            log.warn("user: {} binding {} {} failed", vo.getUid(), getLoginMethod(),
                    wxAppVo.getCode());
            WechatUtils.AppCode2SessionResult temp = wechatUtils.appCode2Result(wxAppVo.getCode());
            if (StrUtil.isBlank(temp.openid())) {
                log.warn("微信App binding: {}, {}", JSON.toJSONString(vo), JSON.toJSONString(temp));
                return false;
            }
            value = JSON.toJSONString(temp);
        }
        WechatUtils.AppCode2SessionResult result = JSON.parseObject(value, WechatUtils.AppCode2SessionResult.class);
        LambdaQueryWrapper<SomytripUserChannel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SomytripUserChannel::getChannel, UserLoginChannel.wechat)
                .eq(SomytripUserChannel::getChannelType, UserLoginChannelType.application)
                .eq(StrUtil.isNotBlank(result.openid()), SomytripUserChannel::getChannelId, result.openid())
                .eq(StrUtil.isNotBlank(result.unionId()), SomytripUserChannel::getChannelUniqueId, result.unionId())
        ;
        SomytripUserChannel somytripUserChannel = somytripUserChannelMapper.selectOne(queryWrapper);
        if (somytripUserChannel == null) {
            somytripUserChannel = new SomytripUserChannel();
            somytripUserChannel.setChannelUserInfo(JSONObject.from(result));
            somytripUserChannel.setChannel(UserLoginChannel.wechat);
            somytripUserChannel.setChannelType(UserLoginChannelType.application);
            somytripUserChannel.setCreateTime(DateUtil.date());
            somytripUserChannel.setUpdateTime(somytripUserChannel.getCreateTime());
            somytripUserChannel.setChannelId(result.openid());
            somytripUserChannel.setChannelUniqueId(result.unionId());
            somytripUserChannel.setUserId(Long.parseLong(vo.getUid()));
            somytripUserChannel.setUserUniqueId("");
            if (somytripUserChannelMapper.insert(somytripUserChannel) > 0) {
                log.info("user: {} binding success", vo.getUid());
                return true;
            } else {
                log.error("user: {} binding failed", vo.getUid());
                throw new BusinessException(500, "binding failed");
            }
        }
        log.info("update user: {} binding success", vo.getUid());
        somytripUserChannel.setChannelUserInfo(JSONObject.from(result));
        somytripUserChannel.setUserId(Long.parseLong(vo.getUid()));
        somytripUserChannel.setUpdateTime(DateUtil.date());
        log.info("update somytripUserChannel: {}", JSON.toJSONString(somytripUserChannel));
        return somytripUserChannelMapper.updateById(somytripUserChannel) > 0;

    }

    @Override
    public boolean unbind(UserOpVo<?> vo) {
        String uid = StrUtil.str(vo.getUserInfo(), StandardCharsets.UTF_8);
        LambdaUpdateWrapper<SomytripUserChannel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SomytripUserChannel::getChannel, UserLoginChannel.wechat)
                .eq(SomytripUserChannel::getChannelType, UserLoginChannelType.application)
                .eq(SomytripUserChannel::getUserId, uid)
                .set(SomytripUserChannel::getUserId, 0)
        ;
        return somytripUserChannelMapper.update(updateWrapper) > 0;
    }

    @Override
    public ResponseResult<Boolean> providerMessage(UserOpVo<?> vo) {
        return null;
    }

    @Override
    public boolean consumerMessage(UserOpVo<?> vo) {
        return false;
    }

    @Override
    public boolean deleteById(String uid) {
        UserOpVo<String> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(uid);
        log.info("delete user: {}", JSON.toJSONString(userOpVo));
        return unbind(userOpVo);
    }

    @Override
    public ResponseResult<Boolean> reset(UserOpVo<?> vo) {
        return ResponseResult.ok(false);
    }

    @Override
    public int state(UserOpVo<?> vo) {
        return 0;
    }

    @Override
    public String getSignById(String id) {
        return "";
    }

    @Override
    public boolean isBlackUser(String value) {
        LambdaQueryWrapper<UserBlackRecordDTO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserBlackRecordDTO::getType, UserBlackTypeEnum.WX_APPLICATION)
                .eq(UserBlackRecordDTO::getValue, value);
        UserBlackRecordDTO userBlackRecordDTO = userBlackRecordDtoMapper.selectOne(queryWrapper);
        if (userBlackRecordDTO == null) {
            return false;
        }
        log.warn("this user black record is black|{}", JSON.toJSONString(userBlackRecordDTO));
        throw new BusinessException(500, userBlackRecordDTO.getRemark());
    }
}
