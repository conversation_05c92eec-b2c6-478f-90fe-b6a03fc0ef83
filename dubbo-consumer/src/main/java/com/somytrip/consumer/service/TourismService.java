package com.somytrip.consumer.service;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.somytrip.api.service.EnterpriseCardService;
import com.somytrip.consumer.mapper.*;
import com.somytrip.consumer.util.*;
import com.somytrip.entity.dto.MemberCardUser;
import com.somytrip.entity.dto.tourism.TourismExportEntity;
import com.somytrip.entity.dto.tourism.UpdateSchemeDto;
import com.somytrip.entity.dto.user.PointsRecordEntity;
import com.somytrip.entity.enums.tourism.DayTimeNameEnums;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.tourism.PlanRouteDay;
import com.somytrip.entity.tourism.PlanScheme;
import com.somytrip.entity.tourism.Planning;
import com.somytrip.entity.tourism.PlanningRoute;
import com.somytrip.entity.vo.TourismHotelVo;
import com.somytrip.exception.BusinessException;
import com.somytrip.utils.JSONResultUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Deprecated
public class TourismService extends BaseService {
    private static final String[] LEVEL_ARR = new String[]{"cheap", "comfort", "performance"};
    /**
     * 匹配一个或多个数字
     */
    private final Pattern scenicPricePattern = Pattern.compile("\\d+");
    @Value("${info.free_mode.enable}")
    public boolean isFreeMode;
    @Value("${info.free_mode.limit}")
    public Integer freeModeLimit;
    //    @Value("${spring.profiles.active}")
    public String active = "dev";
    @Resource
    private PlanningMapper planningMapper;
    @Resource
    private PlanningRouteMapper planningRouteMapper;
    @Resource
    private PlanRouteDayMapper planRouteDayMapper;
    @Resource
    private TourismMapper tourismMapper;
    @Resource
    private PlanSchemeMapper planSchemeMapper;
    @Resource
    private ScenicV1Mapper scenicV1Mapper;
    @Resource
    private HotelMapper hotelMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private CtripService ctripService;
    @Resource
    private HotelV1Service hotelV1Service;
    @Resource
    private EnterpriseCardService enterpriseCardService;
    @Resource
    private MemberCardUserMapper memberCardUserMapper;
    @Resource
    private PointsRecordMapper pointsRecordMapper;
    @Resource
    private FileUtil fileUtil;
    @Resource
    private SomytripUsersMapper somytripUsersMapper;

    public String[] convertCostInterval(String costInterval) {
        if (StrUtil.isEmpty(costInterval)) {
            return null;
        }

        String[] ci = costInterval.split(",");
        if (ci.length == 1) {
            ci = new String[]{ci[0], "0"};
        } else {
            if (StrUtil.isEmpty(ci[0])) {
                ci[0] = "1";
            }
        }

        if (StrUtil.isEmpty(ci[0]) || Integer.parseInt(ci[0]) <= 0) {
            ci[0] = "1";
        }
        if (StrUtil.isEmpty(ci[1]) || Integer.parseInt(ci[0]) + 20 >= Integer.parseInt(ci[1])) {
            ci[1] = "0";
        }
        return ci;
    }

    /**
     * 查询攻略
     */
    public ResponseResult prepareCourseTotalV2(Integer uid, String Starting, String destination, Integer[] cityIds,
                                               String play_time, String remainPrice, String theme, String age_cls,
                                               String visitors, String journey, String[] favScesArr, String favHotels,
                                               String cost_interval, String star_hotels) {

        JSONObject data;
        long start = System.currentTimeMillis();
        /* =============================== 开始处理数据 ============================== */
        String[] city = destination.split(",");
        JSONArray jsonArray = JSONArray.parseArray(JSONArray.toJSONString(city));
        int index = jsonArray.indexOf("香港");
//        if (index >= 0 && index < city.length) {
        if (index > -1) {
            city[index] = "香港特别行政区";
        }
        BigDecimal spend = new BigDecimal(StrUtil.isBlank(remainPrice) ? "0" : remainPrice);
//        String[] themeArr = theme.split(",");
        //        String[] themeArr = new String[]{};
//        try {
//            themeArr = objectMapper.readValue(theme, String[].class);
//        } catch (Exception e) {
//            log.error("风格偏好转换失败");
//            log.error("theme: {}", theme);
//            try {
//                String replace = theme.replace("[", "").replace("]", "");
//                if (StrUtil.isNotBlank(replace)) {
//                    themeArr = replace.split(",");
//                }
//                log.info("再次转换 theme: {}", Arrays.toString(themeArr));
//            } catch (Exception ex) {
//                log.error("再次转换失败");
//            }
//        }
        List<Integer> themeIds = Collections.emptyList();
        try {
            themeIds = JSONArray.parseArray(theme, Integer.class);
        } catch (Exception e) {
            log.info("处理风格偏好错误: {}", theme);
        }
        log.info("风格偏好id列表: {}", themeIds);

        // 获取意向景区和意向酒店
        String[] favHotelsArr = StrUtil.isBlank(favHotels) ? new String[]{} : favHotels.split(",");

        // 获取酒店费用区间和酒店等级
        String[] costInterval = convertCostInterval(cost_interval);
        /* =============================== 结束处理数据 ============================== */

//        boolean isVip = checkMemberCardUserV3(uid);
        long isVipStart = System.currentTimeMillis();
        boolean isVip = checkMemberCardUserV4(uid);
        long isVipEnd = System.currentTimeMillis();
        log.info("检查是否Vip耗时: {}", isVipEnd - isVipStart);
        boolean isVVip = false;
        try {
            isVVip = somytripUsersMapper.isVVip(uid);
        } catch (Exception e) {
            log.error("检查是否是VVip报错");
        }
        log.info("isVip: {}", isVip);
        log.info("isVVip: {}", isVVip);
        if (!isVVip) {
            Integer freeModeTimes = getFreeModeTimes(uid);
            // 判断是否为免费模式
            if (isFreeMode) {
                // 免费模式
                if (freeModeTimes < 1) {
                    return JSONResultUtils.fail(2002, "api.vip.free-mode-use-0");
                }
            } else {
                // 非免费模式
                // 判断是否是会员
                if (!isVip) {
                    // 不是会员
                    if (freeModeTimes < 1) {
                        return JSONResultUtils.fail(2001, "api.vip.need-shop-vip");
                    }
                }
            }
        }

        // 开始查询攻略
        data = prepareCourse(themeIds, age_cls, visitors, play_time, spend,
                favScesArr, favHotelsArr, journey, costInterval, star_hotels, city, cityIds, uid);
//        log.info("data:{}", data);
//        log.info("city:{}", JSONArray.toJSONString(city));
        // 查询服务电话
        JSONObject serviceTel = new JSONObject();
        try {
            serviceTel = getServiceTel(city[1].trim());
        } catch (Exception e) {
            log.info("查询服务电话出错");
            log.error(e.getMessage(), e);
        }
        data.put("serviceTel", serviceTel);
        // 结束查询攻略

        // 保存方案
        Integer sid = savePlanning(uid, Starting, destination.substring(1), cityIds,
                play_time, theme, visitors, journey, age_cls, favHotelsArr, favScesArr, data);
        data.put("sid", sid);

        if (!isVVip) {
            if (isFreeMode) {
                // 扣除每日免费次数
                handleFreeMode(uid);
            } else {
                if (!isVip) {
                    // 不是会员
                    handleFreeMode(uid);
                }
            }
        }

        // 添加积分
        try {
            PointsRecordEntity record = new PointsRecordEntity(uid, 10.0, "行程定制");
            pointsRecordMapper.insert(record);
        } catch (Exception e) {
            log.info("添加积分失败");
            log.error(e.getMessage(), e);
        }
        long end = System.currentTimeMillis();
        log.info("查询攻略总耗时: {}", end - start);

        return JSONResultUtils.success("api.enterpriseCard.data-info-success", data);
    }

    //    @Async
    public CompletableFuture<JSONObject> getPlanData(String level, List<Integer> themeIds,
                                                     String ageCls, String visitors,
                                                     String play_time, BigDecimal spend,
                                                     String[] favSces, String[] favHotels,
                                                     String journey, String[] costInterval,
                                                     String starHotels, String[] city,
                                                     Integer[] cityIds, int uid, String curCity, List<Map<String, Object>> allHotelList) {

        // 是否为生产环境
        boolean isProd = Objects.equals(this.active, "prod");

        log.info("当前等级：{}", level);
        boolean isForeign = true;
        String lngKey;
        String latKey;
        int levelValue = Arrays.asList(LEVEL_ARR).indexOf(level) + 1;
        // 分析游客组合
        JSONObject visitMap = this.ctripService.analysisVisitorV2(visitors);

        String[] times = play_time.split(",");
        int days = DateUtil.differentDaysByMillisecond(times[0], times[1]) + 1;
        // 酒店的比例
//            double hotelRate = Double.parseDouble(Constants.jdbcMap.get("rate_hotel_" + level).toString());
        double hotelRate = ConstantUtil.Tourism.HOTEL_RATE.get(level);
//            log.info("hotelRate: {}", hotelRate);
        // 酒店距离景区的最大距离
        BigDecimal disConfig = new BigDecimal(Constants.jdbcMap.get("distince_hotel_replace").toString());
        log.info("disConfig: {}", disConfig);
        // 总人数
        int visitorTotal = Integer.parseInt(visitMap.get("total").toString());
        // 最低每天开销
        BigDecimal lessCost = new BigDecimal("200").multiply(new BigDecimal(visitorTotal));
        // 成人每人一间房，儿童两人一间
        int number_adultRoom = Integer.parseInt(Constants.jdbcMap.get("number_adult_room").toString());
//        log.info("number_adultRoom: {}", number_adultRoom);
        int number_childRoom = Integer.parseInt(Constants.jdbcMap.get("number_child_room").toString());
//        log.info("number_childRoom: {}", number_childRoom);
        int hotelMan = (visitMap.getInteger("adult") /
                number_adultRoom + visitMap.getInteger("adult") % number_adultRoom) +
                (visitMap.getInteger("child") / number_childRoom +
                        visitMap.getInteger("child") % number_childRoom);
        // 添加餐饮和交通的费用
        BigDecimal caterTrafficCost = this.ctripService.analysisCateringTraffic(visitMap, 3, days);
        // 计算酒店价格
        // 平均每人酒店的最高费用
        BigDecimal hotelCost = spend.multiply(new BigDecimal(hotelRate))
                .divide(new BigDecimal(days), RoundingMode.CEILING)
                .divide(new BigDecimal(hotelMan), RoundingMode.CEILING);
//        log.info("hotelCost: {}", hotelCost);
        // 减去餐饮和交通后，平均每天的费用
        BigDecimal avgCost = spend.subtract(caterTrafficCost)
                .divide(new BigDecimal(days), 2, RoundingMode.HALF_UP);
//        log.info("avgCost: {}", avgCost);

        // 取前days个THI景区信息
        // 获取THI最高的景区
        List<Map<String, Object>> topScenicList;
        if ("cheap".equals(level)) {
            topScenicList = this.ctripService.selectTopThiScenic_cheap_v2(days, themeIds, ageCls, visitMap,
                    favSces, city, cityIds, uid, isForeign);
        } else {
            topScenicList = this.ctripService.selectTopThiScenic_v2(days, themeIds, ageCls, visitMap,
                    favSces, city, cityIds, uid, isForeign);
        }
        // 给查询出的景区按方位排序,定位第一个
        topScenicList = this.ctripService.orderTopScenicList(topScenicList);
        // 查询意向酒店信息（如果有的话），没有则为空
        Map<String, Object> favHotel = this.hotelV1Service.queryFavHotel(favHotels, city);

        List<Map<String, Object>> dataList = new ArrayList<>();
        BigDecimal remainPrice = new BigDecimal("0");    // 自定义剩下的钱
        BigDecimal totalPrice = new BigDecimal("0");     // 共使用多少钱
        // 已使用过的景区
        // 将查询出来的头部景区添加到已有景区数组中
        List<Map<String, Object>> scenicListAlready = new ArrayList<>(topScenicList);

        Map<String, Object> lastHotel = null;         // 当前的酒店
        TourismHotelVo lastHotelVo = null;
//            Item<Map<String, Object>> hotelList = new ArrayList<>();
        for (int i = 0; i < days; i++) {
            BigDecimal dayCost = avgCost;     // 当天的合理平均花费
            dayCost = dayCost.add(remainPrice);
            Map<String, Object> sce = new HashMap<>(16);
            if (Optional.ofNullable(topScenicList).isPresent()) {
                sce = topScenicList.get(0);
                if ("cheap".equals(level)) {
                    topScenicList.remove(0);
                } else {
                    // 如果大于最低每天开销，则可玩收费景点
                    if (dayCost.compareTo(lessCost) > 0) {
                        log.info("允许取收费景区");
                        sce = topScenicList.get(0);
                        topScenicList.remove(0);
                    } else {
//                            log.info("只能取免费景区");
                        // 否则玩免费景区
                        Optional<Map<String, Object>> firstA = topScenicList
                                .stream()
                                .filter(
                                        a -> (a.get("ticket_price") == null
                                                || Double.parseDouble(a.get("ticket_price").toString()) == 0)
                                )
                                .findFirst();
                        if (firstA.isPresent()) {
                            sce = firstA.get();
                        }
                        topScenicList.remove(sce);
                    }
                }
            }

            latKey = DistanceUtil.getLatLngName(sce).get("latName");
            lngKey = DistanceUtil.getLatLngName(sce).get("lngName");
            // 查询周边距离最近的前3景区
            Map<String, Object> info = new HashMap<>(16);

            List<Map<String, Object>> scenicList;
            // 查询景区
            scenicList = this.ctripService.queryForeignScenicByXYZ(i, levelValue, sce, journey,
                    scenicListAlready, topScenicList, city);
//            log.info("scenicList:{}", scenicList);
//            if (isForeign) {
//                scenicList = this.ctripService.queryForeignScenicByXYZ(i, levelValue, sce, journey,
//                        scenicListAlready, topScenicList, city);
//            } else {
//                scenicList = this.ctripService.queryScenicByXYZ(i, levelValue, sce, journey,
//                        scenicListAlready, topScenicList);
//            }

            scenicListAlready.addAll(scenicList);
            // 取下一个景区
            Map<String, Object> sceNext = null;
            if (topScenicList != null && !topScenicList.isEmpty()) {
                sceNext = topScenicList.get(0);
            }
            Map<String, Object> hotel = null;
            TourismHotelVo hotelVo = null;
            // 最后一天不需要住宿信息
            if (i < days - 1) {
                if (isProd) {
                    // 查询酒店
                    Map<String, Object> hotelObj = new HashMap<>(16);
                    try {
                        curCity = (String) scenicList.get(scenicList.size() - 1).get("city");
                        String tmpCity = (String) scenicList.get(scenicList.size() - 1).get("city");
                        // 切换城市查询新城市的所有酒店
                        if (!Objects.equals(tmpCity, curCity)) {
                            curCity = tmpCity;
//                        log.info("hotelCity:{}", curCity);
                            allHotelList = hotelV1Service.queryAllHotel(curCity);
                        }
                        hotelObj = this.hotelV1Service.queryHotelV2(allHotelList, level, sce.get(latKey).toString(),
                                sce.get(lngKey).toString(), hotelCost, favHotel, lastHotel, disConfig,
                                costInterval, starHotels, sceNext, city);
                        hotel = (Map<String, Object>) hotelObj.get("hotel");
//                        log.info("hotel: {}", hotel);
                    } catch (NullPointerException e) {
                        log.info("查询酒店出现 空指针异常");
                    } catch (ArrayIndexOutOfBoundsException e) {
                        log.info("查询酒店出现 下标越界异常");
                    } catch (Exception e) {
                        log.info("查询酒店出现 未知错误");
                    }
                } else {
                    try {
//                        hotelVo = hotelService.getTourismDayHotel(new QueryTourismDayHotelDto(levelValue, String.valueOf(sce.get(lngKey)),
//                                String.valueOf(sce.get(latKey)), (Integer) sce.get("city_id"), times,
//                                lastHotelVo, new ScenicVo()));
                        hotelVo = null;
                    } catch (Exception e) {
                        log.info("酒店查询报错");
//                        log.error(e.getMessage(), e);
                    }
                    log.info("新 - 找到的酒店: {}", hotelVo);
                }
//                    if (hotelObj.get("hotelList") != null) hotelList.addAll((Item) hotelObj.get("hotelList"));
            }
            lastHotel = hotel;    // 将获取到的酒店赋给当前酒店变量
            lastHotelVo = hotelVo;

            BigDecimal scenicPrices = new BigDecimal("0");
            if (!scenicList.isEmpty()) {
                scenicPrices = this.ctripService.analysisScenicPrice(visitMap, scenicList);
            }
            BigDecimal dayPrices = scenicPrices;
            if (isProd && hotel != null) {
                dayPrices = dayPrices.add(new BigDecimal(hotel.get("advice_price").toString())
                        .multiply(new BigDecimal(hotelMan)));    // 当天的花费
            } else {
                if (hotelVo != null) {
                    if (hotelVo.getAdvicePrice() != null) {
                        dayPrices = dayPrices.add(new BigDecimal(hotelVo.getAdvicePrice()));
                    }
                }
            }

            // logger.info("便宜第"+i+"天费用（景区+酒店）："+dayPrices.toString());
            remainPrice = dayCost.subtract(dayPrices);    // 剩下的钱

            totalPrice = totalPrice.add(dayPrices);
            info.put("scenicList", scenicList);
            if (isProd) {
                info.put("hotel", hotel);
            } else {
                info.put("hotel", hotelVo);
            }
            dataList.add(info);

            // 查询离该酒店最近的景区
            if (topScenicList == null || topScenicList.isEmpty()) {
                continue;
            }
            topScenicList = this.ctripService.orderTopScenicListByHotel(lastHotelVo, topScenicList, city);
        }

        totalPrice = totalPrice.add(caterTrafficCost);

        JSONObject data = new JSONObject();
        data.put("dataList", dataList);
        data.put("totalPrice", totalPrice);
//        log.info("data:{}", data);
        return CompletableFuture.completedFuture(data);
//        return data;
    }

    /**
     * 获取所有攻略数据
     */
    public JSONObject prepareCourse(List<Integer> theme, String age_cls, String visitors,
                                    String play_time, BigDecimal spend,
                                    String[] favSces, String[] favHotels,
                                    String journey, String[] costInterval,
                                    String starHotels, String[] city,
                                    Integer[] cityIds, int uid
    ) {
        if (ArrayUtil.isEmpty(city)) {
            throw new BusinessException("参数错误");
        }
        JSONObject data = new JSONObject();
        log.info("===== 用户: {}, 开始攻略查询 =====", uid);
        boolean isProd = Objects.equals(this.active, "prod");

        // 查询所有目标城市所有酒店
//        if (isProd) {
//            String curCity = city[1];
//            Integer curCityId = cityIds[0];
//            log.info("city:{}", curCity);
//            long hotelStart = System.currentTimeMillis();
//            Item<Map<String, Object>> allHotel = hotelV1Service.queryAllHotel(curCity);
//            long hotelEnd = System.currentTimeMillis();
//            log.info("全部酒店查询耗时: {}, 酒店条数: {}", hotelEnd - hotelStart, allHotel.size());
//            for (String level : levelArr) {
//                JSONObject planData = this.getPlanData(level, theme, age_cls,
//                        visitors, play_time, spend, favSces, favHotels, journey, costInterval,
//                        starHotels, city, cityIds, uid, curCity, allHotel);
//                data.put("data_" + level, planData.getJSONArray("dataList"));
//                data.put("totalPrice_" + level, planData.getBigDecimal("totalPrice"));
//            }
//        } else {
//            for (String level : levelArr) {
//                JSONObject planData = this.getPlanData(level, theme, age_cls,
//                        visitors, play_time, spend, favSces, favHotels, journey, costInterval,
//                        starHotels, city, cityIds, uid, "", Collections.emptyList());
//                data.put("data_" + level, planData.getJSONArray("dataList"));
//                data.put("totalPrice_" + level, planData.getBigDecimal("totalPrice"));
//            }
//        }

        // 查询各等级攻略
        List<CompletableFuture<CompletableFuture<JSONObject>>> futures = new ArrayList<>();
        for (String level : LEVEL_ARR) {
//                    Item<Map<String, Object>> realHotelList = new ArrayList<>();
//            for (Map<String, Object> map : allHotel) {
//                Map<String, Object> copiedMap = new HashMap<>(map);
//                realHotelList.add(copiedMap);
//            }
            CompletableFuture<CompletableFuture<JSONObject>> planData =
                    CompletableFuture.supplyAsync(() -> this.getPlanData(level, theme, age_cls,
                            visitors, play_time, spend, favSces, favHotels, journey, costInterval,
                            starHotels, city, cityIds, uid, "", Collections.emptyList()));
            planData.thenAccept(jsonObject -> {
                synchronized (data) {
                    try {
                        data.put("data_" + level, jsonObject.get().get("dataList"));
                        data.put("totalPrice_" + level, jsonObject.get().get("totalPrice"));
                    } catch (InterruptedException | ExecutionException e) {
                        throw new BusinessException("查询失败");
                    }
                }
            });
            futures.add(planData);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("===== 用户: {}, 攻略查询结束 =====", uid);

        return data;
    }

    /**
     * 导出excel
     */
    public JSONObject exportV2(Integer uid, Integer sid) throws IOException {
        JSONObject data = new JSONObject();
        return data;

//        // 创建一个Excel文件
//        Workbook workbook = new XSSFWorkbook();
//        // 创建三个Sheet分别对应三个level
//        Sheet sheet1 = workbook.createSheet("经济型");
//        Sheet sheet2 = workbook.createSheet("舒适型");
//        Sheet sheet3 = workbook.createSheet("高档型");
//
//        /* =============================== 表头level - 开始 ============================== */
//        // 设置表头等级
//        Row levelRow1 = sheet1.createRow(0);
//        Row levelRow2 = sheet2.createRow(0);
//        Row levelRow3 = sheet3.createRow(0);
//
//        CellStyle levelRowStyle = workbook.createCellStyle();
//        // 设置字体样式
//        Font levelRowFont = workbook.createFont();
//        levelRowFont.setFontName("微软雅黑");
//        levelRowFont.setFontHeightInPoints((short) 16);
//        levelRowFont.setBold(true);
//        levelRowFont.setColor(IndexedColors.WHITE.getIndex());
//        levelRowStyle.setFont(levelRowFont);
//        // 居中
//        levelRowStyle.setAlignment(HorizontalAlignment.CENTER);
//        levelRowStyle.setVerticalAlignment(VerticalAlignment.CENTER);
//        // 背景颜色
//        levelRowStyle.setFillForegroundColor(IndexedColors.ROYAL_BLUE.getIndex());
//        levelRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//        // 设置边框
//        ExcelUtil.setThinBorder(levelRowStyle);
//
//        Cell levelCell1 = levelRow1.createCell(0);
//        levelCell1.setCellStyle(levelRowStyle);
//        levelCell1.setCellValue("经济型");
//
//        Cell levelCell2 = levelRow2.createCell(0);
//        levelCell2.setCellStyle(levelRowStyle);
//        levelCell2.setCellValue("舒适型");
//
//        Cell levelCell3 = levelRow3.createCell(0);
//        levelCell3.setCellStyle(levelRowStyle);
//        levelCell3.setCellValue("高档型");
//
//        // 合并单元格
//        // CellRangeAddress(第几行开始，第几行结束，第几列开始，第几列结束)
//        CellRangeAddress craOne = new CellRangeAddress(0, 0, 0, 4);
//        sheet1.addMergedRegion(craOne);
//        sheet2.addMergedRegion(craOne);
//        sheet3.addMergedRegion(craOne);
//        /* =============================== 表头level - 结束 ============================== */
//
//        /* =============================== 表头 - 开始 ============================== */
//        // 设置表头
//        String[] headers = {"天数", "时间", "地点", "简介与说明", "参考价格"};
//        Row headerRow1 = sheet1.createRow(1);
//        Row headerRow2 = sheet2.createRow(1);
//        Row headerRow3 = sheet3.createRow(1);
//
//        // 设置表头样式
//        CellStyle style = workbook.createCellStyle();
//        //设置字体样式
//        Font font = workbook.createFont();
//        font.setFontName("微软雅黑");
//        font.setBold(true);
//        font.setColor(IndexedColors.BLACK.getIndex());
//        style.setFont(font);
//        // 居中
//        style.setAlignment(HorizontalAlignment.CENTER);
//        style.setVerticalAlignment(VerticalAlignment.CENTER);
//        // 自动换行
//        style.setWrapText(true);
//        // 设置边框
//        ExcelUtil.setThinBorder(style);
//
//        for (int i = 0; i < headers.length; i++) {
//            Cell cell1 = headerRow1.createCell(i);
//            cell1.setCellStyle(style);
//            cell1.setCellValue(headers[i]);
//
//            Cell cell2 = headerRow2.createCell(i);
//            cell2.setCellStyle(style);
//            cell2.setCellValue(headers[i]);
//
//            Cell cell3 = headerRow3.createCell(i);
//            cell3.setCellStyle(style);
//            cell3.setCellValue(headers[i]);
//        }
//        /* =============================== 表头 - 结束 ============================== */
//
//        // 设置表头宽度
//        int dayWidth = 256 * 10;
//        int timeWidth = 256 * 8;
//        int descriptionWidth = 256 * 100;
//        int priceWidth = 256 * 10;
//        sheet1.setColumnWidth(0, dayWidth);
//        sheet1.setColumnWidth(1, timeWidth);
//        sheet1.setColumnWidth(2, 256 * 20);
//        sheet1.setColumnWidth(3, descriptionWidth);
//        sheet1.setColumnWidth(4, priceWidth);
//        sheet2.setColumnWidth(0, dayWidth);
//        sheet2.setColumnWidth(1, timeWidth);
//        sheet2.setColumnWidth(2, 256 * 20);
//        sheet2.setColumnWidth(3, descriptionWidth);
//        sheet2.setColumnWidth(4, priceWidth);
//        sheet3.setColumnWidth(0, dayWidth);
//        sheet3.setColumnWidth(1, timeWidth);
//        sheet3.setColumnWidth(2, 256 * 20);
//        sheet3.setColumnWidth(3, descriptionWidth);
//        sheet3.setColumnWidth(4, priceWidth);
//        // 填充数据
//        List<TourismExportEntity> dataList;
//        try {
//            dataList = getExportDataV2(sid);
//        } catch (Exception e) {
//            log.info("数据处理错误");
//            log.error(e.getMessage(), e);
//            throw new RuntimeException("数据处理错误");
//        }
//        int rowIndex1 = 2;
//        int rowIndex2 = 2;
//        int rowIndex3 = 2;
//        for (TourismExportEntity item : dataList) {
//            switch (item.getLevel()) {
//                case "经济型":
//                    ExcelUtil.fillData(sheet1, rowIndex1, item, workbook);
//                    rowIndex1++;
//                    break;
//                case "舒适型":
//                    ExcelUtil.fillData(sheet2, rowIndex2, item, workbook);
//                    rowIndex2++;
//                    break;
//                case "高档型":
//                    ExcelUtil.fillData(sheet3, rowIndex3, item, workbook);
//                    rowIndex3++;
//                    break;
//            }
//        }
//        // 合并单元格
//        ExcelUtil.mergeCells(sheet1);
//        ExcelUtil.mergeCells(sheet2);
//        ExcelUtil.mergeCells(sheet3);
//
//        // 保存Excel文件
//        FileOutputStream outputStream = null;
//        File tmpFile = null;
//        try {
//            String filePath = String.format("./tourism-export_%s_%s.xlsx", uid, System.currentTimeMillis());
//            log.info("filePath: {}", filePath);
//            outputStream = new FileOutputStream(filePath);
//            workbook.write(outputStream);
//
//            // 上传到minio
//            tmpFile = new File(filePath);
//            MultipartFile tempMFile = fileUtil.file2MultipartFile(tmpFile);
//            String name = fileUtil.uploadFile(tempMFile, "tourism-export");
//            log.info("minio name: {}", name);
//
//            data.put("name", name);
//            data.put("url", fileUtil.getUrl(name, "tourism-export"));
//
//            return data;
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            throw new RuntimeException("文件保存失败");
//        } finally {
//            if (tmpFile != null) {
//                tmpFile.deleteOnExit();
//            }
//            if (outputStream != null) {
//                outputStream.close();
//            }
//            workbook.close();
//            log.info("导出完成");
//        }
    }

    /**
     * 获取导出数据
     */
    public List<TourismExportEntity> getExportDataV2(Integer sid) {

        List<TourismExportEntity> data = new ArrayList<>();
        // 查询攻略每日数据
        List<PlanRouteDay> dataList = planRouteDayMapper.queryExportData(sid);
        // 查询起止时间
        JSONObject planTime = planningMapper.queryTimeBySid(sid);
        List<String> dateList = new ArrayList<>();

        String startDateStr = planTime.getString("time1");
        String endDateStr = planTime.getString("time2");

        // 将字符串转换成LocalDate对象
        LocalDate startDate = LocalDate.parse(startDateStr);
        LocalDate endDate = LocalDate.parse(endDateStr);

        // 遍历日期范围内的每一天，并格式化输出
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = startDate;
        while (!date.isAfter(endDate)) {
            try {
                String d = DateUtil.format2format(
                        date.format(formatter),
                        "M月d日",
                        "yyyy-MM-dd");
                dateList.add(d);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            date = date.plusDays(1);
        }

        int levelIndex = 0;
        String level = "";
        for (PlanRouteDay item : dataList) {
            log.info("day: {}", item.getDayNum());
            if (item.getDayNum() == 1) {
                level = levelIndex == 0 ? "经济型" : levelIndex == 1 ? "舒适型" : "高档型";
                levelIndex++;
            }
            // 处理景区
            handleScenicList4Export(level, "早上", Arrays.asList(item.getMorning().split(",")),
                    dateList, item.getDayNum(), data);
            handleScenicList4Export(level, "下午", Arrays.asList(item.getAfternoon().split(",")),
                    dateList, item.getDayNum(), data);
            handleScenicList4Export(level, "晚上", Arrays.asList(item.getEvening().split(",")),
                    dateList, item.getDayNum(), data);
            // 处理酒店
            handleHotel4Export(level, item.getHotel(), dateList, item.getDayNum(), data);
        }
//        log.info("data: {}", data);
        return data;
    }

    /**
     * 处理导出数据的景区数据
     */
    private void handleScenicList4Export(String level, String time, List<String> scenicList,
                                         List<String> dateList, Integer dayNum, List<TourismExportEntity> data) {

        for (String sItem : scenicList) {
            if (StrUtil.isBlank(sItem)) {
                continue;
            }
            sItem = sItem.replace("-s", "");
            JSONObject scenic = scenicV1Mapper.queryExportData(Integer.parseInt(sItem));
            if (scenic != null) {
                TourismExportEntity exportEntity = new TourismExportEntity();
                // 设置地点
                exportEntity.setName(scenic.getString("name"));
                // 查询简介和价格
//                JSONObject priceAndDescription = scenicMapper.queryExportData(Integer.parseInt(sItem));
                // 设置简介
                exportEntity.setDescription(scenic.getString("brief"));
                // 处理价格数据
                String price = scenic.getString("price");
                if (StrUtil.isNotBlank(price) && !"暂无价格".equals(price)) {

                    Matcher matcher = scenicPricePattern.matcher(price);
                    List<Integer> priceList = new ArrayList<>();
                    while (matcher.find()) {
                        int number = Integer.parseInt(matcher.group()); // 将匹配到的数字转换为整数
                        priceList.add(number); // 添加到列表中
                    }
                    if (!priceList.isEmpty()) {
                        price = Collections.min(priceList) + "元起";
                    }
                }
                // 设置价格
                exportEntity.setPrice(price);
                // 设置时间
                exportEntity.setTime(time);
                // 设置天数
                exportEntity.setDay(String.format("第%s天\n%s\n", dayNum, dateList.get(dayNum - 1)));
                // 设置等级
                exportEntity.setLevel(level);
                data.add(exportEntity);
            }
        }

    }

    /**
     * 处理导出数据的酒店数据
     */
    private void handleHotel4Export(String level, String hotelId,
                                    List<String> dateList, Integer dayNum,
                                    List<TourismExportEntity> data
    ) {

        if (StrUtil.isBlank(hotelId)) {
            return;
        }
        JSONObject hotel = hotelMapper.queryExportData(Integer.parseInt(hotelId.replace("-h", "")));
        TourismExportEntity exportEntity = new TourismExportEntity();
        // 设置地点
        exportEntity.setName(hotel.getString("name"));
        // 查询简介和价格
//        JSONObject priceAndDescription = scenicMapper.queryExportData(Integer.parseInt(sItem));
        // 设置简介
        exportEntity.setDescription(hotel.getString("brief"));
        // 处理价格数据
        String price = hotel.getString("price") + "元起";
        // 设置价格
        exportEntity.setPrice(price);
        // 设置时间
        exportEntity.setTime("晚上");
        // 设置天数
        exportEntity.setDay(String.format("第%s天\n%s\n", dayNum, dateList.get(dayNum - 1)));
        // 设置等级
        exportEntity.setLevel(level);
        data.add(exportEntity);
    }

    /**
     * 获取指定城市服务电话
     *
     * @param city 城市
     * @return com.alibaba.fastjson.JSONObject
     */
    public JSONObject getServiceTel(String city) {
        return tourismMapper.queryServiceTel(city);
    }

    /**
     * 自动保存方案
     */
    public Integer savePlanning(Integer uid, String city1, String city2, Integer[] cityIds,
                                String play_time, String themeStr,
                                String visitors, String journey,
                                String age_cls,
                                String[] favHotels, String[] favSces,
                                JSONObject data) {
        String startTime = play_time.split(",")[0];
        String endTime = play_time.split(",")[1];

        // 攻略默认标题  格式：YYYYMMDD目的地
        String title = startTime.replaceAll("-", "") + city2;

        String brief = "";
        String coverPic = "";
        String tags = "[]";

        // 插入plan_scheme
        PlanScheme planScheme = new PlanScheme(title, brief, coverPic, tags, uid);
        planSchemeMapper.insertScheme(planScheme);
        Integer schemeId = planScheme.getId();

        // 插入planning
        Planning planning = new Planning(startTime, endTime, visitors, themeStr, age_cls, city1, city2, journey,
                Arrays.toString(favSces), Arrays.toString(favHotels), uid, schemeId);
        planning.setDestinationIds(JSONArray.toJSONString(cityIds));
        planningMapper.insertPlanning(planning);
        Integer planningId = planning.getId();

        // 插入planning_route
        PlanningRoute route1 = new PlanningRoute(planningId,
                (BigDecimal) data.get("totalPrice_cheap"), 1);
        PlanningRoute route2 = new PlanningRoute(planningId,
                (BigDecimal) data.get("totalPrice_comfort"), 2);
        PlanningRoute route3 = new PlanningRoute(planningId,
                (BigDecimal) data.get("totalPrice_performance"), 3);
        List<PlanningRoute> routes = new ArrayList<>(Arrays.asList(route1, route2, route3));
        planningRouteMapper.insertRoutes(routes);

        JSONArray data_cheap = JSONArray.parseArray(JSONArray.toJSONString(data.get("data_cheap")));
        JSONArray data_comfort = JSONArray.parseArray(JSONArray.toJSONString(data.get("data_comfort")));
        JSONArray data_performance = JSONArray.parseArray(JSONArray.toJSONString(data.get("data_performance")));

        // 行程节奏
        int journeyNum;
        try {
            journeyNum = Integer.parseInt(journey);
        } catch (NumberFormatException e) {
            journeyNum = 8;
        }
        // 根据行程节奏判断早上游玩时间
        Double totalMorningHour = (double) (journeyNum == 5 ? 2 : journeyNum == 8 ? 3 : 5);
//        Integer morningHour = 0;

        // 插入plan_route_day
        handleInsertPlanRouteDay(route1.getId(), totalMorningHour, data_cheap);
        handleInsertPlanRouteDay(route2.getId(), totalMorningHour, data_comfort);
        handleInsertPlanRouteDay(route3.getId(), totalMorningHour, data_performance);

        return schemeId;
    }

    /**
     * 插入plan_route_day
     *
     * @param routeId          planning_route ID
     * @param totalMorningHour 早上游玩时间(小时)
     * @param data             每日安排 数据列表
     */
    private void handleInsertPlanRouteDay(Integer routeId,
                                          Double totalMorningHour,
                                          JSONArray data
    ) {

        boolean isProd = Objects.equals(this.active, "prod");

        if (data.isEmpty()) {
            return;
        }
        List<PlanRouteDay> list = new ArrayList<>();
        for (int i = 0; i < data.size(); ++i) {
            JSONObject iCur = data.getJSONObject(i);
            JSONObject hotel = null;
            String hotelId = null;
            String hotelObjStr = null;
            if (isProd) {
                hotel = iCur.getJSONObject("hotel");
                hotelId = hotel != null ? (hotel.getString("id") + "-h") : "";
                ;
            } else {
                JSONObject hotelJSONObject = iCur.getJSONObject("hotel");
                TourismHotelVo tourismHotelVo = hotelJSONObject != null ? hotelJSONObject.toJavaObject(TourismHotelVo.class) : null;
                hotelId = tourismHotelVo != null ? (tourismHotelVo.getId() + "-h") : "";
                hotelObjStr = JSONObject.toJSONString(tourismHotelVo);
            }
//            JSONObject hotel = iCur.getJSONObject("hotel");
//            JSONObject hotelJSONObject = iCur.getJSONObject("hotel");
//            TourismHotelVo hotel = hotelJSONObject != null ? hotelJSONObject.toJavaObject(TourismHotelVo.class) : null;
//            String hotelId = hotel != null ? (hotel.getId() + "-h") : "";
            JSONArray scenicList = iCur.getJSONArray("scenicList");
            StringBuilder morning = new StringBuilder();
            StringBuilder afternoon = new StringBuilder();
            String evening = "";
            double morningHour = 0.0;
            for (int j = 0; j < scenicList.size(); ++j) {
                JSONObject jCur = scenicList.getJSONObject(j);
                Integer scenicId = jCur.getInteger("id");
                double play_time_v = jCur.getDouble("play_time_v") != null
                        ? jCur.getDouble("play_time_v")
                        : 2.5;
//				log.info("play_time_v: {}", play_time_v);
                if (morningHour < totalMorningHour) { // 早上景区
                    morning.append(scenicId).append("-s,");
                    morningHour += play_time_v;
                } else { // 下午景区
                    afternoon.append(scenicId).append("-s,");
                }
            }
            String morningStr = morning.length() > 0 ? morning.substring(0, morning.length() - 1) : morning.toString();
            String afternoonStr = afternoon.length() > 0 ? afternoon.substring(0, afternoon.length() - 1) : afternoon.toString();
            PlanRouteDay planRouteDay = new PlanRouteDay(routeId, i + 1, "早上", morningStr,
                    "下午", afternoonStr, "晚上", evening, hotelId, hotelObjStr);
            list.add(planRouteDay);
        }
        planRouteDayMapper.insertBatch(list);
    }

    /**
     * 更新方案V2
     */
    public void updateSchemeV2(UpdateSchemeDto dto) {

        Integer sid = dto.getSid();
        String title = dto.getTitle();
        String type = dto.getType();

        if (StrUtil.isNotBlank(title)) {
            planSchemeMapper.updateTitle(sid, title);
        }

        if (Objects.equals("s", type) || Objects.equals("h", type)) {
            PlanRouteDay planRouteDay = planRouteDayMapper.queryObject(dto);

            try {
                String fieldName = Objects.equals("s", dto.getType()) ? dto.getTime() : "hotel";
                Field field = planRouteDay.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                String value = (String) field.get(planRouteDay);
                List<String> valueList = new ArrayList<>(Arrays.asList(value.split(",")));

                switch (dto.getAction()) {
                    // 添加
                    case "add":
                        valueList.add(dto.getIndex(), dto.getData());
                        break;
                    // 编辑
                    case "edit":
                        valueList.set(dto.getIndex(), dto.getData());
                        break;
                    // 删除
                    case "dele":
                        valueList.remove((int) dto.getIndex());
                        break;
                }

                value = String.join(",", valueList);
                field.set(planRouteDay, value);

                // 新酒店
                if (ObjectUtils.isNotNull(dto.getHotelOrigin(), dto.getHotelId())) {
//                    TourismHotelVo newHotelVo = hotelService.getTourismHotelById(dto.getHotelOrigin(), dto.getHotelId());
                    TourismHotelVo newHotelVo = null;
                    if (newHotelVo == null) {
                        throw new BusinessException("");
                    }
                    planRouteDay.setHotelObj(JSONObject.toJSONString(newHotelVo));
                }

                planRouteDayMapper.updateById(planRouteDay);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 更新方案
     */
    public void updateScheme(Integer sid, Integer day, Integer level,
                             String type, String time, String data, String title
    ) {
        if (StrUtil.isNotBlank(title)) {
            planSchemeMapper.updateTitle(sid, title);
        }
        if (Objects.equals("s", type) || Objects.equals("h", type)) {
            String column = Objects.equals("s", type) ? time : "hotel";
            planRouteDayMapper.updateScenic(sid, day, level, column, data);
        }
    }

    public Integer handleUpdateTrain(Integer sid, Integer level,
                                     String goBackValue, String trainContent
    ) {
        String column = Objects.equals("go", goBackValue) ? "train_content1" : "train_content2";
        return planningRouteMapper.updateTrainContent(sid, level, column, trainContent);
    }

    /**
     * 添加规划对象
     *
     * @param planning 规划实体
     */
    @Deprecated
    public int insertPlanning(Planning planning) {
        this.planningMapper.insert(planning);
        return planning.getId();
    }

    /**
     * 添加规划交通对象
     *
     * @param planningRoute PlanningRoute
     */
    @Deprecated
    public int insertPlanningRoute(PlanningRoute planningRoute) {
        this.planningRouteMapper.insert(planningRoute);
        return planningRoute.getId();
    }

    /**
     * 添加路线数据
     *
     * @param formJSON        JSONObject
     * @param comfortJSON     comfortJSON
     * @param performanceJSON performanceJSON
     * @param cheapJSON       cheapJSON
     * @param courseType      cheap:最便宜；performance:最超值；comfort:最舒适；
     * @return int
     */
    @Deprecated
    public int insertRouteData(JSONObject formJSON, JSONObject comfortJSON,
                               JSONObject performanceJSON, JSONObject cheapJSON,
                               int userid, String courseType) {

        String play_time = formJSON.getString("play_time");
        String spend = formJSON.getString("spend");
        String visitors = formJSON.getString("visitors");
        String theme = formJSON.getString("theme");
        String age_cls = formJSON.getString("age_cls");

        String journey = formJSON.getString("journey");
        // 获取意向景区和意向酒店
        String favScesStr = formJSON.getString("favSces");
        String favHotelsStr = formJSON.getString("favHotels");
        String city1 = formJSON.getString("Starting");
        String city2 = formJSON.getString("destination");
        JSONArray times = JSONArray.parseArray(play_time);

        // 添加路线信息
        // int planId, BigDecimal totalPrice, String flightContent1, String flightUrl1,
        // String trainContent1, String flightContent2, String flightUrl2, String trainContent2
        switch (courseType) {
            case "cheap":
                // 添加最便宜
                this.insertCheap(times, spend, visitors, theme, age_cls, city1, city2,
                        journey, favScesStr, favHotelsStr, userid, cheapJSON);
                break;
            case "performance":
                // 添加最超值
                this.insertPerformance(times, spend, visitors, theme, age_cls, city1, city2,
                        journey, favScesStr, favHotelsStr, userid, performanceJSON);
                break;
            case "comfort":
                // 添加最舒适
                this.insertComfort(times, spend, visitors, theme, age_cls, city1, city2,
                        journey, favScesStr, favHotelsStr, userid, comfortJSON);
                break;
        }

        return 1;
    }

    /**
     * 添加最舒适路线
     */
    @Deprecated
    private void insertComfort(JSONArray times, String spend, String visitors,
                               String theme, String age_cls, String city1, String city2,
                               String journey, String favScesStr, String favHotelsStr,
                               int userid, JSONObject comfortJSON) {

        Planning planning = new Planning(times.getString(0), times.getString(1),
                spend, visitors, theme, age_cls, city1, city2, journey,
                favScesStr, favHotelsStr, userid);
        // 添加规划对象
        int planningId = this.insertPlanning(planning);


        // 添加舒适
        int planningRoute1_id = 0;
        if (comfortJSON.getBigDecimal("total_price").intValue() > 0) {
            JSONArray comfort_trafficInfos = comfortJSON.getJSONArray("trafficInfo");
            // [0],[1],[2],[3]  ------ flight_content1, flight_content2, train_content1, train_content2
            JSONArray comfort_trafficUrls = comfortJSON.getJSONArray("trafficUrl");
            // [0],[1] ------ flight_url1, flight_url2
            PlanningRoute planningRoute1 = new PlanningRoute(planningId,
                    comfortJSON.getBigDecimal("total_price"),
                    comfort_trafficInfos.getString(0),
                    comfort_trafficUrls.getString(0),
                    comfort_trafficInfos.getString(2),
                    comfort_trafficInfos.getString(1),
                    comfort_trafficUrls.getString(1),
                    comfort_trafficInfos.getString(3),
                    1);
            planningRoute1_id = this.insertPlanningRoute(planningRoute1);
        }

        // 添加舒适-天信息
        JSONArray comfortDays = comfortJSON.getJSONArray("days");
        for (int i = 0; i < comfortDays.size(); i++) {
            JSONObject day = comfortDays.getJSONObject(i);
            // 上午morning，下午afternoon，晚上evening
            // 拼接相关的ID
            String morningV = jointDayId(day.getJSONArray("morning"));
            String afternoonV = jointDayId(day.getJSONArray("afternoon"));
            String eveningV = jointDayId(day.getJSONArray("evening"));
            String hotelV = jointDayId(day.getJSONArray("hotel"));

            // 获取早餐、午餐、晚餐
            String eat1 = day.getString("eat1");
            String eat2 = day.getString("eat2");
            String eat3 = day.getString("eat3");

            PlanRouteDay planRouteDay = new PlanRouteDay(planningRoute1_id,
                    i + 1, eat1, morningV, eat2, afternoonV, eat3, eveningV, hotelV);
            // int routeId, int dayNum, String morning, String afternoon, String evening
            insertPlanningRouteDay(planRouteDay);
        }

    }

    /**
     * 添加最超值路线
     */
    @Deprecated
    private void insertPerformance(JSONArray times, String spend, String visitors,
                                   String theme, String age_cls, String city1,
                                   String city2, String journey, String favScesStr,
                                   String favHotelsStr, int userid, JSONObject performanceJSON
    ) {
        Planning planning = new Planning(times.getString(0),
                times.getString(1), spend, visitors, theme,
                age_cls, city1, city2, journey, favScesStr,
                favHotelsStr, userid);
        // 添加规划对象
        int planningId = this.insertPlanning(planning);

        // 添加超值
        int planningRoute2_id = 0;
        if (performanceJSON.getBigDecimal("total_price").intValue() > 0) {
            JSONArray performance_trafficInfos = performanceJSON.getJSONArray("trafficInfo");
            // [0],[1],[2],[3]  ------ flight_content1, flight_content2, train_content1, train_content2
            JSONArray performance_trafficUrls = performanceJSON.getJSONArray("trafficUrl");
            // [0],[1] ------ flight_url1, flight_url2
            PlanningRoute planningRoute2 = new PlanningRoute(planningId,
                    performanceJSON.getBigDecimal("total_price"),
                    performance_trafficInfos.getString(0),
                    performance_trafficUrls.getString(0),
                    performance_trafficInfos.getString(2),
                    performance_trafficInfos.getString(1),
                    performance_trafficUrls.getString(1),
                    performance_trafficInfos.getString(3),
                    2);
            planningRoute2_id = this.insertPlanningRoute(planningRoute2);
        }

        // 添加超值-天信息
        JSONArray performanceDays = performanceJSON.getJSONArray("days");
        for (int i = 0; i < performanceDays.size(); i++) {
            JSONObject day = performanceDays.getJSONObject(i);
            // 上午morning，下午afternoon，晚上evening
            // 拼接相关的ID
            String morningV = jointDayId(day.getJSONArray("morning"));
            String afternoonV = jointDayId(day.getJSONArray("afternoon"));
            String eveningV = jointDayId(day.getJSONArray("evening"));
            String hotelV = jointDayId(day.getJSONArray("hotel"));

            // 获取早餐、午餐、晚餐
            String eat1 = day.getString("eat1");
            String eat2 = day.getString("eat2");
            String eat3 = day.getString("eat3");

            PlanRouteDay planRouteDay = new PlanRouteDay(planningRoute2_id,
                    i + 1, eat1, morningV, eat2,
                    afternoonV, eat3, eveningV, hotelV);
            // int routeId, int dayNum, String morning, String afternoon, String evening
            insertPlanningRouteDay(planRouteDay);
        }
    }

    /**
     * 添加最便宜路线
     */
    @Deprecated
    private void insertCheap(JSONArray times, String spend, String visitors,
                             String theme, String age_cls, String city1,
                             String city2, String journey, String favScesStr,
                             String favHotelsStr, int userid, JSONObject cheapJSON
    ) {
        Planning planning = new Planning(times.getString(0),
                times.getString(1), spend, visitors, theme, age_cls,
                city1, city2, journey, favScesStr, favHotelsStr, userid);
        // 添加规划对象
        int planningId = this.insertPlanning(planning);
        // 添加便宜
        int planningRoute3_id = 0;
        if (cheapJSON.getBigDecimal("total_price").intValue() > 0) {
            JSONArray cheap_trafficInfos = cheapJSON.getJSONArray("trafficInfo");
            // [0],[1],[2],[3]  ------ flight_content1, flight_content2, train_content1, train_content2
            JSONArray cheap_trafficUrls = cheapJSON.getJSONArray("trafficUrl");
            // [0],[1] ------ flight_url1, flight_url2
            PlanningRoute planningRoute3 = new PlanningRoute(planningId,
                    cheapJSON.getBigDecimal("total_price"),
                    cheap_trafficInfos.getString(0),
                    cheap_trafficUrls.getString(0),
                    cheap_trafficInfos.getString(2),
                    cheap_trafficInfos.getString(1),
                    cheap_trafficUrls.getString(1),
                    cheap_trafficInfos.getString(3),
                    3);
            planningRoute3_id = this.insertPlanningRoute(planningRoute3);
        }
        // 添加便宜-天信息
        JSONArray cheapDays = cheapJSON.getJSONArray("days");
        for (int i = 0; i < cheapDays.size(); i++) {
            JSONObject day = cheapDays.getJSONObject(i);
            // 上午morning，下午afternoon，晚上evening
            // 拼接相关的ID
            String morningV = jointDayId(day.getJSONArray("morning"));
            String afternoonV = jointDayId(day.getJSONArray("afternoon"));
            String eveningV = jointDayId(day.getJSONArray("evening"));
            String hotelV = jointDayId(day.getJSONArray("hotel"));

            // 获取早餐、午餐、晚餐
            String eat1 = day.getString("eat1");
            String eat2 = day.getString("eat2");
            String eat3 = day.getString("eat3");

            PlanRouteDay planRouteDay = new PlanRouteDay(planningRoute3_id,
                    i + 1, eat1, morningV, eat2, afternoonV,
                    eat3, eveningV, hotelV);
            // int routeId, int dayNum, String morning, String afternoon, String evening
            insertPlanningRouteDay(planRouteDay);
        }
    }


    /**
     * 筛选出来景区ID和酒店ID
     *
     * @param data Item<String>
     * @return Map<String, Object>
     */
    public Map<String, Object> screeningScenicHotel(List<String> data) {
        List<String> scenicList = new ArrayList<>();
        List<String> hotelList = new ArrayList<>();

        for (String d1s : data) {
            String[] v1s = d1s.split(",");

            for (String v1 : v1s) {
                String[] parts = v1.split("-");
                String value = parts[0].trim();

                if (v1.contains("s")) {
                    scenicList.add(value);
                } else if (v1.contains("h")) {
                    hotelList.add(value);
                }
            }
        }

        Map<String, Object> retMap = new HashMap<>(16);
        retMap.put("scenicList", scenicList);
        retMap.put("hotelList", hotelList);

        return retMap;
    }


    /**
     * 添加规划对象、航班对象、路线（三条）
     */
    @Deprecated
    public void insertWxPlan(int schemeId, JSONObject c, JSONObject p, JSONObject cp) {

        // {"city1":"香港","city2":"东京","time1":"2022-07-05","time2":"2022-07-07"}

        Planning planning = new Planning(cp.getString("time1"),
                cp.getString("time2"), cp.getString("city1"),
                cp.getString("city2"), 2, schemeId);

        // 添加规划对象
        int planningId = this.insertPlanning(planning);

        JSONObject flightGo = p.getJSONObject("flightGoObj");
        JSONObject flightBack = p.getJSONObject("flightBackObj");
        // 添加舒适-1
        PlanningRoute planningRoute1 = new PlanningRoute(planningId,
                flightGo.getString("comfortObj"),
                flightBack.getString("comfortObj"), 1);
        int planningRoute1_id = this.insertPlanningRoute(planningRoute1);
        JSONArray comfortArray = c.getJSONArray("comfortCourse");
        this.insertDayData(comfortArray, planningRoute1_id);

        // 添加超值-2
        PlanningRoute planningRoute2 = new PlanningRoute(planningId,
                flightGo.getString("performanceObj"),
                flightBack.getString("performanceObj"), 2);
        int planningRoute2_id = this.insertPlanningRoute(planningRoute2);
        JSONArray performanceArray = c.getJSONArray("performanceCourse");
        this.insertDayData(performanceArray, planningRoute2_id);

        // 添加便宜-3
        PlanningRoute planningRoute3 = new PlanningRoute(planningId,
                flightGo.getString("cheapObj"),
                flightBack.getString("cheapObj"), 3);
        int planningRoute3_id = this.insertPlanningRoute(planningRoute3);
        JSONArray cheapArray = c.getJSONArray("cheapCourse");
        this.insertDayData(cheapArray, planningRoute3_id);
    }

    // 添加路线的“天”数据到表里面
    @Deprecated
    public void insertDayData(JSONArray dayArray, int planRouteId) {
        for (int i = 0; i < dayArray.size(); i++) {
            JSONObject day = dayArray.getJSONObject(i);
            // 上午morning，下午afternoon，晚上evening
            // 0-上午；1-下午；2-夜景；3-酒店
            String[] datas = wx_getDatajointDayId(day);
            String morningV = datas[0];
            String afternoonV = datas[1];
            String eveningV = datas[2];
            String hotelV = datas[3];
            String eat1 = datas[4];
            String eat2 = datas[5];
            String eat3 = datas[6];

            PlanRouteDay planRouteDay = new PlanRouteDay(planRouteId,
                    i + 1, eat1, morningV, eat2, afternoonV,
                    eat3, eveningV, hotelV);
            // int routeId, int dayNum, String morning, String afternoon, String evening
            insertPlanningRouteDay(planRouteDay);
        }
    }

    /**
     * 检查是否是会员V4(兼容企业会员)
     */
    public boolean checkMemberCardUserV4(Integer uid) {

        // 检查普通会员卡
        QueryWrapper<MemberCardUser> qw = new QueryWrapper<>();
        qw.eq("user_id", uid);
        MemberCardUser cardUser = memberCardUserMapper.selectOne(qw);
//        if (cardUser == null || cardUser.getEndTime() == null) {
//            return false;
//        }
        Date curDate = new Date();
        boolean isMemberCardVip = cardUser != null
                && cardUser.getEndTime() != null
                && cardUser.getEndTime().after(curDate);

        // 检查企业会员卡
        boolean isEnterpriseCardVip = enterpriseCardService.checkEnterpriseCard(uid);

        return isMemberCardVip || isEnterpriseCardVip;
    }

    /**
     * 检查是否是会员V3
     */
    @Deprecated
    public boolean checkMemberCardUserV3(String uid) {

        QueryWrapper<MemberCardUser> qw = new QueryWrapper<>();
        qw.eq("user_id", uid);
        MemberCardUser cardUser = memberCardUserMapper.selectOne(qw);
        if (cardUser == null || cardUser.getEndTime() == null) {
            return false;
        }
        Date curDate = new Date();
        return cardUser.getEndTime().after(curDate);
    }

    @Deprecated
    public JSONObject checkMemberCardUserListV2(String uid) {

        QueryWrapper<MemberCardUser> qw = new QueryWrapper<>();
        qw.eq("user_id", uid);
        List<MemberCardUser> cuList = this.memberCardUserMapper.selectList(qw);

        // 优先判断时间的会员信息；再判断次数的会员信息
        JSONObject retObj = new JSONObject();
        retObj.put("isFlag", false);   // 默认返回不能做攻略
        Date curDate = new Date();
        List<MemberCardUser> cuTimeList = cuList.stream().filter(cu
                -> cu.getCardType() == 2).collect(Collectors.toList());
        for (MemberCardUser cu : cuTimeList) {
            if (cu.getEndTime().after(curDate)) {
                retObj.put("isFlag", true);
                retObj.put("cardType", 2);
                return retObj;
            }
        }

        cuTimeList = cuList.stream().filter(cu -> cu.getCardType() == 1)
                .collect(Collectors.toList());
        for (MemberCardUser cu : cuTimeList) {
            if (cu.getSurplusCt() >= 1) {  // 如果次数卡存在，并且
                retObj.put("isFlag", true);
                retObj.put("cardType", 1);
                retObj.put("cuid", cu.getId());
                return retObj;
            }
        }
        return retObj;
    }

    // 分析用户的会员卡信息，是否可以做攻略
    // 返回 isFlag:true/false; cuid:如果是返回对象ID; cardType:1-次数；2-时间；返回是时间还是次数据
    @Deprecated
    public JSONObject checkMemberCardUserList(List<MemberCardUser> cuList) {
        // 优先判断时间的会员信息；再判断次数的会员信息
        JSONObject retObj = new JSONObject();
        retObj.put("isFlag", false);   // 默认返回不能做攻略
        Date curDate = new Date();
        List<MemberCardUser> cuTimeList = cuList.stream().filter(cu
                -> cu.getCardType() == 2).collect(Collectors.toList());
        for (MemberCardUser cu : cuTimeList) {
            if (cu.getEndTime().after(curDate)) {
                retObj.put("isFlag", true);
                retObj.put("cardType", 2);
                return retObj;
            }
        }

        cuTimeList = cuList.stream().filter(cu
                -> cu.getCardType() == 1).collect(Collectors.toList());
        for (MemberCardUser cu : cuTimeList) {
            if (cu.getSurplusCt() >= 1) {  // 如果次数卡存在，并且
                retObj.put("isFlag", true);
                retObj.put("cardType", 1);
                retObj.put("cuid", cu.getId());
                return retObj;
            }
        }
        return retObj;
    }

    /**
     * 查询方案详情V2
     *
     * @param sid 方案ID
     */
    public Map<String, Object> schemeDetailV2(Integer sid) {

        Map<String, Object> data = new HashMap<>(16);
        JSONObject courseDaysData = new JSONObject();
        courseDaysData.put("cheap", new JSONArray());
        courseDaysData.put("comfort", new JSONArray());
        courseDaysData.put("performance", new JSONArray());

        JSONObject cityData = new JSONObject();
        cityData.put("cheap", new JSONArray());
        cityData.put("comfort", new JSONArray());
        cityData.put("performance", new JSONArray());

        JSONObject priceData = new JSONObject();
        priceData.put("cheap", new JSONObject());
        priceData.put("comfort", new JSONObject());
        priceData.put("performance", new JSONObject());

        List<Map<String, String>> days = new ArrayList<>();

        JSONObject flightObj = new JSONObject();
        JSONObject trainObj = new JSONObject();

        List<PlanningRoute> list = planningRouteMapper.queryListBySid(sid);
        log.info("list长度: {}", list.size());
        if (list.size() != 3) {
            throw new BusinessException("数据异常");
        }

        flightObj.put("from", list.get(0).getFlightFrom());

        String[] levelArr = new String[]{"cheap", "comfort", "performance"};

        QueryWrapper<Planning> planningQw = new QueryWrapper<>();
        planningQw.eq("scheme_id", sid);
        Planning plan = planningMapper.selectOne(planningQw);
        String[] destination = plan.getDestination().split(",");
        for (PlanningRoute route : list) {
            int levelValue = route.getRouteLevel();
            String level = levelArr[levelValue - 1];
            log.info("当前等级: {}", level);
            // =========== 处理交通 ===========
            // 火车
            if (StrUtil.isNotBlank(route.getTrainContent1())
                    || StrUtil.isNotBlank(route.getTrainContent2())) {
                Map<String, JSONArray> curTrainObj = new HashMap<>(16);
                curTrainObj.put("go", JSONArray.parseArray(route.getTrainContent1()));
                curTrainObj.put("back", JSONArray.parseArray(route.getTrainContent2()));
                trainObj.put(level, curTrainObj);
            }
            // 飞机
            if (StrUtil.isNotBlank(route.getFlightContent1())
                    || StrUtil.isNotBlank(route.getFlightContent2())) {
                Map<String, JSONObject> curFlightObj = new HashMap<>(16);
                curFlightObj.put("go", JSONObject.parseObject(route.getFlightContent1()));
                curFlightObj.put("back", JSONObject.parseObject(route.getFlightContent2()));
                flightObj.put(level, curFlightObj);
            }
            // =========== 结束处理交通 ===========

            // =========== 处理预算 ===========
            BigDecimal totalPrice = route.getTotalPrice();
            priceData.put(level, totalPrice);
            // =========== 结束处理预算 ===========

            // =========== 处理攻略 ===========
            BigDecimal hotelPrice = new BigDecimal(0);
            String[] dayTimeArr = DayTimeNameEnums.dayTimeArr();
            List<PlanRouteDay> routeDays = planRouteDayMapper.queryListByRid(route.getId());
            for (PlanRouteDay day : routeDays) {
                // =========== 开始处理一天的流程 ===========
                List<Map<String, Object>> activities = new ArrayList<>(16);
                Map<String, Object> lastScenic = new HashMap<>();
                // 处理三餐和景区
                int scenicIndex = 0;
                int hotelIndex = 0;
                int dayTimeIndex = -1;
                List<String> curDayData = Arrays.asList(day.getEat1(), day.getMorning(), day.getEat2(),
                        day.getAfternoon(), day.getEat3());
                for (String item : curDayData) {
                    if (StrUtil.isBlank(item)) {
                        continue;
                    }
                    if (StrUtil.isNotBlank(item) && item.contains("-s")) {
                        // 景区
                        String[] scenicArr = item.replaceAll("-s", "").split(",");
                        List<Map<String, Object>> scenicList = new ArrayList<>();
                        for (String s : scenicArr) {
                            Map<String, Object> cur = scenicV1Mapper.queryScenic4Detail(s);
                            if (cur != null) {
                                // 记录下标
                                cur.put("index", scenicIndex++);
                                // 记录时间(早中晚)
                                cur.put("time", dayTimeArr[dayTimeIndex]);
                                // 封面图
                                JSONArray picArr = JSONArray.parseArray(String.valueOf(cur.get("pic")));
                                String cover = ConstantUtil.Tourism.DEFAULT_COVER;
                                if (picArr != null && !picArr.isEmpty()) {
                                    cover = picArr.getString(0);
                                }
                                cur.put("cover", cover);
                                cur.remove("pic");
                                lastScenic = cur;
                                scenicList.add(cur);
                            }
                        }
                        activities.addAll(scenicList);
                    } else {
                        // 三餐
                        activities.add(getMealObj(item));
                        ++dayTimeIndex;
                    }
                }

                // 酒店
                String hotelId = day.getHotel().replaceAll("-h", "");
                String hotelObj = day.getHotelObj();
                TourismHotelVo hotelVo = null;
                JSONObject hotel = null;
//                log.info("hotelObj: {}", hotelObj);
                if (hotelObj != null && !"null".equals(hotelObj)) {
                    hotelVo = JSONObject.parseObject(hotelObj, TourismHotelVo.class);
                }
                if (hotelVo != null) {
                    hotelVo.setIndex(hotelIndex);
                    hotelVo.setTime(dayTimeArr[dayTimeIndex]);
                    String price = hotelVo.getAdvicePrice();
                    if (StrUtil.isNotBlank(price)) {
                        hotelPrice = hotelPrice.add(new BigDecimal(price));
                    }
                    activities.add(com.alibaba.fastjson2.JSONObject.from(hotelVo));
                } else {
                    hotel = hotelMapper.queryHotel4Detail(hotelId);
                    if (hotel != null) {
                        // 记录下标
                        hotel.put("index", hotelIndex);
                        hotel.put("time", dayTimeArr[dayTimeIndex]);
                        // 预算
                        String price = hotel.getString("advice_price");
                        if (StrUtil.isNotBlank(price)) {
                            hotelPrice = hotelPrice.add(new BigDecimal(price));
                        }
                        // 封面图
                        JSONArray picArr = JSONArray.parseArray(hotel.getString("pic"));
                        String cover = ConstantUtil.Tourism.DEFAULT_COVER;
                        if (picArr != null && !picArr.isEmpty()) {
                            cover = picArr.getString(0);
                        }
                        hotel.put("cover", cover);
                        activities.add(hotel);
                    }
                }

                // 赋值天数据
                courseDaysData.getJSONArray(level).add(activities);
                // =========== 结束处理一天的流程 ===========

                // =========== 开始处理当天城市 ===========
                List<String> cities = new ArrayList<>();
                if (day.getDayNum() == 1) {
                    cities.add(plan.getStartingCity());
                    cities.add(destination[0]);
                } else if (day.getDayNum() == routeDays.size()) {
                    cities.add(destination[destination.length - 1]);
                    cities.add(plan.getStartingCity());
                } else {
                    if (hotel != null || hotelVo != null) {
                        String city = (hotelVo != null) ? hotelVo.getCity() : hotel.getString("city");
                        cities.add(city);
                    } else {
                        cities.add(String.valueOf(lastScenic.get("city")));
                    }
                }
                cityData.getJSONArray(level).add(cities);
                // =========== 结束处理当天城市 ===========
            }

            // =========== 开始处理预算 ===========
            List<Map<String, Object>> curPriceData = new ArrayList<>();
            try {
                if (hotelPrice.compareTo(totalPrice) < 0) {
                    Map<String, Object> playPrice = new HashMap<>(16);
                    playPrice.put("text", "游玩预算");
                    playPrice.put("budget", totalPrice.subtract(hotelPrice));
                    curPriceData.add(playPrice);
                }
                if (hotelPrice.compareTo(new BigDecimal(0)) > 0) {
                    Map<String, Object> hotelPriceObj = new HashMap<>(16);
                    hotelPriceObj.put("text", "酒店预算");
                    hotelPriceObj.put("budget", hotelPrice);
                    curPriceData.add(hotelPriceObj);
                }
            } catch (Exception e) {
                log.error("方案详情v2 - 预算处理报错");
                log.error(e.getMessage(), e);
                // 考虑如果报错是否默认添加为游玩预算
            }
            priceData.put(level, curPriceData);
            // =========== 结束处理预算 ===========

            // =========== 结束处理攻略 ===========
        }

        // =========== 开始处理服务电话 ===========
        JSONObject serviceTel = new JSONObject();
        try {
            serviceTel = getServiceTel(destination[destination.length - 1]);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        // =========== 结束处理服务电话 ===========

        // =========== 开始处理日期列表 ===========
        Calendar calendar = Calendar.getInstance();
        Calendar endDate = Calendar.getInstance();
        try {
            calendar.setTime(Objects.requireNonNull(DateUtil.parse(plan.getTime1(),
                    "yyyy-MM-dd"))); // 设置起始日期
            endDate.setTime(Objects.requireNonNull(DateUtil.parse(plan.getTime2(),
                    "yyyy-MM-dd"))); // 设置结束日期
        } catch (NullPointerException e) {
            throw new BusinessException("数据异常");
        }

        while (!calendar.after(endDate)) {
            int weekday = calendar.get(Calendar.DAY_OF_WEEK) - 1;
            Map<String, String> cur = new HashMap<>(16);
            cur.put("dayOfWeek", ConstantUtil.MyDate.weekArr[weekday]);
            cur.put("format", DateUtil.format(calendar.getTime(),
                    "yyyy-MM-dd"));
            days.add(cur);
            calendar.add(Calendar.DAY_OF_MONTH, 1); // 日期加1
        }
        // =========== 结束处理日期列表 ===========

        data.put("courseDaysData", courseDaysData);
        data.put("cityData", cityData);
        data.put("priceData", priceData);
        data.put("flightObj", flightObj);
        data.put("trainObj", trainObj);
        data.put("serviceTel", serviceTel);
        data.put("days", days);
        return data;
    }

    /**
     * 查询方案详情
     *
     * @param sid 方案ID
     * @return JSONObject
     */
    public JSONObject schemeDetail(Integer sid) {
        JSONObject data = new JSONObject();
        JSONArray courseDaysMorning = new JSONArray();
        JSONArray courseDaysAfternoon = new JSONArray();
        JSONArray courseDaysEvening = new JSONArray();
        JSONArray cityMorning = new JSONArray();
        JSONArray cityAfternoon = new JSONArray();
        JSONArray cityEvening = new JSONArray();
        List<PlanRouteDay> dayData = planRouteDayMapper.queryDayData(sid);
        int dayCount = dayData.size() / 3;
        int index = 0;
        int plusDay = 0;
        String previousCity = "";
        JSONArray days = new JSONArray();
        long handleScenicTime = 0;

        for (PlanRouteDay item : dayData) {
            JSONArray activities = new JSONArray();
            int dayNum = item.getDayNum();
            if (dayNum == 1) {
                index++;
                plusDay = 0;
            }
            long start1 = System.currentTimeMillis();
            JSONObject scenicObj;
            String[] morning = item.getMorning().replaceAll("-s", "").split(",");
            List<JSONObject> scenicMorning = new ArrayList<>();
            for (String s : morning) {
                scenicObj = getScenicObjV2(scenicV1Mapper.queryById(s));
                if (scenicObj != null) {
                    scenicMorning.add(scenicObj);
                }
            }
            String[] afternoon = item.getAfternoon().replaceAll("-s", "").split(",");
            List<JSONObject> scenicAfternoon = new ArrayList<>();
            for (String s : afternoon) {
                scenicObj = getScenicObjV2(scenicV1Mapper.queryById(s));
                if (scenicObj != null) {
                    scenicAfternoon.add(scenicObj);
                }
            }
            String[] evening = item.getEvening().replaceAll("-s", "").split(",");
            List<JSONObject> scenicEvening = new ArrayList<>();
            for (String s : evening) {
                scenicObj = getScenicObjV2(scenicV1Mapper.queryById(s));
                if (scenicObj != null) {
                    scenicEvening.add(scenicObj);
                }
            }
            long end1 = System.currentTimeMillis();
            handleScenicTime += (end1 - start1);

            String hotelId = item.getHotel().replaceAll("-h", "");
            JSONObject hotel = hotelMapper.queryHotel(hotelId);
            JSONObject plan = planningMapper.queryPlanningInfo(sid);

            activities.add(getMealObj(item.getEat1()));
            activities.addAll(scenicMorning);
            activities.add(getMealObj(item.getEat2()));
            activities.addAll(scenicAfternoon);
            activities.add(getMealObj(item.getEat3()));
            activities.addAll(scenicEvening);
            if (hotel != null) {
                activities.add(getHotelObj(hotel));
            }

            String dateValue = plan.getString("time1");
            dateValue = LocalDate.parse(dateValue).plusDays(plusDay++).toString();
            Date date = DateUtil.parse(dateValue, "yyyy-MM-dd");
            String weekDay = DateUtil.dateToWeek(date);
            JSONObject dayObj = new JSONObject();
            dayObj.put("dayOfWeek", "星期" + weekDay);
            dayObj.put("format", dateValue);
            if (!days.contains(dayObj)) {
                days.add(dayObj);
            }

            List<String> cities = new ArrayList<>();
            String[] destinationCities = plan.getString("destination").split(",");
            if (item.getDayNum() == 1) {
                cities.add(plan.getString("starting_city"));
                cities.add(destinationCities[0]);
            } else if (item.getDayNum() == dayCount) {
                cities.add(destinationCities[destinationCities.length - 1]);
                cities.add(plan.getString("starting_city"));
            } else {
                if (hotel != null) {
                    if (!StrUtil.isEmpty(previousCity)
                            && !Objects.equals(previousCity, hotel.getString("city"))) {
                        cities.add(previousCity);
                    }
                    cities.add(hotel.getString("city"));
                    previousCity = hotel.getString("city");
                } else {
                    if (destinationCities.length == 1) {
                        cities.add(destinationCities[destinationCities.length - 1]);
                    }
                }
            }

            if (index == 1) {
                courseDaysMorning.add(activities);
                cityMorning.add(cities);
            } else if (index == 2) {
                courseDaysEvening.add(activities);
                cityEvening.add(cities);
            } else if (index == 3) {
                courseDaysAfternoon.add(activities);
                cityAfternoon.add(cities);
            }
        }

        JSONObject serviceTel = new JSONObject();
        try {
            String lastCity = cityMorning.getJSONArray(cityMorning.size() - 1)
                    .getString(0);
            serviceTel = getServiceTel(lastCity);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        JSONObject routes = new JSONObject();
        JSONObject fromData = new JSONObject();
        JSONObject toData = new JSONObject();
        JSONObject flightObj = new JSONObject();
        JSONObject flightGo = new JSONObject();
        JSONObject flightBack = new JSONObject();
        String flightFrom = "";
        List<JSONObject> trainData = planningRouteMapper.queryTrainInPlan(sid);
        boolean isXc = Objects.equals("xc", trainData.get(0).getString("flight_from"));
        String[] levelArr = new String[]{"cheap", "comfort", "performance"};
        for (JSONObject item : trainData) {
            // 当前等级
            String level = levelArr[item.getInteger("route_level") - 1];
            // 火车
            JSONArray trainFrom = JSONArray.parseArray(item.getString("trainFrom"));
            JSONArray trainTo = JSONArray.parseArray(item.getString("trainTo"));
            fromData.put(level, trainFrom);
            toData.put(level, trainTo);
            // 飞机
            String flightId = item.getString("flight_id");
            JSONObject curFlightGo = item.getJSONObject("flight_content1");
            JSONObject curFlightBack = item.getJSONObject("flight_content2");
            flightFrom = item.getString("flight_from");
            if (Objects.equals("xc", flightFrom)) {
                flightGo.put(level, curFlightGo);
                flightBack.put(level, curFlightBack);
            } else {
                JSONObject obj = new JSONObject();
                obj.put("go", flightFrom);
                obj.put("back", curFlightBack);
                flightObj.put(level, obj);
                flightObj.put(level + "Id", flightId);
                flightObj.put("legs", item.getString("flight_legs"));
            }
        }
        routes.put("fromData", fromData);
        routes.put("toData", toData);
        if (isXc) {
            flightObj.put("go", flightGo);
            flightObj.put("back", flightBack);
        }
        flightObj.put("from", flightFrom);

        data.put("courseDaysMorning", courseDaysMorning);
        data.put("courseDaysAfternoon", courseDaysAfternoon);
        data.put("courseDaysEvening", courseDaysEvening);
        data.put("cityMorning", cityMorning);
        data.put("cityAfternoon", cityAfternoon);
        data.put("cityEvening", cityEvening);
        data.put("days", days);
        data.put("serviceTel", serviceTel);
        data.put("routes", routes);
        data.put("flightObj", flightObj);

        log.info("景区处理耗时: {}", handleScenicTime);
//        log.info("交通耗时: {}", end2 - start2);
        return data;
    }

    /**
     * 封装三餐数据对象
     *
     * @param eat 文本
     * @return JSONObject
     */
    public JSONObject getMealObj(String eat) {
        JSONObject meal = new JSONObject();
        meal.put("title", eat);
        meal.put("type", "meal");
        meal.put("subtitle", "");
        meal.put("isHighlight", true);
        return meal;
    }

    /**
     * 封装景区数据对象V2
     *
     * @param jsonObject 原对象
     * @return JSONObject
     */
    public JSONObject getScenicObjV2(JSONObject jsonObject) {
        if (Optional.ofNullable(jsonObject).isPresent()) {
            jsonObject.put("title", jsonObject.getString("name"));
            jsonObject.remove("name");
            String lng = jsonObject.get("gps_lng") != null && StrUtil.isNotBlank(jsonObject.getString("gps_lng"))
                    ? jsonObject.getString("gps_lng")
                    : jsonObject.getString("lng");
            jsonObject.put("lng", lng);
            String lat = jsonObject.get("gps_lat") != null && StrUtil.isNotBlank(jsonObject.getString("gps_lat"))
                    ? jsonObject.getString("gps_lat")
                    : jsonObject.getString("lat");
            jsonObject.put("lat", lat);
            jsonObject.put("type", "view");
            jsonObject.put("subtitle", "");
            jsonObject.put("isHighlight", false);

            String cover = ConstantUtil.Tourism.DEFAULT_COVER;
            JSONArray picArr = JSONArray.parseArray(jsonObject.getString("pic"));
            if (picArr != null && !picArr.isEmpty()) {
                cover = picArr.getString(0);
            }
            jsonObject.put("cover", cover);
            jsonObject.remove("pic");
            return jsonObject;
        }

        return null;
    }

    /**
     * 封装景区数据对象V1
     *
     * @param objList    原数据列表
     * @param activities 总数据列表
     * @return JSONArray
     */
    @Deprecated
    public JSONArray getScenicObj(List<JSONObject> objList, JSONArray activities) {

        for (JSONObject obj : objList) {
            if (obj == null) {
                continue;
            }
            JSONObject cur = new JSONObject();
            cur.put("id", obj.getString("id"));
            cur.put("title", obj.getString("name"));
            cur.put("lng", obj.getString("gps_lng") != null ?
                    obj.getString("gps_lng") : obj.getString("lng"));
            cur.put("lat", obj.getString("gps_lat") != null ?
                    obj.getString("gps_lat") : obj.getString("lat"));
            cur.put("country", obj.getString("country"));
            cur.put("city", obj.getString("city"));
            cur.put("address", obj.getString("address"));
            cur.put("type", "view");
            cur.put("subtitle", "");
            cur.put("isHighlight", false);

            String cover = ConstantUtil.Tourism.DEFAULT_COVER;
            JSONArray picArr = JSONArray.parseArray(cur.getString("pic"));
            if (picArr != null && !picArr.isEmpty()) {
                cover = picArr.getString(0);
            }
            cur.put("cover", cover);
            cur.remove("pic");
            activities.add(cur);
        }
        return activities;
    }

    /**
     * 封装酒店数据对象
     *
     * @param obj 原对象
     * @return JSONObject
     */
    public JSONObject getHotelObj(JSONObject obj) {
        if (StrUtil.isNotBlank(obj.getString("gps_lng"))) {
            obj.put("lng", obj.getString("gps_lng"));
        }
        if (StrUtil.isNotBlank(obj.getString("gps_lat"))) {
            obj.put("lat", obj.getString("gps_lat"));
        }
        obj.put("type", "hotel");
        obj.put("subtitle", "");
        obj.put("isHighlight", false);
        String cover = ConstantUtil.Tourism.DEFAULT_COVER;
        JSONArray picArr = JSONArray.parseArray(obj.getString("pic"));
        if (picArr != null && !picArr.isEmpty()) {
            cover = picArr.getString(0);
        }
        obj.put("cover", cover);
        return obj;
    }

    /**
     * 查询免费模式剩余次数
     *
     * @param uid 用户ID
     * @return Integer 次数
     */
    public Integer getFreeModeTimes(Integer uid) {
        String date = DateUtil.format(new Date(), "YYYYMMdd");
        log.info("real_date: {}", DateUtil.format(new Date(), "YYYY-MM-dd hh:mm:ss"));
        log.info("date: {}", date);
        // 从redis读取剩余次数
        String times = redisService.getString(String.format("freeMode:%s:%s", uid, date));
        log.info("redis中剩余次数: {}", times);
        // 根据是否为免费模式做不同处理
        if (isFreeMode) {
            // redis中数据不存在或次数小于1时校验数据库数据
            if (times == null || Integer.parseInt(times) < 1) {
                times = "0";
                Integer countToday = planSchemeMapper.queryCountToday(uid);
                log.info("今日实际查询次数: {}", countToday);
                // 如数据不一致，纠正redis剩余次数
                if ((freeModeLimit - countToday) == 0) {
                    times = Integer.toString(0);
                    redisService.setString("freeMode:" + uid + ":" + date,
                            times, 60 * 60 * 24);
                }
            }
        } else {
            if (times == null) {
                times = Integer.toString(freeModeLimit);
                redisService.setString("freeMode:" + uid + ":" + date,
                        times, 60 * 60 * 24);
            }
        }
        return Integer.parseInt(times);
    }

    /**
     * 处理免费模式（减少次数）
     *
     * @param uid 用户ID
     */
    public void handleFreeMode(Integer uid) {
        String date = DateUtil.format(new Date(), "YYYYMMdd");
        String times = redisService.getString(String.format("freeMode:%s:%s", uid, date));
        if (times != null && Integer.parseInt(times) > 0) {
            times = Integer.toString(Integer.parseInt(times) - 1);
            redisService.setString(String.format("freeMode:%s:%s", uid, date),
                    times, 60 * 60 * 24);
        }
    }

}