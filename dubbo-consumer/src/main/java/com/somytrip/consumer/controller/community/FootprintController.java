package com.somytrip.consumer.controller.community;

import com.somytrip.api.service.community.FootprintService;
import com.somytrip.consumer.controller.BaseController;
import com.somytrip.entity.dto.community.footprint.FootprintQueryCitiesParam;
import com.somytrip.entity.dto.hotel.PaginationDto;
import com.somytrip.entity.resp.community.FootprintCityListResp;
import com.somytrip.entity.resp.community.FootprintCountryListResp;
import com.somytrip.entity.response.ResponseResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.controller.community
 * @className: FootprintController
 * @author: shadow
 * @description: 足迹Controller
 * @date: 2024/12/31 10:47
 * @version: 1.0
 */
@RestController
@RequestMapping("/api/community/footprint")
public class FootprintController extends BaseController {

    @DubboReference(check = false)
    private FootprintService footprintService;

    /**
     * 记录足迹
     *
     * @param cityCode   城市code
     * @param recordTime 记录时间
     * @return com.somytrip.entity.response.ResponseResult<java.lang.Void>
     * <AUTHOR>
     * @date 2024/12/31 11:50
     */
    @GetMapping("/record")
    public ResponseResult<Void> recordFootprint(@RequestParam("cityCode") String cityCode,
                                                @RequestParam("recordTime") LocalDateTime recordTime) {

        String uid = getUserId();
        return footprintService.recordFootprint(cityCode, recordTime, uid);
    }

    /**
     * 查询国家记录
     *
     * @param pagination 分页参数
     * @return com.somytrip.entity.response.ResponseResult<com.somytrip.entity.resp.community.FootprintCountryListResp>
     * <AUTHOR>
     * @date 2025/1/2 15:27
     */
    @GetMapping("/countries")
    public ResponseResult<FootprintCountryListResp> queryCountries(@ModelAttribute PaginationDto pagination) {

        String uid = getUserId();
        return footprintService.queryCounties(uid, pagination);
    }

    /**
     * 查询城市记录
     *
     * @param param 查询参数
     * @return com.somytrip.entity.response.ResponseResult<com.somytrip.entity.resp.community.FootprintCityListResp>
     * <AUTHOR>
     * @date 2025/1/3 11:01
     */
    @GetMapping("/cities")
    public ResponseResult<FootprintCityListResp> queryCities(@ModelAttribute FootprintQueryCitiesParam param) {

        String uid = getUserId();
        param.setUid(uid);
        return footprintService.queryCities(param);
    }

    /**
     * 清空足迹记录
     *
     * @return com.somytrip.entity.response.ResponseResult<java.lang.Boolean>
     * <AUTHOR>
     * @date 2025/1/3 17:56
     */
    @GetMapping("/clear")
    public ResponseResult<Boolean> clear() {

        String uid = getUserId();
        return footprintService.clear(uid);
    }
}
