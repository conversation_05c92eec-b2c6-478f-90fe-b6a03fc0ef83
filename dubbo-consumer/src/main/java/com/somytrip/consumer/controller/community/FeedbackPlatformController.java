package com.somytrip.consumer.controller.community;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.somytrip.api.service.GeoMapService;
import com.somytrip.api.service.community.FeedbackPlatformService;
import com.somytrip.consumer.controller.BaseController;
import com.somytrip.consumer.util.CommonUtils;
import com.somytrip.entity.dto.community.feedback.FeedbackCommentDto;
import com.somytrip.entity.dto.community.feedback.FeedbackCommentListReq;
import com.somytrip.entity.dto.community.feedback.FeedbackPlatformDto;
import com.somytrip.entity.dto.community.feedback.FeedbackPlatformListReq;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.community.FeedbackCommentListRes;
import com.somytrip.entity.vo.community.FeedbackPlatformListRes;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.consumer.controller.community
 * @className: FeedbackPlatformController
 * @author: lijunqi
 * @description: 反馈平台控制器
 * @date: 2025/8/4 15:39
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/community/feedback")
public class FeedbackPlatformController extends BaseController {

    @DubboReference
    private FeedbackPlatformService feedbackPlatformService;


    @Resource
    private GeoMapService geoMapService;

    /**
     * 提交反馈建议
     *
     * @param dto     提交参数
     * @param request HTTP请求
     * @return 提交结果
     */
    @PostMapping("/submit")
    public ResponseResult<Long> submitFeedback(@Validated @RequestBody FeedbackPlatformDto dto,
                                               @RequestHeader HttpServletRequest request) {

        log.info("提交反馈建议请求，参数：{}", JSON.toJSONString(dto));

        String userStr = this.getUserId();
        if (StrUtil.isBlank(userStr)) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }

        try {
            Long userId = Long.valueOf(getUserId());
            dto.setUserId(userId);
            // 获取客户端IP和地理位置
            String clientIp = CommonUtils.getClientIp(request);
            try {
                dto.setIpAddress(clientIp);
                JSONObject jsonObject = JSONObject.parseObject(geoMapService.aMapIpResult(clientIp));
                String cityName = jsonObject.getString("city");
                String provinceName = jsonObject.getString("province");
                //获取不到地址 默认广东：深圳
                if (StrUtil.isBlank(cityName)) {
                    cityName = "深圳";
                }
                if (StrUtil.isBlank(provinceName)) {
                    provinceName = "广东";
                }
                dto.setIpCityName(cityName);
                dto.setIpProvinceName(provinceName);
            } catch (Exception e) {
                dto.setIpCityName("深圳");
                dto.setIpProvinceName("广东");
                log.error("getIpAddressCity error:{}", e.getMessage(), e);
            }
            // 提交反馈
            Long id = feedbackPlatformService.submitFeedback(dto);

            log.info("反馈建议提交成功，ID：{}", id);
            return ResponseResult.ok(id);
        } catch (Exception e) {
            log.error("提交反馈建议失败", e);
            return ResponseResult.fail("api.feedback.platform.submit-fail");
        }
    }
    /**
     * 发布反馈评论
     *
     * @param dto     评论参数
     * @param request HTTP请求
     * @return 评论ID
     */
    @PostMapping("/comment/publish")
    public ResponseResult<Long> publishComment(@Validated @RequestBody FeedbackCommentDto dto,
                                               @RequestHeader HttpServletRequest request) {

        log.info("发布反馈评论请求，参数：{}", JSON.toJSONString(dto));

        String userStr = this.getUserId();
        if (StrUtil.isBlank(userStr)) {
            return ResponseResult.fail(401, "api.user.token.token-not-resolve-user");
        }

        try {
            Long userId = Long.valueOf(getUserId());
            dto.setUserId(userId);

            // 获取客户端IP和地理位置
            String clientIp = CommonUtils.getClientIp(request);
            try {
                dto.setIpAddress(clientIp);
                JSONObject jsonObject = JSONObject.parseObject(geoMapService.aMapIpResult(clientIp));
                String cityName = jsonObject.getString("city");
                String provinceName = jsonObject.getString("province");
                //获取不到地址 默认广东：深圳
                if (StrUtil.isBlank(cityName)) {
                    cityName = "深圳";
                }
                if (StrUtil.isBlank(provinceName)) {
                    provinceName = "广东";
                }
                dto.setIpCityName(cityName);
                dto.setIpProvinceName(provinceName);
            } catch (Exception e) {
                dto.setIpCityName("深圳");
                dto.setIpProvinceName("广东");
                log.error("getIpAddressCity error:{}", e.getMessage(), e);
            }

            // 发布评论
            Long commentId = feedbackPlatformService.publishComment(dto);

            log.info("反馈评论发布成功，评论ID：{}", commentId);
            return ResponseResult.ok(commentId);
        } catch (Exception e) {
            log.error("发布反馈评论失败", e);
            return ResponseResult.fail("api.feedback.platform.comment.publish-fail");
        }
    }

    /**
     * 查询反馈列表
     *
     * @param req 查询请求参数
     * @return 反馈列表
     */
    @GetMapping("/list")
    public ResponseResult<FeedbackPlatformListRes> getFeedbackList(@Validated FeedbackPlatformListReq req) {
        log.info("查询反馈列表请求，参数：{}", JSON.toJSONString(req));

        try {
            // 设置当前用户ID
            String userStr = this.getUserId();
            if (StrUtil.isNotBlank(userStr)) {
                req.setUserId(Long.valueOf(userStr));
            }

            // 查询反馈列表
            FeedbackPlatformListRes response = feedbackPlatformService.getFeedbackList(req);

            log.info("查询反馈列表成功，总记录数：{}", response.getTotal());
            return ResponseResult.ok(response);
        } catch (Exception e) {
            log.error("查询反馈列表失败", e);
            return ResponseResult.fail("api.feedback.platform.list-fail");
        }
    }


    /**
     * 查询反馈留言列表
     *
     * @param req 查询请求参数
     * @return 留言列表
     */
    @GetMapping("/comment/list")
    public ResponseResult<FeedbackCommentListRes> getFeedbackCommentList(@Validated FeedbackCommentListReq req) {
        log.info("查询反馈留言列表请求，参数：{}", JSON.toJSONString(req));

        try {
            // 设置当前用户ID
            String userStr = this.getUserId();
            if (StrUtil.isNotBlank(userStr)) {
                req.setUserId(Long.valueOf(userStr));
            }

            // 查询留言列表
            FeedbackCommentListRes response = feedbackPlatformService.getFeedbackCommentList(req);

            log.info("查询反馈留言列表成功，反馈ID：{}, 总记录数：{}", req.getFeedbackId(), response.getTotal());
            return ResponseResult.ok(response);
        } catch (Exception e) {
            log.error("查询反馈留言列表失败", e);
            return ResponseResult.fail("api.feedback.platform.comment.list-fail");
        }
    }


}
