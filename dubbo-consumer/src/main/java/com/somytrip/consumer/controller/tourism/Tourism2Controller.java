package com.somytrip.consumer.controller.tourism;

import com.somytrip.api.service.tourism.GlobalCityService;
import com.somytrip.consumer.mapper.GlobalCityChinaMapper;
import com.somytrip.consumer.mapper.GlobalCityMapper;
import com.somytrip.entity.dto.city.CityDto;
import com.somytrip.entity.dto.city.QueryLatLngCity;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.utils.JSONResultUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/api/tourism2")
public class Tourism2Controller {
    @Resource
    private GlobalCityMapper globalCityMapper;
    @Resource
    private GlobalCityChinaMapper globalCityChinaMapper;
    @Resource
    private GlobalCityService globalCityService;

    @RequestMapping("/city-config/v1")
    public ResponseResult cityConfig() {
        return JSONResultUtils.success(globalCityService.allV2());
    }

    @PostMapping("/query-city/info")
    public ResponseResult<CityDto> queryLatLngCity(@Validated @RequestBody QueryLatLngCity vo) {
        return globalCityService.queryLatLngCity(vo);
    }

    // 查询出发地
    @RequestMapping("/queryDepartCities")
    public Map<String, Object> queryDepartCities() {
        log.info("进来queryDepartCities了");
        Map<String, Object> map = new HashMap<>(5);
        map.put("success", true);
        map.put("data", this.globalCityChinaMapper.queryDepartCities());
        return map;
    }


    // 查询所有的目的地
    @RequestMapping("queryDestinations")
    public Map<String, Object> queryCities() {
        log.info("进来queryCities了");
        Map<String, Object> map = new HashMap<>(16);
        map.put("success", true);
        map.put("data", this.globalCityMapper.queryCities());
        return map;
    }

    // 查询用户是否是会员，是否允许查询路线
    public Object checkCourse(HttpServletRequest request) {
        request.getParameter("city1");
        request.getParameter("city2");
        // 查询两个城市的市、省、国
        return null;
    }
}
