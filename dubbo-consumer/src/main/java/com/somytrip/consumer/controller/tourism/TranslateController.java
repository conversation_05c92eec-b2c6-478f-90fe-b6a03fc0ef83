package com.somytrip.consumer.controller.tourism;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.somytrip.consumer.config.TencentApiConfig;
import com.somytrip.consumer.config.WebClientConfig;
import com.somytrip.consumer.request.BaiduRequestService;
import com.somytrip.consumer.service.RedisService;
import com.somytrip.consumer.util.AccessTokenUtils;
import com.somytrip.entity.response.ResponseResult;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.tmt.v20180321.TmtClient;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateRequest;
import com.tencentcloudapi.tmt.v20180321.models.TextTranslateResponse;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/baiduTranslate")
@Slf4j
public class TranslateController {
    private final AccessTokenUtils accessTokenUtils = AccessTokenUtils.builder()
            .okHttpClient(new OkHttpClient())
            .build();
    @Value("${baidu.translate.apiKey}")
    private String APIKEY;
    @Value("${baidu.translate.secretKey}")
    private String SECRETKEY;
    @Value("${info.center.control.url}")
    private String CENTER_CONTROL_URL;
    @Resource
    private WebClientConfig webClientConfig;
    @Resource
    private RedisService redisService;
    private BaiduRequestService baiduRequestService;

    @Resource
    private TencentApiConfig tencentApiConfig;

    private TmtClient tmtClient;


    @PostConstruct
    public void init() {
        this.baiduRequestService = webClientConfig.createService(BaiduRequestService.class);
        initTencentTmtClient();
    }

    public void initTencentTmtClient() {
        Credential cred = new Credential(tencentApiConfig.getOcr().secretId(),
                tencentApiConfig.getOcr().secretKey());
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("tmt.tencentcloudapi.com");
        // 实例化一个client选项，可选的，没有特殊需求可以跳过
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        // 实例化要请求产品的client对象,clientProfile是可选的
        tmtClient = new TmtClient(cred, tencentApiConfig.getOcr().region(), clientProfile);
    }

    @PostMapping("/textTrans")
    public ResponseResult<Map<String, JSONArray>> textTrans(@Validated @RequestBody TextTranslateVo vo) {
        try {
            TextTranslateRequest req = new TextTranslateRequest();
            req.setTarget(vo.getTo());
            req.setSource(vo.getFrom());
            req.setSourceText(vo.getQ());
            req.setProjectId(0L);
            Map<String, JSONArray> result = new HashMap<>(1);
            JSONArray transResult = new JSONArray();
            TextTranslateResponse resp = tmtClient.TextTranslate(req);
            transResult.add(MapUtil.builder("dst", resp.getTargetText())
                    .put("src", vo.getQ())
                    .build());
            result.put("trans_result", transResult);
            return ResponseResult.ok(result);
        } catch (Exception e) {
            log.error("textTrans: {}", JSON.toJSONString(vo), e);
            return ResponseResult.fail(500, "api.translate.up.error");
        }
    }

    @PostMapping("/speechTrans")
    public ResponseResult<Object> speechTrans(@RequestParam("voice") MultipartFile voice, HttpServletRequest request) {
        // 请求url
        JSONObject jsonObject = new JSONObject();
        try {
            Base64.Encoder encoder = Base64.getEncoder();
            jsonObject.put("from", request.getParameter("from"));
            jsonObject.put("to", request.getParameter("to"));
            jsonObject.put("format", request.getParameter("format"));
            log.info("speechTrans params: {}", jsonObject);
            jsonObject.put("voice", encoder.encodeToString(voice.getBytes()));
            jsonObject = baiduRequestService.speechTranslate(getAccessToken2(), jsonObject);
            log.info("speechTrans response: {}", jsonObject);
            if (!jsonObject.containsKey("error_msg")) {
                return ResponseResult.ok(jsonObject.get("result"));
            }
            jsonObject.remove("voice");
            jsonObject.put("result", "翻译出错啦~~");
        } catch (Exception e) {
            jsonObject.remove("voice");
            jsonObject.put("result", "翻译出错啦~~");
            log.error("speech error", e);
        }
        return ResponseResult.fail(500, "api.tools.translation-error", jsonObject);
    }

    @PostMapping("/picTrans")
    public ResponseResult<JSONObject> picTrans(@RequestParam("image") MultipartFile image, @RequestParam("to") String to) {
        // 请求url
        JSONObject jsonObject = new JSONObject();
        try {
            long maxImageSize = 4 * 1024 * 1024;
            if (image != null && image.getSize() <= maxImageSize) {
                jsonObject = baiduRequestService.imageTranslate(image, to, "auto", 3, getAccessToken2());
                log.info("picTrans response: {}", jsonObject);
                if (jsonObject.getInteger("error_code") != 0) {
                    throw new Exception(jsonObject.getString("error_msg"));
                }
            } else {
                jsonObject.put("result", image == null ? "图片不能为空~~" : "图片大小不能超过4M~~");
            }
        } catch (Exception e) {
            jsonObject.put("result", "翻译出错啦~~");
            log.error("picTrans error", e);
        }
        return ResponseResult.ok(jsonObject);
    }

    public String getAccessToken2() {
        final String key = "baidu_translate_key";
        String value = redisService.getString(key);
        if (StrUtil.isBlank(value)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("grant_type", "client_credentials");
            jsonObject.put("client_id", APIKEY);
            jsonObject.put("client_secret", SECRETKEY);
            String s = accessTokenUtils.baiduTranslateAccessToken(CENTER_CONTROL_URL, jsonObject.toJSONString());
            redisService.set(key, s, 600);
            return s;
        }
        return value;
    }

    @Data
    @Valid
    public static class TextTranslateVo {
        @NotBlank(message = "")
        private String q;
        @NotBlank
        private String from;
        @NotBlank
        private String to;
        private String termIds;
    }
}