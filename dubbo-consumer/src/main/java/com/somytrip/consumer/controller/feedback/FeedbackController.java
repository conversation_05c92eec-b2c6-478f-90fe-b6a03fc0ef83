package com.somytrip.consumer.controller.feedback;

import cn.hutool.core.util.StrUtil;
import com.somytrip.api.service.feedback.FeedbackPlatformService;
import com.somytrip.consumer.controller.BaseController;
import com.somytrip.entity.dto.feedback.SubmitFeedbackDto;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.feedback.SubmitFeedbackVo;
import com.somytrip.exception.BusinessException;
import com.somytrip.utils.IpUtils;
import com.somytrip.utils.JSONResultUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.controller.feedback
 * @className: FeedbackController
 * @author: li<PERSON><PERSON>
 * @description: 反馈控制器
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/api/feedback")
public class FeedbackController extends BaseController {

    @Resource
    private FeedbackPlatformService feedbackPlatformService;

    /**
     * 提交反馈建议
     *
     * @param dto     提交反馈建议请求DTO
     * @param request HTTP请求对象
     * @return 响应结果
     */
    @PostMapping("/submit")
    public ResponseResult<SubmitFeedbackVo> submitFeedback(@Valid @RequestBody SubmitFeedbackDto dto, 
                                                          HttpServletRequest request) {
        try {
            // 获取用户ID
            String userId = getUserId();
            if (StrUtil.isBlank(userId)) {
                return JSONResultUtils.fail401();
            }

            // 获取用户IP
            String userIp = IpUtils.getClientIpAddress(request);

            // 提交反馈
            SubmitFeedbackVo result = feedbackPlatformService.submitFeedback(userId, dto, userIp);

            log.info("用户 {} 提交反馈成功，反馈ID: {}", userId, result.getFeedbackId());
            
            return JSONResultUtils.success("反馈提交成功", result);
            
        } catch (BusinessException e) {
            log.warn("提交反馈失败: {}", e.getMessage());
            return JSONResultUtils.fail(e.getMessage());
        } catch (Exception e) {
            log.error("提交反馈异常", e);
            return JSONResultUtils.fail("系统异常，请稍后重试");
        }
    }

    /**
     * 点赞反馈
     *
     * @param feedbackId 反馈ID
     * @return 响应结果
     */
    @PostMapping("/support/{feedbackId}")
    public ResponseResult<Void> supportFeedback(@PathVariable Long feedbackId) {
        try {
            // 获取用户ID（可选，用于记录日志）
            String userId = getUserId();

            // 增加支持数
            Boolean result = feedbackPlatformService.increaseSupportCount(feedbackId);
            
            if (result) {
                log.info("用户 {} 点赞反馈 {} 成功", userId, feedbackId);
                return JSONResultUtils.success("点赞成功");
            } else {
                return JSONResultUtils.fail("点赞失败，反馈不存在或已删除");
            }
            
        } catch (BusinessException e) {
            log.warn("点赞反馈失败: {}", e.getMessage());
            return JSONResultUtils.fail(e.getMessage());
        } catch (Exception e) {
            log.error("点赞反馈异常", e);
            return JSONResultUtils.fail("系统异常，请稍后重试");
        }
    }


}
