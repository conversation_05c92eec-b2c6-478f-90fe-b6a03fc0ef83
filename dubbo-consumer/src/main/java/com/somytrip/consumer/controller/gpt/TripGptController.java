package com.somytrip.consumer.controller.gpt;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import com.somytrip.api.service.XqService;
import com.somytrip.consumer.controller.BaseController;
import com.somytrip.entity.dto.xiaoqi.XiaoqiShareRecordDTO;
import com.somytrip.entity.proto.xq.ProtoXqQuery;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.utils.JSONResultUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-06-12 17:21
 */
@CrossOrigin
@RestController
@RequestMapping("/api/gpt")
@Tag(name = "TripGpt API接口目录")
@Slf4j
public class TripGptController extends BaseController {
    //    @Resource
//    private TripGptService tripGptService;
    @DubboReference(check = false)
    private XqService xqService;


//    @PostMapping(value = "/auth-test-sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
//    @ResponseBody
//    public Flux<String> authSseTest(@RequestBody @Validated QuestionRequest.Req req) {
//        return tripGptService.sseAuthTest(req, this.getUserId());
//    }
//
//    @PostMapping(value = "/qianfan-sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
//    @ResponseBody
//    public Flux<String> sseChatStream(@RequestBody @Validated QuestionRequest.Req req) {
//        return tripGptService.chatStream(req, this.getUserId());
//    }
//
//    @GetMapping(value = "/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
//    @ResponseBody
//    public Flux<String> hello() {
//        return tripGptService.sseTest();
//    }
//
//    @PostMapping("/message-list")
//    public ResponseResult getUserSessionMessages(
//            @RequestBody @Validated MessageRequest.Req req
//    ) {
//        req.setUid(this.getUserId());
//        return tripGptService.getUserSessionMessages(req);
//    }
//
//    @PostMapping("/session-list")
//    public ResponseResult getUserSessions(
//            @RequestBody @Validated SessionRequest.Req req
//    ) {
//        req.setUid(this.getUserId());
//        return tripGptService.getUserSessions(req);
//    }
//
//    @PutMapping("/crate-session/{uid}")
//    public ResponseResult crateSession(@PathVariable("uid") String uid) {
//        if (!StrUtil.equals(uid, this.getUserId())) {
//            return JSONResultUtils.fail403();
//        }
//        return tripGptService.createSession(uid);
//    }

    @GetMapping("/session-list/v3")
    public ResponseResult getUserSessionsV3(@RequestParam(value = "likeFlag", required = false, defaultValue = "0") Integer likeFlag) throws InvalidProtocolBufferException {
        String userId = this.getUserId();
        log.info("uid: {}", userId);
        ProtoXqQuery.ProtoQuerySession.Rsp rsp = xqService.querySessionsByUid(ProtoXqQuery.ProtoQuerySession.Vo.newBuilder()
                .setUid(userId)
                .setLikeFlag(likeFlag)
                .build());
        return JSONResultUtils.success(JSON.parse(JsonFormat.printer().print(rsp)));
    }

    @GetMapping("/message-list/v3")
    public ResponseResult getUserSessionMessagesV3(@RequestParam(value = "sessionId") String sessionId) {
        String userId = this.getUserId();
        log.info("message-list uid: {}, {}", userId, sessionId);
        return xqService.queryMessagesBySessionId(userId, sessionId);
    }

    @GetMapping("/message-list/v4")
    public ResponseResult getUserSessionMessagesV4(@RequestParam(value = "sessionId") String sessionId) throws InvalidProtocolBufferException {
        String userId = this.getUserId();
        log.info("message-list uid: {}, {}", userId, sessionId);
        ProtoXqQuery.ProtoQueryMessages.Rsp rsp = xqService.queryMessagesBySessionIdV2(ProtoXqQuery.ProtoQueryMessages.Vo.newBuilder()
                .setChatId(sessionId)
                .setUid(userId)
                .build());
        if (rsp == null || CollectionUtil.isEmpty(rsp.getItemsList())) {
            return JSONResultUtils.success(new JSONArray());
        }
        JSONArray result = new JSONArray();
        JSON.parseObject(JsonFormat.printer().print(rsp)).getJSONArray("items").forEach(item -> {
            JSONObject it = (JSONObject) item;
            it.put("data", JSON.parse(it.getString("data")));
            result.add(it);
        });
        return JSONResultUtils.success(result);
    }

    @PutMapping("/crate-session/v3/{uid}")
    public ResponseResult crateSessionV3(@PathVariable("uid") String uid) {
        if (!StrUtil.equals(uid, this.getUserId())) {
            return JSONResultUtils.fail403();
        }
        return xqService.createSession(uid);
    }

    @GetMapping("/session/like")
    public ResponseResult like(@RequestParam("uid") String uid,
                               @RequestParam("sessionId") String sessionId,
                               @RequestParam("likeFlag") int likeFlag) {
        if (!StrUtil.equals(uid, this.getUserId())) {
            return JSONResultUtils.fail403();
        }
        return xqService.like(sessionId, uid, likeFlag);
    }

    /**
     * 终止会话输出
     *
     * @param uid
     * @param operationId
     * @return
     */
    @RequestMapping("/session/terminate")
    public ResponseResult terminate(@RequestParam("uid") String uid,
                                    @RequestParam("operationId") String operationId) {
        if (!StrUtil.equals(uid, this.getUserId())) {
            return JSONResultUtils.fail403();
        }
        xqService.terminate(uid, operationId);
        return JSONResultUtils.success();
    }

    /**
     * 分享保存记录
     *
     * @param xiaoqiShareRecordDTO
     * @return
     */
    @RequestMapping("/session/share-record")
    public ResponseResult shareRecordSave(@RequestBody @Valid XiaoqiShareRecordDTO xiaoqiShareRecordDTO) {
        return xqService.shareRecordSave(xiaoqiShareRecordDTO);
    }

    /**
     * 获取分享记录信息
     *
     * @param shareId
     * @return
     */
    @RequestMapping("/session/getshareinfo")
    public ResponseResult getshareinfo(@RequestParam("shareId") String shareId) {
        return xqService.getshareinfo(shareId);
    }

    /**
     * 分享保存记录
     *
     * @param xiaoqiShareRecordDTO
     * @return
     */
    @RequestMapping("/session/getshareinfov2")
    public ResponseResult getshareinfov2(@RequestBody @Valid XiaoqiShareRecordDTO xiaoqiShareRecordDTO) {

        return xqService.getshareinfoV2(xiaoqiShareRecordDTO);
    }

    /**
     * 小奇分享内容新建会话
     *
     * @param xiaoqiShareRecordDTO
     * @return
     */
    @RequestMapping("/session/share-create-session")
    public ResponseResult shareCreateSession(@RequestBody @Valid XiaoqiShareRecordDTO xiaoqiShareRecordDTO) {
        String uid = this.getUserId();
        if (StrUtil.isBlank(uid)) {
            return JSONResultUtils.fail401();
        }
        xiaoqiShareRecordDTO.setUid(uid);
        return xqService.shareCreateSession(xiaoqiShareRecordDTO);
    }
}
