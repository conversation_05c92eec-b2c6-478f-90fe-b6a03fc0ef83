package com.somytrip.consumer.controller.join;

import com.alibaba.fastjson2.JSONObject;
import com.somytrip.api.service.other.JoinOtherMixService;
import com.somytrip.entity.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/resource")
public class HotelResourceController {

    @DubboReference
    private JoinOtherMixService joinOtherMixService;


    /**
     * 获取房型数据
     */
    @PostMapping("get_hotel_layout")
    public JSONObject getHotelLayout() {
        return joinOtherMixService.getHotelLayout();
    }

    /**
     * 获取设施数据
     */
    @PostMapping("get_hotel_facility")
    public JSONObject getHotelFacility() {
        return joinOtherMixService.getHotelFacility();
    }

    /**
     * 获取语言数据
     */
    @PostMapping("get_language")
    public JSONObject getLanguage() {
        return joinOtherMixService.getLanguage();
    }

    /**
     * 获取热门设施
     */
    @PostMapping("get_hot_facility")
    public JSONObject getHotFacility() {
        return joinOtherMixService.getHotFacility();

    }

    /**
     * 获取热门问题
     */
    @RequestMapping("get_hot_question")
    public JSONObject getHotQuestion() {
        return joinOtherMixService.getHotQuestion();
    }

    /**
     * 获取用户名
     */
    @PostMapping("get_username")
    public ResponseResult getUsername(@RequestParam("uid") String uid) {
        return joinOtherMixService.getUsername(uid);
    }

    /**
     * 获取城市列表
     */
    @GetMapping("/get_city_data")
    public ResponseResult getLocationData() {
        return joinOtherMixService.getLocationData();
    }
}
