package com.somytrip.consumer.controller.tourism;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.somytrip.api.service.user.SomytripGuestUserService;
import com.somytrip.api.service.user.UserOperationService;
import com.somytrip.api.service.user.UserV2Service;
import com.somytrip.constant.HttpHeadConstant;
import com.somytrip.constant.UserSm4Constant;
import com.somytrip.consumer.controller.BaseController;
import com.somytrip.consumer.service.ConnectThirdService;
import com.somytrip.consumer.service.LoginService;
import com.somytrip.consumer.service.SomytripUserService;
import com.somytrip.consumer.service.impl.SmsServiceImpl;
import com.somytrip.consumer.util.CommonUtils;
import com.somytrip.entity.dto.common.VerificationCodeDto;
import com.somytrip.entity.dto.user.GuestUserDto;
import com.somytrip.entity.dto.user.SomytripUsers;
import com.somytrip.entity.enums.user.LoginMethodEnum;
import com.somytrip.entity.enums.user.ResetFieldEnum;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.PhoneMessageVo;
import com.somytrip.entity.vo.user.*;
import com.somytrip.exception.BusinessException;
import com.somytrip.utils.JSONResultUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/user")
@CrossOrigin
@Slf4j
public class UserController extends BaseController {
    @Resource
    private ConnectThirdService thirdService;
    @Resource
    private SomytripUserService somytripUserService;
    @Resource
    private UserV2Service userV2Service;
    @Resource
    private LoginService loginService;
    @Resource
    private SmsServiceImpl smsService;
    @Resource
    private SomytripGuestUserService somytripGuestUserService;
    @Resource
    private UserOperationService userOperationService;

    @GetMapping("/v3/config/all")
    public ResponseResult<JSONObject> configList() {
        return userOperationService.configs(LocaleContextHolder.getLocale().toString());
    }

    @PostMapping("/v3/generate/reset-password/email")
    public ResponseResult<Boolean> generateResetPasswordEmail() {
        UserOpVo<Void> userOpVo = new UserOpVo<>();
        userOpVo.setUid(getUserId());
        userOpVo.setLoginMethod(LoginMethodEnum.EMAIL);
        userOpVo.setLocale(LocaleContextHolder.getLocale());
        userOpVo.setServiceUse(true);
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.generateMessage(userOpVo);
    }

    @PostMapping("/v3/generate/reset-password/phone")
    public ResponseResult<Boolean> generateResetPasswordPhone() {
        UserOpVo<Void> userOpVo = new UserOpVo<>();
        userOpVo.setUid(getUserId());
        userOpVo.setLoginMethod(LoginMethodEnum.PHONE);
        userOpVo.setLocale(LocaleContextHolder.getLocale());
        userOpVo.setResetField(ResetFieldEnum.PASSWORD);
        userOpVo.setServiceUse(true);
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.generateMessage(userOpVo);
    }

    @PostMapping("/v3/generate/reset-sign/phone")
    public ResponseResult<Boolean> generateResetSignPhone(@Validated @RequestBody PhoneMessageVo vo) {
        UserOpVo<PhoneMessageVo> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(vo);
        userOpVo.setUid(getUserId());
        userOpVo.setLoginMethod(LoginMethodEnum.PHONE);
        userOpVo.setLocale(LocaleContextHolder.getLocale());
        userOpVo.setServiceUse(true);
        userOpVo.setResetField(ResetFieldEnum.PHONE_SIGN);
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.generateMessage(userOpVo);
    }

    @PostMapping("/v3/generate/reset-sign/email")
    public ResponseResult<Boolean> generateResetSignEmail(@RequestBody @Validated UniqueSignEmailVo vo) {
        UserOpVo<UniqueSignEmailVo> userOpVo = new UserOpVo<>();
        userOpVo.setLocale(LocaleContextHolder.getLocale());
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.EMAIL);
        return userOperationService.generateMessage(userOpVo);
    }

    @PostMapping("/v3/generate/reset-message/email")
    public ResponseResult<Boolean> generateMessageEmail() {
        UserOpVo<String> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(getUserId());
        userOpVo.setLoginMethod(LoginMethodEnum.EMAIL);
        userOpVo.setLocale(LocaleContextHolder.getLocale());
        return userOperationService.generateMessage(userOpVo);
    }

    @PostMapping("/v3/check/reset-message/email")
    public ResponseResult<Boolean> checkResetMessageEmail(@RequestBody @Validated CheckResetMessageVo vo) {
        log.info("reset message check: {}", JSON.toJSONString(vo));
        vo.setUid(getUserId());
        UserOpVo<CheckResetMessageVo> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.EMAIL);
        return userOperationService.checkMessage(userOpVo);
    }

    @PostMapping("/v3/check/message/from-user-email")
    public ResponseResult<Boolean> checkMessageFromUserEmail(@NotBlank @Size(min = 6, max = 6,
            message = "api.user.consumer.message.length-limit") @RequestParam("verifyCode") String verifyCode) {
        UserOpVo<String> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(verifyCode);
        userOpVo.setLoginMethod(LoginMethodEnum.EMAIL);
        userOpVo.setServiceUse(true);
        userOpVo.setUid(getUserId());
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.checkMessage(userOpVo);
    }

    @PostMapping("/v3/check/message/from-user-phone")
    public ResponseResult<Boolean> checkMessageFromUserPhone(@NotBlank @Size(min = 6, max = 6, message = "api.user.consumer.message.length-limit") @RequestParam("verifyCode") String verifyCode) {
        UserOpVo<String> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(verifyCode);
        userOpVo.setLoginMethod(LoginMethodEnum.PHONE);
        userOpVo.setResetField(ResetFieldEnum.PASSWORD);
        userOpVo.setServiceUse(true);
        userOpVo.setUid(getUserId());
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.checkMessage(userOpVo);
    }

    @PostMapping("/v3/check/message/reset-user-phone")
    public ResponseResult<Boolean> checkMessageResetUserPhone(@RequestBody @Validated PhoneLoginRegisterVo vo) {
        UserOpVo<PhoneLoginRegisterVo> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.PHONE);
        userOpVo.setServiceUse(true);
        userOpVo.setUid(getUserId());
        userOpVo.setResetField(ResetFieldEnum.PHONE_SIGN);
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.checkMessage(userOpVo);
    }

    @PostMapping("/v3/check/message/email")
    public ResponseResult<Boolean> checkMessageEmail(@RequestBody @Validated CheckEmailMessageVo vo) {
        UserOpVo<CheckEmailMessageVo> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.EMAIL);
        return userOperationService.checkMessage(userOpVo);
    }

    @PostMapping("/v3/check/message/phone")
    public ResponseResult<Boolean> checkMessagePhone(@RequestBody @Validated PhoneLoginRegisterVo vo) {
        UserOpVo<PhoneLoginRegisterVo> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.PHONE);
        return userOperationService.checkMessage(userOpVo);
    }

    @PostMapping("/v3/generate/message/email")
    public ResponseResult<Boolean> generateMessageEmail(@RequestBody @Validated UniqueSignEmailVo vo) {
        UserOpVo<UniqueSignEmailVo> userOpVo = new UserOpVo<>();
        userOpVo.setLocale(LocaleContextHolder.getLocale());
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.EMAIL);
        return userOperationService.generateMessage(userOpVo);
    }

    @PostMapping("/v3/generate/message/phone")
    public ResponseResult<Boolean> generateMessagePhone(
            @RequestBody @Validated PhoneMessageVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<PhoneMessageVo> userOpVo = new UserOpVo<>();
        userOpVo.setLocale(LocaleContextHolder.getLocale());
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.PHONE);
        userOpVo.setIp(StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        return userOperationService.generateMessage(userOpVo);
    }

    @PostMapping("/v3/exist/unique-sign/email")
    public ResponseResult<Boolean> existUniqueSignEmail(
            @RequestBody @Validated UniqueSignEmailVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<UniqueSignEmailVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.EMAIL,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        return userOperationService.exists(userOpVo);
    }

    @PostMapping("/v3/state/email")
    public ResponseResult<Integer> stateEmail(
            @RequestBody @Validated UniqueSignEmailVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<UniqueSignEmailVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.EMAIL,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        return userOperationService.state(userOpVo);
    }

    @PostMapping("/v3/state/phone")
    public ResponseResult<Integer> statePhone(
            @RequestBody @Validated UniqueSignPhoneVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<UniqueSignPhoneVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.PHONE,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        return userOperationService.state(userOpVo);
    }

    @PostMapping("/v3/state/password")
    public ResponseResult<Integer> statePassword() {
        UserOpVo<Void> userOpVo = new UserOpVo<>();
        userOpVo.setLoginMethod(LoginMethodEnum.PHONE);
        userOpVo.setServiceUse(true);
        userOpVo.setUid(getUserId());
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.state(userOpVo);
    }

    @PostMapping("/v3/exist/unique-sign/phone")
    public ResponseResult<Boolean> existUniqueSignMobile(
            @RequestBody @Validated UniqueSignPhoneVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<UniqueSignPhoneVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.PHONE,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        return userOperationService.exists(userOpVo);
    }

    @PostMapping("/v3/register/email")
    public ResponseResult<LoginResultVo> emailRegister(
            @RequestBody @Validated EmailRegisterVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<EmailRegisterVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.EMAIL,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.register(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/register/phone")
    public ResponseResult<LoginResultVo> phoneRegister(
            @RequestBody @Validated PhoneLoginRegisterVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<PhoneLoginRegisterVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.PHONE,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.register(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/register/wx-applet/quick-phone")
    public ResponseResult<LoginResultVo> wxAppletRegister(
            @RequestBody @Validated WxAppletVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<WxAppletVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.WX_APPLET,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.register(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/register/wx-application/phone")
    public ResponseResult<LoginResultVo> wxApplicationRegister(
            @RequestBody @Validated PhoneLoginRegisterVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<PhoneLoginRegisterVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.WX_APPLICATION,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.register(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/register/google-application/email")
    public ResponseResult<LoginResultVo> googleApplicationRegister(
            @RequestBody @Validated GgApplicationVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<GgApplicationVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.GOOGLE_APPLICATION,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.register(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/register/apple-application/phone")
    public ResponseResult<LoginResultVo> appleApplicationPhoneRegister(
            @RequestBody @Validated PhoneLoginRegisterVo vo,
            HttpServletRequest request
    ) {
        ApRegisterVo apRegisterVo = new ApRegisterVo(LoginMethodEnum.PHONE, vo);
        UserOpVo<ApRegisterVo> userOpVo = new UserOpVo<>(apRegisterVo,
                LoginMethodEnum.APPLE_APPLICATION,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.register(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/register/apple-application/email")
    public ResponseResult<LoginResultVo> appleApplicationEmailRegister(
            @RequestBody @Validated ApApplicationVo vo,
            HttpServletRequest request
    ) {
        ApRegisterVo apRegisterVo = new ApRegisterVo(LoginMethodEnum.EMAIL, vo);
        UserOpVo<ApRegisterVo> userOpVo = new UserOpVo<>(apRegisterVo,
                LoginMethodEnum.APPLE_APPLICATION,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.register(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/login/email")
    public ResponseResult<LoginResultVo> emailLogin(
            @RequestBody @Validated EmailLoginVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<EmailLoginVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.EMAIL,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.login(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/login/phone")
    public ResponseResult<LoginResultVo> phoneLogin(
            @RequestBody @Validated PhoneLoginRegisterVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<PhoneLoginRegisterVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.PHONE,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.login(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/login/phone-password")
    public ResponseResult<LoginResultVo> phoneLogin(
            @RequestBody @Validated PhonePasswordLoginVo vo,
            HttpServletRequest request
    ) {
        vo.setPassword(UserSm4Constant.decryptPassword(vo.getPassword()));
        UserOpVo<PhonePasswordLoginVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.PHONE,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        userOpVo.setUid(getUserId());
        userOpVo.setResetField(ResetFieldEnum.PASSWORD);
        userOpVo.setLocale(LocaleContextHolder.getLocale());
        return userOperationService.login(userOpVo);
    }

    @PostMapping("/v3/login/google-application")
    public ResponseResult<LoginResultVo> oauth2GoogleLogin(
            @RequestBody @Validated GgApplicationVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<GgApplicationVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.GOOGLE_APPLICATION,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.login(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/login/apple-application")
    public ResponseResult<LoginResultVo> oauth2AppleLogin(
            @RequestBody @Validated ApApplicationVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<ApApplicationVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.APPLE_APPLICATION,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.login(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/login/wx-application")
    public ResponseResult<LoginResultVo> oauth2WxApplicationLogin(
            @RequestBody @Validated WxApplicationVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<WxApplicationVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.WX_APPLICATION,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.login(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/login/wx-applet")
    public ResponseResult<LoginResultVo> oauth2WxAppletLogin(
            @RequestBody @Validated WxApplicationVo vo,
            HttpServletRequest request
    ) {
        UserOpVo<WxApplicationVo> userOpVo = new UserOpVo<>(vo,
                LoginMethodEnum.WX_APPLET,
                StrUtil.str(request.getAttribute("ip"), StandardCharsets.UTF_8));
        ResponseResult<LoginResultVo> result = userOperationService.login(userOpVo);
        convertSaveGuest(request, result.getData());
        return result;
    }

    @PostMapping("/v3/update/email-password")
    public ResponseResult<Boolean> updateEmailPassword(
            @RequestBody @Validated EmailResetPasswordVo vo
    ) {
        vo.setPassword(UserSm4Constant.decryptPassword(vo.getPassword()));
        UserOpVo<EmailResetPasswordVo> userOpVo = new UserOpVo<>();
        vo.setPassword(UserSm4Constant.decryptPassword(vo.getPassword()));
        userOpVo.setUid(getUserId());
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.EMAIL);
        userOpVo.setResetField(ResetFieldEnum.EMAIL_PASSWORD);
        log.info("update email password: {}", JSON.toJSONString(userOpVo));
        return userOperationService.reset(userOpVo);
    }

    @PostMapping("/v3/update/phone-password")
    public ResponseResult<Boolean> updatePhonePassword(@RequestBody @Validated PhonePasswordUpdateVo vo) {
        vo.setPassword(UserSm4Constant.decryptPassword(vo.getPassword()));
        UserOpVo<PhonePasswordUpdateVo> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.PHONE);
        userOpVo.setResetField(ResetFieldEnum.PHONE_PASSWORD);
        log.info("update phone password: {}", JSON.toJSONString(userOpVo));
        return userOperationService.reset(userOpVo);
    }

    @PostMapping("/v3/update/password/from-first-register")
    public ResponseResult<Boolean> updatePassword(@RequestBody @Validated UpdatePasswordFirstRegisterVo vo) {
        vo.setPassword(UserSm4Constant.decryptPassword(vo.getPassword()));
        UserOpVo<String> userOpVo = new UserOpVo<>();
        userOpVo.setUid(getUserId());
        userOpVo.setUserInfo(vo.getPassword());
        userOpVo.setLoginMethod(LoginMethodEnum.PHONE);
        userOpVo.setResetField(ResetFieldEnum.PASSWORD_FIRST);
        log.info("update master password: {}", JSON.toJSONString(userOpVo));
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.reset(userOpVo);
    }

    @PostMapping("/v3/update/password/check-by-phone")
    public ResponseResult<Boolean> updatePasswordCheckByPhone(@RequestBody @Validated UpdatePasswordVo vo) {
        vo.setPassword(UserSm4Constant.decryptPassword(vo.getPassword()));
        UserOpVo<UpdatePasswordVo> userOpVo = new UserOpVo<>();
        userOpVo.setUid(getUserId());
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.PHONE);
        userOpVo.setResetField(ResetFieldEnum.PASSWORD);
        userOpVo.setServiceUse(true);
        log.info("phone update master password: {}", JSON.toJSONString(userOpVo));
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.reset(userOpVo);
    }

    @PostMapping("/v3/update/password/check-by-email")
    public ResponseResult<Boolean> updatePasswordCheckByEmail(@RequestBody @Validated UpdatePasswordVo vo) {
        vo.setPassword(UserSm4Constant.decryptPassword(vo.getPassword()));
        UserOpVo<UpdatePasswordVo> userOpVo = new UserOpVo<>();
        userOpVo.setUid(getUserId());
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.EMAIL);
        userOpVo.setResetField(ResetFieldEnum.EMAIL_PASSWORD);
        log.info("email update master password: {}", JSON.toJSONString(userOpVo));
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.reset(userOpVo);
    }

    @PostMapping("/v3/update/email-sign")
    public ResponseResult<Boolean> updateEmailSign(
            @RequestBody @Validated EmailLoginVo vo
    ) {
        if (vo.getUsePasswordFlag()) {
            vo.setPassword(UserSm4Constant.decryptPassword(vo.getPassword()));
        }
        UserOpVo<EmailLoginVo> userOpVo = new UserOpVo<>();
        userOpVo.setUid(getUserId());
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.EMAIL);
        userOpVo.setResetField(ResetFieldEnum.EMAIL_SIGN);
        log.info("update email: {}", JSON.toJSONString(userOpVo));
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.reset(userOpVo);
    }


    @PostMapping("/v3/update/phone-sign")
    public ResponseResult<Boolean> updatePhoneSign(
            @RequestBody @Validated PhoneLoginRegisterVo vo
    ) {
        UserOpVo<PhoneLoginRegisterVo> userOpVo = new UserOpVo<>();
        userOpVo.setUid(getUserId());
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.PHONE);
        userOpVo.setResetField(ResetFieldEnum.PHONE_SIGN);
        userOpVo.setServiceUse(true);
        log.info("reset phone sign: {}", JSON.toJSONString(userOpVo));
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.reset(userOpVo);
    }


    @GetMapping("/v3/binding/binding-map")
    public ResponseResult<Map<String, Boolean>> bindingMap() {
        return userOperationService.bindingOauth2Map(getUserId());
    }

    @PostMapping("/v3/binding/apple-application")
    public ResponseResult<Boolean> bindingAppleApplication(
            @RequestBody @Validated ApApplicationVo vo
    ) {
        BindingApVo bindingApVo = new BindingApVo(getUserId(), vo);
        UserOpVo<BindingApVo> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(bindingApVo);
        userOpVo.setLoginMethod(LoginMethodEnum.APPLE_APPLICATION);
        return userOperationService.binding(userOpVo);
    }

    @PostMapping("/v3/binding/google-application")
    public ResponseResult<Boolean> bindingGoogleApplication(
            @RequestBody @Validated GgApplicationVo vo
    ) {
        BindingGgVo bindingApVo = new BindingGgVo(getUserId(), vo);
        UserOpVo<BindingGgVo> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(bindingApVo);
        userOpVo.setLoginMethod(LoginMethodEnum.GOOGLE_APPLICATION);
        return userOperationService.binding(userOpVo);
    }

    @PostMapping("/v3/binding/wx-application")
    public ResponseResult<Boolean> bindingWxApplication(
            @RequestBody @Validated WxApplicationVo vo
    ) {
        UserOpVo<WxApplicationVo> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(vo);
        userOpVo.setUid(getUserId());
        userOpVo.setLoginMethod(LoginMethodEnum.WX_APPLICATION);
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.binding(userOpVo);
    }

    @PostMapping("/v3/binding/wx-applet")
    public ResponseResult<Boolean> bindingWxApplet(
            @RequestBody @Validated WxApplicationVo vo
    ) {
        UserOpVo<WxApplicationVo> userOpVo = new UserOpVo<>();
        userOpVo.setUid(getUserId());
        userOpVo.setUserInfo(vo);
        userOpVo.setLoginMethod(LoginMethodEnum.WX_APPLET);
        if (StrUtil.isBlank(userOpVo.getUid())) {
            return ResponseResult.fail401();
        }
        return userOperationService.binding(userOpVo);
    }

    @PostMapping("/v3/unbind/apple-application")
    public ResponseResult<Boolean> unbindAppleApplication() {
        UserOpVo<String> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(getUserId());
        userOpVo.setLoginMethod(LoginMethodEnum.APPLE_APPLICATION);
        return userOperationService.unbind(userOpVo);
    }

    @PostMapping("/v3/unbind/google-application")
    public ResponseResult<Boolean> unbindGoogleApplication() {
        UserOpVo<String> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(getUserId());
        userOpVo.setLoginMethod(LoginMethodEnum.GOOGLE_APPLICATION);
        return userOperationService.unbind(userOpVo);
    }

    @PostMapping("/v3/unbind/wx-application")
    public ResponseResult<Boolean> unbindWxApplication() {
        UserOpVo<String> userOpVo = new UserOpVo<>();
        userOpVo.setUserInfo(getUserId());
        userOpVo.setLoginMethod(LoginMethodEnum.WX_APPLICATION);
        return userOperationService.unbind(userOpVo);
    }

    @PostMapping("/v3/logout")
    public void logout() {
        String userId = getUserId();
        if (StrUtil.isBlank(userId)) {
            log.warn("logout fail: {}", userId);
            return;
        }
        loginService.logout(userId);
    }

    @PostMapping("/v3/del-user")
    public ResponseResult<Boolean> delUser() {
        String userId = getUserId();
        if (StrUtil.isBlank(userId)) {
            return ResponseResult.fail401();
        }
        ResponseResult<Boolean> result = userOperationService.deleteUserById(userId);
        log.info("del user: {}, {}", userId, result);
        if (result.getData()) {
            log.info("del user success logout: {}", userId);
            loginService.logout(userId);
        }
        return result;
    }


    /*-----------------------------------------分割线------------------------------------------------*/

    @GetMapping("/order/third/{thirdName}")
    public ResponseResult getThirdOrder(
            @PathVariable("thirdName") ThirdName thirdName,
            @RequestParam("userId") Integer userId,
            @RequestParam("page") @Min(1) @Max(30) int page,
            @RequestParam("pageSize") @Min(10) @Max(50) int pageSize
    ) {
        if (!StringUtils.equals(getUserId(), userId.toString())) {
            return JSONResultUtils.fail(403, "api.user.login.error-auth-403");
        }
        if (thirdName.equals(ThirdName.zuzuche)) {
            return thirdService.getZuZuCheOrderByUserId(userId, page, pageSize);
        } else {
            return JSONResultUtils.fail(400, "api.user.incorrect-thirdname");
        }
    }

    @RequestMapping("/refresh-token")
    public ResponseResult refreshToken(@RequestParam("refreshToken") String refreshToken) {
        return loginService.generateAccessToken(refreshToken);
    }

    @RequestMapping("/check-access-token")
    public ResponseResult checkAccessTokenReturnModel(@RequestParam("accessToken") String accessToken) {
        return loginService.checkAccessTokenReturnModel(accessToken);
    }

    @GetMapping("/third/getCode")
    public ResponseResult getCode(@RequestParam("thirdSign") String thirdSign) {
        String uidStr = getUserId();
        if (StringUtils.isBlank(uidStr)) {
            return JSONResultUtils.fail("api.user.login.user-sign-error");
        }
        return thirdService.getJwtCode(Integer.parseInt(uidStr), thirdSign);
    }

    @GetMapping("/isVip/v2")
    public ResponseResult isVipV2() {
        String uidStr = getUserId();
        if (StringUtils.isBlank(uidStr)) {
            return JSONResultUtils.fail("api.user.login.user-sign-error");
        }
        Integer uid = Integer.parseInt(uidStr);
        log.info("uid: {}", uid);

        try {
            return JSONResultUtils.success(true);
        } catch (BusinessException e) {
            log.error(e.getMessage(), e);
            return JSONResultUtils.fail(e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return JSONResultUtils.fail();
        }
    }

    @DeleteMapping("/deleteByUserId")
    public ResponseResult<Boolean> deleteUserByUserId(
            @RequestParam("userId") String userId,
            @RequestHeader(HttpHeadConstant.AUTHORIZATION) String authorization
    ) {
        if (StringUtils.isBlank(userId)) {
            return ResponseResult.fail("api.common.params.error");
        }
        String tokenUid = getUserId();
        log.info("deleteByUserId userId: {}", userId);
        if (!StrUtil.equals(tokenUid, userId)) {
            log.info("deleteByUserId tokenUid: {}, userId: {}", tokenUid, userId);
            return ResponseResult.fail(403, "api.user.login.error-auth-403");
        }
        return userV2Service.deleteUserByUserId(userId, authorization);
    }

    @PostMapping("/delete/user")
    public ResponseResult<Boolean> deleteUser() {
        String userId = getUserId();
        String token = StpUtil.getTokenValue();
        ResponseResult<Boolean> result = userOperationService.deleteUserById(userId);
        if (result.getData()) {
            log.info("deleteUser token: {}, userId: {}", token, userId);
            StpUtil.logout(userId);
        }
        return result;
    }

    @PostMapping("/deleteByUserId/v2")
    public ResponseResult<Boolean> deleteUserByUserIdV2(
            @RequestParam("userId") String userId,
            @RequestHeader(HttpHeadConstant.AUTHORIZATION) String authorization
    ) {
        return deleteUserByUserId(userId, authorization);
    }

    @DeleteMapping("/delete-user-order/{userId}/{orderNo}")
    public ResponseResult deleteOrderByOrderNo(
            @PathVariable("userId") String userId,
            @PathVariable("orderNo") String orderNo
    ) {
        String uid = getUserId();
        if (StrUtil.equals(uid, userId)) {
            return somytripUserService.deleteOrderByOrderNo(userId, orderNo);
        }
        return JSONResultUtils.fail(403, "api.user.login.error-auth-403");
    }

    @GetMapping("/tripgpt/isvip")
    public ResponseResult getIsVip() {
        String userId = getUserId();
        if (StringUtils.isNotBlank(userId)) {
            return somytripUserService.isVip(Integer.parseInt(userId));
        } else {
            return JSONResultUtils.success("api.vip.query-user-isVip-error", false);
        }
    }

    @PostMapping("/verificationCode/v2")
    public ResponseResult getVerificationCode2(@Validated VerificationCodeDto verificationCodeDto) {
        return smsService.sendSMSText2(verificationCodeDto);
    }

    @PostMapping("/login/v2")
    public ResponseResult login2(HttpServletRequest httpServletRequest, @RequestBody SomytripUsers.LoginEntity loginEntity) {
        ResponseResult result = userV2Service.login(loginEntity,
                httpServletRequest.getHeader(HttpHeadConstant.APPTYPE),
                CommonUtils.getIpByHttpServletRequest(httpServletRequest));
        //保存游客访问记录
        convertSaveGuestUserVo(httpServletRequest, result.getData());
        return result;
    }

    @PostMapping("/login/v3")
    public ResponseResult login3(HttpServletRequest httpServletRequest, @RequestBody SomytripUsers.LoginEntity loginEntity) {

        ResponseResult result = userV2Service.login(loginEntity,
                httpServletRequest.getHeader(HttpHeadConstant.APPTYPE),
                CommonUtils.getIpByHttpServletRequest(httpServletRequest));
        //保存游客访问记录
        convertSaveGuestUserVo(httpServletRequest, result.getData());
        return result;
    }

    private void convertSaveGuest(HttpServletRequest httpServletRequest, LoginResultVo result) {
        GuestUserDto guestUserDto = null;
        try {
            Oauth2Vo oAuth2Vo = result.getToken();
            guestUserDto = GuestUserDto
                    .builder()
                    .channelType(httpServletRequest.getHeader(HttpHeadConstant.CHANNEL_TYPE))
                    .appType(httpServletRequest.getHeader(HttpHeadConstant.APPTYPE))
                    .deviceNo(httpServletRequest.getHeader(HttpHeadConstant.DEVICE_NO))
                    .deviceType(httpServletRequest.getHeader(HttpHeadConstant.DEVICE_TYPE))
                    .authorization(oAuth2Vo.getAccessToken())
                    .versionCode(httpServletRequest.getHeader(HttpHeadConstant.VERSION_CODE))
                    .build();
            somytripGuestUserService.saveGuestUserRecord(guestUserDto);
        } catch (Exception e) {
            log.error("保存访客记录异常，请求参数：{}，异常信息：{}", JSON.toJSONString(guestUserDto), e.getMessage(), e);
        }
    }

    /**
     * 封装保存游客vo
     *
     * @param httpServletRequest
     * @return
     */
    private void convertSaveGuestUserVo(HttpServletRequest httpServletRequest, Object object) {
        GuestUserDto guestUserDto = null;
        try {
            Map map = (Map) object;
            Oauth2Vo oAuth2Vo = (Oauth2Vo) map.get("token");
            guestUserDto = GuestUserDto
                    .builder()
                    .channelType(httpServletRequest.getHeader(HttpHeadConstant.CHANNEL_TYPE))
                    .appType(httpServletRequest.getHeader(HttpHeadConstant.APPTYPE))
                    .deviceNo(httpServletRequest.getHeader(HttpHeadConstant.DEVICE_NO))
                    .deviceType(httpServletRequest.getHeader(HttpHeadConstant.DEVICE_TYPE))
                    .authorization(oAuth2Vo.getAccessToken())
                    .versionCode(httpServletRequest.getHeader(HttpHeadConstant.VERSION_CODE))
                    .build();
            somytripGuestUserService.saveGuestUserRecord(guestUserDto);
        } catch (Exception e) {
            log.error("保存访客记录异常，请求参数：{}，异常信息：{}", JSON.toJSONString(guestUserDto), e.getMessage(), e);
        }
    }


    private void convertSaveGuestUserVo(HttpServletRequest httpServletRequest, LoginResultVo vo) {
        try {
            Oauth2Vo oAuth2Vo = vo.getToken();
            GuestUserDto guestUserDto = GuestUserDto
                    .builder()
                    .channelType(httpServletRequest.getHeader(HttpHeadConstant.CHANNEL_TYPE))
                    .appType(httpServletRequest.getHeader(HttpHeadConstant.APPTYPE))
                    .deviceNo(httpServletRequest.getHeader(HttpHeadConstant.DEVICE_NO))
                    .deviceType(httpServletRequest.getHeader(HttpHeadConstant.DEVICE_TYPE))
                    .authorization(oAuth2Vo.getAccessToken())
                    .versionCode(httpServletRequest.getHeader(HttpHeadConstant.VERSION_CODE))
                    .build();
            somytripGuestUserService.saveGuestUserRecord(guestUserDto);
        } catch (Exception e) {
            log.error("保存访客记录异常，请求参数: {}, 异常信息", JSON.toJSONString(vo), e);
        }
    }

    public enum ThirdName {
        zuzuche,
    }

}
