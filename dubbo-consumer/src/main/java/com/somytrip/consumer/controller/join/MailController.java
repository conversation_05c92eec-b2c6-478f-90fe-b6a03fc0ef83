package com.somytrip.consumer.controller.join;

import com.somytrip.consumer.service.RedisService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/api/joinin_hotel_user")
public class MailController {

    @Resource
    private RedisService redisService;

    @RequestMapping("reg_link_verify/{uuid}")
    public String verify(HttpServletRequest request, @PathVariable("uuid") String uuid) {
        String mailAddress = request.getParameter("mailAddress");
        log.info("mailAddress: {}", mailAddress);
        log.info("uuid: {}", uuid);
        if (Objects.equals(uuid, redisService.getString("user:link_verify:" + mailAddress))) {

            return "success";
        }
        return "fail";
    }
}
