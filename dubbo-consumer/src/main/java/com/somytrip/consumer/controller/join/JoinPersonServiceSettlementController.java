package com.somytrip.consumer.controller.join;

import com.somytrip.api.service.join.JoinPersonServiceSettlementService;
import com.somytrip.bean.queries.AggregateQueries;
import com.somytrip.bean.queries.JoinPersonServiceSettlementFuzzyQueries;
import com.somytrip.bean.queries.JoinPersonServiceSettlementQueries;
import com.somytrip.bean.vo.JoinPersonServiceSettlementVo;
import com.somytrip.consumer.controller.BaseController;
import com.somytrip.entity.dto.hotel.PaginationDto;
import com.somytrip.entity.resp.ListResp;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.utils.JSONResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: JoinPersonServiceSettlementController
 * @Description: 个人加盟服务结算Controller层
 * @Author: shadow
 * @Date: 2023/12/12 17:49
 */
@Slf4j
@RestController
@RequestMapping("/api/join_person/service/settlement")
public class JoinPersonServiceSettlementController extends BaseController {

    @DubboReference
    private JoinPersonServiceSettlementService settlementService;

    /**
     * 查询个人结算记录列表
     *
     * @param pageDto 分页dto
     * @return ResponseResult
     */
    @GetMapping("/list")
    public ResponseResult list(@Validated PaginationDto pageDto) {

        Long uid = Long.parseLong(getUserId());

        log.info("uid: {}", uid);
        log.info("pageDto: {}", pageDto);

        ListResp<JoinPersonServiceSettlementVo> settlementListResp = settlementService.queryList(uid, pageDto);
        Map<String, Object> result = new HashMap<>(5);
        result.put("settlementList", settlementListResp.getList());
        result.put("total", settlementListResp.getTotal());
        return JSONResultUtils.success(result);

    }

    /**
     * 查询全部结算记录
     *
     * @param queries 查询类
     * @return ResponseResult
     */
    @PostMapping("/all")
    public ResponseResult all(
            @RequestBody AggregateQueries<
                    JoinPersonServiceSettlementQueries,
                    JoinPersonServiceSettlementFuzzyQueries
                    > queries
    ) {

        log.info("queries: {}", queries);

        ListResp<JoinPersonServiceSettlementVo> settlementListResp = settlementService.queryAll(queries);
        Map<String, Object> result = new HashMap<>(5);
        result.put("settlementList", settlementListResp.getList());
        result.put("total", settlementListResp.getTotal());
        return JSONResultUtils.success(result);
//        return JSONResultUtils.success()
//                .put("settlementList", settlementListResp.getList())
//                .put("total", settlementListResp.getTotal());
    }

    /**
     * 同意结算
     *
     * @param settlementSn 结算序列号
     * @return ResponseResult
     */
    @GetMapping("/agree")
    public ResponseResult agree(@RequestParam("settlementSn") String settlementSn) {
        Boolean agreeResult = settlementService.agree(settlementSn);
        if (!agreeResult) {
            return JSONResultUtils.fail("api.join-person.agree-settlement-failed");
        }
        return JSONResultUtils.success(200, "api.join-person.agree-settlement-success");
    }

    /**
     * 拒绝结算
     *
     * @param settlementSn 结算序列号
     * @param reason       原因
     * @return ResponseResult
     */
    @GetMapping("/refuse")
    public ResponseResult refuse(@RequestParam("settlementSn") String settlementSn, @RequestParam("reason") String reason) {
        Boolean refuseResult = settlementService.refuse(settlementSn, reason);
        if (!refuseResult) {
            return JSONResultUtils.fail("api.join-person.reject-settlement-failed");
        }
        return JSONResultUtils.success(200, "api.join-person.reject-settlement-success");
    }

    /**
     * 结算通知
     *
     * @param settlementSn 结算序列号
     * @param isSuccess    是否成功
     * @return ResponseResult
     */
    @GetMapping("/notify")
    public ResponseResult settlementNotify(@RequestParam("settlementSn") String settlementSn, @RequestParam("isSuccess") boolean isSuccess) {
        Boolean result = settlementService.settlementNotify(settlementSn, isSuccess);
        if (!result) {
            return JSONResultUtils.fail("api.join-person.settlement-notify-ope-failed");
        }
        return JSONResultUtils.success(200, "api.join-person.settlement-notify-ope-success");
    }
}
