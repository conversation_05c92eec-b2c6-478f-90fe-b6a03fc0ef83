package com.somytrip.consumer.controller.join;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.somytrip.api.service.join.JoinEnterpriseService;
import com.somytrip.consumer.controller.BaseController;
import com.somytrip.consumer.util.FileUtil;
import com.somytrip.entity.dto.join.JoinEnterpriseJoinIdParam;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.exception.BusinessException;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/api/joinin_enterprise")
public class JoininEnterpriseController extends BaseController {

    @DubboReference
    private JoinEnterpriseService joinEnterpriseService;
    @Resource
    private FileUtil fileUtil;

    @PostMapping("/join_id")
    public ResponseResult<Void> joinId(@RequestParam("idPic1") MultipartFile idPic1,
                                       @RequestParam("idPic2") MultipartFile idPic2,
                                       @RequestParam("license_file") MultipartFile license_file,
                                       HttpServletRequest request) {

        try {
            JoinEnterpriseJoinIdParam param = new JoinEnterpriseJoinIdParam();
            param.setUid(getUserId());
            param.setType(request.getParameter("type"));
            param.setCoName(request.getParameter("co_name"));
            param.setSocialCode(request.getParameter("social_code"));
            param.setEmail(request.getParameter("email"));
            param.setManagerId(request.getParameter("manager_id"));
            param.setManagerName(request.getParameter("manager"));
//            param.setIdPic1(idPic1);
//            param.setIdPic2(idPic2);
//            param.setLicenseFile(license_file);
            param.setIdPic1FileName(fileUtil.getNewFileName(idPic1));
            param.setIdPic2FileName(fileUtil.getNewFileName(idPic2));
            param.setLicenseFileName(fileUtil.getNewFileName(license_file));
            log.info("param: {}", JSON.toJSONString(param));
            param.setIdPic1Bytes(idPic1.getBytes());
            param.setIdPic2Bytes(idPic2.getBytes());
            param.setLicenseFileBytes(license_file.getBytes());
            return joinEnterpriseService.joinId(param);
        } catch (BusinessException e) {
            return ResponseResult.fail(201, e.getMessage());
        } catch (IOException e) {
            return ResponseResult.fail(201, "");
        }
    }

    @PostMapping("/join_account")
    public ResponseResult<Void> joinAccount(HttpServletRequest request) {

        try {
//            String uid = request.getParameter("uid");
//            String bankCity = request.getParameter("bank_city");
//            String bankDetail = request.getParameter("bank_detail");
//            String bankName = request.getParameter("bank_name");
//            String subBankName = request.getParameter("sub_bank_name");
//            String bankNumber = request.getParameter("bank_number");
            JSONObject param = new JSONObject();
            param.put("uid", getUserId());
            param.put("bank_city", request.getParameter("bank_city"));
            param.put("bank_detail", request.getParameter("bank_detail"));
            param.put("bank_name", request.getParameter("bank_name"));
            param.put("sub_bank_name", request.getParameter("sub_bank_name"));
            param.put("bank_number", request.getParameter("bank_number"));
            return joinEnterpriseService.joinAccount(param);
        } catch (BusinessException e) {
            return ResponseResult.fail(201, e.getMessage());
        }
    }

    @PostMapping("/join_send_otp")
    public ResponseResult<Void> joinSendOtp(HttpServletRequest request) {

        try {
            return joinEnterpriseService.joinSendOtp(request);
        } catch (BusinessException e) {
            return ResponseResult.fail(201, e.getMessage());
        }
    }

    @PostMapping("/join_safe_code")
    public ResponseResult<Void> joinSafeCode(HttpServletRequest request) {

        try {
            JSONObject param = new JSONObject();
            param.put("uid", getUserId());
            param.put("phone", request.getParameter("phone"));
            param.put("safe_code", request.getParameter("safe_code"));
            param.put("smscode", request.getParameter("smscode"));
            return joinEnterpriseService.joinSafeCode(param);
        } catch (BusinessException e) {
            return ResponseResult.fail(201, e.getMessage());
        }
    }

    @GetMapping("/get_confirm_info")
    public ResponseResult<JSONObject> getJoinConfirmInfo() {

        try {
            return joinEnterpriseService.getJoinConfirmInfo();
        } catch (BusinessException e) {
            return ResponseResult.fail(201, e.getMessage());
        }
    }

    @PostMapping("/join_confirm")
    public ResponseResult<Object> joinConfirm() {
        return ResponseResult.success("成功");
    }

    @PostMapping("/join_commit")
    public ResponseResult<Void> joinCommit(@RequestParam("protocols") MultipartFile protocols, HttpServletRequest request) {

        try {
            JSONObject param = new JSONObject();
            param.put("uid", getUserId());
            param.put("protocolsFileName", fileUtil.getNewFileName(protocols));
            param.put("protocolsBytes", protocols.getBytes());
            return joinEnterpriseService.joinCommit(param);
        } catch (BusinessException e) {
            return ResponseResult.fail(201, e.getMessage());
        } catch (IOException e) {
            return ResponseResult.fail(201, "");
        }
    }
}
