<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.somytrip.consumer.mapper.ItineraryGuidebookMapper">

    <select id="queryOneByCityId" resultType="com.somytrip.entity.itinerary.ItineraryGuidebookEntity">
        SELECT ig.id,
               ig.alarm_tel,
               ig.tourism_ad_tel,
               ig.china_embassy_tel,
               IFNULL(JSON_UNQUOTE(JSON_EXTRACT(ig.locale_dict, CONCAT('$.brief.', #{lang}))), ig.brief)
                   AS brief,
               IFNULL(JSON_UNQUOTE(JSON_EXTRACT(ig.locale_dict, CONCAT('$.tripTime.', #{lang}))), ig.trip_time)
                   AS trip_time,
               IFNULL(JSON_UNQUOTE(JSON_EXTRACT(ig.locale_dict, CONCAT('$.actMessage.', #{lang}))), ig.act_message)
                   AS act_message
        FROM itinerary_guidebook ig
                 LEFT JOIN global_city gc ON ig.city_id = gc.id
        WHERE ig.city_id = #{cityId}
    </select>

    <resultMap id="guidebookVoResultMap" type="com.somytrip.entity.itinerary.ItineraryGuidebookVo">
        <result property="cityId" column="city_id"/>
        <result property="cityCode" column="city_code"/>
        <result property="cityName" column="city_name"/>

        <association property="guidebookDetail"
                     javaType="com.somytrip.entity.itinerary.ItineraryGuidebookVo$GuidebookDetail">
            <result property="brief" column="brief"/>
            <result property="tripTime" column="trip_time"/>
            <result property="actMessage" column="act_message"/>

            <association property="emergencyContact"
                         javaType="com.somytrip.entity.itinerary.ItineraryGuidebookVo$GuidebookDetail$EmergencyContact">
                <result property="alarmTel" column="alarm_tel"/>
                <result property="tourismAdTel" column="tourism_ad_tel"/>
                <result property="chinaEmbassyTel" column="china_embassy_tel"/>
            </association>
        </association>
    </resultMap>

    <select id="queryVoByCityId" resultType="com.somytrip.entity.itinerary.ItineraryGuidebookVo"
            resultMap="guidebookVoResultMap">
        SELECT ig.alarm_tel,
               ig.tourism_ad_tel,
               ig.china_embassy_tel,
               gc.city_code,
               IFNULL(gcl.city_name, gc.city_name),
               IFNULL(JSON_UNQUOTE(JSON_EXTRACT(ig.locale_dict, CONCAT('$.brief.', #{lang}))), ig.brief)
                   AS brief,
               IFNULL(JSON_UNQUOTE(JSON_EXTRACT(ig.locale_dict, CONCAT('$.tripTime.', #{lang}))), ig.trip_time)
                   AS trip_time,
               IFNULL(JSON_UNQUOTE(JSON_EXTRACT(ig.locale_dict, CONCAT('$.actMessage.', #{lang}))), ig.act_message)
                   AS act_message
        FROM itinerary_guidebook ig
                 LEFT JOIN global_city gc ON ig.city_id = gc.id
                 LEFT JOIN global_city_locale gcl ON gcl.city_id = gc.id AND gcl.lang = #{lang}
        WHERE ig.city_id = #{cityId}
    </select>

    <select id="queryVosByCityIds" resultType="com.somytrip.entity.itinerary.ItineraryGuidebookVo"
            resultMap="guidebookVoResultMap">
        SELECT ig.alarm_tel,
        ig.tourism_ad_tel,
        ig.china_embassy_tel,
        ig.city_id,
        gc.city_code,
        IFNULL(gcl.city_name, gc.city_name) AS city_name,
        <choose>
            <when test="lang == 'zh_CN'">
                ig.brief,
                ig.trip_time,
                ig.act_message
            </when>
            <otherwise>
                IFNULL(JSON_UNQUOTE(JSON_EXTRACT(ig.locale_dict, CONCAT('$.brief.', #{lang}))), ig.brief)
                AS brief,
                IFNULL(JSON_UNQUOTE(JSON_EXTRACT(ig.locale_dict, CONCAT('$.tripTime.', #{lang}))), ig.trip_time)
                AS trip_time,
                IFNULL(JSON_UNQUOTE(JSON_EXTRACT(ig.locale_dict, CONCAT('$.actMessage.', #{lang}))), ig.act_message)
                AS act_message
            </otherwise>
        </choose>
        FROM itinerary_guidebook ig
        LEFT JOIN global_city gc ON ig.city_id = gc.id
        LEFT JOIN global_city_locale gcl ON gcl.city_id = gc.id AND gcl.lang = #{lang}
        WHERE ig.city_id IN
        <foreach collection="cityIds" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
    </select>

</mapper>