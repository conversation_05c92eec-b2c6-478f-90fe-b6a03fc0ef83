<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.somytrip.consumer.mapper.FeedbackCommentMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.somytrip.entity.community.FeedbackComment">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="feedback_id" property="feedbackId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_type" property="userType" jdbcType="TINYINT"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="images" property="images" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result column="ipaddress" property="ipaddress" jdbcType="VARCHAR"/>
        <result column="IpProvinceName" property="ipProvinceName" jdbcType="VARCHAR"/>
        <result column="IpCityName" property="ipCityName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="reply_to_user_id" property="replyToUserId" jdbcType="VARCHAR"/>
        <result column="reply_to_user_type" property="replyToUserType" jdbcType="TINYINT"/>
        <result column="del_flag" property="delFlag" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, feedback_id, user_id, user_type, content, images, ipaddress, IpProvinceName, IpCityName,
        create_time, update_time, parent_id, reply_to_user_id, reply_to_user_type, del_flag
    </sql>

    <!-- 根据反馈ID查询评论列表 -->
    <select id="selectByFeedbackId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM feedback_comment
        WHERE feedback_id = #{feedbackId} AND del_flag = 0
        ORDER BY create_time ASC
    </select>

    <!-- 根据父评论ID查询回复列表 -->
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM feedback_comment
        WHERE parent_id = #{parentId} AND del_flag = 0
        ORDER BY create_time ASC
    </select>

    <!-- 统计反馈的评论数量 -->
    <select id="countByFeedbackId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM feedback_comment
        WHERE feedback_id = #{feedbackId} AND del_flag = 0
    </select>

</mapper>
