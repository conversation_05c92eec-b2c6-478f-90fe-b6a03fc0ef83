<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.somytrip.consumer.mapper.FeedbackPlatformMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.somytrip.entity.community.FeedbackPlatform">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="images" property="images" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="ipaddress" property="ipaddress" jdbcType="VARCHAR"/>
        <result column="IpProvinceName" property="ipProvinceName" jdbcType="VARCHAR"/>
        <result column="IpCityName" property="ipCityName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="support_count" property="supportCount" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="category" property="category" jdbcType="TINYINT"/>
        <result column="source" property="source" jdbcType="TINYINT"/>
        <result column="del_flag" property="delFlag" jdbcType="TINYINT"/>
        <result column="is_adopted" property="isAdopted" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, title, content, images, phone, ipaddress, IpProvinceName, IpCityName,
        create_time, update_time, support_count, status, category, source, del_flag, is_adopted
    </sql>

    <!-- 根据ID查询反馈详情 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM feedback_platform
        WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 根据用户ID查询反馈列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM feedback_platform
        WHERE user_id = #{userId} AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 分页查询反馈列表 -->
    <select id="selectFeedbackPage" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM feedback_platform
        WHERE del_flag = 0
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="category != null">
            AND category = #{category}
        </if>
        <if test="source != null">
            AND source = #{source}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (title LIKE CONCAT('%', #{keyword}, '%') OR content LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
