<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.somytrip.consumer.mapper.MainRecommendInfoMapper">

    <select id="getMainRecommendInfo" resultType="com.somytrip.entity.dto.tourism.MainRecommendInfoDTO">
        SELECT m.city_code     cityCode,
               m.city_name     cityName,
               m.type,
               g.hotel_id      hotelId,
               IFNULL(JSON_UNQUOTE(JSON_EXTRACT(g.locale_dict, CONCAT('$.hotelName.', #{lang}))), g.hotel_name)
                   AS          hotel_name,
               g.hotel_origin  hotelOrigin,
               g.cover         cover,
               g.score         score,
               min(r.min_rate) price
        FROM main_recommend_info m
                 LEFT JOIN hotel_general g ON m.business_id = g.hotel_id
            AND m.hotel_origin = g.hotel_origin
                 LEFT JOIN hotel_min_rate r ON m.business_id = r.hotel_id
            AND m.hotel_origin = r.hotel_origin
        where `date` = #{currentDate}
          and m.del_flag = 0
        group by g.hotel_id
        ORDER BY m.sort
    </select>
</mapper>
