<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.somytrip.consumer.mapper.city.CountryAreaCodeMapper">
    <select id="queryList" resultMap="countryAreaCodeDTOMap">
        SELECT c.*,
               CASE
                   WHEN c.city_value_type = 1 THEN gcl.country_name
                   WHEN c.city_value_type = 2 THEN gcl.province_name
                   ELSE ''
                   END AS value,
        gc.country_code as code
        FROM country_area_code c
            LEFT JOIN global_city_locale gcl
        ON gcl.city_id = c.city_id
            LEFT JOIN global_city gc ON gc.id = gcl.city_id
        WHERE gcl.lang=#{lang} and c.city_id > 0
    </select>

    <resultMap id="countryAreaCodeDTOMap" type="com.somytrip.entity.dto.city.CountryAreaCodeDTO" autoMapping="true">
        <result property="regx" column="regx"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result property="areaValue" column="value"/>
        <result property="areaValueCode" column="code"/>
    </resultMap>
</mapper>
