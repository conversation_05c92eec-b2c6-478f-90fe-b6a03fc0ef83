package com.somytrip.consumer.test.city;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.somytrip.api.service.tourism.GlobalCityService;
import com.somytrip.consumer.service.CountryAreaCodeService;
import com.somytrip.entity.dto.city.CityListResponse;
import com.somytrip.entity.dto.city.CountryAreaCodeDTO;
import com.somytrip.entity.dto.city.GlobalCityEntity;
import com.somytrip.entity.enums.city.AreaCodeTypeEnum;
import com.somytrip.entity.vo.CityLinkReq;
import com.somytrip.entity.vo.CityLinkRes;
import com.somytrip.entity.vo.city.CountryAreaCodeVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.controller.tourism
 * @className: ItineraryTest
 * @author: shadow
 * @description:
 * @date: 2024/3/27 16:46
 * @version: 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class CityServiceTest {

    @Resource
    private GlobalCityService globalCityService;

    @Resource
    private CountryAreaCodeService countryAreaCodeService;

    @Test
    public void testList() {
        List<CountryAreaCodeVo> zhCn = countryAreaCodeService.queryList("zh_CN");
        log.info("list: {}", JSON.toJSONString(zhCn));
    }

    @Test
    public void testUpdateAreaCodeRegx() {
        List<String> regx = ListUtil.of("^1[3-9]\\d{9}$");
        CountryAreaCodeDTO one = countryAreaCodeService.lambdaQuery().eq(CountryAreaCodeDTO::getAreaCode, "86")
                .one();
        one.setRegx(regx);
        ;

        log.info("update area regx result: {}", countryAreaCodeService.updateById(one));
    }

    @Test
    public void initAreaCode() {
        String value = """
                [{"areaCode":"86","country":"CN"},{"areaCode":"0244","country":"AO"},{"areaCode":"93","country":"AF"},{"areaCode":"335","country":"AL"},{"areaCode":"213","country":"DZ"},{"areaCode":"376","country":"AD"},{"areaCode":"1268","country":"AG"},{"areaCode":"54","country":"AR"},{"areaCode":"374","country":"AM"},{"areaCode":"61","country":"AU"},{"areaCode":"43","country":"AT"},{"areaCode":"994","country":"AZ"},{"areaCode":"1242","country":"BS"},{"areaCode":"973","country":"BH"},{"areaCode":"880","country":"BD"},{"areaCode":"1246","country":"BB"},{"areaCode":"375","country":"BY"},{"areaCode":"32","country":"BE"},{"areaCode":"229","country":"BJ"},{"areaCode":"591","country":"BO"},{"areaCode":"267","country":"BW"},{"areaCode":"55","country":"BR"},{"areaCode":"673","country":"BN"},{"areaCode":"359","country":"BG"},{"areaCode":"226","country":"BF"},{"areaCode":"95","country":"MM"},{"areaCode":"257","country":"BI"},{"areaCode":"237","country":"CM"},{"areaCode":"1","country":"CA"},{"areaCode":"236","country":"CF"},{"areaCode":"235","country":"TD"},{"areaCode":"56","country":"CL"},{"areaCode":"57","country":"CO"},{"areaCode":"242","country":"CG"},{"areaCode":"506","country":"CR"},{"areaCode":"53","country":"CU"},{"areaCode":"357","country":"CY"},{"areaCode":"420","country":"CZ"},{"areaCode":"45","country":"DK"},{"areaCode":"253","country":"DJ"},{"areaCode":"1890","country":"DO"},{"areaCode":"593","country":"EC"},{"areaCode":"20","country":"EG"},{"areaCode":"503","country":"SV"},{"areaCode":"372","country":"EE"},{"areaCode":"251","country":"ET"},{"areaCode":"679","country":"FJ"},{"areaCode":"358","country":"FI"},{"areaCode":"33","country":"FR"},{"areaCode":"241","country":"GA"},{"areaCode":"220","country":"GM"},{"areaCode":"995","country":"GE"},{"areaCode":"49","country":"DE"},{"areaCode":"233","country":"GH"},{"areaCode":"30","country":"GR"},{"areaCode":"1809","country":"GD"},{"areaCode":"502","country":"GT"},{"areaCode":"224","country":"GN"},{"areaCode":"592","country":"GY"},{"areaCode":"504","country":"HN"},{"areaCode":"852","country":"CN"},{"areaCode":"36","country":"HU"},{"areaCode":"354","country":"IS"},{"areaCode":"91","country":"IN"},{"areaCode":"62","country":"ID"},{"areaCode":"98","country":"IR"},{"areaCode":"964","country":"IQ"},{"areaCode":"353","country":"IE"},{"areaCode":"972","country":"IL"},{"areaCode":"39","country":"IT"},{"areaCode":"225","country":"CI"},{"areaCode":"1876","country":"JM"},{"areaCode":"81","country":"JP"},{"areaCode":"962","country":"JO"},{"areaCode":"855","country":"KH"},{"areaCode":"327","country":"KZ"},{"areaCode":"254","country":"KE"},{"areaCode":"82","country":"KR"},{"areaCode":"965","country":"KW"},{"areaCode":"331","country":"KG"},{"areaCode":"856","country":"LA"},{"areaCode":"371","country":"LV"},{"areaCode":"961","country":"LB"},{"areaCode":"266","country":"LS"},{"areaCode":"231","country":"LR"},{"areaCode":"218","country":"LY"},{"areaCode":"","country":"LI"},{"areaCode":"370","country":"LT"},{"areaCode":"352","country":"LU"},{"areaCode":"853","country":"CN"},{"areaCode":"261","country":"MG"},{"areaCode":"265","country":"MW"},{"areaCode":"60","country":"MY"},{"areaCode":"960","country":"MV"},{"areaCode":"223","country":"ML"},{"areaCode":"356","country":"MT"},{"areaCode":"230","country":"MU"},{"areaCode":"52","country":"MX"},{"areaCode":"373","country":"MD"},{"areaCode":"377","country":"MC"},{"areaCode":"976","country":"MN"},{"areaCode":"212","country":"MA"},{"areaCode":"258","country":"MZ"},{"areaCode":"977","country":"NP"},{"areaCode":"31","country":"NL"},{"areaCode":"64","country":"NZ"},{"areaCode":"505","country":"NI"},{"areaCode":"227","country":"NE"},{"areaCode":"234","country":"NG"},{"areaCode":"850","country":"KP"},{"areaCode":"47","country":"NO"},{"areaCode":"968","country":"OM"},{"areaCode":"92","country":"PK"},{"areaCode":"507","country":"PA"},{"areaCode":"675","country":"PG"},{"areaCode":"595","country":"PY"},{"areaCode":"51","country":"PE"},{"areaCode":"63","country":"PH"},{"areaCode":"48","country":"PL"},{"areaCode":"351","country":"PT"},{"areaCode":"1787","country":"PR"},{"areaCode":"974","country":"QA"},{"areaCode":"40","country":"RO"},{"areaCode":"7","country":"RU"},{"areaCode":"378","country":"SM"},{"areaCode":"239","country":"ST"},{"areaCode":"966","country":"SA"},{"areaCode":"221","country":"SN"},{"areaCode":"248","country":"SC"},{"areaCode":"232","country":"SL"},{"areaCode":"65","country":"SG"},{"areaCode":"421","country":"SK"},{"areaCode":"386","country":"SI"},{"areaCode":"677","country":"SB"},{"areaCode":"252","country":"SO"},{"areaCode":"27","country":"ZA"},{"areaCode":"34","country":"ES"},{"areaCode":"94","country":"LK"},{"areaCode":"249","country":"SD"},{"areaCode":"597","country":"SR"},{"areaCode":"268","country":"SZ"},{"areaCode":"46","country":"SE"},{"areaCode":"41","country":"CH"},{"areaCode":"886","country":"CN"},{"areaCode":"992","country":"TJ"},{"areaCode":"255","country":"TZ"},{"areaCode":"66","country":"TH"},{"areaCode":"228","country":"\tTG"},{"areaCode":"1809","country":"TT"},{"areaCode":"216","country":"TN"},{"areaCode":"90","country":"TR"},{"areaCode":"993","country":"TM"},{"areaCode":"256","country":"UG"},{"areaCode":"380","country":"UA"},{"areaCode":"971","country":"AE"},{"areaCode":"44","country":"GB"},{"areaCode":"1","country":"US"},{"areaCode":"598","country":"UY"},{"areaCode":"233","country":"UZ"},{"areaCode":"58","country":"VE"},{"areaCode":"84","country":"VN"},{"areaCode":"967","country":"YE"},{"areaCode":"263","country":"ZW"},{"areaCode":"260","country":"ZM"}]""";
        JSONArray jsonArray = JSON.parseArray(value);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject temp = jsonArray.getJSONObject(i);
            String areaCode = temp.getString("areaCode");
            String countryCode = temp.getString("country") + "001";
            GlobalCityEntity city = globalCityService.getEntityByCityCode(countryCode);
            if (city == null) {
                log.warn("city not found: {}", temp.toJSONString());
                continue;
            }
            CountryAreaCodeDTO dto = new CountryAreaCodeDTO();
            dto.setAreaCode(areaCode);
            dto.setCityId(city.getId());
            dto.setCityValueType(AreaCodeTypeEnum.COUNTRY_VALUE);
            dto.setRegx(ListUtil.empty());
            if (countryAreaCodeService.lambdaQuery().eq(CountryAreaCodeDTO::getAreaCode, dto.getAreaCode()).exists()) {
                continue;
            }
            countryAreaCodeService.save(dto);
        }

    }

    @Test
    public void test() {
        CityListResponse cityListResponse = globalCityService.allIataCityV2();
        log.info("json result: {}", JSON.toJSONString(cityListResponse));
    }

    @Test
    public void keyword() {
        String keyword = "北";
//        CityKeywordSearchVo cityKeywordSearchVo = globalCityService.keywordSearch(keyword);
//        log.info("cityKeywordSearchVo: {}", JSON.toJSONString(cityKeywordSearchVo));
    }

    @Test
    public void testCityLink() {
        CityLinkReq cityLinkReq = new CityLinkReq();
        cityLinkReq.setCityCode("61");
        //cityLinkReq.setKeyworld("深圳");
        List<CityLinkRes> res = globalCityService.cityLinkSearch(cityLinkReq);
        System.out.println(JSON.toJSONString(res));
    }
}
