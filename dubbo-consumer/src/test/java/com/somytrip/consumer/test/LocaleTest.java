package com.somytrip.consumer.test;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.somytrip.api.service.hotel.HotelFacilityTypeService;
import com.somytrip.api.service.hotel.HotelFacilityV2Service;
import com.somytrip.api.service.hotel.HotelGeneralService;
import com.somytrip.api.service.itinerary.ItineraryGuidebookService;
import com.somytrip.api.service.tourism.GlobalCityService;
import com.somytrip.api.service.tourism.ScenicService;
import com.somytrip.consumer.mapper.AirCodeCityMappingMapper;
import com.somytrip.consumer.mapper.GlobalCityMapper;
import com.somytrip.consumer.mapper.TrainStationLocaleMapper;
import com.somytrip.consumer.service.ConfigService;
import com.somytrip.entity.dto.Scenic;
import com.somytrip.entity.dto.city.GlobalCityEntity;
import com.somytrip.entity.dto.city.GlobalCityIataV2Dto;
import com.somytrip.entity.hotel.HotelFacilityTypeEntity;
import com.somytrip.entity.hotel.HotelFacilityV2Entity;
import com.somytrip.entity.hotel.HotelGeneralEntity;
import com.somytrip.entity.itinerary.ItineraryGuidebookEntity;
import com.somytrip.entity.train.TrainStationLocaleEntity;
import com.somytrip.utils.ListUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.controller.tourism
 * @className: ItineraryTest
 * @author: shadow
 * @description:
 * @date: 2024/3/27 16:46
 * @version: 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class LocaleTest {

    private static final String zh_CN = Locale.CHINA.toString();
    private static final String en_US = Locale.US.toString();
    private static final String zh_TW = Locale.TAIWAN.toString();
    @Resource
    private ConfigService configService;
    @Resource
    private ScenicService scenicService;
    @Resource
    private ItineraryGuidebookService itineraryGuidebookService;
    @DubboReference
    private HotelGeneralService hotelGeneralService;
    @Resource
    private GlobalCityService globalCityService;
    @DubboReference
    private HotelFacilityV2Service hotelFacilityV2Service;
    @DubboReference
    private HotelFacilityTypeService hotelFacilityTypeService;
    @Resource
    private AirCodeCityMappingMapper airCodeCityMappingMapper;
    @Resource
    private TrainStationLocaleMapper trainStationLocaleMapper;
    @Resource
    private GlobalCityMapper globalCityMapper;

    private static void processHotelGeneral(Map<String, Map<String, String>> localeDict,
                                            String KEY, String cn, String en) {

        if (!StringUtils.isAllBlank(cn, en)) {
            Locale zh_CN = Locale.CHINA;
            Locale en_US = Locale.US;

            localeDict.put(KEY, new HashMap<>());
            if (StringUtils.isNotBlank(cn)) {
                localeDict.get(KEY).put(zh_CN.toString(), cn);
            }
            if (StringUtils.isNotBlank(en)) {
                localeDict.get(KEY).put(en_US.toString(), en);
            }
        }
    }

    public static void main(String[] args) {
        try {
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(new FileInputStream("D:\\workspace\\document\\Translation Data\\enterprise_card.xlsx"));
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);
            // 获取最后一行的num，即总行数。此处从0开始
            int maxRow = sheet.getLastRowNum();
            for (int row = 1; row <= maxRow; row++) {
                String name_zh_CN = sheet.getRow(row).getCell(1) + "";
                String name_en_US = sheet.getRow(row).getCell(3) + "";
                String name_zh_TW = sheet.getRow(row).getCell(4) + "";

                String remark_zh_CN = sheet.getRow(row).getCell(2) + "";
                String remark_en_US = sheet.getRow(row).getCell(5) + "";
                String remark_zh_TW = sheet.getRow(row).getCell(6) + "";

                Map<String, Map<String, String>> localeDict = new LinkedHashMap<>();
                Map<String, String> nameLocaleDict = new LinkedHashMap<>();
                nameLocaleDict.put(zh_CN, name_zh_CN);
                nameLocaleDict.put(en_US, name_en_US);
                nameLocaleDict.put(zh_TW, name_zh_TW);
                localeDict.put("name", nameLocaleDict);
                Map<String, String> remarkLocaleDict = new LinkedHashMap<>();
                remarkLocaleDict.put(zh_CN, remark_zh_CN);
                remarkLocaleDict.put(en_US, remark_en_US);
                remarkLocaleDict.put(zh_TW, remark_zh_TW);
                localeDict.put("remark", remarkLocaleDict);

                log.info("localeDict: {}", JSON.toJSONString(localeDict));
            }
        } catch (IOException ignored) {
        }
    }

    private static void readScenicExcel(Map<String, String> en_USMap, Map<String, String> zh_TWMap, String path) {

//        Map<String, String> en_USMap = new HashMap<>();
//        Map<String, String> zh_TWMap = new HashMap<>();

        String noGood1 = "Please provide the Chinese";
        String noGood2 = "I don't understand";

        long start = System.currentTimeMillis();

        try {
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(new FileInputStream(path));
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);
            // 获取最后一行的num，即总行数。此处从0开始
            int maxRow = sheet.getLastRowNum();
            for (int row = 0; row <= maxRow; row++) {
                // 获取最后单元格num，即总单元格数 ***注意：此处从1开始计数***
//                int maxRol = sheet.getRow(row).getLastCellNum();
//                for (int rol = 0; rol < maxRol; rol++){
//                    System.out.print(sheet.getRow(row).getCell(rol) + "  ");
//                }
                String zh_CN = sheet.getRow(row).getCell(0) + "";
                String en_US = sheet.getRow(row).getCell(1) + "";
                String zh_TW = sheet.getRow(row).getCell(2) + "";

                zh_CN = zh_CN.replaceAll("\"", "").trim();
                if (StringUtils.isNotBlank(en_US)) {
                    if (!en_US.contains(noGood1) && !en_US.contains(noGood2)) {
                        en_US = en_US.replaceAll("\"", "").trim();
                        en_USMap.put(zh_CN, en_US);
                    }
                }

                if (StringUtils.isNotBlank(zh_TW)) {
                    zh_TWMap.put(zh_CN, zh_TW.replaceAll("\"", "").trim());
                }
            }

        } catch (IOException ignored) {
        }

        long end = System.currentTimeMillis();
        log.info("读取文件用时: {}, path: {}", end - start, path);
    }

    private static void setValue(Map<String, Map<String, String>> localeDict,
                                 String originalFieldValue,
                                 String fieldName,
                                 Map<String, String> en_USMap,
                                 Map<String, String> zh_TWMap) {

        if (StringUtils.isBlank(originalFieldValue)) return;
        originalFieldValue = originalFieldValue.trim();

        Map<String, String> subLocaleDict = localeDict.get(fieldName);
        if (subLocaleDict == null) subLocaleDict = new HashMap<>();
        subLocaleDict.put(zh_CN, originalFieldValue);
        if (StringUtils.isBlank(subLocaleDict.get(en_US))) {
            String en_USValue = en_USMap.get(originalFieldValue);
            subLocaleDict.put(en_US, en_USValue);
        }
        if (StringUtils.isBlank(subLocaleDict.get(zh_TW))) {
            String zh_TWValue = zh_TWMap.get(originalFieldValue);
            subLocaleDict.put(zh_TW, zh_TWValue);
        }
//        log.info("fieldName: {}, subLocaleDict: {}", fieldName, JSON.toJSONString(subLocaleDict));
        localeDict.put(fieldName, subLocaleDict);
    }
//    @Resource
//    private ScenicService scenicService;

//    public static void main(String[] args) {
//        BigDecimal b1 = new BigDecimal("550.0");
//        BigDecimal b2 = new BigDecimal("1");
//        log.info("b1: {}", b1.stripTrailingZeros().toPlainString());
//        log.info("res: {}", b1.equals(b2));
//    }

    private static List<TrainStationLocaleEntity> parseFile(String filePath) {
        List<TrainStationLocaleEntity> list = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = br.readLine()) != null) {
                String[] parts1 = line.split("@");
                for (int i = 0; i < parts1.length; i++) {
                    String line1 = parts1[i];
                    if (StrUtil.isBlank(line1)) {
                        continue;
                    }
                    String[] parts = line1.split("\\|");
                    TrainStationLocaleEntity data = new TrainStationLocaleEntity();
                    data.setName(parts[1]);
                    data.setPinyin(parts[3]);
                    list.add(data);
                    if (line1.startsWith("@")) {

                    }
                }

            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return list;
    }

    @Test
    public void testFacilityV2() {

//        String configValue = configService.getConfigValue(ConfigEnum.TOURISM_QUERY_SETTING_CONFIG_LIST);
//        TourismConfigList tourismConfigList = JSONObject.parseObject(configValue, TourismConfigList.class);
//        log.info("tourismConfigList: {}", JSON.toJSONString(tourismConfigList));

        try (BufferedReader br = Files.newBufferedReader(Paths.get("D:\\workspace\\dubbo-somytrip\\new.csv"))) {
            // CSV文件的分隔符
            String DELIMITER = ",";
            // 按行读取
            String line;
            while ((line = br.readLine()) != null) {
                // 分割
                String[] columns = line.split(DELIMITER);
//                // 打印行
//                System.out.println("User["+ String.join(", ", columns) +"]");
                log.info("columns: {}", Arrays.toString(columns));
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    @Test
    public void testGlobalCity() {

//        LocaleContextHolder.setLocale(Locale.TAIWAN);

        LambdaQueryWrapper<GlobalCityEntity> qw = new LambdaQueryWrapper<>();
        qw.isNull(GlobalCityEntity::getLocaleDict);
        List<GlobalCityEntity> list = globalCityService.list(qw);
//        log.info("list: {}", JSON.toJSONString(list));
        log.info("size: {}", list.size());

        String CITY_NAME = "cityName";
        String PROVINCE_NAME = "provinceName";
        String COUNTRY_NAME = "countryName";
        String CONTINENT_NAME = "continentName";

//        Locale zh_CN = Locale.CHINA;
//        Locale zh_TW = Locale.TAIWAN;
//        Locale en_US = Locale.US;

        for (GlobalCityEntity entity : list) {
            String cityNameCn = entity.getCityNameCn();
            String cityNameEn = entity.getCityNameEn();
            String cityNameFt = entity.getCityNameFt();
            String provinceNameCn = entity.getProvinceNameCn();
            String provinceNameEn = entity.getProvinceNameEn();
            String countryNameCn = entity.getCountryNameCn();
            String countryNameEn = entity.getCountryNameEn();
            String continentNameCn = entity.getContinentNameCn();
            String continentNameEn = entity.getContinentNameEn();

            Map<String, Map<String, String>> localeDict = new HashMap<>();
            localeDict.put(COUNTRY_NAME, new HashMap<>());

            if (!StringUtils.isAllBlank(cityNameCn, cityNameEn, cityNameFt)) {
                localeDict.put(CITY_NAME, new HashMap<>());

                if (StringUtils.isNotBlank(cityNameCn)) {
                    localeDict.get(CITY_NAME).put(zh_CN, cityNameCn);
                }
                if (StringUtils.isNotBlank(cityNameEn)) {
                    localeDict.get(CITY_NAME).put(en_US, cityNameEn);
                }
                if (StringUtils.isNotBlank(cityNameFt)) {
                    localeDict.get(CITY_NAME).put(zh_TW, cityNameFt);
                }
            }

            if (!StringUtils.isAllBlank(provinceNameCn, provinceNameEn)) {
                localeDict.put(PROVINCE_NAME, new HashMap<>());
                if (StringUtils.isNotBlank(provinceNameCn)) {
                    localeDict.get(PROVINCE_NAME).put(zh_CN, provinceNameCn);
                }
                if (StringUtils.isNotBlank(provinceNameEn)) {
                    localeDict.get(PROVINCE_NAME).put(en_US, provinceNameEn);
                }
                localeDict.get(PROVINCE_NAME).put(zh_TW, ZhConverterUtil.toTraditional(provinceNameCn));
            }

            if (!StringUtils.isAllBlank(countryNameCn, countryNameEn)) {
                localeDict.put(COUNTRY_NAME, new HashMap<>());
                if (StringUtils.isNotBlank(countryNameCn)) {
                    localeDict.get(COUNTRY_NAME).put(zh_CN, countryNameCn);
                }
                if (StringUtils.isNotBlank(countryNameEn)) {
                    localeDict.get(COUNTRY_NAME).put(en_US, countryNameEn);
                }
                localeDict.get(COUNTRY_NAME).put(zh_TW, ZhConverterUtil.toTraditional(countryNameCn));
            }

            if (!StringUtils.isAllBlank(continentNameCn, continentNameEn)) {
                localeDict.put(CONTINENT_NAME, new HashMap<>());
                if (StringUtils.isNotBlank(continentNameCn)) {
                    localeDict.get(CONTINENT_NAME).put(zh_CN, continentNameCn);
                }
                if (StringUtils.isNotBlank(continentNameEn)) {
                    localeDict.get(CONTINENT_NAME).put(en_US, continentNameEn);
                }
                localeDict.get(CONTINENT_NAME).put(zh_TW, ZhConverterUtil.toTraditional(continentNameCn));
            }

            log.info("localeDict: {}", JSON.toJSONString(localeDict));
            entity.setLocaleDict(localeDict);
            globalCityService.updateById(entity);
        }
    }

    @Test
    public void scenic() {

        String NAME = "name";
        Locale zh_CN = Locale.CHINA;
        Locale en_US = Locale.US;

//        qw.eq(Scenic::getScenicFlag, 1);
        LambdaQueryWrapper<Scenic> qw = new LambdaQueryWrapper<>();
        qw.eq(Scenic::getScenicFlag, 1).isNull(Scenic::getLocaleDict);
        List<Scenic> list = scenicService.list(qw);
        int size = list.size();
        int index = 0;
        for (Scenic scenic : list) {
            log.info("Start {}/{}", index++, size - 1);
            String name = scenic.getName();
            String engName = scenic.getEngName();

            Map<String, Map<String, String>> localeDict = new HashMap<>();
            if (!StringUtils.isAllBlank(name, engName)) {
                localeDict.put(NAME, new HashMap<>());
                if (StringUtils.isNotBlank(name)) {
                    localeDict.get(NAME).put(zh_CN.toString(), name);
                }
                if (StringUtils.isNotBlank(engName)) {
                    localeDict.get(NAME).put(en_US.toString(), engName);
                }
            }

//            log.info("localeDict: {}", JSON.toJSONString(localeDict));
            scenic.setLocaleDict(localeDict);
            scenicService.updateById(scenic);
        }
    }

    @Before
    public void initTable() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), HotelGeneralEntity.class);
    }

    @Test
    public void testUpdate() {

//        LambdaQueryWrapper<ItineraryGuidebookEntity> qw = new LambdaQueryWrapper<>();
//        qw.eq(ItineraryGuidebookEntity::getId, 1).select(
//                ItineraryGuidebookEntity::getId,
//                ItineraryGuidebookEntity::getCityId,
//                ItineraryGuidebookEntity::getCityNameCn,
//                ItineraryGuidebookEntity::getCountryId
////                ItineraryGuidebookEntity::getAlarmTel,
////                ItineraryGuidebookEntity::getTourismAdTel,
////                ItineraryGuidebookEntity::getChinaEmbassyTel,
////                ItineraryGuidebookEntity::getBrief,
////                ItineraryGuidebookEntity::getTripTime,
////                ItineraryGuidebookEntity::getActMessage
//        );
//        ItineraryGuidebookEntity entity = itineraryGuidebookService.getOne(qw);
//        log.info("entity: {}", JSON.toJSONString(entity));
//        entity.setCityNameCn("东京1");
//        itineraryGuidebookService.updateById(entity);

//        LambdaQueryWrapper<HotelGeneralEntity> qw = new LambdaQueryWrapper<>();
//        qw.eq(HotelGeneralEntity::getId, 1718741)
//                .select(
//                        HotelGeneralEntity::getId,
//                        HotelGeneralEntity::getHotelName,
//                        HotelGeneralEntity::getHotelNameEn,
//                        HotelGeneralEntity::getAddress,
//                        HotelGeneralEntity::getAddressEn,
//                        HotelGeneralEntity::getCountryName,
//                        HotelGeneralEntity::getCountryNameEn,
//                        HotelGeneralEntity::getProvinceName,
//                        HotelGeneralEntity::getProvinceNameEn,
//                        HotelGeneralEntity::getCityName,
//                        HotelGeneralEntity::getCityNameEn,
//                        HotelGeneralEntity::getBusinessZoneName,
//                        HotelGeneralEntity::getBusinessZoneNameEn,
//                        HotelGeneralEntity::getIntroEditor,
//                        HotelGeneralEntity::getIntroEditorEn,
//                        HotelGeneralEntity::getDescription,
//                        HotelGeneralEntity::getDescriptionEn,
//                        HotelGeneralEntity::getGroupName,
//                        HotelGeneralEntity::getGroupNameEn,
//                        HotelGeneralEntity::getBrandName,
//                        HotelGeneralEntity::getBrandNameEn
//                )
//        ;
//
//        HotelGeneralEntity entity = hotelGeneralService.getOne(qw);
//        entity.setHotelName("丽水岛华美达广场大石山酒店1");
////        hotelGeneralService.updateById(entity);
//        log.info("entity: {}", JSON.toJSONString(entity));
    }

    @Test
    public void hotelGeneral() {

        Locale zh_CN = Locale.CHINA;
        Locale en_US = Locale.US;
        String HOTEL_NAME = "hotelName";
        String ADDRESS = "address";
        String COUNTRY_NAME = "countryName";
        String PROVINCE_NAME = "provinceName";
        String CITY_NAME = "cityName";
        String BUSINESS_ZONE_NAME = "businessZoneName";
        String INTRO_EDITOR = "introEditor";
        String DESCRIPTION = "description";
//        String PET_POLICY = "petPolicy";
        String GROUP_NAME = "groupName";
        String BRAND_NAME = "brandName";

        int pageSize = 100;
        int pageNum = 1;
        int index = 0;

        while (true) {
            LambdaQueryWrapper<HotelGeneralEntity> qw = new LambdaQueryWrapper<>();
            qw.isNull(HotelGeneralEntity::getLocaleDict);
            IPage<HotelGeneralEntity> page = new Page<>(pageNum, pageSize);
            List<HotelGeneralEntity> list = hotelGeneralService.list(page, qw);
            if (ListUtil.isEmptySafe(list)) break;
            for (HotelGeneralEntity entity : list) {
                log.info("Start Index: {}", index++);
                Map<String, Map<String, String>> localeDict = new HashMap<>();

                String hotelName = entity.getHotelName();
                String hotelNameEn = entity.getHotelNameEn();
                if (!StringUtils.isAllBlank(hotelName, hotelNameEn)) {
                    localeDict.put(HOTEL_NAME, new HashMap<>());
                    if (StringUtils.isNotBlank(hotelName)) {
                        localeDict.get(HOTEL_NAME).put(zh_CN.toString(), hotelName);
                    }
                    if (StringUtils.isNotBlank(hotelNameEn)) {
                        localeDict.get(HOTEL_NAME).put(en_US.toString(), hotelNameEn);
                    }
                }

                String address = entity.getAddress();
                String addressEn = entity.getAddressEn();
                processHotelGeneral(localeDict, ADDRESS, address, addressEn);

                processHotelGeneral(localeDict, COUNTRY_NAME, entity.getCountryName(), entity.getCountryNameEn());
                processHotelGeneral(localeDict, PROVINCE_NAME, entity.getProvinceName(), entity.getProvinceNameEn());
                processHotelGeneral(localeDict, CITY_NAME, entity.getCityName(), entity.getCityNameEn());
                processHotelGeneral(localeDict, BUSINESS_ZONE_NAME, entity.getBusinessZoneName(), entity.getBusinessZoneNameEn());
                processHotelGeneral(localeDict, INTRO_EDITOR, entity.getIntroEditor(), entity.getIntroEditorEn());
                processHotelGeneral(localeDict, DESCRIPTION, entity.getDescription(), entity.getDescriptionEn());
                processHotelGeneral(localeDict, GROUP_NAME, entity.getGroupName(), entity.getGroupNameEn());
                processHotelGeneral(localeDict, BRAND_NAME, entity.getBrandName(), entity.getBrandNameEn());

//                log.info("localeDict: {}", JSON.toJSONString(localeDict));
                entity.setLocaleDict(localeDict);
                hotelGeneralService.updateById(entity);
            }

            ++pageNum;
        }
    }

    @Test
    public void processScenic() {

        // 景点名称
        Map<String, String> nameMap_en_US = new HashMap<>();
        Map<String, String> nameMap_zh_TW = new HashMap<>();
        readScenicExcel(nameMap_en_US, nameMap_zh_TW, "D:\\workspace\\document\\Translation Data\\scenic_name.xlsx");

//        // 地址
//        Map<String, String> addressMap_en_US = new LinkedHashMap<>();
//        Map<String, String> addressMap_zh_TW = new LinkedHashMap<>();
//        readScenicExcel(addressMap_en_US, addressMap_zh_TW, "D:\\workspace\\document\\Translation Data\\scenic_address.xlsx");
//
//        // 介绍
//        Map<String, String> briefMap_en_US = new LinkedHashMap<>();
//        Map<String, String> briefMap_zh_TW = new LinkedHashMap<>();
//        readScenicExcel(briefMap_en_US, briefMap_zh_TW, "D:\\workspace\\document\\Translation Data\\scenic_brief.xlsx");
//
//        // 营业时间
//        Map<String, String> businessHourMap_en_US = new LinkedHashMap<>();
//        Map<String, String> businessHourMap_zh_TW = new LinkedHashMap<>();
//        readScenicExcel(businessHourMap_en_US, businessHourMap_zh_TW, "D:\\workspace\\document\\Translation Data\\scenic_business_hours.xlsx");
//
//        // 城市
//        Map<String, String> cityMap_en_US = new LinkedHashMap<>();
//        Map<String, String> cityMap_zh_TW = new LinkedHashMap<>();
//        readScenicExcel(cityMap_en_US, cityMap_zh_TW, "D:\\workspace\\document\\Translation Data\\scenic_city.xlsx");

//        log.info("en_USMap: {}", JSON.toJSONString(en_USMap));

        LambdaQueryWrapper<Scenic> qw = new LambdaQueryWrapper<>();
        qw.eq(Scenic::getScenicFlag, 1).isNotNull(Scenic::getLocaleDict);
        List<Scenic> list = scenicService.list(qw);
        int size = list.size();
        int index = 0;

        String NAME = "name";
        String ADDRESS = "address";
        String BRIEF = "brief";
        String BUSINESS_HOURS = "businessHours";
        String CITY = "city";

        List<Scenic> dataList = new ArrayList<>();
        for (Scenic scenic : list) {
            log.info("Start Index: {}/{}", index++, size - 1);
            Map<String, Map<String, String>> localeDict = scenic.getLocaleDict();
            if (localeDict == null) localeDict = new HashMap<>();

            // 景点名称
//            String zh_CNName = scenic.getName();
//            Map<String, String> nameLocaleDict = localeDict.get(NAME);
//            if (nameLocaleDict.get(en_US) == null) {
//                String en_USName = nameMap_en_US.get(zh_CNName);
//                nameLocaleDict.put(en_US, en_USName);
//            }
//            if (nameLocaleDict.get(zh_TW) == null) {
//                String zh_TWName = nameMap_zh_TW.get(zh_CNName);
//                nameLocaleDict.put(zh_TW, zh_TWName);
//            }
            String name = scenic.getName();
            setValue(localeDict, name, NAME, nameMap_en_US, nameMap_zh_TW);

//            // 地址
//            String address = scenic.getAddress();
//            setValue(localeDict, address, ADDRESS, addressMap_en_US, addressMap_zh_TW);
//
//            // 介绍
//            String brief = scenic.getBrief();
//            setValue(localeDict, brief, BRIEF, briefMap_en_US, briefMap_zh_TW);
//
//            // 营业时间
//            String businessHours = scenic.getBusinessHours();
//            setValue(localeDict, businessHours, BUSINESS_HOURS, businessHourMap_en_US, businessHourMap_zh_TW);
//
//            // 城市
//            String city = scenic.getCity();
//            setValue(localeDict, city, CITY, cityMap_en_US, cityMap_zh_TW);

//            log.info("localeDict: {}", JSON.toJSONString(scenic.getLocaleDict()));
//            scenicService.updateById(scenic);
//            DBDataUtil.processBatchSaveAsync(dataList, scenic, scenicService);
            dataList.add(scenic);
            if (dataList.size() >= 5000) {
                log.info("save");
                scenicService.updateBatchById(dataList);
                dataList.clear();
            }
        }
        scenicService.updateBatchById(dataList);
//        DBDataUtil.batchSaveAsync(dataList, scenicService);
    }

    @Test
    public void processGuidebook() {

        String BRIEF = "brief";
        String TRIP_TIME = "tripTime";
        String ACT_MESSAGE = "actMessage";

        List<ItineraryGuidebookEntity> list = itineraryGuidebookService.list();
        List<ItineraryGuidebookEntity> updateList = new ArrayList<>();
        try {
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(new FileInputStream("D:\\workspace\\document\\Translation Data\\itinerary_guidebook.xlsx"));
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);
            // 获取最后一行的num，即总行数。此处从0开始
            int maxRow = sheet.getLastRowNum();
            for (int row = 1; row <= maxRow; row++) {
                Map<String, Map<String, String>> localeDict = new HashMap<>();

                String id = sheet.getRow(row).getCell(0) + "";
                String idValue = new BigDecimal(id).stripTrailingZeros().toPlainString();
                ItineraryGuidebookEntity target = list.stream()
                        .filter(item -> Objects.equals(idValue, String.valueOf(item.getId())))
//                        .filter(item -> idValue.equals(BigDecimal.valueOf(item.getId())))
                        .findFirst()
                        .orElse(null);
//                log.info("target: {}", JSON.toJSONString(target));

                if (target == null) {
                    log.warn("ID: {}, Not Found", idValue);
                    continue;
                }

                String brief_zh_CN = target.getBrief();
                String brief_en_US = sheet.getRow(row).getCell(4) + "";
                String brief_zh_TW = sheet.getRow(row).getCell(5) + "";

                String tripTime_zh_CN = target.getTripTime();
                String tripTime_en_US = sheet.getRow(row).getCell(6) + "";
                String tripTime_zh_TW = sheet.getRow(row).getCell(7) + "";

                String actMessage_zh_Cn = target.getActMessage();
                String actMessage_en_US = sheet.getRow(row).getCell(8) + "";
                String actMessage_zh_TW = sheet.getRow(row).getCell(9) + "";

                if (StringUtils.isNotBlank(brief_zh_CN)) {
                    Map<String, String> briefLocaleDict = new HashMap<>();
                    briefLocaleDict.put(zh_CN, brief_zh_CN);
                    briefLocaleDict.put(en_US, brief_en_US);
                    briefLocaleDict.put(zh_TW, brief_zh_TW);
                    localeDict.put(BRIEF, briefLocaleDict);
                }

                if (StringUtils.isNotBlank(tripTime_zh_CN)) {
                    Map<String, String> tripTimeLocaleDict = new HashMap<>();
                    tripTimeLocaleDict.put(zh_CN, tripTime_zh_CN);
                    tripTimeLocaleDict.put(en_US, tripTime_en_US);
                    tripTimeLocaleDict.put(zh_TW, tripTime_zh_TW);
                    localeDict.put(TRIP_TIME, tripTimeLocaleDict);
                }

                if (StringUtils.isNotBlank(actMessage_zh_Cn)) {
                    Map<String, String> actMessageLocaleDict = new HashMap<>();
                    actMessageLocaleDict.put(zh_CN, actMessage_zh_Cn);
                    actMessageLocaleDict.put(en_US, actMessage_en_US);
                    actMessageLocaleDict.put(zh_TW, actMessage_zh_TW);
                    localeDict.put(ACT_MESSAGE, actMessageLocaleDict);
                }

//                log.info("localeDict: {}", JSON.toJSONString(localeDict));
                target.setLocaleDict(localeDict);
                updateList.add(target);
            }

        } catch (IOException ignored) {
        }

        itineraryGuidebookService.updateBatchById(updateList);
    }

    @Test
    public void processHotelFacilities() {

        String FACILITY_NAME = "facilityName";

        List<HotelFacilityV2Entity> list = hotelFacilityV2Service.list();
        Map<String, String> facilityNameMap_en_US = new HashMap<>();
        Map<String, String> facilityNameMap_zh_TW = new HashMap<>();

        try {
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(new FileInputStream("D:\\workspace\\document\\Translation Data\\hotel_facilities_v2.xlsx"));
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);
            // 获取最后一行的num，即总行数。此处从0开始
            int maxRow = sheet.getLastRowNum();
            for (int row = 1; row <= maxRow; row++) {
                String facilityName_zh_CN = sheet.getRow(row).getCell(1) + "";
                String facilityName_en_US = sheet.getRow(row).getCell(2) + "";
                String facilityName_zh_TW = sheet.getRow(row).getCell(3) + "";

                facilityNameMap_en_US.put(facilityName_zh_CN, facilityName_en_US);
                facilityNameMap_zh_TW.put(facilityName_zh_CN, facilityName_zh_TW);
            }
        } catch (IOException ignored) {
        }

        for (HotelFacilityV2Entity entity : list) {
            String facilityName = entity.getFacilityName();
            String facilityName_en_US = facilityNameMap_en_US.get(facilityName);
            String facilityName_zh_TW = facilityNameMap_zh_TW.get(facilityName);

            Map<String, Map<String, String>> localeDict = new HashMap<>();
            Map<String, String> facilityNameLocaleDict = new HashMap<>();

            facilityNameLocaleDict.put(zh_CN, facilityName);
            if (StringUtils.isNotBlank(facilityName_en_US)) {
                facilityNameLocaleDict.put(en_US, facilityName_en_US);
            }
            if (StringUtils.isNotBlank(facilityName_zh_TW)) {
                facilityNameLocaleDict.put(zh_TW, facilityName_zh_TW);
            }
            localeDict.put(FACILITY_NAME, facilityNameLocaleDict);
            entity.setLocaleDict(localeDict);

            log.info("localeDict: {}", JSON.toJSONString(localeDict));
        }

        hotelFacilityV2Service.updateBatchById(list);
    }

    @Test
    public void processHotelFacilityTypes() {

        String FACILITY_TYPE_NAME = "facilityTypeName";
        List<HotelFacilityTypeEntity> list = hotelFacilityTypeService.list();
        Map<String, String> facilityTypeNameMap_en_US = new HashMap<>();
        Map<String, String> facilityTypeNameMap_zh_TW = new HashMap<>();

        try {
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(new FileInputStream("D:\\workspace\\document\\Translation Data\\hotel_facility_types.xlsx"));
            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);
            // 获取最后一行的num，即总行数。此处从0开始
            int maxRow = sheet.getLastRowNum();
            for (int row = 1; row <= maxRow; row++) {
                String facilityTypeName_zh_CN = sheet.getRow(row).getCell(1) + "";
                String facilityTypeName_en_US = sheet.getRow(row).getCell(2) + "";
                String facilityTypeName_zh_TW = sheet.getRow(row).getCell(3) + "";

                facilityTypeNameMap_en_US.put(facilityTypeName_zh_CN, facilityTypeName_en_US);
                facilityTypeNameMap_zh_TW.put(facilityTypeName_zh_CN, facilityTypeName_zh_TW);
            }
        } catch (IOException ignored) {
        }

        for (HotelFacilityTypeEntity entity : list) {
            String facilityTypeName = entity.getFacilityTypeName();
            String facilityTypeName_en_US = facilityTypeNameMap_en_US.get(facilityTypeName);
            String facilityTypeName_zh_TW = facilityTypeNameMap_zh_TW.get(facilityTypeName);

            Map<String, Map<String, String>> localeDict = new HashMap<>();
            Map<String, String> facilityTypeNameLocaleDict = new HashMap<>();

            facilityTypeNameLocaleDict.put(zh_CN, facilityTypeName);
            if (StringUtils.isNotBlank(facilityTypeName_en_US)) {
                facilityTypeNameLocaleDict.put(en_US, facilityTypeName_en_US);
            }
            if (StringUtils.isNotBlank(facilityTypeName_zh_TW)) {
                facilityTypeNameLocaleDict.put(zh_TW, facilityTypeName_zh_TW);
            }
            localeDict.put(FACILITY_TYPE_NAME, facilityTypeNameLocaleDict);
            entity.setLocaleDict(localeDict);

            log.info("localeDict: {}", JSON.toJSONString(localeDict));
        }

        hotelFacilityTypeService.updateBatchById(list);
    }

    //    @Resource
//    private TestHotelLocaleMapper testHotelLocaleMapper;
    @Test
    public void hotel() {

//        long start = System.currentTimeMillis();
//        Map<String, Map<String, String>> introEditorMap = new HashMap<>();
//        int introEditorPageNum = 1;
//        int introEditorPageSize = 100;
//        while (true) {
//            IPage<JSONObject> introEditorPage = new Page<>(introEditorPageNum++, introEditorPageSize);
//            introEditorPage = testHotelLocaleMapper.introEditorList(introEditorPage);
//
//            List<JSONObject> records = introEditorPage.getRecords();
//            if (ListUtil.isEmptySafe(records)) break;
//
//            for (JSONObject record : records) {
//                String hotelId = record.getString("hotel_id");
//                String hotelOrigin = record.getString("hotel_origin");
//                String introEditor_zh_CN = record.getString("intro_editor");
//                String introEditor_en_US = record.getString("intro_editor_en");
//                String introEditor_zh_TW = record.getString("intro_editor_ft");
//
//                introEditor_zh_CN = processField(introEditor_zh_CN);
//                introEditor_en_US = processField(introEditor_en_US);
//                introEditor_zh_TW = processField(introEditor_zh_TW);
//
//                String hotelIdOrigin = hotelOrigin + "-" + hotelId;
//                introEditorMap.put(hotelIdOrigin, new HashMap<>());
//                introEditorMap.get(hotelIdOrigin).put(zh_CN, introEditor_zh_CN);
//                introEditorMap.get(hotelIdOrigin).put(en_US, introEditor_en_US);
//                introEditorMap.get(hotelIdOrigin).put(zh_TW, introEditor_zh_TW);
//
////                log.info("introEditorMap: {}", JSON.toJSONString(introEditorMap));
//            }
//        }

//        long end = System.currentTimeMillis();
//        log.info("用时: {}", end - start);

//        List<JSONObject> hotelGeneralList = testHotelLocaleMapper.hotelGeneralList();
//        LambdaQueryWrapper<HotelGeneralEntity> hotelGeneralQW = new LambdaQueryWrapper<>();
//        hotelGeneralQW.eq(HotelGeneralEntity::getHotelId, "23073").eq(HotelGeneralEntity::getHotelOrigin, HotelOrigin.NCNB);
//        HotelGeneralEntity entity = hotelGeneralService.getOne(hotelGeneralQW);


//        int hotelGeneralPageNum = 1;
//        int hotelGeneralPageSize = 10;
//        LambdaQueryWrapper<HotelGeneralEntity> hotelGeneralQW = new LambdaQueryWrapper<>();
//        hotelGeneralQW.select(
//                HotelGeneralEntity::getId,
//                HotelGeneralEntity::getHotelId,
//                HotelGeneralEntity::getHotelOrigin,
//                HotelGeneralEntity::getLocaleDict
//        );
//        while (true) {
//            IPage<HotelGeneralEntity> hotelGeneralPage = new Page<>(hotelGeneralPageNum++, hotelGeneralPageSize);
////            hotelGeneralPage = hotelGeneralService.page(hotelGeneralPage, hotelGeneralQW);
////            hotelGeneralPage = testHotelLocaleMapper.hotelGeneralList(hotelGeneralPage);
////            List<HotelGeneralEntity> hotelGeneralList = hotelGeneralService.list(hotelGeneralPage, hotelGeneralQW);
//            List<Object> hotelGeneralList = testHotelLocaleMapper.hotelGeneralList();
////            List<HotelGeneralEntity> records = hotelGeneralPage.getRecords();
//            if (ListUtil.isEmptySafe(hotelGeneralList)) break;
//
//            log.info("hotelGeneralList: {}", JSON.toJSONString(hotelGeneralList));
//        }
    }

    private String processField(String field) {


        boolean startsWithPrefix = field.startsWith("\"");
        boolean endsWithPrefix = field.endsWith("\"");

        // 替换开头
        if (startsWithPrefix) {
            field = field.replaceFirst("^" + "\"", "");
        }

        // 替换结尾
        if (endsWithPrefix) {
            field = field.replaceAll("\"" + "$", "");
        }

        return field;
    }

    @Test
    public void processAirportCodeNameMapping() {

//        List<AirportCodeNameDTO> list = airCodeCityMappingMapper.selectAirportCodeNameDTOList();
////        Map<String, String> airportNameMap_en_US = new HashMap<>();
////        Map<String, String> airportNameMap_zh_TW = new HashMap<>();
//
//        try {
//            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(new FileInputStream("D:\\workspace\\document\\Translation Data\\airport_code_name.xlsx"));
//            XSSFSheet sheet = xssfWorkbook.getSheetAt(0);
//            // 获取最后一行的num，即总行数。此处从0开始
//            int maxRow = sheet.getLastRowNum();
//            for (int row = 1; row <= maxRow; row++) {
//                String cityId = new BigDecimal(sheet.getRow(row).getCell(0) + "").stripTrailingZeros().toPlainString();
//                String iataCode = sheet.getRow(row).getCell(3) + "";
//                log.info("cityId: {}, iataCode: {}", cityId, iataCode);
//
//                String airportName_zh_CN = sheet.getRow(row).getCell(4) + "";
//                String airportName_en_US = sheet.getRow(row).getCell(1) + "";
//                String airportName_zh_TW = sheet.getRow(row).getCell(2) + "";
//
////                airportNameMap_en_US.put(airportName_zh_CN, airportName_en_US);
////                airportNameMap_zh_TW.put(airportName_zh_CN, airportName_zh_TW);
//
//                AirportCodeNameDTO target = list.stream()
//                        .filter(item -> Objects.equals(item.getCode(), iataCode)
//                                && Objects.equals(String.valueOf(item.getCityId()), cityId))
//                        .findFirst()
//                        .orElse(null);
//
//                if (target == null) {
//                    continue;
//                }
//
//                Map<String, Map<String, String>> localeDict = new HashMap<>();
//                Map<String, String> airportNameLocaleDict = new LinkedHashMap<>();
//                airportNameLocaleDict.put(zh_CN, airportName_zh_CN);
//                airportNameLocaleDict.put(en_US, airportName_en_US);
//                airportNameLocaleDict.put(zh_TW, airportName_zh_TW);
//                localeDict.put("airportName", airportNameLocaleDict);
//
//                target.setLocaleDict(localeDict);
////                log.info("localeDict: {}", JSON.toJSONString(localeDict));
//
////                airCodeCityMappingMapper.updateLocaleDict(target);
//            }
//        } catch (IOException ignored) {
//        }
//
//        // 创建工作簿
//        Workbook workbook = new XSSFWorkbook();
//
//        // 创建工作表
//        Sheet sheet = workbook.createSheet("sheet1");
//
//        // 创建第一行
//        Row headerRow = sheet.createRow(0);
//        Cell headerCell = headerRow.createCell(0);
//        headerCell.setCellValue("localeDict");
//
//        // 创建数据行
//        for (int i = 0; i < list.size(); i++) {
//            Row row = sheet.createRow(i + 1); // 下一行
//            Cell cell = row.createCell(0); // 第一列
//            cell.setCellValue(JSON.toJSONString(list.get(i).getLocaleDict()));
//        }
//
//        // 保存Excel文件
//        try (FileOutputStream fileOut = new FileOutputStream("output.xlsx")) {
//            workbook.write(fileOut);
//            System.out.println("Excel file created successfully!");
//        } catch (IOException e) {
//            log.error(e.getMessage(), e);
//        } finally {
//            try {
//                workbook.close();
//            } catch (IOException e) {
//                log.error(e.getMessage(), e);
//            }
//        }
    }

    @Test
    public void importBaiduTrainStation() {

        String data = "33,嘉峪关市\n" +
                "34,金昌市\n" +
                "35,白银市\n" +
                "36,兰州市\n" +
                "37,酒泉市\n" +
                "38,大兴安岭地区\n" +
                "39,黑河市\n" +
                "40,伊春市\n" +
                "41,齐齐哈尔市\n" +
                "42,佳木斯市\n" +
                "43,鹤岗市\n" +
                "44,绥化市\n" +
                "45,双鸭山市\n" +
                "46,鸡西市\n" +
                "47,七台河市\n" +
                "48,哈尔滨市\n" +
                "49,牡丹江市\n" +
                "50,大庆市\n" +
                "51,白城市\n" +
                "52,松原市\n" +
                "53,长春市\n" +
                "54,延边朝鲜族自治州\n" +
                "55,吉林市\n" +
                "56,四平市\n" +
                "57,白山市\n" +
                "58,沈阳市\n" +
                "59,阜新市\n" +
                "60,铁岭市\n" +
                "61,呼伦贝尔市\n" +
                "62,兴安盟\n" +
                "63,锡林郭勒盟\n" +
                "64,通辽市\n" +
                "65,海西蒙古族藏族自治州\n" +
                "66,西宁市\n" +
                "67,海北藏族自治州\n" +
                "68,海南藏族自治州\n" +
                "69,海东地区\n" +
                "70,黄南藏族自治州\n" +
                "71,玉树藏族自治州\n" +
                "72,果洛藏族自治州\n" +
                "73,甘孜藏族自治州\n" +
                "74,德阳市\n" +
                "75,成都市\n" +
                "76,雅安市\n" +
                "77,眉山市\n" +
                "78,自贡市\n" +
                "79,乐山市\n" +
                "80,凉山彝族自治州\n" +
                "81,攀枝花市\n" +
                "82,和田地区\n" +
                "83,喀什地区\n" +
                "84,克孜勒苏柯尔克孜自治州\n" +
                "85,阿克苏地区\n" +
                "86,巴音郭楞蒙古自治州\n" +
                "88,博尔塔拉蒙古自治州\n" +
                "89,吐鲁番地区\n" +
                "90,伊犁哈萨克自治州\n" +
                "91,哈密地区\n" +
                "92,乌鲁木齐市\n" +
                "93,昌吉回族自治州\n" +
                "94,塔城地区\n" +
                "95,克拉玛依市\n" +
                "96,阿勒泰地区\n" +
                "97,山南地区\n" +
                "98,林芝地区\n" +
                "99,昌都地区\n" +
                "100,拉萨市\n" +
                "101,那曲地区\n" +
                "102,日喀则地区\n" +
                "103,阿里地区\n" +
                "104,昆明市\n" +
                "105,楚雄彝族自治州\n" +
                "106,玉溪市\n" +
                "107,红河哈尼族彝族自治州\n" +
                "108,普洱市\n" +
                "109,西双版纳傣族自治州\n" +
                "110,临沧市\n" +
                "111,大理白族自治州\n" +
                "112,保山市\n" +
                "113,怒江傈僳族自治州\n" +
                "114,丽江市\n" +
                "115,迪庆藏族自治州\n" +
                "116,德宏傣族景颇族自治州\n" +
                "117,张掖市\n" +
                "118,武威市\n" +
                "119,东莞市\n" +
                "120,东沙群岛\n" +
                "121,三亚市\n" +
                "122,鄂州市\n" +
                "123,乌海市\n" +
                "124,莱芜市\n" +
                "125,海口市\n" +
                "126,蚌埠市\n" +
                "127,合肥市\n" +
                "128,阜阳市\n" +
                "129,芜湖市\n" +
                "130,安庆市\n" +
                "131,北京市\n" +
                "132,重庆市\n" +
                "133,南平市\n" +
                "134,泉州市\n" +
                "135,庆阳市\n" +
                "136,定西市\n" +
                "137,韶关市\n" +
                "138,佛山市\n" +
                "139,茂名市\n" +
                "140,珠海市\n" +
                "141,梅州市\n" +
                "142,桂林市\n" +
                "143,河池市\n" +
                "144,崇左市\n" +
                "145,钦州市\n" +
                "146,贵阳市\n" +
                "147,六盘水市\n" +
                "148,秦皇岛市\n" +
                "149,沧州市\n" +
                "150,石家庄市\n" +
                "151,邯郸市\n" +
                "152,新乡市\n" +
                "153,洛阳市\n" +
                "154,商丘市\n" +
                "155,许昌市\n" +
                "156,襄阳市\n" +
                "157,荆州市\n" +
                "158,长沙市\n" +
                "159,衡阳市\n" +
                "160,镇江市\n" +
                "161,南通市\n" +
                "162,淮安市\n" +
                "163,南昌市\n" +
                "164,新余市\n" +
                "165,通化市\n" +
                "166,锦州市\n" +
                "167,大连市\n" +
                "168,乌兰察布市\n" +
                "169,巴彦淖尔市\n" +
                "170,渭南市\n" +
                "171,宝鸡市\n" +
                "172,枣庄市\n" +
                "173,日照市\n" +
                "174,东营市\n" +
                "175,威海市\n" +
                "176,太原市\n" +
                "177,文山壮族苗族自治州\n" +
                "178,温州市\n" +
                "179,杭州市\n" +
                "180,宁波市\n" +
                "181,中卫市\n" +
                "182,临夏回族自治州\n" +
                "183,辽源市\n" +
                "184,抚顺市\n" +
                "185,阿坝藏族羌族自治州\n" +
                "186,宜宾市\n" +
                "187,中山市\n" +
                "188,亳州市\n" +
                "189,滁州市\n" +
                "190,宣城市\n" +
                "191,廊坊市\n" +
                "192,宁德市\n" +
                "193,龙岩市\n" +
                "194,厦门市\n" +
                "195,莆田市\n" +
                "196,天水市\n" +
                "197,清远市\n" +
                "198,湛江市\n" +
                "199,阳江市\n" +
                "200,河源市\n" +
                "201,潮州市\n" +
                "202,来宾市\n" +
                "203,百色市\n" +
                "204,防城港市\n" +
                "205,铜仁地区\n" +
                "206,毕节地区\n" +
                "207,承德市\n" +
                "208,衡水市\n" +
                "209,濮阳市\n" +
                "210,开封市\n" +
                "211,焦作市\n" +
                "212,三门峡市\n" +
                "213,平顶山市\n" +
                "214,信阳市\n" +
                "215,鹤壁市\n" +
                "216,十堰市\n" +
                "217,荆门市\n" +
                "218,武汉市\n" +
                "219,常德市\n" +
                "220,岳阳市\n" +
                "221,娄底市\n" +
                "222,株洲市\n" +
                "223,盐城市\n" +
                "224,苏州市\n" +
                "225,景德镇市\n" +
                "226,抚州市\n" +
                "227,本溪市\n" +
                "228,盘锦市\n" +
                "229,包头市\n" +
                "230,阿拉善盟\n" +
                "231,榆林市\n" +
                "232,铜川市\n" +
                "233,西安市\n" +
                "234,临沂市\n" +
                "235,滨州市\n" +
                "236,青岛市\n" +
                "237,朔州市\n" +
                "238,晋中市\n" +
                "239,巴中市\n" +
                "240,绵阳市\n" +
                "241,广安市\n" +
                "242,资阳市\n" +
                "243,衢州市\n" +
                "244,台州市\n" +
                "245,舟山市\n" +
                "246,固原市\n" +
                "247,甘南藏族自治州\n" +
                "248,内江市\n" +
                "249,曲靖市\n" +
                "250,淮南市\n" +
                "251,巢湖市\n" +
                "252,黄山市\n" +
                "253,淮北市\n" +
                "254,三明市\n" +
                "255,漳州市\n" +
                "256,陇南市\n" +
                "257,广州市\n" +
                "258,云浮市\n" +
                "259,揭阳市\n" +
                "260,贺州市\n" +
                "261,南宁市\n" +
                "262,遵义市\n" +
                "263,安顺市\n" +
                "264,张家口市\n" +
                "265,唐山市\n" +
                "266,邢台市\n" +
                "267,安阳市\n" +
                "268,郑州市\n" +
                "269,驻马店市\n" +
                "270,宜昌市\n" +
                "271,黄冈市\n" +
                "272,益阳市\n" +
                "273,邵阳市\n" +
                "274,湘西土家族苗族自治州\n" +
                "275,郴州市\n" +
                "276,泰州市\n" +
                "277,宿迁市\n" +
                "278,宜春市\n" +
                "279,鹰潭市\n" +
                "280,朝阳市\n" +
                "281,营口市\n" +
                "282,丹东市\n" +
                "283,鄂尔多斯市\n" +
                "284,延安市\n" +
                "285,商洛市\n" +
                "286,济宁市\n" +
                "287,潍坊市\n" +
                "288,济南市\n" +
                "289,上海市\n" +
                "290,晋城市\n" +
                "291,南充市\n" +
                "292,丽水市\n" +
                "293,绍兴市\n" +
                "294,湖州市\n" +
                "295,北海市\n" +
                "297,赤峰市\n" +
                "298,六安市\n" +
                "299,池州市\n" +
                "300,福州市\n" +
                "301,惠州市\n" +
                "302,江门市\n" +
                "303,汕头市\n" +
                "304,梧州市\n" +
                "305,柳州市\n" +
                "306,黔南布依族苗族自治州\n" +
                "307,保定市\n" +
                "308,周口市\n" +
                "309,南阳市\n" +
                "310,孝感市\n" +
                "311,黄石市\n" +
                "312,张家界市\n" +
                "313,湘潭市\n" +
                "314,永州市\n" +
                "315,南京市\n" +
                "316,徐州市\n" +
                "317,无锡市\n" +
                "318,吉安市\n" +
                "319,葫芦岛市\n" +
                "320,鞍山市\n" +
                "321,呼和浩特市\n" +
                "322,吴忠市\n" +
                "323,咸阳市\n" +
                "324,安康市\n" +
                "325,泰安市\n" +
                "326,烟台市\n" +
                "327,吕梁市\n" +
                "328,运城市\n" +
                "329,广元市\n" +
                "330,遂宁市\n" +
                "331,泸州市\n" +
                "332,天津市\n" +
                "333,金华市\n" +
                "334,嘉兴市\n" +
                "335,石嘴山市\n" +
                "336,昭通市\n" +
                "337,铜陵市\n" +
                "338,肇庆市\n" +
                "339,汕尾市\n" +
                "340,深圳市\n" +
                "341,贵港市\n" +
                "342,黔东南苗族侗族自治州\n" +
                "343,黔西南布依族苗族自治州\n" +
                "344,漯河市\n" +
                "346,扬州市\n" +
                "347,连云港市\n" +
                "348,常州市\n" +
                "349,九江市\n" +
                "350,萍乡市\n" +
                "351,辽阳市\n" +
                "352,汉中市\n" +
                "353,菏泽市\n" +
                "354,淄博市\n" +
                "355,大同市\n" +
                "356,长治市\n" +
                "357,阳泉市\n" +
                "358,马鞍山市\n" +
                "359,平凉市\n" +
                "360,银川市\n" +
                "361,玉林市\n" +
                "362,咸宁市\n" +
                "363,怀化市\n" +
                "364,上饶市\n" +
                "365,赣州市\n" +
                "366,聊城市\n" +
                "367,忻州市\n" +
                "368,临汾市\n" +
                "369,达州市\n" +
                "370,宿州市\n" +
                "371,随州市\n" +
                "372,德州市\n" +
                "373,恩施土家族苗族自治州\n" +
                "731,阿拉尔市\n" +
                "770,石河子市\n" +
                "789,五家渠市\n" +
                "792,图木舒克市\n" +
                "1214,定安县\n" +
                "1215,儋州市\n" +
                "1216,万宁市\n" +
                "1217,保亭黎族苗族自治县\n" +
                "1218,西沙群岛\n" +
                "1277,济源市\n" +
                "1293,潜江市\n" +
                "1498,中沙群岛\n" +
                "1515,南沙群岛\n" +
                "1641,屯昌县\n" +
                "1642,昌江黎族自治县\n" +
                "1643,陵水黎族自治县\n" +
                "1644,五指山市\n" +
                "1713,仙桃市\n" +
                "2031,琼中黎族苗族自治县\n" +
                "2032,乐东黎族自治县\n" +
                "2033,临高县\n" +
                "2358,琼海市\n" +
                "2359,白沙黎族自治县\n" +
                "2634,东方市\n" +
                "2654,天门市\n" +
                "2734,神农架林区\n" +
                "2757,澄迈县\n" +
                "2758,文昌市\n" +
                "2911,澳门特别行政区\n" +
                "2912,香港特别行政区\n" +
                "9001,桃园市\n" +
                "9002,台北市\n" +
                "9003,南投县\n" +
                "9004,嘉义市\n" +
                "9005,彰化县\n" +
                "9006,新竹县\n" +
                "9007,澎湖县\n" +
                "9008,台东县\n" +
                "9009,宜兰县\n" +
                "9010,新北市\n" +
                "9011,基隆市\n" +
                "9012,屏东县\n" +
                "9013,嘉义县\n" +
                "9014,云林县\n" +
                "9015,花莲县\n" +
                "9016,台南市\n" +
                "9017,台中市\n" +
                "9018,新竹市\n" +
                "9019,高雄市\n" +
                "9020,苗栗县";

        ArrayList<String> dataList = new ArrayList<>(Arrays.asList(data.split("\n")));
        System.out.println(dataList.size());

        String url = "https://api.map.baidu.com/place/v2/search?query=railway_station&tag=交通设施&region=%s" +
                "&output=json&ak=GlgdDupquW6Ik37e8pBGIgg79TLqQtkO&page_num=%s&page_size=%S";
        int pageSize = 20;

        int index = 1;
//        for (String d : dataList) {
//            String cityId = d.split(",")[0];
//            int curCityCount = 0;
//
//            log.info("Start: {}, {}/{}", d, index++, dataList.size());
//
//            int pageNum = 0;
//            while (true) {
//                String result = HttpUtil.get(url.formatted(cityId, pageNum++, pageSize));
//                JSONObject resultObj = JSONObject.parseObject(result);
//                log.info("resultObj: {}", resultObj);
//                Integer total = resultObj.getInteger("total");
//                JSONArray results = resultObj.getJSONArray("results");
//                if (CollUtil.isEmpty(results)) break;
//                curCityCount += results.size();
////                if (curCityCount >= total) break;
//
//                for (int i = 0; i < results.size(); i++) {
//                    JSONObject obj = results.getJSONObject(i);
//                    TrainStationLocaleEntity baiduTrainStation = new TrainStationLocaleEntity();
//                    baiduTrainStation.setName(obj.getString("name"));
//                    baiduTrainStation.setUid(obj.getString("uid"));
//                    baiduTrainStation.setAddress(obj.getString("address"));
//                    baiduTrainStation.setProvince(obj.getString("province"));
//                    baiduTrainStation.setCity(obj.getString("city"));
//                    baiduTrainStation.setArea(obj.getString("area"));
//                    JSONObject location = obj.getJSONObject("location");
//                    if (location != null) {
//                        baiduTrainStation.setLon(location.getString("name"));
//                        baiduTrainStation.setLat(location.getString("name"));
//                    }
//                    baiduTrainStation.setStreetId(obj.getString("street_id"));
//                    baiduTrainStationMapper.insert(baiduTrainStation);
//                }
//
//            }
//        }
    }

    @Test
    public void testinsertData() {
        String filePath = "D:/train_station.txt";

        List<TrainStationLocaleEntity> stationList = parseFile(filePath);
        stationList.forEach(item -> {
            trainStationLocaleMapper.insert(item);
        });
    }

    @Test
    public void testCity() {
//        List<CityDto> all = globalCityService.all();
//        log.info("all: {}", JSON.toJSONString(all));

        List<GlobalCityIataV2Dto> result = globalCityMapper.queryIataDtoV2List("en_US");
        log.info("result: {}", JSON.toJSONString(result));
    }
}
