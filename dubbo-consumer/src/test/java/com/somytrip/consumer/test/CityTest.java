package com.somytrip.consumer.test;

import com.alibaba.fastjson2.JSON;
import com.somytrip.api.service.tourism.GlobalCityService;
import com.somytrip.consumer.util.AliUtil;
import com.somytrip.entity.dto.city.CityDto;
import com.somytrip.entity.dto.city.CityListResponse;
import com.somytrip.entity.dto.city.QueryLatLngCity;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.CityLinkReq;
import com.somytrip.entity.vo.CityLinkRes;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.controller.tourism
 * @className: ItineraryTest
 * @author: shadow
 * @description:
 * @date: 2024/3/27 16:46
 * @version: 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class CityTest {

    @Resource
    private GlobalCityService globalCityService;
    @Resource
    private AliUtil aliUtil;

    @Test
    public void testExtendsParameters() {
        aliUtil.checkCaptchaParams("wcy0gv4d", "eyJjZXJ0aWZ5SWQiOiJuRVh2aDJGRmE0Iiwic2NlbmVJZCI6IndjeTBndjRkIiwiaXNTaWduIjp0cnVlLCJzZWN1cml0eVRva2VuIjoiNm9PbzdlNzJuQTYxdVZMaVpWS2lMV0RrczkwWld3YU5xWExLb2krMy9jRDZhUDV0UTVVaCttOXZxUVF5OEp1YlpIZFZsNFRXNGNGMnRUamNLSVdwOTdrYk5RakVQUndlWVMyS3VrSlBaVHBVU01Ya3BlQmFWZWlaRVI1N2VpVmEifQ==");
    }

    @Test
    public void testQueryLatLng() {
        QueryLatLngCity vo = new QueryLatLngCity();
        vo.setLat("52.308056");
        vo.setLng("4.764167");
        vo.setCoordinate(QueryLatLngCity.Coordinate.gcj02);
        ResponseResult<CityDto> result = globalCityService.queryLatLngCity(vo);
        log.info("query result: {}", JSON.toJSONString(result));
    }

    @Test
    public void test() {
        CityListResponse cityListResponse = globalCityService.allIataCityV2();
        log.info("json result: {}", JSON.toJSONString(cityListResponse));
    }

    @Test
    public void keyword() {
        String keyword = "北";
//        CityKeywordSearchVo cityKeywordSearchVo = globalCityService.keywordSearch(keyword);
//        log.info("cityKeywordSearchVo: {}", JSON.toJSONString(cityKeywordSearchVo));
    }

    @Test
    public void testCityLink() {
        CityLinkReq cityLinkReq = new CityLinkReq();
        cityLinkReq.setCityCode("61");
        //cityLinkReq.setKeyworld("深圳");
        List<CityLinkRes> res = globalCityService.cityLinkSearch(cityLinkReq);
        System.out.println(JSON.toJSONString(res));
    }
}
