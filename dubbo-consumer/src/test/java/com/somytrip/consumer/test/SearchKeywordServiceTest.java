package com.somytrip.consumer.test;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson2.JSON;
import com.somytrip.api.service.SearchKeywordService;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.common.SearchKeywordSaveVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SearchKeywordServiceTest {

    @Resource
    private SearchKeywordService searchKeywordService;

    @Test
    public void test() {
        ResponseResult<List<String>> result = searchKeywordService.getUserKeywords("1344", "zh_CN", 2);
        log.info("result: {}", JSON.toJSONString(result));
    }

    @Test
    public void testSave() {
        SearchKeywordSaveVo vo = new SearchKeywordSaveVo();
        vo.setKeywords(ListUtil.of("222"));
        vo.setType(2);
        ResponseResult<Boolean> result = searchKeywordService.saveUserKeywords("1344", "zh_CN", vo);
        log.info("result: {}", JSON.toJSONString(result));
    }
}
