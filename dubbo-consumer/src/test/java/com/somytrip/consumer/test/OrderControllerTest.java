package com.somytrip.consumer.test;

import com.alibaba.fastjson2.JSON;
import com.somytrip.api.service.order.OrderServiceV2;
import com.somytrip.entity.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-04-22 16:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class OrderControllerTest {
    @DubboReference
    private OrderServiceV2 orderServiceV2;

    @Test
    public void testDetail() {
        ResponseResult responseResult = orderServiceV2.queryOrderDetail("1831136926286385152", "109");
        log.info("responseResult: {}", JSON.toJSONString(responseResult));
    }
}
