package com.somytrip.consumer.test;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.somytrip.api.service.LocaleDictService;
import com.somytrip.api.service.tourism.GlobalCityService;
import com.somytrip.api.service.tourism.ScenicService;
import com.somytrip.entity.LocaleDictEntity;
import com.somytrip.entity.dto.Scenic;
import com.somytrip.entity.dto.city.GlobalCityEntity;
import com.somytrip.entity.dto.order.CancelOrder;
import com.somytrip.entity.dto.order.QueryUserOrderListDTO;
import com.somytrip.entity.enums.hotel.HotelFilterTypeEnum;
import com.somytrip.entity.enums.itinerary.ItineraryBudgetType;
import com.somytrip.entity.enums.itinerary.TravelBudgetLocale;
import com.somytrip.entity.enums.locale.LocaleDictType;
import com.somytrip.model.flight.enums.RuleWightTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.LocaleUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.controller.tourism
 * @className: ItineraryTest
 * @author: shadow
 * @description:
 * @date: 2024/3/27 16:46
 * @version: 1.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class LocaleDictTest {

    private static final String zh_CN = Locale.CHINA.toString();
    private static final String en_US = Locale.US.toString();
    private static final String zh_TW = Locale.TAIWAN.toString();

    private static final String[] localeArr = {zh_CN, en_US, zh_TW};

    @Resource
    private LocaleDictService localeDictService;
    @Resource
    private ScenicService scenicService;
    @Resource
    private GlobalCityService globalCityService;

    public static void main(String[] args) {
//        String value = "风格偏好";
//        System.out.println(ZhConverterUtil.toTraditional(value));
        String name = QueryUserOrderListDTO.OrderStateEnum.class.getCanonicalName();
        System.out.println(name);
    }

    @Test
    public void itineraryBudgetType() {

        for (ItineraryBudgetType itineraryBudgetType : ItineraryBudgetType.values()) {
            for (String locale : localeArr) {
                LocaleDictEntity entity = new LocaleDictEntity();
                entity.setType(LocaleDictType.ITINERARY_BUDGET_TYPE);
                entity.setColumnName(itineraryBudgetType.name());
                entity.setLocale(LocaleUtils.toLocale(locale));
                if (zh_CN.equals(locale)) {
                    entity.setValue(itineraryBudgetType.getName());
                } else if (en_US.equals(locale)) {
                    entity.setValue(itineraryBudgetType.name().toLowerCase());
                } else if (zh_TW.equals(locale)) {
                    entity.setValue(ZhConverterUtil.toTraditional(itineraryBudgetType.getName()));
                }

                localeDictService.save(entity);
            }
        }
    }

    @Test
    public void scenic() {
        LambdaQueryWrapper<Scenic> qw = new LambdaQueryWrapper<>();
        qw.isNotNull(Scenic::getLocaleDict);
//        List<Scenic> list = scenicService.list(qw);
//        int size = list.size();
        int pageNum = 1;
        int pageSize = 1000;
        String tableName = "scenic";

        while (true) {
            IPage<Scenic> page = new Page<>(pageNum, pageSize);
            page = scenicService.page(page, qw);

            List<Scenic> records = page.getRecords();
            if (CollUtil.isEmpty(records)) break;

            long pages = page.getPages();
            log.info("page: {}/{}", pageNum++, pages);

            for (Scenic scenic : records) {
//            log.info("index: {}/{}", index++, size - 1);
                Map<String, Map<String, String>> localeDict = scenic.getLocaleDict();
                if (localeDict == null) continue;

                Map<String, String> nameLocaleDict = localeDict.get("name");
                processSubLocaleDict(nameLocaleDict, Long.valueOf(scenic.getId()), tableName, "name");
            }
        }
    }

    @Test
    public void globalCity() {
        LambdaQueryWrapper<GlobalCityEntity> qw = new LambdaQueryWrapper<>();
        qw.isNull(GlobalCityEntity::getLocaleDict);
        List<GlobalCityEntity> list = globalCityService.list(qw);
        log.info("size: {}", list.size());
//        if (true)return;
        String tableName = "global_city";
        List<String> countryList = new ArrayList<>();
        List<String> continentList = new ArrayList<>();
        List<String> provinceList = new ArrayList<>();

        for (GlobalCityEntity cityEntity : list) {
            Map<String, Map<String, String>> localeDict = cityEntity.getLocaleDict();
            if (localeDict == null) continue;
            Map<String, String> cityNameLocaleDict = localeDict.get("cityName");
            processSubLocaleDict(cityNameLocaleDict, Long.valueOf(cityEntity.getId()), tableName, "city_name");

            String country = cityEntity.getCountry();
            if (!countryList.contains(country)) {
                Map<String, String> countryLocaleDict = localeDict.get("countryName");
                processSubLocaleDict(countryLocaleDict, Long.valueOf(cityEntity.getId()), tableName, "country");
                countryList.add(country);
            }

            String continent = cityEntity.getContinent();
            if (!continentList.contains(continent)) {
                Map<String, String> continentLocaleDict = localeDict.get("continentName");
                processSubLocaleDict(continentLocaleDict, Long.valueOf(cityEntity.getId()), tableName, "continent");
                continentList.add(continent);
            }

            String province = cityEntity.getProvince();
            if (!provinceList.contains(province)) {
                Map<String, String> provinceLocaleDict = localeDict.get("provinceName");
                processSubLocaleDict(provinceLocaleDict, Long.valueOf(cityEntity.getId()), tableName, "province");
                provinceList.add(province);
            }
        }
    }

    private void processSubLocaleDict(Map<String, String> subLocaleDict, Long pid, String tableName, String columnName) {
        List<LocaleDictEntity> insertList = new ArrayList<>();
        for (String localeStr : localeArr) {
            String curName = subLocaleDict.get(localeStr);
            if (StringUtils.isBlank(curName)) continue;
            LocaleDictEntity localeDictEntity = new LocaleDictEntity();
            localeDictEntity.setLocale(LocaleUtils.toLocale(localeStr));
            localeDictEntity.setValue(curName);
            localeDictEntity.setTableName(tableName);
            localeDictEntity.setColumnName(columnName);
            localeDictEntity.setPid(pid);
            insertList.add(localeDictEntity);
//            localeDictService.save(localeDictEntity);
        }
        localeDictService.saveBatch(insertList);
    }

    @Test
    public void orderSetting() {

        for (CancelOrder.RefundMethod value : CancelOrder.RefundMethod.values()) {
            String className = value.getClass().getSimpleName();

            String name = value.name();
            String display = value.getDisplay();

            String[] localeArr = {zh_CN, en_US, zh_TW};
            for (String locale : localeArr) {
                LocaleDictEntity entity = new LocaleDictEntity();
                entity.setType(LocaleDictType.ORDER_SETTING);
                entity.setTableName(className);
                entity.setColumnName(name);

                if (zh_CN.equals(locale)) {
                    entity.setLocale(Locale.CHINA);
                    entity.setValue(display);
                } else if (en_US.equals(locale)) {
                    entity.setLocale(Locale.US);
                    StringBuilder sb = new StringBuilder();
                    String[] split = name.split("_");
                    String first = split[0];
                    String l = first.substring(0, 1);
                    String r = first.substring(1);
                    sb.append(l).append(r.toLowerCase());
                    if (split.length > 1) {
                        for (int i = 1; i < split.length; i++) {
                            sb.append(" ").append(split[i].toLowerCase());
                        }
                    }
                    entity.setValue(sb.toString());
                } else if (zh_TW.equals(locale)) {
                    entity.setLocale(Locale.TAIWAN);
                    entity.setValue(ZhConverterUtil.toTraditional(display));
                }

                localeDictService.save(entity);
            }

        }
    }

    @Test
    public void testRead() {

        LambdaQueryWrapper<LocaleDictEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(LocaleDictEntity::getType, LocaleDictType.FLIGHT_CONFIG).eq(LocaleDictEntity::getLocale, Locale.CHINA);
        List<LocaleDictEntity> list = localeDictService.list(qw);
        log.info("list: {}", JSON.toJSONString(list));
    }

    @Test
    public void insertHotelFilterType() {

        String[] localeArr = {zh_CN, en_US, zh_TW};
        for (HotelFilterTypeEnum hotelFilterTypeEnum : HotelFilterTypeEnum.values()) {
            String name = hotelFilterTypeEnum.name();
            String value = hotelFilterTypeEnum.getName();

            for (String locale : localeArr) {
                LocaleDictEntity entity = new LocaleDictEntity();
                entity.setType(LocaleDictType.HOTEL_FILTER_CONFIG);
                entity.setLocale(LocaleUtils.toLocale(locale));
                entity.setColumnName(name);
                if (zh_CN.equals(locale)) {
                    entity.setValue(value);
                } else if (en_US.equals(locale)) {
                    StringBuilder sb = new StringBuilder();
                    String[] split = name.split("_");
                    String first = split[0];
                    String l = first.substring(0, 1);
                    String r = first.substring(1);
                    sb.append(l).append(r.toLowerCase());
                    if (split.length > 1) {
                        for (int i = 1; i < split.length; i++) {
                            sb.append(" ").append(split[i].toLowerCase());
                        }
                    }
                    entity.setValue(sb.toString());
                } else if (zh_TW.equals(locale)) {
                    entity.setValue(ZhConverterUtil.toTraditional(value));
                }
                localeDictService.save(entity);
            }
        }
    }

    @Test
    public void insertFlightConfig() {

        String[] localeArr = {zh_CN, en_US, zh_TW};

        for (RuleWightTypeEnum value : RuleWightTypeEnum.values()) {
            String name = value.name();
            String display = value.getDisplay();

            for (String locale : localeArr) {
                LocaleDictEntity entity = new LocaleDictEntity();
                entity.setType(LocaleDictType.FLIGHT_CONFIG);
                entity.setLocale(LocaleUtils.toLocale(locale));
                entity.setColumnName(name);
                if (zh_CN.equals(locale)) {
                    entity.setValue(display);
                }
                localeDictService.save(entity);
            }
        }
    }

    @Test
    public void update_en_US_value() {
        LambdaQueryWrapper<LocaleDictEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(LocaleDictEntity::getLocale, Locale.US.toString());
        qw.and((w) -> {
            w.eq(LocaleDictEntity::getValue, "").or()
                    .isNull(LocaleDictEntity::getValue);
        });
        List<LocaleDictEntity> list = localeDictService.list(qw);
        for (LocaleDictEntity entity : list) {
            StringBuilder value = new StringBuilder();

            String columnName = entity.getColumnName();
            String[] split = columnName.split("_");
            String first = split[0];
            String l = first.substring(0, 1);
            String r = first.substring(1);
            value.append(l).append(r.toLowerCase());
            if (split.length > 1) {
                for (int i = 1; i < split.length; i++) {
                    value.append(" ").append(split[i].toLowerCase());
                }
            }

            entity.setValue(value.toString());
        }

        localeDictService.updateBatchById(list);
    }

    @Test
    public void update_zh_TW_value() {
        LambdaQueryWrapper<LocaleDictEntity> qw = new LambdaQueryWrapper<>();
        qw.in(LocaleDictEntity::getLocale, List.of(Locale.TAIWAN.toString(), Locale.CHINA.toString()));
        List<LocaleDictEntity> list = localeDictService.list(qw);
        List<LocaleDictEntity> updateList = new ArrayList<>();
        for (LocaleDictEntity entity : list) {
            Locale locale = entity.getLocale();
            if (!locale.equals(Locale.TAIWAN)) continue;
            LocaleDictEntity zn_CN_entity = list.stream()
                    .filter(item -> Objects.equals(item.getColumnName(), entity.getColumnName())
                            && item.getLocale().equals(Locale.CHINA)).findFirst().orElse(null);
            if (zn_CN_entity == null) continue;
            String zh_TW_value = ZhConverterUtil.toTraditional(zn_CN_entity.getValue());
            entity.setValue(zh_TW_value);
            updateList.add(entity);
        }
        localeDictService.updateBatchById(updateList);
    }

    @Test
    public void test1() {

        for (TravelBudgetLocale item : TravelBudgetLocale.values()) {
            String name = item.name();
            String value = item.getValue();

            LocaleDictEntity entity = new LocaleDictEntity();
            entity.setType(LocaleDictType.ITINERARY_QUERY_SETTING);
            entity.setTableName("TravelBudget");
            entity.setColumnName(name.substring(name.length() - 1));
            entity.setValue(value);
            entity.setLocale(LocaleUtils.toLocale(name.substring(0, 5)));
            localeDictService.save(entity);
        }
    }
}
