package com.somytrip.provider.flight.test

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.somytrip.model.dto.TicketChangeOrder
import com.somytrip.provider.flight.mapper.TicketChangeOrderMapper
import jakarta.annotation.Resource
import org.junit.Test
import org.junit.runner.RunWith
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.junit4.SpringRunner

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-08 16:04
 */
@RunWith(SpringRunner::class)
@SpringBootTest
class TestMapper {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Resource
    private lateinit var ticketChangeOrderMapper: TicketChangeOrderMapper

    @Test
    fun test() {
        val queryWrapper = QueryWrapper<TicketChangeOrder>()
        queryWrapper.eq("order_no", "xxx")
        val exists = ticketChangeOrderMapper.exists(queryWrapper)
        log.info("exists: {}", exists)
    }
}