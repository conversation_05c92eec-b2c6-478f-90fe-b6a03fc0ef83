package com.somytrip.provider.flight.test

import cn.hutool.core.net.url.UrlBuilder
import cn.hutool.crypto.SecureUtil
import cn.hutool.http.HttpUtil
import com.somytrip.provider.flight.config.FlightConfig
import jakarta.annotation.Resource
import org.junit.Test
import org.junit.runner.RunWith
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.junit4.SpringRunner
import java.time.Instant

/**
 * @Description: 测试智飞
 * @author: pigeon
 * @created: 2024-03-08 16:04
 */
@RunWith(SpringRunner::class)
@SpringBootTest
class TestZfFlightService {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Resource
    private lateinit var flightConfig: FlightConfig

    @Test
    fun test() {
        val config = flightConfig.zf()
        val url = UrlBuilder.of(config.baseUrl)
            .addPath("/api/v2/flight/shopping")
            .build()
        val code = config.apiCode
        val timestamp = "${Instant.now().toEpochMilli() / 1000}"
        val signature = SecureUtil.sha1("${code}${timestamp}${config.apiKey}")
        val response = HttpUtil.createPost(url)
            .header("code", code)
            .header("timestamp", timestamp)
            .header("signature", signature)
            .body(
                """{
    "cabinClass": "economy",
    "excludeAirlines": [
        "CA",
        "UA"
    ],
    "includeAirlines": [
        "LX",
        "CZ"
    ],
    "journeys": [
        {
            "departureDate": "2025-06-01",
            "destination": "SHA",
            "origin": "SZX"
        }
    ],
    "maxDuration": 150,
    "maxPrice": 8899,
    "maxSegments": 0,
    "mustHaveBag": false,
    "passengers": {
        "adult": 1,
        "child": 0,
        "infant": 0
    }
}"""
            )
            .execute()
            .body()
        log.info("json result: $response")
    }
}