package com.somytrip.provider.flight.test

import cn.hutool.core.codec.Base64
import cn.hutool.crypto.Mode
import cn.hutool.crypto.Padding
import cn.hutool.crypto.symmetric.AES
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.somytrip.api.service.flight.ManyFlightService
import com.somytrip.api.service.user.TripCommonService
import com.somytrip.model.flight.tc.TCNotifyBody
import com.somytrip.model.flight.vo.ComputePriceVo
import com.somytrip.model.flight.vo.CreateOrderReq
import com.somytrip.model.flight.vo.NotifyBody
import jakarta.annotation.Resource
import org.apache.dubbo.config.annotation.DubboReference
import org.junit.Test
import org.junit.runner.RunWith
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.junit4.SpringRunner
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-08 16:04
 */
@RunWith(SpringRunner::class)
@SpringBootTest
class TestManyFlightService {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Resource
    private lateinit var manyFlightService: ManyFlightService

    @Test
    fun testRefund() {
//        val s = """{
//  "orderNo": "1879445780894167040",
//  "type": "VOLUNTARY",
//  "fileKeys": [],
//  "remark": "不想要了",
//  "tickets": []
//}""";
//        manyFlightService.applyRefund();
    }

    @Test
    fun testPayZf() {
        manyFlightService.pay("1879049548254511104");
    }

    @Test
    fun testCancelOrderZf() {
        manyFlightService.notPayCancelOrder("1879049548254511104", "不想要了");
    }

    @Test
    fun testQueryFee() {
        val s = "{\"orderNo\":\"1871391403299356672\",\"type\":\"VOLUNTARY\",\"tickets\":[\"781-2298303870\"]}"
        val res = manyFlightService.queryRefundFee(s.into<ComputePriceVo>())
        log.info("res: {}", res)
    }

    @Test
    fun testUploadFile() {
//        val vo = UploadFilesNVo("", listOf(MockMultipartFile("file",
//            "test.txt",
//            MediaType.TEXT_PLAIN_VALUE,
//            "Hello, World!".byteInputStream())))
//        val result = manyFlightService.uploadChangeFiles(vo)
//        log.info(result.toString())
    }

    @Test
    fun testRefundResultNotify() {
        val s =
            """{"businessRequest":"H4sIAAAAAAAAAE1QTU/CQBD9K2bOQGZ2+7Ht0YAhJqApeNF4WOuAjWWL3UUhhKtHr1408dfh33AtKXp782bezJu3hbuVLQxbO90sGVLsQK5NzmXG2lamIXjtuDa6vKjvuR5XkAKpQAYqkhQkgVCxjBChA9WxP8RkRNdXWUBDpQYqlJFvL7W1bOZcT4v8kZ2F9GYLOdeukUiBkkggSpQijpGEl8yK2rqxXnhjsP98+3792n+8e77UR9pXppo8VC9nfLC/1JtRZXgDaUxJT0YdqHm2MveHfg/buh1S/4cmXD8XObe7nF77iUbkYZ+dLkrvewubJ0hFy0MaerjzuHmseSdWJIQKMSGKQu9xZdkvny/YuMPy3W178y/W7FSgCAhRkY9CyP4Qw8tBdg5He047LxZtPS2aDH5VXcIuqhOilEQaxLD7Ab9lvv7aAQAA","businessType":0,"params":"{\"businessType\":0,\"cancelReason\":0,\"externalOrderNo\":\"1843486314942873600\",\"orderNo\":\"H09M1ZUR41H88E8536\",\"passengerTickets\":[{\"certNo\":\"320311200303277012\",\"firstName\":\"李熠朗\",\"lastName\":\"\",\"noShowFee\":0,\"payMoney\":719.36,\"refundFee\":0.0,\"refundMoney\":789.36,\"refundServiceFee\":0,\"tax\":70.0,\"taxDetails\":{\"yq\":20.0,\"tax\":50.0},\"ticketNo\":\"7812285091165\",\"usedSegmentFee\":0}],\"refundOrderNo\":\"RB20241008111223DH05PERJ\",\"refundState\":2,\"refundTime\":\"2024-10-08 11:12:47\"}","pid":"48B1913B354E496B93ADFEDCFF941CD5","requestID":"product_e1683ea030714ffabd57161f7422dd91","serviceCode":"RefundResult","sign":"747D33DBFE7F49EA726E4CB6923BFD7C","timestamp":"2024-10-08 11:12:47"}"""

        manyFlightService.tcNotify(NotifyBody<TCNotifyBody>(s.into<TCNotifyBody>()))
    }


    @Test
    fun testCreateOrder() {
        val s = """{
    "checkedInsure": false,
    "userId": "109",
    "sourceType": 0,
    "sessionId": "",
    "contactInfo": {
        "name": "叶福星",
        "mobile": "17003099837",
        "email": ""
    },
    "passengers": [
        1
    ],
    "productSource": "FR24",
    "accurateRequest": {
        "adultNum": 1,
        "chdNum": 0,
        "infNum": 0,
        "mainAirline": "Z2",
        "recommend": 0,
        "productInfos": [
            {
                "productCode": "2142c1db470105f3b867fcb6398c7d5b",
                "productTag": "FR24_PRODUCT",
                "productSource": "FR24",
                "orderInfo": "{\"offerId\":\"17851288406593536\"}"
            }
        ],
        "segments": [
            {
                "airline": "Z2",
                "arrAirport": "HKG",
                "arrDate": "2024-10-17 21:48",
                "cabinCode": "Q",
                "depAirport": "NAY",
                "depDate": "2024-10-17 18:40",
                "flightNo": "Z29231",
                "flightShare": false,
                "segmentIndex": 1,
                "tripType": 0,
                "cabinClass": "ECONOMY_CLASS"
            }
        ],
        "sourceType": 0,
        "tripType": 0
    }
}"""
        val booking = manyFlightService.booking(s.into<CreateOrderReq>())
        log.info("result: {}", booking.toJSONString())
    }

    @DubboReference
    private lateinit var tripCommonService: TripCommonService

    @Test
    fun testUserInfo() {
        val queryTripInfoDtoListByUserIdAndIds = tripCommonService.queryTripInfoDtoListByUserIdAndIds("109", listOf(1))
        log.info("json: {}", queryTripInfoDtoListByUserIdAndIds.toJSONString())
    }

    @Test
    fun testPay() {
        val pay = manyFlightService.pay("1834127074366824448")
        log.info("pay success: {}", pay)
    }

    @Test
    fun testCancel() {
        manyFlightService.notPayCancelOrder("1834114949855879168", "不想要了")
    }

    @Test
    fun testAes() {
        val aes = AES(Mode.CBC, Padding.PKCS5Padding)
        val key = "Ibdd7eIcddowEDKs"
        aes.setIv(key.encodeToByteArray())
        val s = "MTNFR0123456"
        val sE = "MAIULqaKA2HCTHVh/iMemQ=="
        val se = aes.encryptBase64(s)
        log.info("se: {}", se)
        log.info("se==sE: {}", sE == se)
        log.info("s: {}", aes.decryptStr(se))

        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        cipher.init(Cipher.ENCRYPT_MODE, SecretKeySpec(key.encodeToByteArray(), "AES"), IvParameterSpec(ByteArray(16)))
        val ddd = Base64.encode(cipher.doFinal(s.encodeToByteArray()))
        log.info("ddd: {}", ddd)
    }
}