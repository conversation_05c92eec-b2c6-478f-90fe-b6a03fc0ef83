package com.somytrip.provider.flight.test

import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.JSONObject
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.somytrip.api.service.flight.TCFlightService
import com.somytrip.entity.vo.flight.CancelFlightOrderVo
import com.somytrip.entity.vo.flight.CreateFlightOrderVo
import com.somytrip.entity.vo.flight.QueryFlightAccurateRequest
import com.somytrip.entity.vo.flight.QueryFlightShoppingRequest
import com.somytrip.model.flight.tc.*
import com.somytrip.model.vo.flight.OrderCancelVo
import com.somytrip.provider.flight.config.CacheConfig
import jakarta.annotation.Resource
import org.junit.Test
import org.junit.runner.RunWith
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.junit4.SpringRunner

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-08 16:04
 */
@RunWith(SpringRunner::class)
@SpringBootTest
class TestService {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Resource
    private lateinit var tcFlightService: TCFlightService

    @Resource
    private lateinit var cacheConfig: CacheConfig

    @Test
    fun testShopTimeOut() {
        while (true) {
            log.info("shop timeout: {}", cacheConfig.timeout().shopSeconds)
            Thread.sleep(1000)
        }
    }


    @Test
    fun testCancel() {
        val response = tcFlightService.cancelOrder(
            CancelFlightOrderVo(
                "1831160253250637824",
                "109",
                "不需要了"
            )
        )
        log.info("response: {}", response.toJSONString())
    }

    @Test
    fun testCreateOrder() {
        val s = """{
  "sourceType": "0",
  "userId": "109",
  "productSource": "TC",
  "passengers": [{
    "birthday": "2001-11-07",
    "creditIssueNotion": "CN",
    "creditNo": "******************",
    "creditType": "ID_CARD",
    "creditValidDate": "2032-04-01",
    "firstName": "叶",
    "gender": "OTHER",
    "lastName": "福星",
    "linkPhone": "17003099837",
    "notion": "CN",
    "notionCode": "CN",
    "type": "ADULT"
  }],
  "contactInfo": {
    "name": "叶福星",
    "mobile": "17003099837",
    "email": ""
  },
  "accurateRequest": {
    "adultNum": 1,
    "chdNum": 0,
    "infNum": 0,
    "recommend": 0,
    "sourceType": "0",
    "tripType": "0",
    "mainAirline": "CZ",
    "productInfos": [{
      "productCode": "1",
      "productTag": "NORMAL_PRODUCT",
      "productSource": "TC",
      "orderInfo": "0,5a0541144c544c93a1cfa46ec6cf2ca7,10,1E,PUB,0"
    }],
    "segments": [{
      "airline": "CZ",
      "arrAirport": "DLC",
      "arrDate": "2024-06-27 13:00:00",
      "cabinCode": "Z",
      "depAirport": "SZX",
      "depDate": "2024-06-27 09:30:00",
      "flightNo": "CZ6833",
      "flightShare": false,
      "segmentIndex": 1,
      "tripType": 0,
      "cabinClass": "ECONOMY_CLASS",
      "operationAirline": "",
      "operationFlightNo": ""
    }]
  }
}"""
        val response =
            tcFlightService.createOrder(s.into<CreateFlightOrderVo>())
        log.info("response: {}", response)
    }

    @Test
    fun testNotify() {
        val s = """{
  "businessRequest": "H4sIAAAAAAAAAH1R2U7DMBD8lcrPreQjbo63KFIoghakpgiKeHBbN7WaC9uVqKo+wxeA+Bq+BiT+AifpEQ7ht5nZ3VnPbsBkpUTGlYrWBQcebAP+oLnMWHIhZ1wOcuABZDvEoi6kpNtFmNo2dkAb5Ae9B93zq5COT858KwxCU1XKKx2J6ZLroWa6nnygIpEaBmCIrQ60Opi2kOsR26PlXMXjlGdaAe92A5iQiVnPFPdHRmNS+kIWudSGGfb8mgqEXjdxxGUqzA8MF+Ed1XTsdiBtYeJR6kFo9CmbiCxImFJBPivLbg5kjccGz3jRsB5f19Teeo+b1mRH/bZGHoG19TwR8UJXKfZHJt8jN1wwafrmLFHcRFdwybTIYv8QCGiw4R9jdjnWd0Ulvl/xbFqC7V0b6OoSg/xU87SKWsyqfstCDraR60CEkUsgKm9SZNJIsHolVHLAql99vj69v718PD+C48T6chMRX/7o+g7/2e84yzTYDsLYgtSxiENAtfryb2X7BblNiWzOAgAA",
  "businessType": 0,
  "params": "{\"businessType\":0,\"externalOrderNo\":\"1783459053661257728\",\"orderNo\":\"H09LVF5ZGKA4FCF366\",\"outTicketState\":0,\"outTicketTime\":\"2024-04-25 19:37:58\",\"segments\":[{\"airline\":\"MU\",\"arrAirport\":\"SHA\",\"arrCity\":\"SHA\",\"arrTerminal\":\"T2\",\"arrTime\":\"2024-06-05 23:55:00\",\"cabinClassCode\":\"Y\",\"cabinCode\":\"Z\",\"depAirport\":\"SZX\",\"depCity\":\"SZX\",\"depTerminal\":\"T3\",\"depTime\":\"2024-06-05 21:30:00\",\"flightNo\":\"MU5360\",\"flightShare\":false,\"operatingAirline\":\"\",\"operatingFlightNo\":\"MU5360\",\"segmentType\":1,\"sequence\":1}],\"ticketNoItem\":[{\"idNo\":\"441827198012193018\",\"pnr\":\"000000\",\"psrName\":\"陈志文\",\"ticketNos\":[{\"bigPnr\":\"000000\",\"pnr\":\"000000\",\"segmentType\":1,\"sequence\":1,\"ticketNo\":\"7812240584383\"}],\"tktNo\":\"7812240584383\"}]}",
  "pid": "48B1913B354E496B93ADFEDCFF941CD5",
  "requestID": "product_6d2fef57-1f2d-4127-b3e7-dc938efb79f4",
  "serviceCode": "TicketResult",
  "sign": "08CA710AE2796F7A24FC36270329B800",
  "timestamp": "2024-04-25 19:37:58"
}"""
        val tcNotify = tcFlightService.tcTicketNotify(s.into<TCNotifyBody>())
        log.info("tcNotify: {}", JSONObject.toJSONString(tcNotify))
    }

    @Test
    fun testAbleRefundQuery() {
        val request = TCRefundAbleRequest(
            "H09LVF5ZGKA4FCF366",
            1,
            listOf(
                TCRefundAblePassengerInfo(
                    "******************",
                    "陈志文",
                    "7812240584383"
                )
            ),
            "local1"
        )
//        val res = tcFlightService.queryRefundable(request, 0);
//        log.info("json: {}", JSONObject.from(res))
    }


    @Test
    fun testAccSearch() {

        val s =
            """{
  "adultNum": 1,
  "chdNum": 0,
  "infNum": 0,
  "mainAirline": "CZ",
  "recommend": 0,
  "productInfos": [
    {
      "productCode": "1",
      "productTag": "NORMAL_PRODUCT",
      "productSource": "TC",
      "orderInfo": ""
    }
  ],
  "segments": [
    {
      "airline": "CZ",
      "arrAirport": "SZX",
      "arrDate": "2024-05-09 23:35:00",
      "cabinCode": "N",
      "depAirport": "SHA",
      "depDate": "2024-05-09 21:10:00",
      "flightNo": "CZ3564",
      "flightShare": false,
      "segmentIndex": 1,
      "segmentType": 0,
      "tripType": 0,
      "cabinClass": "ECONOMY_CLASS",
      "operationAirline": "",
      "operationFlightNo": "CZ3564"
    }
  ],
  "sourceType": 0,
  "tripType": 0
}"""
        val request = s.into<QueryFlightAccurateRequest>()
        val accurateRequest = tcFlightService.flightAccurateSearch(request);
        log.info("response: {}", JSONObject.from(accurateRequest).toString())
    }

    @Test
    fun testQueryDetail() {
//        val queryOrderDetail = tcFlightService.queryOrderDetail(TCOrderDetailQueryRequest("H09LUI5XW4V15D1509", ""), 0)
        val queryOrderDetail = tcFlightService.queryOrderDetail("H09LZVG4L3T579A673", 0)
        log.info("response: {}", JSONObject.from(queryOrderDetail).toString())
    }

    @Test
    fun test2() {
        tcFlightService.cancelOrder(
            OrderCancelVo(
                0, TCOrderCancelRequest(
                    "H09LUI5XW4V15D1509", listOf(
                        TCOrderCancelProduct(null, "0")
                    ), "系统自动取消"
                )
            )
        )
    }

    @Test
    fun test3() {
        val queryFlightShoppingRequest = JSONObject.parseObject(
            """{
    "segments": [
        {
            "departCode": "SZX",
            "arriveCode": "PEK",
            "departTime": "2024-09-21"
        }
    ],
    "passengers": [
        {
            "type": "ADULT",
            "num": 1
        }
    ],
    "cabin": "ECONOMY_CLASS",
    "tripType": 0,
    "sourceType": 0,
    "screenCondition": {
        "airline": [],
        "cabin": [
            "ECONOMY_CLASS"
        ],
        "sort": "PRICE_LOW_HIGH",
        "timeRange": [],
        "stylePreference": []
    }
}"""
        )
            .into<QueryFlightShoppingRequest>()
        log.info("json: {}", JSONObject.from(queryFlightShoppingRequest).toString())
        val shopFlightListV2 = tcFlightService.getShopFlightListV2(queryFlightShoppingRequest);
        log.info("json response: {}", JSON.toJSONString(shopFlightListV2))
//        log.info("response: ${JSONObject.from(shopFlightListV2)}")
    }
//    @Test
//    fun test1() {
//        val str = """{
//	"adultNum": 1,
//	"chdNum": 0,
//	"infNum": 0,
//	"mainAirline": "CA",
//	"recommend": 0,
//	"segments": [
//		{
//			"airline": "CA",
//			"arrAirport": "PEK",
//			"arrDate": "2024-04-07 00:25:00",
//			"cabinCode": "U",
//			"depAirport": "SZX",
//			"depDate": "2024-04-06 21:00:00",
//			"flightNo": "CA1338",
//			"flightShare": false,
//			"segmentIndex": 1,
//			"segmentType": 1
//		}
//	],
//	"sourceType": 0,
//	"tripType": 0
//}"""
////        val res = tcFlightService.flightAccurateSearch(str)
//        val res = tcFlightService.flightAccurateSearch(JSONObject.parseObject(str).toJSONString())
//        log.info("res: {}", JSONObject.from(res))
//    }
}