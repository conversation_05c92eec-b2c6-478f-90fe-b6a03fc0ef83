package com.somytrip.provider.flight.test

import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.somytrip.model.flight.common.ProductInfo
import com.somytrip.model.flight.vo.AccurateReq
import com.somytrip.model.flight.vo.SegmentVo
import com.somytrip.model.flight.vo.ShopListReq
import com.somytrip.provider.flight.service.FlightService
import jakarta.annotation.Resource
import org.junit.Test
import org.junit.runner.RunWith
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.junit4.SpringRunner

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-08 16:04
 */
@RunWith(SpringRunner::class)
@SpringBootTest
class TestFlightService {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Resource
    private lateinit var fR24FlightService: FlightService; // dev

    @Resource
    private lateinit var tcDomesticFlightService: FlightService;

    @Resource(name = "zhiFeiFlightService")
    private lateinit var zhiFeiFlightService: FlightService;

    @Test
    fun testShopList() {
        val req = """{
	"adultNum": 1,
	"childNum": 1,
	"infantNum": 0,
	"screenCondition": {
		"airline": [],
		"airports": [],
		"cabin": [
			"ECONOMY_CLASS"
		],
		"sort": "PRICE_LOW_HIGH",
		"stylePreference": [],
		"timeRange": [],
		"flightNos": []
	},
	"segments": [
		{
			"arriveCode": "SHA",
			"departCode": "CAN",
			"departTime": "2025-05-26"
		}
	],
	"sourceType": 0,
	"tripType": 0
}""".into<ShopListReq>()
        val shoppingList = zhiFeiFlightService.shoppingList(req)
        log.info("shoppingList: {}", shoppingList.toJSONString())
    }

    @Test
    fun testAccRule() {
        val req = AccurateReq().copy(
            adultNum = 1, productInfos = listOf(
                """{
                    "productCode": "preference-standard",
                    "productTag": "ZF_PRODUCT",
                    "productTagDisplay": "智飞科技",
                    "productSource": "ZF",
                    "orderInfo": "958cb5c1be5578b24562837954dfcdac"
                }""".into<ProductInfo>()
            )
        )
        val result = zhiFeiFlightService.accSearch(req)
        log.info("acc rule: {}", result.toJSONString())
    }


    @Test
    fun testOrderDetail() {
        tcDomesticFlightService.orderDetail("")
    }

    @Test
    fun testShopping() {
        val res = fR24FlightService.shoppingList(
            ShopListReq(
                1, 0, 0, 0, 0, listOf(SegmentVo("SZX", "PVG", "2024-12-12"))
            )
        )
        log.info("result: {}", res.toJSONString())
//        val l = res.values.toList()[0][0]
//        val req = AccurateReq(1, 2, 1, 0, 0, "", 0, listOf(l.productInfo), l.segments)
//        val accResult = fR24FlightService.accSearch(req)
//        log.info("accResult: {}", accResult.toJSONString())
    }
}