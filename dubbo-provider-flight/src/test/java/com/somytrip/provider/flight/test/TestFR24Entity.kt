package com.somytrip.provider.flight.test

import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.somytrip.entity.dto.order.OrderBigFieldDTO
import com.somytrip.model.flight.fr24.*
import com.somytrip.model.flight.vo.OrderBaseVo
import org.junit.Test
import org.junit.runner.RunWith
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.junit4.SpringRunner

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-08 16:04
 */
@RunWith(SpringRunner::class)
@SpringBootTest
class TestFR24Entity {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Test
    fun test() {
        val s = """{
    "orderNo": "1836215673554640896",
    "thirdServiceType": "flight",
    "field1": {
        "accurate": {
            "request": {
                "contact": {
                    "name": "叶福星",
                    "email": "",
                    "phone": "17003099837"
                },
                "products": [
                    {
                        "data": "0,537c5cf7a688495b91113c0e50b05597,10,1E,PAPER,0",
                        "price": "1768.50",
                        "productType": 0,
                        "productDetail": {
                            "prices": [
                                {
                                    "tax": "100",
                                    "type": 1,
                                    "price": "1668.5"
                                }
                            ],
                            "passengers": [
                                {
                                    "type": 1,
                                    "gender": 0,
                                    "nation": "CN",
                                    "birthday": "2001-11-07",
                                    "creditNo": "******************",
                                    "lastName": "",
                                    "firstName": "叶福星",
                                    "linkPhone": "17003099837",
                                    "creditType": 1,
                                    "nationCode": "CN",
                                    "cardIssuePlace": "CN",
                                    "gmtCreditValidate": "2032-04-01"
                                }
                            ]
                        }
                    }
                ],
                "totalPrice": "1768.50",
                "buisinessLineType": 0
            },
            "ruleUnit": {
                "prices": [
                    {
                        "price": 1686,
                        "taxTotal": "100",
                        "taxDetail": {
                            "yq": "50.0",
                            "tax": "50.0"
                        },
                        "passengerType": "ADULT"
                    }
                ],
                "segments": [
                    {
                        "meal": "",
                        "stops": [],
                        "airline": "MU",
                        "mileage": "0",
                        "aircraft": "32L",
                        "duration": "2h25m",
                        "flightNo": "MU5155",
                        "stopTime": "",
                        "tripType": 0,
                        "flightShare": false,
                        "transitTime": "",
                        "aircraftType": "0",
                        "segmentIndex": 1,
                        "stopsDisplay": [],
                        "arriveAirport": "PEK",
                        "departAirport": "PVG",
                        "durationTotal": "2h25m",
                        "arriveDateTime": "2024-10-31 13:00:00",
                        "departDateTime": "2024-10-31 10:35:00",
                        "operationAirline": "",
                        "arriveAirportTerm": "T2",
                        "departAirportTerm": "T1",
                        "operationFlightNo": "MU5155",
                        "arriveAirportDisplay": "",
                        "departAirportDisplay": ""
                    }
                ],
                "limitInfo": {
                    "ages": [],
                    "allowNations": [],
                    "forbidNations": [],
                    "maxPersonCount": 9,
                    "minPersonCount": 1
                },
                "baggageInfo": {
                    "rules": [
                        {
                            "type": "HAND",
                            "rules": [
                                {
                                    "volume": "20*40*55cm",
                                    "weight": "8.0",
                                    "freePrices": "1",
                                    "weightType": "ONLY_ONE",
                                    "segmentIndex": 1
                                }
                            ],
                            "passengerType": "ADULT"
                        },
                        {
                            "type": "CHECKED",
                            "rules": [
                                {
                                    "volume": "40*60*100cm",
                                    "weight": "20.0",
                                    "freePrices": "",
                                    "weightType": "ONLY_ONE",
                                    "segmentIndex": 1
                                }
                            ],
                            "passengerType": "ADULT"
                        }
                    ],
                    "description": "上海-北京:\n成人, 托运每件20.0KG, 行李体积限制为：40*60*100cm; \n成人, 手提每人1件, 每件8.0KG, 行李体积限制为：20*40*55cm; "
                },
                "productInfo": {
                    "orderInfo": "0,537c5cf7a688495b91113c0e50b05597,10,1E,PAPER,0",
                    "productTag": "PRODUCT_21",
                    "productCode": "21",
                    "productSource": "TC",
                    "productTagDisplay": "特惠1h"
                },
                "serviceTime": {
                    "ticket": [
                        {
                            "time": "00:00-23:59",
                            "week": 3
                        }
                    ],
                    "refundChange": [
                        {
                            "time": "08:00-20:00",
                            "week": 7
                        },
                        {
                            "time": "08:00-20:00",
                            "week": 1
                        },
                        {
                            "time": "00:00-20:00",
                            "week": 4
                        },
                        {
                            "time": "08:00-23:59",
                            "week": 3
                        },
                        {
                            "time": "08:00-20:00",
                            "week": 2
                        },
                        {
                            "time": "08:00-20:00",
                            "week": 5
                        },
                        {
                            "time": "08:00-20:00",
                            "week": 6
                        }
                    ]
                },
                "issueTicketInfo": {
                    "gds": "TravelSky",
                    "autoTicket": false,
                    "billMethod": "OTHER",
                    "billAirline": "MU",
                    "billMaterials": "TRAVEL_ITINERARY_AND_DIFF_INVOICE",
                    "changePnrIssueTicket": true
                },
                "refundChangeInfo": {
                    "rules": [
                        {
                            "fee": 0,
                            "feeDesc": "￥0.00/人",
                            "percent": 0,
                            "currency": "CNY",
                            "endpoint": -720,
                            "ruleType": "REFUND",
                            "timeRange": "2024-10-01 10:35前",
                            "startPoint": -4320,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 0,
                            "feeDesc": "￥0.00/人",
                            "percent": 0,
                            "currency": "CNY",
                            "endpoint": -720,
                            "ruleType": "CHANGE",
                            "timeRange": "2024-10-01 10:35前",
                            "startPoint": -4320,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 167,
                            "feeDesc": "￥167.00/人",
                            "percent": 10,
                            "currency": "CNY",
                            "endpoint": -168,
                            "ruleType": "REFUND",
                            "timeRange": "2024-10-24 10:35前",
                            "startPoint": -720,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 84,
                            "feeDesc": "￥84.00/人",
                            "percent": 5,
                            "currency": "CNY",
                            "endpoint": -168,
                            "ruleType": "CHANGE",
                            "timeRange": "2024-10-24 10:35前",
                            "startPoint": -720,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 251,
                            "feeDesc": "￥251.00/人",
                            "percent": 15,
                            "currency": "CNY",
                            "endpoint": -48,
                            "ruleType": "REFUND",
                            "timeRange": "2024-10-29 10:35前",
                            "startPoint": -168,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 167,
                            "feeDesc": "￥167.00/人",
                            "percent": 10,
                            "currency": "CNY",
                            "endpoint": -48,
                            "ruleType": "CHANGE",
                            "timeRange": "2024-10-29 10:35前",
                            "startPoint": -168,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 418,
                            "feeDesc": "￥418.00/人",
                            "percent": 25,
                            "currency": "CNY",
                            "endpoint": -4,
                            "ruleType": "REFUND",
                            "timeRange": "2024-10-31 06:35前",
                            "startPoint": -48,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 251,
                            "feeDesc": "￥251.00/人",
                            "percent": 15,
                            "currency": "CNY",
                            "endpoint": -4,
                            "ruleType": "CHANGE",
                            "timeRange": "2024-10-31 06:35前",
                            "startPoint": -48,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 502,
                            "feeDesc": "￥502.00/人",
                            "percent": 30,
                            "currency": "CNY",
                            "endpoint": 0,
                            "ruleType": "REFUND",
                            "timeRange": "2024-10-31 10:35前",
                            "startPoint": -4,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 335,
                            "feeDesc": "￥335.00/人",
                            "percent": 20,
                            "currency": "CNY",
                            "endpoint": 0,
                            "ruleType": "CHANGE",
                            "timeRange": "2024-10-31 10:35前",
                            "startPoint": -4,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 502,
                            "feeDesc": "￥502.00/人",
                            "percent": 30,
                            "currency": "CNY",
                            "endpoint": 4320,
                            "ruleType": "REFUND",
                            "timeRange": "2024-10-31 10:35后",
                            "startPoint": 0,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 335,
                            "feeDesc": "￥335.00/人",
                            "percent": 20,
                            "currency": "CNY",
                            "endpoint": 4320,
                            "ruleType": "CHANGE",
                            "timeRange": "2024-10-31 10:35后",
                            "startPoint": 0,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": true
                        },
                        {
                            "fee": 0,
                            "feeDesc": "￥0.00/人",
                            "percent": 0,
                            "currency": "CNY",
                            "endpoint": 0,
                            "ruleType": "UNKNOWN",
                            "timeRange": "",
                            "startPoint": 0,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": false
                        },
                        {
                            "fee": 0,
                            "feeDesc": "￥0.00/人",
                            "percent": 0,
                            "currency": "CNY",
                            "endpoint": 0,
                            "ruleType": "UNKNOWN",
                            "timeRange": "",
                            "startPoint": 0,
                            "segmentIndex": 1,
                            "passengerType": "ADULT",
                            "includeEndPoint": false,
                            "includeStartPoint": false
                        }
                    ],
                    "applyChangeFees": [],
                    "applyRefundFees": [
                        "￥0.00/人"
                    ],
                    "changeDescription": "上海-北京:\n    成人改期费:\n        起飞前720小时前0元/人\n        起飞前720小时后到起飞前168小时前84元/人, 手续费率5%\n        起飞前168小时后到起飞前48小时前167元/人, 手续费率10%\n        起飞前48小时后到起飞前4小时前251元/人, 手续费率15%\n        起飞前4小时后到起飞前335元/人, 手续费率20%\n        起飞后335元/人, 手续费率20%\n",
                    "refundDescription": "上海-北京:\n    成人退票费:\n        起飞前720小时前0元/人\n        起飞前720小时后到起飞前168小时前167元/人, 手续费率10%\n        起飞前168小时后到起飞前48小时前251元/人, 手续费率15%\n        起飞前48小时后到起飞前4小时前418元/人, 手续费率25%\n        起飞前4小时后到起飞前502元/人, 手续费率30%\n        起飞后502元/人, 手续费率30%\n"
                }
            },
            "totalTax": "100.0",
            "totalPrice": "1668.5"
        },
        "products": [
            {
                "state": true,
                "unitKey": "0,537c5cf7a688495b91113c0e50b05597,10,1E,PAPER,0",
                "productType": "0",
                "productDetail": "{\"verify\":false}",
                "productTotalPrice": "1768.50"
            }
        ],
        "totalPrice": "1768.50",
        "orderSerialNo": "H09M176P3H9379E389"
    }
}"""
        val dto = s.into<OrderBigFieldDTO>()
        val vo = dto.field1.toJSONString().into<OrderBaseVo>()
        vo.accurate.ruleUnit.productInfo
        log.info("dto: {}", dto)
        log.info("vo: {}", vo)
    }

    @Test
    fun testShopEntity() {
        val s = """{
    "authentication": {
        "sign": "{{sign}}",
        "timestamp": "{{time}}"
    },
    "searchLegs": [
        {
            "origin": "SIN",
            "destination": "HKG",
            "depDate": "2023-11-15"
        }
    ],
    "adultNum": 2,
    "childNum": 0,
    "infantNum": 0,
    "preferences": {
        "cabin": "Y"
    }
}""";
        val req = JSON.parseObject(s, ShoppingRequest::class.java);
        log.info("res: {}", JSON.toJSONString(req))
        val rspS = """{
    "traceId": "queryFlight_NEWAPI230911165800484095",
    "code": "000000",
    "message": "成功",
    "processingTime": 676,
    "data": {
        "offers": [
            {
                "offerId": "15803089789194240",
                "legId": "55623c5bf92ccd6d3cf30b543c0bd5ad",
                "currency": "CNY",
                "platingCarrier": "IJ",
                "pricePerPax": [
                    {
                        "paxType": "ADT",
                        "baseFare": 2356,
                        "totalTax": 2050,
                        "taxBreakdown": [],
                        "serviceFee": null
                    }
                ],
                "cabin": [
                    "Y"
                ],
                "fareBasis": [
                    null
                ],
                "availability": [
                    "9"
                ],
                "extraInfo": {
                    "freeBaggageAllowance": [
                        {
                            "segmentId": "d50b31d910612e8edb45b9466649a3a3",
                            "cabinBagPc": "e-e-e",
                            "cabinBagSize": "e-e-e",
                            "cabinBagWeight": "e-e-e",
                            "checkedBagPc": "e-e-e",
                            "checkedBagSize": "e-e-e",
                            "checkedBagWeight": "20-e-e"
                        }
                    ]
                },
                "eligibilityFlag": false,
                "eligibilityDetail": null,
                "rules": {
                    "refund": [
                        {
                            "paxType": "ADT",
                            "couponStatus": 0,
                            "refundPolicy": "airlinePolicyApplied",
                            "applicableTime": null,
                            "fullRefundAP": null,
                            "refundFee": null
                        }
                    ],
                    "change": [
                        {
                            "paxType": "ADT",
                            "couponStatus": 0,
                            "changePolicy": "airlinePolicyApplied",
                            "applicableTime": null,
                            "changeFee": null
                        }
                    ]
                },
                "productType": "AAA",
                "productTag": {
                    "ticketPromise": 0,
                    "refuseDeadline": 0,
                    "ticketingTime": 60,
                    "reschedulePendingTime": 240,
                    "voluntaryRefundTime": 21600,
                    "involuntaryRefundTime": 21600,
                    "refundCondition": 0,
                    "reissueCondition": 0,
                    "voidingCondition": false,
                    "voluntaryServiceStandard": 2,
                    "involuntaryServiceStandard": 1,
                    "RBDChangedRisk": false
                },
                "productSource": "OTH",
                "RBD": [
                    "K"
                ]
            },
            {
                "offerId": "15803089789194241",
                "legId": "6569cc7f11c26f09901f5af0063aff8c",
                "currency": "CNY",
                "platingCarrier": "IJ",
                "pricePerPax": [
                    {
                        "paxType": "ADT",
                        "baseFare": 2573,
                        "totalTax": 2239,
                        "taxBreakdown": [],
                        "serviceFee": null
                    }
                ],
                "cabin": [
                    "Y"
                ],
                "fareBasis": [
                    null
                ],
                "availability": [
                    "9"
                ],
                "extraInfo": {
                    "freeBaggageAllowance": [
                        {
                            "segmentId": "3dfc73915f991967df377cc232a4eeef",
                            "cabinBagPc": "e-e-e",
                            "cabinBagSize": "e-e-e",
                            "cabinBagWeight": "e-e-e",
                            "checkedBagPc": "e-e-e",
                            "checkedBagSize": "e-e-e",
                            "checkedBagWeight": "20-e-e"
                        }
                    ]
                },
                "eligibilityFlag": false,
                "eligibilityDetail": null,
                "rules": {
                    "refund": [
                        {
                            "paxType": "ADT",
                            "couponStatus": 0,
                            "refundPolicy": "airlinePolicyApplied",
                            "applicableTime": null,
                            "fullRefundAP": null,
                            "refundFee": null
                        }
                    ],
                    "change": [
                        {
                            "paxType": "ADT",
                            "couponStatus": 0,
                            "changePolicy": "airlinePolicyApplied",
                            "applicableTime": null,
                            "changeFee": null
                        }
                    ]
                },
                "productType": "AAA",
                "productTag": {
                    "ticketPromise": 0,
                    "refuseDeadline": 0,
                    "ticketingTime": 60,
                    "reschedulePendingTime": 240,
                    "voluntaryRefundTime": 21600,
                    "involuntaryRefundTime": 21600,
                    "refundCondition": 0,
                    "reissueCondition": 0,
                    "voidingCondition": false,
                    "voluntaryServiceStandard": 2,
                    "involuntaryServiceStandard": 1,
                    "RBDChangedRisk": false
                },
                "productSource": "OTH",
                "RBD": [
                    "S"
                ]
            },
            {
                "offerId": "15803089789194242",
                "legId": "e3504f0918ff9594ec8537663b02f925",
                "currency": "CNY",
                "platingCarrier": "IJ",
                "pricePerPax": [
                    {
                        "paxType": "ADT",
                        "baseFare": 3327,
                        "totalTax": 2896,
                        "taxBreakdown": [],
                        "serviceFee": null
                    }
                ],
                "cabin": [
                    "Y"
                ],
                "fareBasis": [
                    null
                ],
                "availability": [
                    "9"
                ],
                "extraInfo": {
                    "freeBaggageAllowance": [
                        {
                            "segmentId": "b628821e9ac613fb74d9fc49d8391883",
                            "cabinBagPc": "e-e-e",
                            "cabinBagSize": "e-e-e",
                            "cabinBagWeight": "e-e-e",
                            "checkedBagPc": "e-e-e",
                            "checkedBagSize": "e-e-e",
                            "checkedBagWeight": "20-e-e"
                        }
                    ]
                },
                "eligibilityFlag": false,
                "eligibilityDetail": null,
                "rules": {
                    "refund": [
                        {
                            "paxType": "ADT",
                            "couponStatus": 0,
                            "refundPolicy": "airlinePolicyApplied",
                            "applicableTime": null,
                            "fullRefundAP": null,
                            "refundFee": null
                        }
                    ],
                    "change": [
                        {
                            "paxType": "ADT",
                            "couponStatus": 0,
                            "changePolicy": "airlinePolicyApplied",
                            "applicableTime": null,
                            "changeFee": null
                        }
                    ]
                },
                "productType": "AAA",
                "productTag": {
                    "ticketPromise": 0,
                    "refuseDeadline": 0,
                    "ticketingTime": 60,
                    "reschedulePendingTime": 240,
                    "voluntaryRefundTime": 21600,
                    "involuntaryRefundTime": 21600,
                    "refundCondition": 0,
                    "reissueCondition": 0,
                    "voidingCondition": false,
                    "voluntaryServiceStandard": 2,
                    "involuntaryServiceStandard": 1,
                    "RBDChangedRisk": false
                },
                "productSource": "OTH",
                "RBD": [
                    "U"
                ]
            }
        ],
        "legs": [
            {
                "legId": "55623c5bf92ccd6d3cf30b543c0bd5ad",
                "segmentIds": [
                    "d50b31d910612e8edb45b9466649a3a3"
                ]
            },
            {
                "legId": "6569cc7f11c26f09901f5af0063aff8c",
                "segmentIds": [
                    "3dfc73915f991967df377cc232a4eeef"
                ]
            },
            {
                "legId": "e3504f0918ff9594ec8537663b02f925",
                "segmentIds": [
                    "b628821e9ac613fb74d9fc49d8391883"
                ]
            }
        ],
        "segments": [
            {
                "segmentId": "d50b31d910612e8edb45b9466649a3a3",
                "duration": 142,
                "carrier": "IJ",
                "flightNo": "5817",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "SIN",
                "depTerminal": null,
                "depTime": "2023-11-15T00:25",
                "arrAirport": "HKG",
                "arrTerminal": null,
                "arrTime": "2023-11-15T02:47",
                "stopAirport": null,
                "stopDuration": null
            },
            {
                "segmentId": "3dfc73915f991967df377cc232a4eeef",
                "duration": 197,
                "carrier": "IJ",
                "flightNo": "1811",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "SIN",
                "depTerminal": null,
                "depTime": "2023-11-15T12:05",
                "arrAirport": "HKG",
                "arrTerminal": null,
                "arrTime": "2023-11-15T15:22",
                "stopAirport": null,
                "stopDuration": null
            },
            {
                "segmentId": "b628821e9ac613fb74d9fc49d8391883",
                "duration": 147,
                "carrier": "IJ",
                "flightNo": "8199",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "QPG",
                "depTerminal": null,
                "depTime": "2023-11-15T12:40",
                "arrAirport": "HKG",
                "arrTerminal": null,
                "arrTime": "2023-11-15T15:07",
                "stopAirport": null,
                "stopDuration": null
            }
        ]
    }
}"""

        val rsp = rspS.into<FR24Response<ShoppingData>>()
        log.info("rsp: {}", JSON.toJSONString(rsp))
    }

    @Test
    fun testPricingEntity() {
        val reqS = """{
    "authentication": {
        "sign": "",
        "timestamp": ""
    },
    "offerId": "a17f56079af111ca6278d72124595910",
    "adultNum": 2,
    "childNum": 0,
    "infantNum": 0
}""";
        val rspS = """{
    "traceId": "verify_NEWAPI2309111710329910910",
    "code": "000000",
    "message": "success",
    "processingTime": 477,
    "data": {
        "offer": [
            {
                "offerId": "15803139091927040",
                "legId": "55623c5bf92ccd6d3cf30b543c0bd5ad",
                "currency": "CNY",
                "platingCarrier": "IJ",
                "pricePerPax": [
                    {
                        "paxType": "ADT",
                        "baseFare": 2316,
                        "totalTax": 1982,
                        "taxBreakdown": [],
                        "serviceFee": null
                    }
                ],
                "cabin": [
                    "Y"
                ],
                "fareBasis": [
                    null
                ],
                "availability": [
                    "2"
                ],
                "extraInfo": {
                    "freeBaggageAllowance": [
                        {
                            "segmentId": "d50b31d910612e8edb45b9466649a3a3",
                            "cabinBagPc": "e-e-e",
                            "cabinBagSize": "e-e-e",
                            "cabinBagWeight": "e-e-e",
                            "checkedBagPc": "e-e-e",
                            "checkedBagSize": "e-e-e",
                            "checkedBagWeight": "20-e-e"
                        }
                    ]
                },
                "eligibilityFlag": false,
                "eligibilityDetail": null,
                "rules": {
                    "refund": [
                        {
                            "paxType": "ADT",
                            "couponStatus": 0,
                            "refundPolicy": "airlinePolicyApplied",
                            "applicableTime": null,
                            "fullRefundAP": null,
                            "refundFee": null
                        }
                    ],
                    "change": [
                        {
                            "paxType": "ADT",
                            "couponStatus": 0,
                            "changePolicy": "airlinePolicyApplied",
                            "applicableTime": null,
                            "changeFee": null
                        }
                    ]
                },
                "productType": "AAA",
                "productTag": {
                    "ticketPromise": 0,
                    "refuseDeadline": 0,
                    "ticketingTime": 60,
                    "reschedulePendingTime": 240,
                    "voluntaryRefundTime": 21600,
                    "involuntaryRefundTime": 21600,
                    "refundCondition": 0,
                    "reissueCondition": 0,
                    "voidingCondition": false,
                    "voluntaryServiceStandard": 2,
                    "involuntaryServiceStandard": 1,
                    "RBDChangedRisk": false
                },
                "productSource": "OTH",
                "totalPrice": 8596,
                "paxInfoRequired": {
                    "birthday": true,
                    "gender": true,
                    "cardNum": true,
                    "cardType": true,
                    "cardIssuedPlace": true,
                    "cardExpiryDate": true,
                    "nationality": true,
                    "paxEmail": true,
                    "paxMobile": true,
                    "areaCode": true
                },
                "RBD": [
                    "K"
                ]
            }
        ],
        "legs": {
            "legId": "55623c5bf92ccd6d3cf30b543c0bd5ad",
            "segmentIds": [
                "d50b31d910612e8edb45b9466649a3a3"
            ]
        },
        "segments": [
            {
                "segmentId": "d50b31d910612e8edb45b9466649a3a3",
                "duration": 142,
                "carrier": "IJ",
                "flightNo": "5817",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "SIN",
                "depTerminal": null,
                "depTime": "2023-11-15T00:25",
                "arrAirport": "HKG",
                "arrTerminal": null,
                "arrTime": "2023-11-15T02:47",
                "stopAirport": null,
                "stopDuration": null
            }
        ]
    }
}""";
        val req = reqS.into<PricingRequest>()
        val rsp = rspS.into<FR24Response<PricingData>>()
        log.info("req: {}", JSON.toJSONString(req))
        log.info("rsp: {}", JSON.toJSONString(rsp))
    }

    @Test
    fun testBookingEntity() {
        val reqS = """{
    "authentication": {
        "sign": "{{sign}}",
        "timestamp": "{{time}}"
    },
    "offerId": "{{offerId_pricing}}",
    "partnerOrderNo": null,
    "passengers": "xEmzT/4wz4i8kCMIFwNpRvYuIndFLC3O1g8w5fCFU31hxZXhHGkPJX7MT0C/8JIschPAshsfiBIwX7j+5b4iwuv9uNJXCTbs4DNdJMQSkqd4rGv7Ap7Tz0xEVwaLadcM+fK/vglFI48feimxlCtMAcfe7YJ8Uwr9sxHvNc2bFg8QO120TytClhiP5LZjMawlkmI+mUVsnppfPFgdxLYplontWGF6euXIhgYtrUigRDEQAf+jefsxYrw90QdDll+yx0gBK3IN3R7kCgzBKCPU6/G70z9ssafAe8af/mfq2EsbXh5hUieMf3VlN6kth3aENnhtr32Go8CE0MdCt5ix9fR1zUtORcGPTeilc9xWQNS7h6T2ZCxZhkVIfJ0FMkfW5Nq5/uNzY7DDm5P3xVz++f4n31kH64UhOs1Z7FhtLBcQlpneEN4eQtNcS+SEwaWovZoTA3dsrZv6rijXn2p4j6YKIDZsdVGh2yeObOPR9RQp0XjwBAVbAtPGSICIozPJXlUkYUxzfZBVa8kzI3bS8RAr5jUsBWPYf2OynjD1/GZAt5LDFLUfO8iC81KxfG3fDGjmsDNXb/17dXLazC56J3MqIok3eOYpqMmLuuVb71B6iJNLyLo0RMD7xC6b7mfmjl5/KmzahBiAqYIC0eTfxkV//XX+wvdRVgdRl8I7HyhG56mYrri/lDZosZKWpYPmhk/tDoJVdFD07UdfaYllS/DjXCtIS3+0KyKqoCHdCOe8rYo+HB04wUNp0fOJGFBtrfJFL97BcByejNt9ltpp7ecH4SvcHACNG1Z+vYzmSpxq8Efi4tsnZOd4bbfSX2PadcXjUGoXP2mQOYNBCInsfadtx9Jf7ItYjITIOXbSedNmaSaBpChxyArN6sK1PEXejrp/9Xn9I1N6sTCRbGZzNagY3O9k+0vzERxrGw2IEd/FkgstsLiBccX5PnYfJwQibpLSmW8CxND2wmQPaV5VTJmUz2u4j5q97/A0oFNHl23qBt6+C0GYku9aw/13RaL6JEqSUeU3sfWqfzIP8DI3awdc7Ej62FgySuM2/PlEgI5kV949AuL7BaBbC2dK8K053Ynf2uWTHlT7BEMr+Vn/9+bY3dh3l3PeSHG0c1hN2FHOYC6LPu2s7gxgOy3fAe1kXsBa0FFQIwpQO2qjPYnXiQl9cUEwEM0YrsbTAZdJS2OntdYPzDMSX4TjexlAUK5VXl72mu+YipdLzKnlLboXtRj9D5dRNzSZw52SZPI/BEJbOC3vrqRH7F9RFBrUTbspoM+waR87W7pPtpwEdKzqOqXISKQr9iAeUvkI9SwvBsxkvqWr1t5A1ay5ir3ETpmk1mgUdWYM5SU6hssBHMMz5Ve3CLzbxoXDtYRVYPr/d0w=",
    "agentContact": {
        "agentName": "ZHANG/SAN",
        "agentEmail": "{{agent_email}} ",
        "mobile": "agent_mobile",
        "areaCode": "CN"
    }
}""";
        val rspS = """{
    "traceId": "order_NEWAPI2309111727001540916",
    "code": "000000",
    "message": "成功",
    "processingTime": 413,
    "data": {
        "orderNo": "15803203776942080",
        "orderStatus": "11",
        "partnerOrderNo": "",
        "currency": "CNY",
        "offer": {
            "offerId": "15803200840142848",
            "legId": "55623c5bf92ccd6d3cf30b543c0bd5ad",
            "platingCarrier": "IJ",
            "pricePerPax": [
                {
                    "paxType": "ADT",
                    "baseFare": 2316,
                    "totalTax": 1982,
                    "taxBreakdown": [],
                    "serviceFee": null
                }
            ],
            "cabin": [
                "Y"
            ],
            "fareBasis": [
                null
            ],
            "extraInfo": {
                "freeBaggageAllowance": [
                    {
                        "segmentId": "d50b31d910612e8edb45b9466649a3a3",
                        "cabinBagPc": "e-e-e",
                        "cabinBagSize": "e-e-e",
                        "cabinBagWeight": "e-e-e",
                        "checkedBagPc": "e-e-e",
                        "checkedBagSize": "e-e-e",
                        "checkedBagWeight": "20-e-e"
                    }
                ]
            },
            "eligibilityFlag": false,
            "eligibilityDetail": null,
            "rules": {
                "refund": [
                    {
                        "paxType": "ADT",
                        "couponStatus": 0,
                        "refundPolicy": "airlinePolicyApplied",
                        "applicableTime": null,
                        "fullRefundAP": null,
                        "refundFee": null
                    }
                ],
                "change": [
                    {
                        "paxType": "ADT",
                        "couponStatus": 0,
                        "changePolicy": "airlinePolicyApplied",
                        "applicableTime": null,
                        "changeFee": null
                    }
                ]
            },
            "productType": "AAA",
            "productTag": {
                "ticketPromise": 0,
                "refuseDeadline": 0,
                "ticketingTime": 60,
                "reschedulePendingTime": 240,
                "voluntaryRefundTime": 21600,
                "involuntaryRefundTime": 21600,
                "refundCondition": 0,
                "reissueCondition": 0,
                "voidingCondition": false,
                "voluntaryServiceStandard": 2,
                "involuntaryServiceStandard": 1,
                "RBDChangedRisk": false
            },
            "productSource": "OTH",
            "paymentMethod": null,
            "RBD": [
                "K"
            ]
        },
        "legs": {
            "legId": "55623c5bf92ccd6d3cf30b543c0bd5ad",
            "segmentIds": [
                "d50b31d910612e8edb45b9466649a3a3"
            ]
        },
        "segments": [
            {
                "segmentId": "d50b31d910612e8edb45b9466649a3a3",
                "duration": 142,
                "carrier": "IJ",
                "flightNo": "5817",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "SIN",
                "depTerminal": null,
                "depTime": "2023-11-15T00:25",
                "arrAirport": "HKG",
                "arrTerminal": null,
                "arrTime": "2023-11-15T02:47",
                "stopAirport": null,
                "stopDuration": null
            }
        ],
        "ticketInfo": [
            {
                "airlinePnr": null,
                "gdsPnr": "MNGG32",
                "paxId": "1",
                "paxName": "RING/BIN",
                "ticketNo": null
            },
            {
                "airlinePnr": null,
                "gdsPnr": "MNGG32",
                "paxId": "2",
                "paxName": "BING/LIN",
                "ticketNo": null
            }
        ],
        "payDeadline": "2023-09-11T22:57",
        "voidDeadline": null,
        "agentContact": {
            "agentName": "ZHANG/SAN",
            "agentEmail": "agent_email",
            "mobile": "agent_mobile",
            "areaCode": "CN"
        },
        "totalPrice": 8596,
        "ancillary": null,
        "splitJoint": false,
        "passengers": "xEmzT/4wz4i8kCMIFwNpRvYuIndFLC3O1g8w5fCFU31hxZXhHGkPJX7MT0C/8JIschPAshsfiBIwX7j+5b4iwuv9uNJXCTbs4DNdJMQSkqd4rGv7Ap7Tz0xEVwaLadcM+fK/vglFI48feimxlCtMAcfe7YJ8Uwr9sxHvNc2bFg8QO120TytClhiP5LZjMawlkmI+mUVsnppfPFgdxLYplontWGF6euXIhgYtrUigRDEQAf+jefsxYrw90QdDll+yx0gBK3IN3R7kCgzBKCPU6/G70z9ssafAe8af/mfq2EsbXh5hUieMf3VlN6kth3aENnhtr32Go8CE0MdCt5ix9fR1zUtORcGPTeilc9xWQNS7h6T2ZCxZhkVIfJ0FMkfW5Nq5/uNzY7DDm5P3xVz++f4n31kH64UhOs1Z7FhtLBcQlpneEN4eQtNcS+SEwaWovZoTA3dsrZv6rijXn2p4j6YKIDZsdVGh2yeObOPR9RQp0XjwBAVbAtPGSICIozPJXlUkYUxzfZBVa8kzI3bS8RAr5jUsBWPYf2OynjD1/GZAt5LDFLUfO8iC81KxfG3fDGjmsDNXb/17dXLazC56J3MqIok3eOYpqMmLuuVb71B6iJNLyLo0RMD7xC6b7mfmjl5/KmzahBiAqYIC0eTfxkV//XX+wvdRVgdRl8I7HyhG56mYrri/lDZosZKWpYPmhk/tDoJVdFD07UdfaYllS/DjXCtIS3+0KyKqoCHdCOe8rYo+HB04wUNp0fOJGFBtrfJFL97BcByejNt9ltpp7ecH4SvcHACNG1Z+vYzmSpxq8Efi4tsnZOd4bbfSX2PadcXjUGoXP2mQOYNBCInsfadtx9Jf7ItYjITIOXbSedNmaSaBpChxyArN6sK1PEXejrp/9Xn9I1N6sTCRbGZzNagY3O9k+0vzERxrGw2IEd/FkgstsLiBccX5PnYfJwQibpLSmW8CxND2wmQPaV5VTJmUz2u4j5q97/A0oFNHl23qBt6+C0GYku9aw/13RaL6JEqSUeU3sfWqfzIP8DI3awdc7Ej62FgySuM2/PlEgI5kV949AuL7BaBbC2dK8K053Ynf2uWTHlT7BEMr+Vn/9+bY3dh3l3PeSHG0c1hN2FHOYC6LPu2s7gxgOy3fAe1kXsBa0FFQIwpQO2qjPYnXiQl9cUEwEM0YrsbTAZdJS2OntdYPzDMSX4TjexlAUK5VXl72mu+YipdLzKnlLboXtRj9D5dRNzSZw52SZPI/BEJbOC3vrqRH7F9RFBrUTbspoM+waR87W7pPtpwEdKzqOqXISKQr9iAeUvkI9SwvBsxkvqWr1t5A1ay5ir3ETpmk1mgUdWYM5SU6hssBHMMz5Ve3CLzbxoXDtYRVYPr/d0w="
    }
}""";
        val req = reqS.into<BookingRequest>()
        val rsp = rspS.into<FR24Response<BookingData>>()
        log.info("req: {}", req.toJSONString())
        log.info("rsp: {}", rsp.toJSONString())
    }

    @Test
    fun testTickingEntity() {
        val reqS = """{
    "authentication": {
        "sign": "{{sign}}",
        "timestamp": "{{time}}"
    },
    "orderNo": "{{orderNo_booking}}",
    "partnerOrderNo": "1234567ss",
    "totalPrice": "",
    "currency": "{{currency_booking}}",
    "paymentMethod": "0"
}""";
        val rspS = """{
    "traceId": "",
    "code": "000000",
    "message": "success",
    "processingTime": 160,
    "data": {
        "orderNo": "{{orderNo_booking}}",
        "orderStatus": "11",
        "partnerOrderNo": "1234567ss ",
        "totalPrice": 1234.5,
        "currency": "{{currency_booking}}"
    }
}""";
        log.info("req: {}", reqS.into<TicketingRequest>().toJSONString())
        log.info("rsp: {}", rspS.into<FR24Response<TicketingData>>().toJSONString())
    }

    @Test
    fun testOrderDetail() {
        val reqS = """{
    "authentication": {
        "sign": "{{sign}}",
        "timestamp": "{{time}}"
    },
    "orderNo": "15803203776942080",
    "partnerOrderNo": ""
}""";
        val rspS = """{
    "traceId": "queryDetail_NEWAPI2309111739046620921",
    "code": "000000",
    "message": "success",
    "processingTime": 105,
    "data": {
        "orderNo": "15803203776942080",
        "orderStatus": "11",
        "partnerOrderNo": "",
        "currency": "CNY",
        "offer": {
            "offerId": "15803200840142848",
            "legId": "55623c5bf92ccd6d3cf30b543c0bd5ad",
            "platingCarrier": "IJ",
            "pricePerPax": [
                {
                    "paxType": "ADT",
                    "baseFare": 2316,
                    "totalTax": 1982,
                    "taxBreakdown": [],
                    "serviceFee": null
                }
            ],
            "cabin": [
                "Y"
            ],
            "fareBasis": [
                null
            ],
            "extraInfo": {
                "freeBaggageAllowance": [
                    {
                        "segmentId": "d50b31d910612e8edb45b9466649a3a3",
                        "cabinBagPc": "e-e-e",
                        "cabinBagSize": "e-e-e",
                        "cabinBagWeight": "e-e-e",
                        "checkedBagPc": "e-e-e",
                        "checkedBagSize": "e-e-e",
                        "checkedBagWeight": "20-e-e"
                    }
                ]
            },
            "eligibilityFlag": false,
            "eligibilityDetail": null,
            "rules": {
                "refund": [
                    {
                        "paxType": "ADT",
                        "couponStatus": 0,
                        "refundPolicy": "airlinePolicyApplied",
                        "applicableTime": null,
                        "fullRefundAP": null,
                        "refundFee": null
                    }
                ],
                "change": [
                    {
                        "paxType": "ADT",
                        "couponStatus": 0,
                        "changePolicy": "airlinePolicyApplied",
                        "applicableTime": null,
                        "changeFee": null
                    }
                ]
            },
            "productType": "AAA",
            "productTag": {
                "ticketPromise": 0,
                "refuseDeadline": 0,
                "ticketingTime": 60,
                "reschedulePendingTime": 240,
                "voluntaryRefundTime": 21600,
                "involuntaryRefundTime": 21600,
                "refundCondition": 0,
                "reissueCondition": 0,
                "voidingCondition": false,
                "voluntaryServiceStandard": 2,
                "involuntaryServiceStandard": 1,
                "RBDChangedRisk": false
            },
            "productSource": "OTH",
            "paymentMethod": null,
            "RBD": [
                "K"
            ]
        },
        "legs": {
            "legId": "55623c5bf92ccd6d3cf30b543c0bd5ad",
            "segmentIds": [
                "d50b31d910612e8edb45b9466649a3a3"
            ]
        },
        "segments": [
            {
                "segmentId": "d50b31d910612e8edb45b9466649a3a3",
                "duration": 142,
                "carrier": "IJ",
                "flightNo": "5817",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "SIN",
                "depTerminal": null,
                "depTime": "2023-11-15T00:25",
                "arrAirport": "HKG",
                "arrTerminal": null,
                "arrTime": "2023-11-15T02:47",
                "stopAirport": null,
                "stopDuration": null
            }
        ],
        "ticketInfo": [
            {
                "airlinePnr": "A5CD16",
                "gdsPnr": "MNGG32",
                "paxId": null,
                "paxName": "BING/LIN",
                "ticketNo": ""
            },
            {
                "airlinePnr": "A5CD16",
                "gdsPnr": "MNGG32",
                "paxId": null,
                "paxName": "RING/BIN",
                "ticketNo": ""
            }
        ],
        "payDeadline": "2023-09-11T22:57",
        "voidDeadline": null,
        "agentContact": {
            "agentName": "ZHANG/SAN",
            "agentEmail": "{{agent_email}}",
            "mobile": "agent_mobile",
            "areaCode": "CN"
        },
        "totalPrice": 8596,
        "ancillary": null,
        "passengers": "xEmzT/4wz4i8kCMIFwNpRvYuIndFLC3O1g8w5fCFU31hxZXhHGkPJX7MT0C/8JIschPAshsfiBIwX7j+5b4iwuv9uNJXCTbs4DNdJMQSkqd4rGv7Ap7Tz0xEVwaLadcM+fK/vglFI48feimxlCtMAcfe7YJ8Uwr9sxHvNc2bFg8QO120TytClhiP5LZjMawlkmI+mUVsnppfPFgdxLYplontWGF6euXIhgYtrUigRDEQAf+jefsxYrw90QdDll+yx0gBK3IN3R7kCgzBKCPU6/G70z9ssafAe8af/mfq2EsbXh5hUieMf3VlN6kth3aENnhtr32Go8CE0MdCt5ix9fR1zUtORcGPTeilc9xWQNS7h6T2ZCxZhkVIfJ0FMkfW5Nq5/uNzY7DDm5P3xVz++f4n31kH64UhOs1Z7FhtLBcQlpneEN4eQtNcS+SEwaWovZoTA3dsrZv6rijXn2p4j6YKIDZsdVGh2yeObOPR9RQp0XjwBAVbAtPGSICIozPJXlUkYUxzfZBVa8kzI3bS8RAr5jUsBWPYf2OynjD1/GZAt5LDFLUfO8iC81KxfG3fDGjmsDNXb/17dXLazC56J3MqIok3eOYpqMmLuuVb71B6iJNLyLo0RMD7xC6b7mfmjl5/KmzahBiAqYIC0eTfxkV//XX+wvdRVgdRl8I7HyhG56mYrri/lDZosZKWpYPmhk/tDoJVdFD07UdfaYllS/DjXCtIS3+0KyKqoCHdCOe8rYo+HB04wUNp0fOJGFBtrfJFL97BcByejNt9ltpp7ecH4SvcHACNG1Z+vYzmSpxq8Efi4tsnZOd4bbfSX2PadcXjUGoXP2mQOYNBCInsfadtx9Jf7ItYjITIOXbSedNmaSaBpChxyArN6sK1PEXejrp/9Xn9I1N6sTCRbGZzNagY3O9k+0vzERxrGw2IEd/FkgstsLiBccX5PnYfJwQibpLSmW8CxND2wmQPaV5VTJmUz2u4j5q97/A0oFNHl23qBt6+C0GYku9aw/13RaL6JEqSUeU3sfWqfzIP8DI3awdc7Ej62FgySuM2/PlEgI5kV949AuL7BaBbC2dK8K053Ynf2uWTHlT7BEMr+Vn/9+bY3dh3l3PeSHG0c1hN2FHOYC6LPu2s7gxgOy3fAe1kXsBa0FFQIwpQO2qjPYnXiQl9cUEwEM0YrsbTAZdJS2OntdYPzDMSX4TjexlAUK5VXl72mu+YipdLzKnlLboXtRj9D5dRNzSZw52SZPI/BEJbOC3vrqRH7F9RFBrUTbspoM+waR87W7pPtpwEdKzqOqXISKQr9iAeUvkI9SwvBsxkvqWr1t5A1ay5ir3ETpmk1mgUdWYM5SU6hssBHMMz5Ve3CLzbxoXDtYRVYPr/d0w="
    }
}"""
        log.info("req: {}", reqS.into<OrderDetailRequest>())
        log.info("rsp: {}", rspS.into<FR24Response<OrderDetailData>>().toJSONString())
    }

    @Test
    fun testOrderChangeInfoEntity() {
        val reqBody = """{
    "authentication": {
        "sign": "",
        "timeStamp": ""
    },
    "traceId": "",
    "type": "ticketNo/ticketNoUpdate",
    "orderNo": "",
    "partnerOrderNo": "",
    "info": {
        "ticketInfo": [
            {
                "paxId": "1",
                "airlinePnr": [
                    "ASDDSA",
                    "ASDDSA"
                ],
                "gdsPnr": [
                    "WWEWEW",
                    "UIUIYD"
                ],
                "ticketNo": [
                    "GTREDS",
                    "ASWSAS^ASWSAS^SSWSWS^EWEWER"
                ],
                "segmentIds": [
                    "1ajshbbgxisnwwqw",
                    "2poppojkjnbjasdj^3uiutgbchdbwjuiu"
                ]
            },
            {
                "paxId": "2",
                "airlinePnr": [
                    "ASDSDS",
                    "AJHKGH"
                ],
                "gdsPnr": [
                    "RERERE",
                    "RERERE"
                ],
                "ticketNo": [
                    "ASWERT",
                    "QUIOPO"
                ],
                "segmentIds": [
                    "1ajshbbgxisnwwqw",
                    "2poppojkjnbjasdj^3uiutgbchdbwjuiu"
                ]
            }
        ],
        "segments": [
            {
                "segmentId": "1ajshbbgxisnwwqw",
                "duration": 182,
                "carrier": "LH",
                "flightNo": "7965",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "SHA",
                "depTerminal": null,
                "depTime": "2023-04-20T17:10",
                "arrAirport": "HKG",
                "arrTerminal": null,
                "arrTime": "2023-04-20T20:12",
                "stopAirport": null,
                "stopDuration": null
            },
            {
                "segmentId": "2poppojkjnbjasdj",
                "duration": 243,
                "carrier": "ZH",
                "flightNo": "4668",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "PVG",
                "depTerminal": null,
                "depTime": "2023-04-20T10:10",
                "arrAirport": "HKG",
                "arrTerminal": null,
                "arrTime": "2023-04-20T14:13",
                "stopAirport": null,
                "stopDuration": null
            },
            {
                "segmentId": "3uiutgbchdbwjuiu",
                "duration": 288,
                "carrier": "ZH",
                "flightNo": "4668",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "PVG",
                "depTerminal": null,
                "depTime": "2023-04-20T10:10",
                "arrAirport": "HKG",
                "arrTerminal": null,
                "arrTime": "2023-04-20T14:13",
                "stopAirport": null,
                "stopDuration": null
            }
        ],
        "passengers": [
            {
                "paxId": "1",
                "name": "ZHANG/SAN",
                "birthday": "1989-11-12"
            },
            {
                "paxId": "2",
                "name": "LI/SI",
                "birthday": "1994-05-06"
            }
        ]
    }
}"""
        val notify = reqBody.into<OrderChangeInfoNotify>()
        log.info("json: {}", notify.toJSONString())
    }

    @Test
    fun testOrderChangeInfoEntityS() {
        val reqBody = """{
    "authentication": {
        "sign": "",
        "timeStamp": ""
    },
    "traceId": "",
    "type": "scheduleChange",
    "orderNo": "",
    "partnerOrderNo": "",
    "info": {
        "originInfo": [
            {
                "segmentId": "1ujhujhuijhuiknb",
                "duration": 140,
                "carrier": "LH",
                "flightNo": "2046",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "SHA",
                "depTerminal": null,
                "depTime": "2023-04-25T06:35",
                "arrAirport": "HKG",
                "arrTerminal": null,
                "arrTime": "2023-04-25T08:55",
                "stopAirport": null,
                "stopDuration": null
            },
            {
                "segmentId": "2poiujhjklmnjk8o",
                "duration": 247,
                "carrier": "LH",
                "flightNo": "8725",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "HKG",
                "depTerminal": null,
                "depTime": "2023-04-28T05:50",
                "arrAirport": "SHA",
                "arrTerminal": null,
                "arrTime": "2023-04-28T09:57",
                "stopAirport": null,
                "stopDuration": null
            },
            {
                "segmentId": "3yuiokjhnmlppupo",
                "duration": 192,
                "carrier": "ZH",
                "flightNo": "9050",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "HKG",
                "depTerminal": null,
                "depTime": "2023-04-26T07:35",
                "arrAirport": "SIN",
                "arrTerminal": null,
                "arrTime": "2023-04-26T10:47",
                "stopAirport": null,
                "stopDuration": null
            },
            {
                "segmentId": "4uiyhjuiknbgtfgy",
                "duration": 249,
                "carrier": "LH",
                "flightNo": "7504",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "XSP",
                "depTerminal": null,
                "depTime": "2023-04-27T00:20",
                "arrAirport": "HKG",
                "arrTerminal": null,
                "arrTime": "2023-04-27T04:29",
                "stopAirport": null,
                "stopDuration": null
            },
            {
                "segmentId": "5bfghsbhjungbgdd",
                "duration": 154,
                "carrier": "LH",
                "flightNo": "756",
                "codeShare": false,
                "operatingCarrier": null,
                "operatingFlightNo": null,
                "aircraftCode": "333",
                "depAirport": "HKG",
                "depTerminal": null,
                "depTime": "2023-04-28T08:40",
                "arrAirport": "SIN",
                "arrTerminal": null,
                "arrTime": "2023-04-28T11:14",
                "stopAirport": null,
                "stopDuration": null
            }
        ],
        "newInfo": [
            {
                "segmentIds": "1ujhujhuijhuiknb",
                "segmentStatus": "change",
                "segments": [
                    {
                        "segmentId": "5ujhujhuijhuiknb",
                        "duration": 140,
                        "carrier": "LH",
                        "flightNo": "2048",
                        "codeShare": false,
                        "operatingCarrier": null,
                        "operatingFlightNo": null,
                        "aircraftCode": "333",
                        "depAirport": "SHA",
                        "depTerminal": null,
                        "depTime": "2023-04-25T07:35",
                        "arrAirport": "WHU",
                        "arrTerminal": null,
                        "arrTime": "2023-04-25T09:55",
                        "stopAirport": null,
                        "stopDuration": null
                    },
                    {
                        "segmentId": "6poiujhjklmnjk8o",
                        "duration": 127,
                        "carrier": "LH",
                        "flightNo": "8725",
                        "codeShare": false,
                        "operatingCarrier": null,
                        "operatingFlightNo": null,
                        "aircraftCode": "333",
                        "depAirport": "WHU",
                        "depTerminal": null,
                        "depTime": "2023-04-25T10:50",
                        "arrAirport": "HKG",
                        "arrTerminal": null,
                        "arrTime": "2023-04-25T12:57",
                        "stopAirport": null,
                        "stopDuration": null
                    }
                ]
            },
            {
                "segmentIds": "2poiujhjklmnjk8o^3yuiokjhnmlppupo",
                "segmentStatus": "change",
                "segments": [
                    {
                        "segmentId": "7hghdch7hfj4hovf",
                        "duration": 247,
                        "carrier": "LH",
                        "flightNo": "8725",
                        "codeShare": false,
                        "operatingCarrier": null,
                        "operatingFlightNo": null,
                        "aircraftCode": "333",
                        "depAirport": "HKG",
                        "depTerminal": null,
                        "depTime": "2023-04-28T06:50",
                        "arrAirport": "SHA",
                        "arrTerminal": null,
                        "arrTime": "2023-04-28T10:57",
                        "stopAirport": null,
                        "stopDuration": null
                    }
                ]
            },
            {
                "segmentIds": "4thgjlkjhyupoikj",
                "changeType": "cancel",
                "segments": [
                    {
                        "segmentId": "4thgjlkjhyupoikj",
                        "duration": 249,
                        "carrier": "LH",
                        "flightNo": "7504",
                        "codeShare": false,
                        "operatingCarrier": null,
                        "operatingFlightNo": null,
                        "aircraftCode": "333",
                        "depAirport": "XSP",
                        "depTerminal": null,
                        "depTime": "2023-04-27T00:20",
                        "arrAirport": "HKG",
                        "arrTerminal": null,
                        "arrTime": "2023-04-27T04:29",
                        "stopAirport": null,
                        "stopDuration": null
                    }
                ]
            },
            {
                "segmentIds": "5bfghsbhjungbgdd",
                "segmentStatus": "recover",
                "segments": [
                    {
                        "segmentId": "5bfghsbhjungbgdd",
                        "duration": 154,
                        "carrier": "LH",
                        "flightNo": "756",
                        "codeShare": false,
                        "operatingCarrier": null,
                        "operatingFlightNo": null,
                        "aircraftCode": "333",
                        "depAirport": "HKG",
                        "depTerminal": null,
                        "depTime": "2023-04-28T08:40",
                        "arrAirport": "SIN",
                        "arrTerminal": null,
                        "arrTime": "2023-04-28T11:14",
                        "stopAirport": null,
                        "stopDuration": null
                    }
                ]
            }
        ]
    }
}"""
        val notify = reqBody.into<OrderChangeInfoNotify>()
        log.info("json: {}", notify.toJSONString())
    }

    @Test
    fun testOrderChangeInfoEntityStatus() {
        val reqBody = """{
    "authentication": {
        "sign": "",
        "timestamp": ""
    },
    "traceId": "",
    "type": "orderStatus",
    "orderNo": "",
    "partnerOrderNo": "",
    "orderStatus": 12
}"""
        val notify = reqBody.into<OrderChangeInfoNotify>()
        log.info("json: {}", notify.toJSONString())
    }

    @Test
    fun testOrderChangeTripInfoEntity() {
        val reqBody = """{
    "authentication": {
        "sign": "",
        "timeStamp": ""
    },
    "traceId": "",
    "type": "change/refund/void",
    "orderNo": "",
    "partnerOrderNo": "",
    "orderStatus": "",
    "info": {
        "feeInfo": {
            "currency": "",
            "totalAmount": "ADTNUM*ADT(totalFare + totalTax + serviceFee)+CHDNUM* CHD(...)+INFNUM * INF(...)",
            "refundAmount": 111.2,
            "changeFee": [
                {
                    "paxType": "",
                    "totalFare": "票面差+误机费+改期费",
                    "deductFare": [
                        {
                            "faretype": "票面差/误机费/改期费",
                            "fareAmount": 12.3
                        }
                    ],
                    "totalTax": 12,
                    "taxBreakdown": [
                        {
                            "taxType": "",
                            "taxAmount": 12
                        }
                    ],
                    "serviceFee": 10
                }
            ]
        }
    }
}"""
        val notify = reqBody.into<OrderChangeInfoNotify>()
        log.info("json: {}", notify.toJSONString())
    }

    @Test
    fun testPreSaleAncillaryShoppingEntity() {
        val reqS = """{
 "authentication" : {
 "sign" : "",
 "timestamp" : ""
 },
 "offerId" : ""
}"""
        val rspS = """{
    "traceId": "",
    "code": "",
    "message": "",
    "processingTime": 123421,
    "data": {
        "currency": "",
        "auxes": [
            {
                "segmentIds": [
                    ""
                ],
                "auxId": "",
                "baseFare": 150,
                "serviceFee": 10,
                "size": "",
                "piece": 1,
                "weight": 10,
                "limitPerPax": 5,
                "availability": 99
            }
        ],
        "segment": [
            {
                "segmentId": "",
                "duration": "",
                "carrier": "",
                "flightNo": "",
                "aircraftCode": "",
                "depAirport": "",
                "depTerminal": "",
                "depTime": "",
                "arrAirport": "",
                "arrTerminal": "",
                "arrTime": "",
                "stopAirport": [],
                "stopDuration": []
            }
        ]
    }
}"""

        log.info("req: {}", reqS.into<PreSaleAncillaryShoppingRequest>().toJSONString())
        log.info("rsp: {}", rspS.into<FR24Response<PreSaleAncillaryShoppingData>>().toJSONString())
    }

    @Test
    fun testPostSaleAncillaryEntity() {
        val reqS = """{
    "authentication": {
        "sign": "",
        "timestamp": ""
    },
    "orderNo": ""
}"""
        val rspS = """{
    "traceId": "",
    "code": "",
    "message": "",
    "processingTime": 123421,
    "data": {
        "currency": "",
        "auxes": [
            {
                "segmentIds": [
                    ""
                ],
                "auxId": "",
                "baseFare": 150,
                "serviceFee": 10,
                "size": "",
                "piece": 1,
                "weight": 10,
                "limitPerPax": 5,
                "availability": 99
            }
        ],
        "segment": [
            {
                "segmentId": "",
                "duration": "",
                "carrier": "",
                "flightNo": "",
                "aircraftCode": "",
                "depAirport": "",
                "depTerminal": "",
                "depTime": "",
                "arrAirport": "",
                "arrTerminal": "",
                "arrTime": "",
                "stopAirport": null,
                "stopDuration": null
            }
        ]
    }
}"""
        log.info("req: {}", reqS.into<PostSaleAncillaryRequest>().toJSONString())
        log.info("rsp: {}", rspS.into<FR24Response<PostSaleAncillaryData>>().toJSONString())
    }

    @Test
    fun testAncillaryBooking() {
        val reqS = """{
    "authentication": {
        "sign": "",
        "timestamp": ""
    },
    "orderNo": "",
    "partnerOrderNo": "",
    "ancillary": [
        {
            "paxId": 1,
            "segmentIds": [
                ""
            ],
            "auxId": "",
            "quantity": 1
        }
    ],
    "passengers": [
        {
            "paxId": "",
            "name": ""
        }
    ],
    "segments": [
        {
            "segmentId": "1bd336d0305b04f9d59e9a2d1895034c",
            "duration": 1234,
            "carrier": "BA",
            "flightNo": "1234",
            "codeShare": false,
            "operatingtCarrier": "BC",
            "operatingFlightNo": "2222",
            "aircraftCode": "",
            "depAirport": "",
            "depTerminal": "",
            "depTime": "",
            "arrAirport": "",
            "arrTerminal": "",
            "arrTime": "",
            "stopAirport": [
                "",
                ""
            ],
            "stopDuration": [
                1256,
                5678
            ]
        }
    ]
}"""
        val rspS = """{
    "traceId": "",
    "code": "",
    "message": "",
    "processingTime": "1",
    "data": {
        "auxOrderNo": "",
        "auxOrderStatus": "",
        "partnerAuxOrderNo": "",
        "orderNo": "",
        "createTime": "",
        "currency": "",
        "totalPrice": "",
        "ancillary": {
            "baggage": [
                {
                    "paxId": 1,
                    "segmentIds": [
                        ""
                    ],
                    "auxId": "",
                    "totalPrice": 810,
                    "baseFare": 800,
                    "serviceFee": 10,
                    "size": " ",
                    "piece": 1,
                    "weight": 23,
                    "quantity": 1
                }
            ]
        },
        "passengers": [
            {
                "paxId": "",
                "name": ""
            }
        ],
        "segments": [
            {
                "segmentId": "1bd336d0305b04f9d59e9a2d1895034c",
                "duration": 1234,
                "carrier": "BA",
                "flightNo": "1234",
                "codeShare": false,
                "operatingtCarrier": "BC",
                "operatingFlightNo": "2222",
                "aircraftCode": "",
                "depAirport": "",
                "depTerminal": "",
                "depTime": "",
                "arrAirport": "",
                "arrTerminal": "",
                "arrTime": "",
                "stopAirport": [
                    "",
                    ""
                ],
                "stopDuration": [
                    1256,
                    5678
                ]
            }
        ],
        "payDeadline": ""
    }
}"""
        log.info("req: {}", reqS.into<AncillaryBookingRequest>().toJSONString())
        val rsp = rspS.into<FR24Response<AncillaryBookingData>>()
        log.info("rsp: {}", rsp)
    }

    @Test
    fun testAncillaryPurchase() {
        val reqS = """{
 "authentication" : {
 },
 "auxOrderNo" : "",
 "partnerAuxOrderNo" : "",
"orderNo" : "",
 "currency" : "",
 "totalPrice" : "",
 "paymentMethod" : ""
}"""
        val rspS = """{
 "traceId" : "",
 "code" : "",
 "message" : "",
 "processingTime" : "",
 "data":{
 "auxOrderNo" : "",
 "partnerOrderNo" : "",
 "orderNo" : "",
 "currency" : "",
 "totalPrice" : ""
 }
}"""
        log.info("req: {}", reqS.into<AncillaryPurchaseRequest>().toJSONString())
        val rsp = rspS.into<FR24Response<AncillaryPurchaseData>>().toJSONString()
        log.info("rsp: {}", rsp)

    }

    @Test
    fun testAncillaryPurchaseDetail() {
        val reqS = """{
 "authentication" : {
 "sign" : "",
 "timestamp" : ""
 },
 "auxOrderNo" : "",
 "partnerOrderNo" : ""
}"""
        val rspS = """{
 "traceId" : "",
 "code" : "",
 "message" : "",
 "processingTime" : "",
 "data" : {
 "auxOrderNo" : "",
 "orderStatus" : "",
 "partnerOrderNo" : "",
 "orderNo" : "",
 "createTime" : "",
 "currency" : "",
 "totalPrice" : 810.00,
 "ancillary" : {
 "baggage":[
 {
 "paxId" : 1,
 "segmentIds" : [""],
 "auxId" : "",
 "totalPrice" : 810.00,
 "baseFare" : 800.00,
 "serviceFee" : 10.00,
 "size" : " ",
 "piece" : 1,
 "weight" : 23,
 "quantity" : 1
 }
 ]
 },
 "passengers" : [
 {
 "paxId" : "",
 "name" : ""
 }
 ],
 "segments" : [
 {
 "segmentId" : "1bd336d0305b04f9d59e9a2d1895034c",
 "duration" : 1234,
 "carrier" : "BA",
 "flightNo" : "1234",
 "codeShare" : false,
 "operatingtCarrier" : "",
 "operatingFlightNo" : "",
 "aircraftCode" : "",
 "depAirport" : "",
 "depTerminal" : "",
 "depTime" : "",
 "arrAirport" : "",
 "arrTerminal" : "",
 "arrTime" : "",
 "stopAirport" : ["",""],
 "stopDuration" : [1256,5678]
 }
 ],
 "payDeadline" : ""
 }
}"""
        log.info("req: {}", reqS.into<AncillaryOrderDetailRequest>().toJSONString())
        log.info("rsp: {}", rspS.into<FR24Response<AncillaryOrderDetailData>>().toJSONString())
    }
}