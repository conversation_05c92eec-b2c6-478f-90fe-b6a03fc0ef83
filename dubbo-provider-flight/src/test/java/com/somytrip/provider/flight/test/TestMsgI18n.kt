package com.somytrip.provider.flight.test

import cn.hutool.core.io.resource.ResourceUtil
import org.junit.Test
import org.junit.runner.RunWith
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.MessageSource
import org.springframework.test.context.junit4.SpringRunner
import java.util.Locale

/**
 * @Description:
 * @author: pigeon
 * @created: 2025-07-03 16:22
 */
@RunWith(SpringRunner::class)
@SpringBootTest
class TestMsgI18n {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var messageSource: MessageSource

    @Test
    fun test() {
        val resource = ResourceUtil.readUtf8Str("classpath:i18n/messages.properties")
        log.info("messages: {}", resource)
        val msg = messageSource.getMessage("all.product-type.24", null, Locale("zh", "CN"))
        log.info("msg: {}", msg)
    }
}