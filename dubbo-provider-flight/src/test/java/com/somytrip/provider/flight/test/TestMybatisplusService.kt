package com.somytrip.provider.flight.test

import com.alibaba.fastjson2.toJSONString
import com.somytrip.provider.flight.mapper.ChangeRecordMapper
import jakarta.annotation.Resource
import org.junit.Test
import org.junit.runner.RunWith
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.junit4.SpringRunner

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-08 16:04
 */
@RunWith(SpringRunner::class)
@SpringBootTest
class TestMybatisplusService {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Resource
    private lateinit var changeRecordMapper: ChangeRecordMapper

    @Test
    fun test() {
        val result = changeRecordMapper.selectById("1722885866634928128")
        val resultNew = result.copy(outOrderNo = result.outOrderNo.substring(0..10));
        val flag = changeRecordMapper.updateById(resultNew)
        log.info("update flag: {}", flag)
        log.info(result.toJSONString())
        log.info(resultNew.toJSONString())
    }
}