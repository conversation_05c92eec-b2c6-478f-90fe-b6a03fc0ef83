package com.somytrip.provider.flight.test

import cn.hutool.core.io.FileUtil
import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.somytrip.model.flight.config.FR24Config
import com.somytrip.model.flight.fr24.*
import com.somytrip.provider.flight.config.PriceConfig
import com.somytrip.provider.flight.service.CommonService
import com.somytrip.provider.flight.utils.FR24Util
import jakarta.annotation.Resource
import okhttp3.Credentials
import okhttp3.OkHttpClient
import org.junit.Test
import org.junit.runner.RunWith
import org.slf4j.LoggerFactory
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.junit4.SpringRunner
import java.net.InetSocketAddress
import java.net.Proxy

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-08 16:04
 */
@RunWith(SpringRunner::class)
@SpringBootTest
class TestFR24Util {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Resource
    private lateinit var commonService: CommonService

    @Resource
    private lateinit var priceConfig: PriceConfig
    private val fR24Util by lazy {
        FR24Util.Builder()
            .okHttpClient(
                OkHttpClient.Builder()
                    .proxy(Proxy(Proxy.Type.HTTP, InetSocketAddress("*************", 31280)))
                    .proxyAuthenticator { _, response ->
                        val credential = Credentials.basic("smt-proxy", "@@smt\$\$proxy##password");
                        return@proxyAuthenticator response.request.newBuilder()
                            .header("Proxy-Authorization", credential).build()
                    }
                    .build()
            )
            .commonService(commonService)
//            .fR24Config(FR24Config("YHJ", "Yf6BzEHJYMBsJaLi", "https://flight-test.flightroutes24.com"))
            .fR24Config(FR24Config("YHJ", "YgpQM5HSV3fEJFQD", "https://flight.flightroutes24.com"))
            .build()
    }

    @Test
    fun testPassengerDec() {
        val s =
            """AeLFoolhSHP9BhvfXM4LwojT5PO+YOPCJx7z/fRKryNZfoFJEvtVKoOqczSfweyyMZ5xhrYtSUcRS5lJs7B139NFgAuVNQswCJgizt3Rpzi9hT5+5Y3YjRBRe+48QSfR4lzEVpq5XKtgdPUgigQjSLPnWq/iP9coSn5ByKQ6jcqiFPh/GOGCmQ8f/S5ftBBTFiKO7i20izWPm5pDa782DFeIAZTpITj9fVvG/1R1JVGUcV/21pSzSPdemONCld/RRbGU1mjWFwzQCLSQX3UcdFuS/Qe+d/cGEyG9eAknurC6PviOFjJ8k9s1EMQfjM5JdFmOmmQ/YiNI/9S30fB7v3kQOjCzfHPzKnMfKvz5cSfL1VDD1zbHtWdLGSbYNbd2wlfqOqZDhja2uUPMMyN6iGu7RfgQ6wwJFn/+IA9aHZ2r1+C9VFl8hy11iDBdtqdJACEuDo7WwM+fNck8/A2FqFbS6G9HbH8IVe+k7vmdYtynuYbZ5+y/wLaxY1yCLCrck2W8/cjjAAwg3pJSQLPJiWBFf5sV0h5dgni02PRNF+jC+jnRcAGxlFhz8tGjjk/G0D2ZwTe1kra1ODtRg/ad/KVJEmMSs6B9qhlLC6ECoWIjAT+w45dcajzIN67XWTheiv9uLWQua9zYdYNqSW78kY/+8GLyDhwIpviW6W0wHARjKa9sAygE2bIkz5WZZ7UBAdcKgkVohBtW7us3oqCp56szT241Js++16Vs8jpuxPnMPMWuxSaeIKjDnOfbPn2g"""
        val aesPassengerList = fR24Util.aesPassengerList(s)
        log.info("aesPassengerList: {}", aesPassengerList.toJSONString())
    }

    @Test
    fun testShoppingRequest() {
        val request = ShoppingRequest(
            fR24Util.getAuthentication(),
            listOf(SearchLegItem("HKG", "TPE", "2024-12-06")),
            1,
            2,
            1
        )
        val shoppingRsp = fR24Util.shoppingList(request);
        log.info("json: {}", JSON.toJSONString(shoppingRsp))
        val shoppingData = fR24Util.shoppingData(shoppingRsp.data)
        log.info("shopping data: {}", shoppingData.toJSONString())
    }

    @Test
    fun testShopping() {
        val shoppingData =
            fR24Util.shoppingData(
                FileUtil.readUtf8String("D:\\fr24.json").into<FR24Response<ShoppingData>>().data
            )
        log.info("shoppingData: {}", shoppingData.toJSONString())
    }

    @Test
    fun testPricing() {
        val pricingData =
            fR24Util.pricingData(
                FileUtil.readUtf8String("D:\\fr24-pricing.json").into<FR24Response<PricingData>>().data
            )
        log.info("pricingData: {}", pricingData.toJSONString())
    }

    @Test
    fun testBooking() {
        val bookBody =
            """{"agentContact":{"agentEmail":"","agentName":"叶福星","areaCode":"+86","mobile":"17003099837"},"ancillary":[],"authentication":{"sign":"952bc6f6d9befef46a3b9e14e14c3895cce416966057ce7f866d5386c7e3cdf74b374dedb9bb13979e2e15679ad9c5880c59fab7d7a2966a0fbbca0b0b824ba3","timestamp":"1725609270"},"offerId":"17846934089240576","partnerOrderNo":"dev587664867823941","passengers":[{"accompaniedPaxId":"","areaCode":"+86","birthday":"2001-11-07","cardExpiryDate":"","cardIssuedPlace":"","cardNum":"******************","cardType":"ID","gender":"M","name":"福星叶","nationality":"CN","paxEmail":"","paxId":1,"paxMobile":"17003099837","paxType":"ADT"}]}"""
        val booking = fR24Util.booking(bookBody.into())
        log.info("booking: {}", booking.toJSONString())
    }

    @Test
    fun testQueryOrder() {
        val res = fR24Util.orderDetail(
            OrderDetailRequest(
                fR24Util.getAuthentication(),
                "17885390251888640",
                "1834425370092343296"
            )
        )
        log.info("res: {}", JSON.toJSONString(res))
    }
}