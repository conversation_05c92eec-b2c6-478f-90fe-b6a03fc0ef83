package com.somytrip.provider.flight.utils

import cn.hutool.core.date.DateUnit
import cn.hutool.core.date.DateUtil
import cn.hutool.core.date.LocalDateTimeUtil
import cn.hutool.core.net.url.UrlBuilder
import cn.hutool.core.util.NumberUtil
import cn.hutool.core.util.StrUtil
import cn.hutool.crypto.SecureUtil
import cn.hutool.http.HttpException
import cn.hutool.http.HttpUtil
import cn.hutool.http.Method
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.somytrip.exception.BusinessException
import com.somytrip.model.flight.common.*
import com.somytrip.model.flight.common.LimitInfo
import com.somytrip.model.flight.common.PriceItem
import com.somytrip.model.flight.config.ZfConfig
import com.somytrip.model.flight.enums.*
import com.somytrip.model.flight.vo.AccurateReq
import com.somytrip.model.flight.vo.CreateOrderReq
import com.somytrip.model.flight.vo.ShopListReq
import com.somytrip.model.flight.zf.*
import com.somytrip.provider.flight.service.CommonService
import com.somytrip.provider.flight.service.impl.CommonServiceImpl
import org.apache.dubbo.rpc.RpcContext
import org.slf4j.LoggerFactory
import java.time.Instant
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import java.util.Locale

/**
 * @Description: 智飞科技Utils
 * @author: pigeon
 * @created: 2025-01-07 17:43
 */
class ZfFlightUtil(
    builder: Builder
) {
    class Builder {
        internal var config = ZfConfig()
        internal var commonService: CommonService = CommonServiceImpl()

        fun config() = config

        fun config(config: ZfConfig) = apply {
            this.config = config
        }

        fun commonService() = commonService

        fun commonService(commonService: CommonService) = apply {
            this.commonService = commonService
        }

        fun builder(): ZfFlightUtil {
            return ZfFlightUtil(this)
        }
    }

    private val log = LoggerFactory.getLogger(this::class.java)
    private val config = builder.config()
    private val commonService = builder.commonService()
    private val shopTimeout: Int = 8000

    fun shop(req: ShopListReq): Map<String, List<ShopListRsp>> {
        val request = ShopReq(req).toJSONString()
        log.info("shop uri: ${ZfApi.SHOP}, request: $request")
        val rsp = post(ZfApi.SHOP.uri(), request, shopTimeout)
        val data = rsp.into<Resp<ShopRsp>>()
        log.info("shop uri: ${ZfApi.SHOP}, request: $request\nresponse: ${data.print()}")
        val lData = data.data ?: throw BusinessException(500, data.message)
        val sourceTypeFlag = commonService.isInternational(req.segments.map { it.departCode }
                + req.segments.map { it.arriveCode })
        val sourceType = if (sourceTypeFlag) {
            1
        } else {
            0
        }
        log.info("start init shop data: {}, {}", lData.searchKey, lData.solutions.size)
        val result = shopData(lData, sourceType)
        log.info("init shop result success: {}, {}", lData.searchKey, result.size)
        return result
    }

    private fun shopData(lData: ShopRsp, sourceType: Int): MutableMap<String, MutableList<ShopListRsp>> {
        val result = mutableMapOf<String, MutableList<ShopListRsp>>()
        log.info("shop solutions size: {}", lData.solutions.size)
        val lang = RpcContext.getServerAttachment().getAttachment("lang");
        val l = StrUtil.split(lang, StrUtil.C_UNDERLINE)
        val zfProductType = "zf.product-type."
        val locale = Locale(l[0], l[1])
        log.info("shop data deal lang is: {}", lang)
        lData.solutions.forEach {
            val notSkipFlag = it.journeys.first().segments.all { s -> s.availability > 0 }
            if (notSkipFlag) {
                var key = ""
                val item = ShopListRsp(
                    prices = it.priceDetail.priceList.map { p ->
                        val yq = p.taxDetail.filter { t -> t.type == "fuelTax" }
                            .sumOf { t -> NumberUtil.toBigDecimal(t.amount) }
                        val jq = p.taxDetail.filter { t -> t.type == "airportTax" }
                            .sumOf { t -> NumberUtil.toBigDecimal(t.amount) }

                        PriceItem(
                            PassengerTypeEnum.zfValueToEnum(p.passengerType),
                            p.tax,
                            commonService.getMarkUpPrice(p.price).toString(),
                            TaxDetail(
                                NumberUtil.toStr(yq),
                                NumberUtil.toStr(jq),
                            )
                        )
                    },
                    segments = it.journeys.first().segments.mapIndexed { index, s ->
                        val item = lData.segments[s.coreSegmentId] ?: throw BusinessException(500, "seg init error")
                        key += "${item.flightNo},"
                        val tranTime = if (index + 1 < it.journeys.first().segments.size) {
                            val nextS = it.journeys.first().segments[index + 1]
                            val itemNext =
                                lData.segments[nextS.coreSegmentId] ?: throw BusinessException(500, "seg init error *")
                            val start =
                                DateUtil.parse(
                                    "${item.arrivalDate} ${item.arrivalTime}",
                                    CommonValFunc.DATE_TIME_FORMAT
                                )
                            val end = DateUtil.parse(
                                "${itemNext.departureDate} ${itemNext.departureTime}",
                                CommonValFunc.DATE_TIME_FORMAT
                            )
                            val between = DateUtil.between(start, end, DateUnit.MINUTE)
                            CommonValFunc.formatIntToDuration(between.toInt())
                        } else {
                            ""
                        }
                        SegmentItem(
                            item.opAirline,
                            item.opFlightNo,
                            item.airline,
                            0,
                            item.isCodeShare,
                            item.flightNo,
                            item.equipment,
                            "", // 机型类型
                            item.arrival,
                            item.arrivalTerminal,
                            "${item.arrivalDate} ${item.arrivalTime}",
                            item.departure,
                            item.departureTerminal,
                            "${item.departureDate} ${item.departureTime}",
                            "", // 餐食信息
                            "${item.distance}",
                            index + 1,
                            item.stopovers.filter { s -> s.airport.isNotBlank() }.map { s ->
                                SegmentStopItem(
                                    s.airport,
                                    s.duration,
                                )
                            },
                            tranTime,
                            item.flightTime,
                            it.journeys.first().duration,
                            commonService.getAirportName(item.arrival),
                            commonService.getAirportName(item.departure),
                            commonService.getAirline(item.airline),
                            item.stopovers.filter { s -> s.airport.isNotBlank() }
                                .map { s -> commonService.getAirportName(s.airport) },
                            tranTime,
                            s.cabinCode
                        )
                    },
                    cabins = it.journeys.first().segments.mapIndexed { index, s ->
                        CabinItem(
                            CabinClassEnum.zfValueToEnums(s.cabinClass),
                            s.cabinCode,
                            s.availability,
                            index + 1,
                        )
                    },
                    productInfo = ProductInfo(
                        it.priceDetail.productType,
                        ProductTagEnum.ZF_PRODUCT,
                        ProviderSourceEnum.ZF,
                        it.solutionId,
                        getI18nMsg(zfProductType + it.priceDetail.productType, locale),
                    ),
                    sourceType = sourceType,
                    durationTotal = it.journeys.first().duration,
                )
                key = key.removeSuffix(",")
                if (!result.containsKey(key)) {
                    result[key] = mutableListOf()
                }
                result[key]?.add(item)
            } else {
                log.warn("skip solution: {}", it.solutionId)
            }
        }
        log.info("shop result size: {}", result.size)
        return result
    }

    fun pricing(req: PricingReq, sourceType: Int): Map<String, List<ShopListRsp>> {
        val request = req.toJSONString()
        val api = ZfApi.PRICING
        log.info("pricing uri: $api, request: $request")
        val rsp = post(api.uri(), request)
        val data = rsp.into<Resp<ShopRsp>>()
        log.info("pricing uri: $api, request: $request\nresponse: ${data.print()}")
        val lData = data.data ?: throw BusinessException(500, data.message)
        log.info("start init pricing data: {}, {}", lData.searchKey, lData.solutions.size)
        val result = shopData(lData, sourceType)
        log.info("init pricing result success: {}, {}", lData.searchKey, result.size)
        return result
    }

    fun accRuleOrigin(req: AccurateReq): Solution {
        val request = Passenger(
            req.adultNum,
            req.childNum,
            req.infantNum
        ).toJSONString()
        log.info("accRule uri: ${ZfApi.VERIFY}, request: $request")
        val rsp = post(StrUtil.format(ZfApi.VERIFY.uri(), req.productInfos.first().orderInfo), request)
        log.info("accRule response: {}", rsp)
        val data = rsp.into<Resp<Solution>>()
        log.info("accRule uri: ${ZfApi.VERIFY}, request: $request\nresponse: ${data.print()}")
        return data.data ?: throw BusinessException(500, data.message)
    }

    fun accRule(req: AccurateReq): AccurateRsp {
        val lData = accRuleOrigin(req)
        return AccurateRsp(
            req.sourceType,
            listOf(accRuleFormat(lData, req))
        )
    }

    fun accRuleFormat(lData: Solution, req: AccurateReq): AccRuleItem {
        val depTime = LocalDateTimeUtil.parse(
            lData.journeys.first().departureDate + " " +
                    lData.journeys.first().departureTime, CommonValFunc.DATE_TIME_FORMAT
        )
        log.info("depTime is: {}", depTime)
        val changeDec = mutableListOf<String>("${I18nUtil.getValue(I18n.CHANGE, req.locale)}: ")
        val refundDec = mutableListOf<String>("${I18nUtil.getValue(I18n.REFUND, req.locale)}: ")
        lData.fareRules.forEach {
            val d = when (PassengerTypeEnum.zfValueToEnum(it.passengerType)) {
                PassengerTypeEnum.ADULT -> {
                    I18nUtil.getValue(I18n.ADULT, req.locale)
                }

                PassengerTypeEnum.CHILD -> {
                    I18nUtil.getValue(I18n.CHILD, req.locale)
                }

                PassengerTypeEnum.INFANT -> {
                    I18nUtil.getValue(I18n.INF, req.locale)
                }

                else -> {
                    I18nUtil.getValue(I18n.ADULT, req.locale)
                }
            }
            val change = it.change.map { c ->
                var dec = ""
                val fee = if (c.value == "-1") {
                    I18nUtil.getValue(I18n.CAN_NOT_CHANGE, req.locale)
                } else {
                    "￥${c.value}"
                }
                if (c.key.startsWith(">")) {
                    val hour = NumberUtil.parseLong(c.key.removePrefix(">"))
                    val time = if (hour < 0) {
                        formatTimeBack(depTime, hour, req.locale)
                    } else {
                        formatTimePre(depTime, hour, req.locale)
                    }
                    dec = "$d-${time},${fee}"
                } else if (c.key.startsWith("<")) {
                    val hour = NumberUtil.parseLong(c.key.removePrefix("<"))
                    val time = if (hour < 0) {
                        formatTimeBack(depTime, hour, req.locale)
                    } else {
                        formatTimeBack(depTime, hour, req.locale)
                    }
                    dec = "$d-${time},${fee}"
                } else {
                    dec = "$d-${I18nUtil.getValue(I18n.ALL_TIME_RANGE, req.locale)},${fee}"
                }
                dec
            }
            changeDec.addAll(change)
            val refund = it.refund.map { c ->
                var dec = ""
                val fee = if (c.value == "-1") {
                    I18nUtil.getValue(I18n.CAN_NOT_REFUND, req.locale)
                } else {
                    "￥${c.value}"
                }
                if (c.key.startsWith(">")) {
                    val hour = NumberUtil.parseLong(c.key.removePrefix(">"))
                    val time = if (hour < 0) {
                        formatTimeBack(depTime, hour, req.locale)
                    } else {
                        formatTimePre(depTime, hour, req.locale)
                    }
                    dec = "$d-${time},${fee}"
                } else if (c.key.startsWith("<")) {
                    val hour = NumberUtil.parseLong(c.key.removePrefix("<"))
                    val time = if (hour < 0) {
                        formatTimeBack(depTime, hour, req.locale)
                    } else {
                        formatTimeBack(depTime, hour, req.locale)
                    }
                    dec = "$d-${time},${fee}"
                } else {
                    dec = "$d-${I18nUtil.getValue(I18n.ALL_TIME_RANGE, req.locale)},${fee}"
                }
                dec
            }
            refundDec.addAll(refund)
        }
        val bags = mutableListOf<String>()
        lData.journeys.forEach {
            it.segments.forEachIndexed { index, s ->
                s.baggageRules.forEach { b ->
                    // 免费手提行李额: 每件5KG，每人1件。单件行李质量不超过5KG，单件体积不超过20*40*55CM
                    // 无免费托运行李额
                    val d = when (PassengerTypeEnum.zfValueToEnum(b.passengerType)) {
                        PassengerTypeEnum.ADULT -> {
                            I18nUtil.getValue(I18n.ADULT, req.locale)
                        }

                        PassengerTypeEnum.CHILD -> {
                            I18nUtil.getValue(I18n.CHILD, req.locale)
                        }

                        PassengerTypeEnum.INFANT -> {
                            I18nUtil.getValue(I18n.INF, req.locale)
                        }

                        else -> {
                            I18nUtil.getValue(I18n.ADULT, req.locale)
                        }
                    }
                    val quotaB = if (b.carryOn.pieces == 0) {
                        I18nUtil.getValue(I18n.NOT_FREE_CARRY_LUGGAGE, req.locale)
                    } else if (b.carryOn.pieces == -1) {
                        I18nUtil.getValue(I18n.AIRLINE_POLICY, req.locale)
                    } else {
                        val weightLimit = "${b.carryOn.weight}${b.carryOn.unit}"
                        I18nUtil.getValue(
                            I18n.FREE_CARRY_LUGGAGE,
                            req.locale,
                            arrayOf(weightLimit, "${b.carryOn.pieces}", weightLimit)
                        )
                    }
                    val quotaC = if (b.checked.pieces == 0) {
                        I18nUtil.getValue(I18n.NOT_FREE_CHECKED_LUGGAGE, req.locale)
                    } else if (b.checked.pieces == -1) {
                        I18nUtil.getValue(I18n.AIRLINE_POLICY, req.locale)
                    } else {
                        val weightLimit = "${b.checked.weight}${b.checked.unit}"
                        I18nUtil.getValue(
                            I18n.FREE_CHECKED_LUGGAGE,
                            req.locale,
                            arrayOf(weightLimit, "${b.checked.pieces}", weightLimit)
                        )
                    }
                    bags.add(
                        "${I18nUtil.getValue(I18n.SEGMENT, req.locale)} " +
                                "${index + 1}:\n$d-${I18nUtil.getValue(I18n.CARRY_BAG, req.locale)}: " +
                                "$quotaB\n$d-${I18nUtil.getValue(I18n.CHECK_BAG, req.locale)}: $quotaC\n"
                    )
                }
            }
        }
        val accRule = AccRuleItem(
            prices = lData.priceDetail.priceList.map { p ->
                val yq = p.taxDetail.filter { t -> t.type == "fuelTax" }
                    .sumOf { t -> NumberUtil.toBigDecimal(t.amount) }
                val jq = p.taxDetail.filter { t -> t.type == "airportTax" }
                    .sumOf { t -> NumberUtil.toBigDecimal(t.amount) }
                PriceItem(
                    PassengerTypeEnum.zfValueToEnum(p.passengerType),
                    p.tax,
                    commonService.getMarkUpPrice(p.price).toString(),
                    TaxDetail(
                        NumberUtil.toStr(yq),
                        NumberUtil.toStr(jq),
                    )
                )
            },
            segments = req.segments,
            productInfo = ProductInfo(
                lData.priceDetail.productType,
                ProductTagEnum.ZF_PRODUCT,
                ProviderSourceEnum.ZF,
                lData.solutionId
            ),
            refundChangeInfo = ReverseInformation(
                if (changeDec.size <= 1) {
                    I18nUtil.getValue(I18n.CAN_NOT_REFUND_CHANGE, req.locale)
                } else {
                    changeDec.joinToString(";\n")
                },
                if (refundDec.size <= 1) {
                    I18nUtil.getValue(I18n.CAN_NOT_REFUND_CHANGE, req.locale)
                } else {
                    refundDec.joinToString(";\n")
                }
            ),
            serviceTime = ServiceTime(),
            baggageInfo = BaggageInfo(
                bags.joinToString(";\n"),
            ),
            issueTicketInfo = IssueTicketInfo(),
            limitInfo = LimitInfo(
                lData.priceDetail.limitInfo.groupInfo.min,
                lData.priceDetail.limitInfo.groupInfo.max,
                lData.priceDetail.limitInfo.agePairs.map {
                    AgeLimitItem(
                        it.minAge,
                        it.maxAge,
                    )
                },
                lData.priceDetail.limitInfo.nationalityAllows,
                lData.priceDetail.limitInfo.nationalityForbids,
            )
        )
        return accRule
    }

    fun booking(req: CreateOrderReq, solution: Solution, passengers: List<OrderPassenger>): OrderDetailRsp {
        val request = BookingReq(
            solution.orderKey,
            passengers,
            req.contactInfo.email,
            req.contactInfo.mobile,
            req.contactInfo.name,
            ""
        )
        val booingRsp = post(ZfApi.BOOKING.uri(), request.toJSONString())
        val lData = booingRsp.into<Resp<OrderDetailRsp>>()
        log.info("api: ${ZfApi.BOOKING}, response: {}", lData.print())
        log.info("api: ${ZfApi.BOOKING}, res: {}", booingRsp)
        return lData.data ?: throw BusinessException(lData.code, lData.message)
    }

    fun pay(req: PayReq): OrderDetailRsp {
        log.info("pay uri: ${ZfApi.PAY}, {}", req.toJSONString())
        val rsp = post(StrUtil.format(ZfApi.PAY.uri(), req.orderId), """{"amount": ${req.amount}}""")
        log.info("pay response: {}", rsp)
        val lData = rsp.into<Resp<OrderDetailRsp>>()
        if (lData.code == 202) {
            log.warn("价格不匹配: {}", rsp)
            throw BusinessException(lData.code, lData.message)
        }
        log.info("pay uri: ${ZfApi.PAY}, {}, response: {}", req.toJSONString(), lData.print())
        return lData.data ?: throw BusinessException(lData.code, lData.message)
    }

    fun cancel(orderId: String): OrderDetailRsp {
        val uri = ZfApi.CANCEL
        log.info("cancel uri: ${uri}, {}", orderId)
        val rsp = delete(StrUtil.format(uri.uri(), orderId))
        log.info("cancel response: {}", rsp)
        val lData = rsp.into<Resp<OrderDetailRsp>>()
        log.info("cancel uri: ${uri}, {}, response: {}", orderId, lData.print())
        return lData.data ?: throw BusinessException(lData.code, lData.message)
    }

    fun detail(orderId: String): OrderDetailRsp {
        val uri = ZfApi.DETAIL
        log.info("detail uri: ${uri}, {}", orderId)
        val rsp = get(StrUtil.format(uri.uri(), orderId))
        log.info("detail response: {}", rsp)
        val lData = rsp.into<Resp<OrderDetailRsp>>()
        log.info("detail uri: ${uri}, {}, response: {}", orderId, lData.print())
        return lData.data ?: throw BusinessException(lData.code, lData.message)
    }

    fun changeList(req: ChangeListReq): ChangeListRsp {
        val uri = ZfApi.CHANGE_LIST
        log.info("changeList uri: ${uri}, {}", req.toJSONString())
        val rsp = post(StrUtil.format(uri.uri(), req.orderId), req.toJSONString())
        log.info("changeList response: {}", rsp)
        val lData = rsp.into<Resp<ChangeListRsp>>()
        log.info("changeList uri: ${uri}, {}, response: {}", req.toJSONString(), lData.print())
        return lData.data ?: throw BusinessException(lData.code, lData.message)
    }

    fun change(req: ChangeReq): OrderDetailRsp {
        val uri = ZfApi.CHANGE
        log.info("change uri: ${uri}, {}", req.toJSONString())
        val rsp = post(StrUtil.format(uri.uri(), req.orderId), req.toJSONString())
        log.info("change response: {}", rsp)
        val lData = rsp.into<Resp<OrderDetailRsp>>()
        log.info("change uri: ${uri}, {}, response: {}", req.toJSONString(), lData.print())
        return lData.data ?: throw BusinessException(lData.code, lData.message)
    }

    fun refund(req: RefundReq): OrderDetailRsp {
        val uri = ZfApi.REFUND
        log.info("refund uri: ${uri}, {}", req.toJSONString())
        val rsp = post(StrUtil.format(uri.uri(), req.orderId), req.toJSONString())
        log.info("refund response: {}", rsp)
        val lData = rsp.into<Resp<OrderDetailRsp>>()
        log.info("refund uri: ${uri}, {}, response: {}", req.toJSONString(), lData.print())
        return lData.data ?: throw BusinessException(lData.code, lData.message)
    }

    fun confirmRefund(orderId: String): OrderDetailRsp {
        val uri = ZfApi.REFUND_CONFIRM
        log.info("confirmRefund uri: ${uri}, {}", orderId)
        val rsp = post(StrUtil.format(uri.uri(), orderId), "{}")
        log.info("confirmRefund response: {}", rsp)
        val lData = rsp.into<Resp<OrderDetailRsp>>()
        log.info("confirmRefund uri: ${uri}, {}, response: {}", orderId, lData.print())
        return lData.data ?: throw BusinessException(lData.code, lData.message)
    }

    fun get(uri: String): String {
        val url = UrlBuilder.of(config.baseUrl)
            .addPath(uri)
            .build()
        val response = HttpUtil.createGet(url)
            .headerMap(getHeaders(), true)
            .execute().body()
        log.debug("get uri: $uri, response: $response")
        val result = response.into<Resp<String>>()
        log.info("get uri: {}, result: {}", uri, result.print())
        if (result.code != 0) {
            log.info("get uri: {}, response: {}", uri, response)
            throw BusinessException(result.code, result.message)
        }
        return response
    }

    fun delete(uri: String, body: String = "{}"): String {
        val url = UrlBuilder.of(config.baseUrl)
            .addPath(uri)
            .build()
        val response = HttpUtil.createRequest(Method.DELETE, url)
            .headerMap(getHeaders(), true)
            .body(body).execute().body()
        log.debug("delete uri: $uri, response: $response")
        val result = response.into<Resp<String>>()
        log.info("delete uri: {}, result: {}", uri, result.print())
        if (result.code != 0) {
            log.info("delete uri: {}, response: {}", uri, response)
            throw BusinessException(result.code, result.message)
        }
        return response
    }

    fun post(uri: String, body: String, timeout: Int = -1): String {
        val url = UrlBuilder.of(config.baseUrl)
            .addPath(uri)
            .build()
        val headers = getHeaders()
        try {
            val response = HttpUtil.createPost(url)
                .headerMap(headers, true)
                .timeout(timeout)
                .body(body).execute().body()
            log.debug("uri: $uri, response: $response")
            val result = response.into<Resp<String>>()
            log.info("uri: {}, result: {}", uri, result.print())
            if (result.code != 0) {
                log.error("uri: {}, headers: ${headers}\n request: {}\n response: {}", uri, body, response)
                if (result.code == 101000 || result.code == 207027) {
                    throw BusinessException(
                        500,
                        "api.common.order.booking.zf-warn"
                    )
                }
                if (result.code == 207055) {
                    throw BusinessException(
                        500,
                        "api.common.q.id-card.birthday-warn"
                    )
                }
                if (result.code == 207027) {
                    throw BusinessException(
                        500,
                        "api.common.order.booking.zf-warn"
                    )
                }
                throw BusinessException(result.code, result.message)
            }
            return response
        } catch (e: HttpException) {
            log.error("http exception: ", e)
            throw BusinessException(200, "api.common.q.timeout")
        }
    }

    private fun getHeaders(): Map<String, String> {
        val timestamp = "${Instant.now().toEpochMilli() / 1000}"
        return mapOf(
            "code" to config.apiCode,
            "timestamp" to timestamp,
            "signature" to SecureUtil.sha1("${config.apiCode}${timestamp}${config.apiKey}"),
        )
    }

    private fun formatTimePre(depTime: LocalDateTime, hour: Long, locale: Locale): String {
        val t = LocalDateTimeUtil.offset(depTime, hour, ChronoUnit.HOURS);
        log.info("formatTimePre t is: {}", t)
        val ts = LocalDateTimeUtil.format(t, CommonValFunc.DATE_TIME_FORMAT)
        val ym = StrUtil.split(ts, " ")
        val ymd = StrUtil.split(ym[0], "-");
        log.info("ym: {}", ym.toJSONString())
        return I18nUtil.getValue(I18n.DATE_TIME_FORMAT_BEFORE, locale, arrayOf(ymd[0], ymd[1], ymd[2], ym[1]))
    }

    private fun formatTimeBack(depTime: LocalDateTime, hour: Long, locale: Locale): String {
        val t = LocalDateTimeUtil.offset(depTime, hour, ChronoUnit.HOURS);
        log.info("formatTimeBack t is: {}", t)
        val ts = LocalDateTimeUtil.format(t, CommonValFunc.DATE_TIME_FORMAT)
        val ym = StrUtil.split(ts, " ")
        val ymd = StrUtil.split(ym[0], "-");
        log.info("ym: {}", ym.toJSONString())
        return I18nUtil.getValue(I18n.DATE_TIME_FORMAT_AFTER, locale, arrayOf(ymd[0], ymd[1], ymd[2], ym[1]))
    }

    private fun getI18nMsg(key: String, locale: Locale): String {
        try {
            return commonService.getI18nMsg(key, locale)
        } catch (e: Exception) {
            log.error("get msg value fail: {}, locale: {}", key, locale, e)
        }
        return commonService.getI18nMsg("all.product-type.24", locale)
    }
}