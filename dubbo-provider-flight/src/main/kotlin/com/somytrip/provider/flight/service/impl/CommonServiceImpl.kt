package com.somytrip.provider.flight.service.impl

import cn.hutool.core.codec.Base64
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.IdcardUtil
import cn.hutool.core.util.StrUtil
import cn.hutool.core.util.ZipUtil
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.somytrip.api.service.FlightV2Service
import com.somytrip.api.service.user.TripCommonService
import com.somytrip.entity.dto.flight.AirportCodeNameDTO
import com.somytrip.entity.vo.flight.AirLineCodeVo
import com.somytrip.model.flight.common.AccRuleItem
import com.somytrip.model.flight.common.CommonValFunc
import com.somytrip.model.flight.common.CommonValFunc.BIRTHDAY_DATE_FORMAT
import com.somytrip.model.flight.common.ShopListRsp
import com.somytrip.model.flight.enums.CreditTypeEnum
import com.somytrip.model.flight.enums.GenderEnum
import com.somytrip.model.flight.enums.PassengerTypeEnum
import com.somytrip.model.flight.enums.ProviderSourceEnum
import com.somytrip.model.flight.tc.TCAccurateSearchItem
import com.somytrip.model.flight.vo.PassengerInfo
import com.somytrip.model.flight.vo.ShopListReq
import com.somytrip.provider.flight.config.CacheConfig
import com.somytrip.provider.flight.config.FlightConfig
import com.somytrip.provider.flight.config.PriceConfig
import com.somytrip.provider.flight.service.CommonService
import jakarta.annotation.PostConstruct
import jakarta.annotation.Resource
import org.apache.dubbo.config.annotation.DubboReference
import org.slf4j.LoggerFactory
import org.springframework.context.MessageSource
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.time.ZoneId
import java.util.Locale
import java.util.concurrent.TimeUnit

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-08-20 16:44
 */
@Service
class CommonServiceImpl : CommonService {
    private val log = LoggerFactory.getLogger(this::class.java)

    @DubboReference(check = false)
    private lateinit var flightV2Service: FlightV2Service

    @DubboReference(check = false)
    private lateinit var tripCommonService: TripCommonService

    @Resource
    private lateinit var stringRedisTemplate: StringRedisTemplate

    @Resource
    private lateinit var priceConfig: PriceConfig

    @Resource
    private lateinit var cacheConfig: CacheConfig

    @Resource
    private lateinit var flightConfig: FlightConfig

    @Resource
    private lateinit var messageSource: MessageSource

    private lateinit var cityIdAirportNameMap: Map<Int, AirportCodeNameDTO>
    private lateinit var airlineMap: Map<String, AirLineCodeVo>
    private lateinit var airportMap: Map<String, AirportCodeNameDTO>
    private var airportTimezoneMap: MutableMap<String, String> = mutableMapOf()

    @PostConstruct
    fun init() {
        updateCacheMap()
        updateAirportTimezoneMap()
    }

    @Scheduled(fixedRate = 3000000)
    fun updateAirportTimezoneMap() {
        log.info("timer updateAirportTimezoneMap")
        if (airportTimezoneMap.isEmpty()) {
//            airportTimezoneMap = flightV2Service.getAirportTimezone()
        } else {
            val temp = flightV2Service.getAirportTimezone()
            temp.forEach {
                if (!airportTimezoneMap.contains(it.key)) {
                    airportTimezoneMap.put(it.key, it.value)
                }
            }
        }
    }

    @Scheduled(fixedRate = 300000)
    fun updateCacheMap() {
        log.info("timer updateCacheMap")
        cityIdAirportNameMap = getAirportCityIdMap()
        airlineMap = getAirlineMap()
        airportMap = getAirportMap()
    }

    private fun getAirportMap(): Map<String, AirportCodeNameDTO> {
        val key = CommonValFunc.REDIS_KEY_FOR_MAP + "airport:entity"
        val value = stringRedisTemplate.opsForValue().get(key)
        if (value.isNullOrEmpty()) {
            val res: MutableMap<String, AirportCodeNameDTO> = mutableMapOf()
            flightV2Service.getAirportList().forEach {
                res[it.code] = it
            }
            stringRedisTemplate.opsForValue()
                .set(key, res.toJSONString(), cacheConfig.timeout().airportMapSeconds, TimeUnit.SECONDS)
            return res
        }
        return value.into<Map<String, AirportCodeNameDTO>>()
    }

    private fun getAirportCityIdMap(): Map<Int, AirportCodeNameDTO> {
        val key = CommonValFunc.REDIS_KEY_FOR_MAP + "airport:cityId"
        val value = stringRedisTemplate.opsForValue().get(key)
        if (value.isNullOrEmpty()) {
            val res: MutableMap<Int, AirportCodeNameDTO> = mutableMapOf()
            flightV2Service.getAirportList().forEach {
                if (!res.contains(it.cityId)) {
                    res[it.cityId] = it
                }
            }
            stringRedisTemplate.opsForValue()
                .set(key, res.toJSONString(), cacheConfig.timeout().airportCityMapSeconds, TimeUnit.SECONDS)
            return res
        }
        return value.into<Map<Int, AirportCodeNameDTO>>()
    }

    private fun getAirlineMap(): Map<String, AirLineCodeVo> {
        val key = CommonValFunc.REDIS_KEY_FOR_MAP + "airline"
        val value = stringRedisTemplate.opsForValue().get(key)
        if (value.isNullOrEmpty()) {
            val res: MutableMap<String, AirLineCodeVo> = mutableMapOf()
            flightV2Service.getAirlineList().forEach {
                res[it.code] = it
            }
            stringRedisTemplate.opsForValue()
                .set(key, res.toJSONString(), cacheConfig.timeout().airlineSeconds, TimeUnit.SECONDS)
            return res
        }
        return value.into<Map<String, AirLineCodeVo>>()
    }

    override fun getAirportName(airCode: String): String {
        val airportName = airportMap[airCode]
        if (airportName == null) {
            log.warn("not found airport: {}", airCode)
            return ""
        } else {
            return airportName.nameCn
        }
    }

    override fun getAirportCodeNameDTOByCityId(cityId: Int): AirportCodeNameDTO {
        return cityIdAirportNameMap[cityId] ?: AirportCodeNameDTO()
    }

    override fun getAirline(airlineCode: String): AirLineCodeVo {
        return airlineMap[airlineCode] ?: AirLineCodeVo()
    }

    override fun isInternational(airportList: List<String>): Boolean {
        for (airport in airportList) {
            if (airportMap[airport]?.international == true) {
                return true
            }
        }
        return false
    }

    override fun getMarkUpPrice(price: String): Int {
        return priceConfig.getChangePrice(price)
    }

    override fun getPassengerInfos(uid: String, ids: List<Int>): List<PassengerInfo> {
        log.info("get passenger infos: {}, {}", uid, ids.toJSONString())
        val infos = tripCommonService.queryTripInfoDtoListByUserIdAndIds(uid, ids);
        log.info("get passenger infos: {}, {}, size: {}", uid, ids.toJSONString(), infos.size)
        return infos.map {
            var (lastName, firstName) = listOf(it.word, it.surname)
            it.name = StrUtil.removeAll(it.name, " ")
            if (lastName.isBlank() && firstName.isBlank()) {
                firstName = it.name.substring(0, it.name.length / 2)
                lastName = it.name.substring(it.name.length / 2, it.name.length);
                if (it.name.contains(CommonValFunc.EN_NAME_SPLIT)) {
                    val names = it.name.split(CommonValFunc.EN_NAME_SPLIT)
                    lastName = names[0]
                    firstName = names[1]
                }
            } else if (lastName.isBlank()) {
                it.name.substring(it.name.length / 2, it.name.length);
            } else if (firstName.isBlank()) {
                firstName = it.name.substring(0, it.name.length / 2)
            }
            val res = PassengerInfo(
                lastName,
                firstName,
                if (it.date.isNullOrBlank()) {
                    if (it.identity.name == CreditTypeEnum.ID_CARD.name) {
                        IdcardUtil.getIdcardInfo(it.identityNumber).birthDate.toString(BIRTHDAY_DATE_FORMAT)
                    } else {
                        DateUtil.date().toString(BIRTHDAY_DATE_FORMAT)
                    }
                } else {
                    it.date
                },
                it.iphone,
                if (it.period.isNullOrBlank()) {
                    DateUtil.offsetMonth(DateUtil.date(), 12).toString(BIRTHDAY_DATE_FORMAT)
                } else {
                    it.period
                },
                it.identityNumber,
                if (it.issue.isNullOrBlank()) {
                    "CN"
                } else {
                    it.issue
                },
                if (it.nationality.isNullOrBlank()) {
                    "CN"
                } else {
                    it.nationality
                },
                if (it.nationality.isNullOrBlank()) {
                    "CN"
                } else {
                    it.nationality
                },
                CreditTypeEnum.valueOf(it.identity.name),
                PassengerTypeEnum.birthDayToEnum(it.date),
                GenderEnum.valueOf(it.gender.name),
                email = flightConfig.system().defaultEmail
            )
            if (res.creditType == CreditTypeEnum.ID_CARD) {
                if (IdcardUtil.getGenderByIdCard(res.creditNo) == 0) {
                    res.copy(gender = GenderEnum.FEMALE)
                } else {
                    res.copy(gender = GenderEnum.MAN)
                }
            } else {
                res
            }
        }.sortedBy { it.type.tcIntValue() }
    }

    override fun getMovePoint2(price: String): Int {
        return priceConfig.movePointRight(price, 2)
    }

    override fun cacheShoppingList(
        req: ShopListReq,
        result: Map<String, List<ShopListRsp>>,
        source: ProviderSourceEnum
    ) {
        cacheShopGzip(req, result, source)
    }

    override fun delCacheShoppingList(
        req: ShopListReq,
        source: ProviderSourceEnum
    ) {
        try {
            val key = getShopListKey(req, source)
            val result = stringRedisTemplate.delete(key)
            log.info("del cache shop: {}, result: {}", key, result)
        } catch (e: Exception) {
            log.error("del cache shopping: {}, {}", source, req.toJSONString(), e)
        }
    }

    override fun getCacheShoppingList(req: ShopListReq, source: ProviderSourceEnum): Map<String, List<ShopListRsp>> {
        return getCacheShopUnZip(req, source)
    }

    private fun cacheShopGzip(
        req: ShopListReq,
        result: Map<String, List<ShopListRsp>>,
        source: ProviderSourceEnum
    ) {
        val k = getShopListKey(req, source)
        log.info("cacheShop key: {}, value size: {}", k, result.size)
        val v = Base64.encode(ZipUtil.gzip(result.toJSONString(), "UTF-8"))
        stringRedisTemplate.opsForValue().set(k, v, cacheConfig.timeout().shopSeconds, TimeUnit.SECONDS)
    }

    private fun getShopListKey(req: ShopListReq, source: ProviderSourceEnum): String {
        val key = source.name + req.adultNum() + req.childNum() + req.infantNum() +
                req.segments.joinToString("", "", "") {
                    "${it.departCode}${it.arriveCode}${it.departTime.replace("-", "")}"
                } + req.screenCondition.cabin.joinToString("", "", "") { it.name }
        return "${CommonValFunc.REDIS_KEY_FOR_SHOP_LIST_V2}${key}"
    }

    override fun getCacheShopUnZip(
        req: ShopListReq,
        source: ProviderSourceEnum
    ): Map<String, List<ShopListRsp>> {
        val k = getShopListKey(req, source)
        val result = stringRedisTemplate.opsForValue().get(k)
        if (result.isNullOrBlank()) {
            log.info("getCacheShop v2 key: {}, value empty: {}", k, result)
            return emptyMap()
        }
        val v = ZipUtil.unGzip(Base64.decode(result));
        val res = v.into<Map<String, List<ShopListRsp>>>()
        log.info("getCacheShop v2 key: {}, value size: {}", k, res.size)
        return res
    }

    override fun cacheAccRuleItem(key: String, value: AccRuleItem) {
        val k = CommonValFunc.REDIS_KEY_FOR_ACC_RULE_ITEM + key
        log.info("cache set accRuleItem: {}", k)
        stringRedisTemplate.opsForValue()
            .set(k, value.toJSONString(), cacheConfig.timeout().accSeconds, TimeUnit.SECONDS)
        log.info("cache set accRuleItem success: {}", k)
    }

    override fun getCacheAccRuleItem(key: String): AccRuleItem {
        val k = CommonValFunc.REDIS_KEY_FOR_ACC_RULE_ITEM + key
        val result = stringRedisTemplate.opsForValue().get(k)
        if (result.isNullOrBlank()) {
            log.info("get cache value is empty: {}", k)
            return AccRuleItem()
        }
        log.info("get cache value is success: {}", k)
        return result.into<AccRuleItem>()
    }

    override fun cacheTCAccurateSearchItem(item: TCAccurateSearchItem) {
        val k = CommonValFunc.REDIS_KEY_FOR_OLD_ACC_RULE_ITEM + item.data
        log.info("old cache set accRuleItem: {}", k)
        stringRedisTemplate.opsForValue()
            .set(k, item.toJSONString(), cacheConfig.timeout().shopSeconds, TimeUnit.SECONDS)
        log.info("old cache set accRuleItem success: {}", k)
    }

    override fun getCacheTCAccurateSearchItem(key: String): TCAccurateSearchItem? {
        val k = CommonValFunc.REDIS_KEY_FOR_OLD_ACC_RULE_ITEM + key
        val result = stringRedisTemplate.opsForValue().get(k)
        if (result.isNullOrBlank()) {
            log.info("old get cache value is empty: {}", k)
            return null
        }
        log.info("old get cache value is success: {}", k)
        return result.into<TCAccurateSearchItem>()
    }

    override fun getAirportTimezone(airport: String): ZoneId {
        if (airportTimezoneMap.contains(airport)) {
            return ZoneId.of(airportTimezoneMap.getValue(airport))
        }
        return ZoneId.of("UTC+8")
    }

    override fun getI18nMsg(key: String, locale: Locale): String {
        return messageSource.getMessage(key, arrayOf(), "", locale)
    }
}