package com.somytrip.provider.flight.utils

import cn.hutool.core.codec.Base64
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.NumberUtil
import cn.hutool.core.util.StrUtil
import cn.hutool.crypto.digest.DigestAlgorithm
import cn.hutool.crypto.digest.Digester
import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONByteArray
import com.alibaba.fastjson2.toJSONString
import com.somytrip.model.flight.common.*
import com.somytrip.model.flight.common.SegmentItem
import com.somytrip.model.flight.config.FR24Config
import com.somytrip.model.flight.enums.*
import com.somytrip.model.flight.fr24.*
import com.somytrip.model.flight.vo.ShopListReq
import com.somytrip.model.flight.vo.TripPlanItem
import com.somytrip.model.flight.vo.TripPlanRsp
import com.somytrip.provider.flight.service.CommonService
import com.somytrip.provider.flight.service.impl.CommonServiceImpl
import okhttp3.Headers
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.apache.dubbo.rpc.RpcContext
import org.slf4j.LoggerFactory
import java.util.Locale
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * @Description: FlightRoute24 接口
 * @author: pigeon
 * @created: 2024-08-06 17:44
 */
class FR24Util(builder: Builder) {
    private val log = LoggerFactory.getLogger(this::class.java)
    private val okHttpClient = builder.okHttpClient
    private val fR24Config: FR24Config = builder.fR24Config
    private val authenticationSignDigester = builder.authenticationSignDigester
    private val commonService: CommonService = builder.commonService
    private val aesEncryptCipher by lazy {
        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        cipher.init(
            Cipher.ENCRYPT_MODE,
            SecretKeySpec(fR24Config.appSecret.encodeToByteArray(), "AES"),
            IvParameterSpec(ByteArray(16))
        )
        cipher
    }

    private val aesDecryptCipher by lazy {
        val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
        cipher.init(
            Cipher.DECRYPT_MODE,
            SecretKeySpec(fR24Config.appSecret.encodeToByteArray(), "AES"),
            IvParameterSpec(ByteArray(16))
        )
        cipher
    }

    class Builder {
        internal var okHttpClient: OkHttpClient = OkHttpClient()
        internal var fR24Config: FR24Config = FR24Config()
        internal var authenticationSignDigester = Digester(DigestAlgorithm.SHA512)
        internal var commonService: CommonService = CommonServiceImpl()
        fun okHttpClient(okHttpClient: OkHttpClient) = apply {
            this.okHttpClient = okHttpClient
        }

        fun fR24Config(config: FR24Config) = apply {
            this.fR24Config = config
        }

        fun authenticationSignDigester(authenticationSignDigester: Digester) = apply {
            this.authenticationSignDigester = authenticationSignDigester
        }

        fun commonService(commonService: CommonService) = apply { this.commonService = commonService }

        private fun check() {
            if (fR24Config.appKey.isBlank() || fR24Config.appSecret.isBlank()) {
                throw RuntimeException("fR24Config init error")
            }
        }

        fun build(): FR24Util {
            check()
            return FR24Util(this)
        }
    }

    fun getAuthentication(): Authentication {
        val timestamp = "${System.currentTimeMillis() / 1000}";
        return Authentication(
            authenticationSignDigester.digestHex(
                "${fR24Config.appKey}${fR24Config.appSecret}$timestamp"
            ),
            timestamp
        )
    }

    fun shoppingList(request: ShoppingRequest): FR24Response<ShoppingData> {
        val jsonStr = post(JSON.toJSONString(request), FR24UriEnum.SHOPPING)
        if (jsonStr.isNotBlank()) {
            log.debug("shop data: {}", jsonStr)
            val result = jsonStr.into<FR24Response<ShoppingData>>()
            log.info("shop common rsp: {}", result.notDataStr())
            return result
        }
        log.error("shoppingList get data error: {}", jsonStr)
        throw RuntimeException("shop common rsp fail")
    }

    fun shoppingData(data: ShoppingData, sourceType: Int = 0): Map<String, MutableList<ShopListRsp>> {
        log.info("deal shop data list start")
        val lang = RpcContext.getServerAttachment().getAttachment("lang");
        val l = StrUtil.split(lang, StrUtil.C_UNDERLINE)
        val locale = Locale(l[0], l[1])
        val result = mutableMapOf<String, MutableList<ShopListRsp>>()
        val segmentMap = data.segments.associateBy { it.segmentId }
        for (i in 0 until data.legs.size) {
            val offer = data.offers[i]
            val segments = mutableListOf<SegmentItem>()
            var segDurationTotal = 0
            data.legs[i].segmentIds.forEachIndexed { segI, it ->
                var stopTimeTotal: Int = 0;
                var durationTotal: Int = 0;
                segments.addAll(it.split(CommonValFunc.FR24_SEGMENT_SPLIT).mapIndexed { sI, segId ->
                    val seg = segmentMap[segId]
                    if (seg != null) {
                        durationTotal += seg.duration
                        SegmentItem(
                            seg.operatingCarrier,
                            seg.operatingFlightNo,
                            seg.carrier,
                            0,
                            seg.codeShare,
                            seg.carrier + seg.flightNo,
                            "",
                            "",
                            seg.arrAirport,
                            seg.arrTerminal,
                            formatDateTime(seg.arrTime),
                            seg.depAirport,
                            seg.depTerminal,
                            formatDateTime(seg.depTime),
                            "",
                            "",
                            sI + 1 + segI,
                            seg.stopAirport.filter { it.isNotBlank() }.mapIndexed { stopI, stop ->
                                if (seg.stopDuration.isEmpty()) {
                                    SegmentStopItem()
                                } else {
                                    stopTimeTotal += seg.stopDuration[stopI]
                                    SegmentStopItem(
                                        stop,
                                        CommonValFunc.formatIntToDuration(seg.stopDuration[stopI])
                                    )
                                }
                            },
                            CommonValFunc.formatIntToDuration(stopTimeTotal),
                            CommonValFunc.formatIntToDuration(seg.duration),
                            CommonValFunc.formatIntToDuration(stopTimeTotal + durationTotal),
                            commonService.getAirportName(seg.arrAirport),
                            commonService.getAirportName(seg.depAirport),
                            commonService.getAirline(seg.carrier),
                            seg.stopAirport.filter { it.isNotBlank() }.map { commonService.getAirportName(it) },
                            ""
                        )
                    } else {
                        SegmentItem()
                    }
                })
                segDurationTotal += (stopTimeTotal + durationTotal)
            }
            val item = ShopListRsp(
                prices = offer.pricePerPax.map {
                    PriceItem(
                        PassengerTypeEnum.fr24ValueToEnum(it.paxType),
                        it.totalTax,
                        commonService.getMarkUpPrice(it.baseFare).toString(),
                        TaxDetail(
                            it.taxBreakdown.find { tax -> tax.taxType == CommonValFunc.FR24_TAX_YQ }?.taxAmount ?: "",
                            it.taxBreakdown.find { tax -> tax.taxType == CommonValFunc.FR24_TAX_TAX }?.taxAmount ?: ""
                        )
                    )
                },
                cabins = offer.cabin.mapIndexed { cI, c ->
                    CabinItem(
                        CabinClassEnum.fr24ToEnums(c),
                        offer.rbd[cI],
                        offer.availability[cI],
                        cI + 1
                    )
                },
                productInfo = ProductInfo(
                    offer.legId,
                    ProductTagEnum.FR24_PRODUCT,
                    ProductTagEnum.FR24_PRODUCT.display,
                    ProviderSourceEnum.FR24,
                    FR24OrderInfo(offer.offerId).toJSONString(),
                    getI18nMsg(locale)
                ),
                segments = segments,
                sourceType = sourceType,
                durationTotal = CommonValFunc.formatIntToDuration(segDurationTotal),
            )
            val key = segments.joinToString { (it.flightNo) }
            if (result.containsKey(key)) {
                result[key]?.add(item)
            } else {
                result[key] = mutableListOf(item)
            }
        }
        log.info("deal shop data list end")
        return result;
    }

    fun shoppingTripPlan(data: ShoppingData, req: ShopListReq, sourceType: Int = 0): TripPlanRsp {
        val segMapList = mutableMapOf<Int, MutableList<ShopListRsp>>()
        for (i in 0 until req.segments.size) {
            segMapList[i] = mutableListOf()
        }
        var index = 0
        for (i in 0 until data.legs.size) {
            val curIndex = index
            data.legs[i].segmentIds.forEachIndexed { segI, it ->
                it.split(CommonValFunc.FR24_SEGMENT_SPLIT).forEachIndexed { sI, _ ->
                    var stopTimeTotal = 0
                    segMapList[index - curIndex]?.add(
                        ShopListRsp(
                            prices = data.offers[i].pricePerPax.map {
                                PriceItem(
                                    PassengerTypeEnum.fr24ValueToEnum(it.paxType),
                                    it.totalTax,
                                    commonService.getMarkUpPrice(it.baseFare).toString(),
                                    TaxDetail(
                                        it.taxBreakdown.find { tax -> tax.taxType == CommonValFunc.FR24_TAX_YQ }?.taxAmount.let { "" },
                                        it.taxBreakdown.find { tax -> tax.taxType == CommonValFunc.FR24_TAX_TAX }?.taxAmount.let { "" }
                                    )
                                )
                            },
                            cabins = data.offers[i].cabin.mapIndexed { cI, c ->
                                CabinItem(
                                    CabinClassEnum.fr24ToEnums(c),
                                    data.offers[i].rbd[cI],
                                    data.offers[i].availability[cI],
                                    cI + 1
                                )
                            },
                            productInfo = ProductInfo(
                                data.offers[i].legId,
                                ProductTagEnum.FR24_PRODUCT,
                                ProductTagEnum.FR24_PRODUCT.display,
                                ProviderSourceEnum.FR24,
                                FR24OrderInfo(data.offers[i].offerId).toJSONString()
                            ),
                            segments = listOf(
                                SegmentItem(
                                    data.segments[index].operatingCarrier,
                                    data.segments[index].operatingFlightNo,
                                    data.segments[index].carrier,
                                    0,
                                    data.segments[index].codeShare,
                                    data.segments[index].carrier + data.segments[index].flightNo,
                                    "",
                                    "",
                                    data.segments[index].arrAirport,
                                    data.segments[index].arrTerminal,
                                    formatDateTime(data.segments[index].arrTime),
                                    data.segments[index].depAirport,
                                    data.segments[index].depTerminal,
                                    formatDateTime(data.segments[index].depTime),
                                    "",
                                    "",
                                    sI + 1 + segI,
                                    data.segments[index].stopAirport.filter { it.isNotBlank() }
                                        .mapIndexed { stopI, stop ->
                                            if (data.segments[index].stopDuration.isEmpty()) {
                                                SegmentStopItem()
                                            } else {
                                                stopTimeTotal += data.segments[index].stopDuration[stopI]
                                                SegmentStopItem(
                                                    stop,
                                                    CommonValFunc.formatIntToDuration(data.segments[index].stopDuration[stopI])
                                                )
                                            }
                                        },
                                    CommonValFunc.formatIntToDuration(stopTimeTotal),
                                    CommonValFunc.formatIntToDuration(data.segments[index].duration),
                                    CommonValFunc.formatIntToDuration(stopTimeTotal + data.segments[index].duration),
                                    commonService.getAirportName(data.segments[index].arrAirport),
                                    commonService.getAirportName(data.segments[index].depAirport),
                                    commonService.getAirline(data.segments[index].carrier),
                                    data.segments[index].stopAirport.filter { it.isNotBlank() }
                                        .map { commonService.getAirportName(it) },
                                    ""
                                )
                            ),
                            sourceType = sourceType,
                            durationTotal = CommonValFunc.formatIntToDuration(stopTimeTotal + data.segments[index].duration),
                        )
                    )
                    index++
                }
            }
        }
        val result = mutableListOf<TripPlanItem>()
        for (i in 0 until req.segments.size) {
            if (segMapList[i].isNullOrEmpty()) {
                log.info("segMapList is empty")
                result.add(TripPlanItem())
                continue
            }
            val fLow = segMapList[i]?.sortedBy { it.prices.first().price }?.filter {
                it.segments.first().departDateTime.substring(CommonValFunc.DATE_HOUR_SUBSTRING_RANGE)
                    .toInt() in DateHourRangeEnum.LOW.range
            }
            if (fLow.isNullOrEmpty()) {
                log.info("fLow is empty")
                result.add(TripPlanItem())
                continue
            }
            val fMid = fLow.filter {
                it.segments.first().departDateTime.substring(CommonValFunc.DATE_HOUR_SUBSTRING_RANGE)
                    .toInt() in DateHourRangeEnum.MID.range
            }
            if (fMid.isEmpty()) {
                log.info("fMid is empty")
                result.add(TripPlanItem(fLow.first(), fLow.first(), fLow.first()))
                continue
            }
            val fHigh = fMid.filter {
                it.segments.first().departDateTime.substring(CommonValFunc.DATE_HOUR_SUBSTRING_RANGE)
                    .toInt() in DateHourRangeEnum.HIGH.range
                        &&
                        it.segments.first().stops.isEmpty()
            }
            if (fHigh.isEmpty()) {
                log.info("fHigh is empty")
                result.add(TripPlanItem(fLow.first(), fMid.first(), fMid.first()))
                continue
            }
            log.info("low mid high is full")
            result.add(TripPlanItem(fLow.first(), fMid.first(), fHigh.first()))
        }
        return TripPlanRsp(result, true)
    }

    fun pricing(request: PricingRequest): FR24Response<PricingData> {
        val jsonStr = post(JSON.toJSONString(request), FR24UriEnum.PRICING)
        if (jsonStr.isNotBlank()) {
            log.debug("pricing data: {}", jsonStr)
            val result = jsonStr.into<FR24Response<PricingData>>()
            log.info("pricing common rsp: {}", result.notDataStr())
            return result
        }
        log.error("get data error: {}", jsonStr)
        throw RuntimeException("pricing common rsp fail")
    }

    fun pricingData(data: PricingData, sourceType: Int = 0): AccurateRsp {
        val lang = RpcContext.getServerAttachment().getAttachment("lang");
        val l = StrUtil.split(lang, StrUtil.C_UNDERLINE)
        val locale = Locale(l[0], l[1])
        val rules = mutableListOf<AccRuleItem>()
        val segmentMap = data.segments.associateBy { it.segmentId }
        for (i in 0 until data.legs.size) {
            val segments = mutableListOf<SegmentItem>()
            val segmentIdMap = mutableMapOf<String, Int>()
            data.legs[i].segmentIds.forEachIndexed { segI, it ->
                var stopTimeTotal: Int = 0;
                var durationTotal: Int = 0;
                it.split(CommonValFunc.FR24_SEGMENT_SPLIT).forEachIndexed { sI, segId ->
                    val seg = segmentMap[segId]
                    if (seg != null) {
                        durationTotal += seg.duration
                        segmentIdMap[seg.segmentId] = sI + 1 + segI
                        segments.add(
                            SegmentItem(
                                seg.operatingCarrier,
                                seg.operatingFlightNo,
                                seg.carrier,
                                0,
                                seg.codeShare,
                                seg.carrier + seg.flightNo,
                                "",
                                "",
                                seg.arrAirport,
                                seg.arrTerminal,
                                formatDateTime(seg.arrTime),
                                seg.depAirport,
                                seg.depTerminal,
                                formatDateTime(seg.depTime),
                                "",
                                "",
                                segmentIdMap[seg.segmentId] ?: 0,
                                seg.stopAirport.filter { it.isNotBlank() }.mapIndexed { stopI, stop ->
                                    if (seg.stopDuration.isEmpty()) {
                                        SegmentStopItem()
                                    } else {
                                        stopTimeTotal += seg.stopDuration[stopI]
                                        SegmentStopItem(
                                            stop,
                                            CommonValFunc.formatIntToDuration(seg.stopDuration[stopI])
                                        )
                                    }
                                },
                                CommonValFunc.formatIntToDuration(stopTimeTotal),
                                CommonValFunc.formatIntToDuration(seg.duration),
                                CommonValFunc.formatIntToDuration(stopTimeTotal + durationTotal),
                                commonService.getAirportName(seg.arrAirport),
                                commonService.getAirportName(seg.depAirport),
                                commonService.getAirline(seg.carrier),
                                seg.stopAirport.filter { it.isNotBlank() }.map { commonService.getAirportName(it) },
                                ""
                            )
                        );
                    }
                }
            }

            rules.add(
                AccRuleItem(
                    productInfo = ProductInfo(
                        data.offer[i].legId,
                        ProductTagEnum.FR24_PRODUCT,
                        ProductTagEnum.FR24_PRODUCT.display,
                        ProviderSourceEnum.FR24,
                        FR24OrderInfo(data.offer[i].offerId).toJSONString(),
                        getI18nMsg(locale)
                    ),
                    prices = data.offer[i].pricePerPax.map {
                        PriceItem(
                            PassengerTypeEnum.fr24ValueToEnum(it.paxType),
                            it.totalTax,
                            commonService.getMarkUpPrice(it.baseFare).toString(),
                            TaxDetail(
                                it.taxBreakdown.find { tax -> tax.taxType == CommonValFunc.FR24_TAX_YQ }?.taxAmount
                                    ?: "",
                                it.taxBreakdown.find { tax -> tax.taxType == CommonValFunc.FR24_TAX_TAX }?.taxAmount
                                    ?: ""
                            )
                        )
                    },
                    serviceTime = ServiceTime(),
                    segments = segments,
                    refundChangeInfo = initReverseInformation(data.offer[i].rules),
                    issueTicketInfo = IssueTicketInfo(
                        data.offer[i].productTag.ticketPromise == 0,
                        false,
                        "",
                        GdsEnum.fr24ToEnum(data.offer[i].productSource),
                        BillMethodEnum.OTHER,
                        BillMaterialsEnum.NOT_PROVIDER
                    ),
                    baggageInfo = initBagInfo(data.offer[i].extraInfo, segmentIdMap),
                    limitInfo = initLimitInfo(data.offer[i].eligibilityDetail)
                )
            );
        }
        return AccurateRsp(sourceType, rules)
    }

    fun booking(requestBody: BookingRequestBody): FR24Response<BookingData> {
        val request = BookingRequest(requestBody).copy(passengers = aesPassengerList(requestBody.passengers))
        val jsonStr = post(JSON.toJSONString(request), FR24UriEnum.BOOKING)
        if (jsonStr.isNotBlank()) {
            log.debug("booking data: {}", jsonStr)
            val result = jsonStr.into<FR24Response<BookingData>>()
            log.info("booking common rsp: {}", result.notDataStr())
            return result
        }
        log.error("booking get data error: {}", jsonStr)
        throw RuntimeException("booking common rsp fail")
    }

    fun ticketing(request: TicketingRequest): FR24Response<TicketingData> {
        val jsonStr = post(JSON.toJSONString(request), FR24UriEnum.TICKETING)
        if (jsonStr.isNotBlank()) {
            log.debug("ticketing data: {}", jsonStr)
            val result = jsonStr.into<FR24Response<TicketingData>>()
            log.info("ticketing common rsp: {}", result.notDataStr())
            return result
        }
        log.error("get data error: {}", jsonStr)
        throw RuntimeException("ticketing common rsp fail")
    }

    fun orderDetail(request: OrderDetailRequest): FR24Response<OrderDetailData> {
        val jsonStr = post(JSON.toJSONString(request), FR24UriEnum.ORDER_DETAIL)
        if (jsonStr.isNotBlank()) {
            log.debug("orderDetail data: {}", jsonStr)
            val result = jsonStr.into<FR24Response<OrderDetailData>>()
            log.info("orderDetail common rsp: {}", result.notDataStr())
            return result
        }
        log.error("orderDetail get data error: {}", jsonStr)
        throw RuntimeException("orderDetail common rsp fail")
    }

    fun changeReShop(request: ChangeReShopRequest): FR24Response<ChangeReShopData> {
        val jsonStr = post(JSON.toJSONString(request), FR24UriEnum.CHANGE_RE_SHOP)
        if (jsonStr.isNotBlank()) {
            log.debug("changeReShop data: {}", jsonStr)
            val result = jsonStr.into<FR24Response<ChangeReShopData>>()
            log.info("changeReShop common rsp: {}", result.notDataStr())
            return result
        }
        log.error("changeReShop get data error: {}", jsonStr)
        throw RuntimeException("changeReShop common rsp fail")
    }

    fun changeReShopData(data: ChangeReShopData, sourceType: Int = 0) {
        val segmentMap = data.segments.associateBy { it.segmentId }
        data.offers.forEach { o ->
            o.legs.forEach { l ->
                var stopTimeTotal: Int = 0;
                var durationTotal: Int = 0;
                l.segmentIds.mapIndexed { index, id ->
                    val seg = segmentMap.getValue(id)

                    SegmentItem(
                        seg.operatingCarrier,
                        seg.operatingFlightNo,
                        seg.carrier,
                        0,
                        seg.codeShare,
                        seg.carrier + seg.flightNo,
                        "",
                        "",
                        seg.arrAirport,
                        seg.arrTerminal,
                        formatDateTime(seg.arrTime),
                        seg.depAirport,
                        seg.depTerminal,
                        formatDateTime(seg.depTime),
                        "",
                        "",
                        index + 1,
                        seg.stopAirport.filter { it.isNotBlank() }.mapIndexed { stopI, stop ->
                            if (seg.stopDuration.isEmpty()) {
                                SegmentStopItem()
                            } else {
                                stopTimeTotal += seg.stopDuration[stopI]
                                SegmentStopItem(
                                    stop,
                                    CommonValFunc.formatIntToDuration(seg.stopDuration[stopI])
                                )
                            }
                        },
                        CommonValFunc.formatIntToDuration(stopTimeTotal),
                        CommonValFunc.formatIntToDuration(seg.duration),
                        CommonValFunc.formatIntToDuration(stopTimeTotal + durationTotal),
                        commonService.getAirportName(seg.arrAirport),
                        commonService.getAirportName(seg.depAirport),
                        commonService.getAirline(seg.carrier),
                        seg.stopAirport.filter { it.isNotBlank() }.map { commonService.getAirportName(it) },
                        ""
                    )
                }
//                ShopListRsp(
//                    prices = offer.pricePerPax.map {
//                        PriceItem(
//                            PassengerTypeEnum.fr24ValueToEnum(it.paxType),
//                            it.totalTax,
//                            commonService.getMarkUpPrice(it.baseFare).toString(),
//                            TaxDetail(
//                                it.taxBreakdown.find { tax -> tax.taxType == CommonValFunc.FR24_TAX_YQ }?.taxAmount ?: "",
//                                it.taxBreakdown.find { tax -> tax.taxType == CommonValFunc.FR24_TAX_TAX }?.taxAmount ?: ""
//                            )
//                        )
//                    },
//                    cabins = offer.cabin.mapIndexed { cI, c ->
//                        CabinItem(
//                            CabinClassEnum.fr24ToEnums(c),
//                            offer.rbd[cI],
//                            offer.availability[cI],
//                            cI + 1
//                        )
//                    },
//                    productInfo = ProductInfo(
//                        offer.legId,
//                        ProductTagEnum.FR24_PRODUCT,
//                        ProviderSourceEnum.FR24,
//                        FR24OrderInfo(offer.offerId).toJSONString()
//                    ),
//                    segments = segments,
//                    sourceType = sourceType,
//                    durationTotal = CommonValFunc.formatIntToDuration(segDurationTotal),
//                )
            }
        }
    }

    fun changeReissue(request: ReissueRequest): FR24Response<ReissueData> {
        val jsonStr = post(JSON.toJSONString(request), FR24UriEnum.RE_ISSUE_REQUEST)
        if (jsonStr.isNotBlank()) {
            log.debug("changeReissue data: {}", jsonStr)
            val result = jsonStr.into<FR24Response<ReissueData>>()
            log.info("changeReissue common rsp: {}", result.notDataStr())
            return result
        }
        log.error("changeReissue get data error: {}", jsonStr)
        throw RuntimeException("changeReissue common rsp fail")
    }

    fun refundApply(request: RefundRequest): FR24Response<RefundData> {
        val jsonStr = post(JSON.toJSONString(request), FR24UriEnum.REFUND_REQUEST)
        if (jsonStr.isNotBlank()) {
            log.debug("refundApply data: {}", jsonStr)
            val result = jsonStr.into<FR24Response<RefundData>>()
            log.info("refundApply common rsp: {}", result.notDataStr())
            return result
        }
        log.error("refundApply get data error: {}", jsonStr)
        throw RuntimeException("refundApply common rsp fail")
    }

    fun refundConfirm(request: RefundConfirmRequest): FR24Response<RefundConfirmData> {
        val jsonStr = post(JSON.toJSONString(request), FR24UriEnum.REFUND_CONFIRM)
        if (jsonStr.isNotBlank()) {
            log.debug("refundConfirm data: {}", jsonStr)
            val result = jsonStr.into<FR24Response<RefundConfirmData>>()
            log.info("refundConfirm common rsp: {}", result.notDataStr())
            return result
        }
        log.error("refundConfirm get data error: {}", jsonStr)
        throw RuntimeException("refundConfirm common rsp fail")
    }

    fun orderChangeNotify(notify: OrderChangeInfoNotify): FR24NotifyResponse {
        log.info("orderChangeNotify body: {}", notify.toJSONString())
        val rsp = FR24NotifyResponse(
            notify.traceId,
            CommonValFunc.FR24_CODE_SUCCESS,
            CommonValFunc.FR24_MESSAGE_SUCCESS,
            0,
            getAuthentication()
        )
        log.info("orderChangeNotify response: {}", rsp.toJSONString())
        return rsp
    }

    private fun aesPassengerList(passengers: List<PassengerItem>): String {
        return Base64.encode(aesEncryptCipher.doFinal(passengers.toJSONByteArray()))
    }

    fun aesPassengerList(content: String): List<PassengerItem> {
        return aesDecryptCipher.doFinal(Base64.decode(content)).into<List<PassengerItem>>()
    }

    private fun initLimitInfo(detail: EligibilityDetail): LimitInfo {
        return LimitInfo(
            detail.minPaxNum,
            detail.maxPaxNum,
            if (detail.minAge != 0 || detail.maxAge != 0) {
                listOf(
                    AgeLimitItem(
                        detail.minAge,
                        detail.maxAge
                    )
                )
            } else {
                emptyList()
            },
            if (detail.nationalityType == 1) {
                detail.nationality
            } else {
                emptyList()
            },
            if (detail.nationalityType == 2) {
                detail.nationality
            } else {
                emptyList()
            }
        )
    }

    private fun initReverseInformation(item: RuleItem): ReverseInformation {
        /**
         * 深圳-北京:\n    成人改期费:\n        起飞前168小时前157元/人, 手续费率5%\n        起飞前168小时后到起飞前48小时前314元/人, 手续费率10%\n        起飞前48小时后到起飞前4小时前471元/人, 手续费率15%\n        起飞前4小时后628元/人, 手续费率20%\n
         */
        /**
         * 深圳-北京:\n    成人退票费:\n        起飞前168小时前314元/人, 手续费率10%\n        起飞前168小时后到起飞前48小时前471元/人, 手续费率15%\n        起飞前48小时后到起飞前4小时前785元/人, 手续费率25%\n        起飞前4小时后942元/人, 手续费率30%\n
         */
        var changeDec = ""
        var refundDec = ""
        val result = ReverseInformation(
            "",
            "",
            item.change.map {
                val changeDisplay = FR24PolicyEnum.valueOf(it.changePolicy).changeDisplay()
                if (changeDisplay.isNotBlank()) {
                    changeDec += "${changeDisplay}\n"
                }

                val flag = it.applicableTime.isEmpty()
                ReverseInfoItem(
                    "CNY",
                    if (flag) {
                        0
                    } else {
                        it.applicableTime[1] ?: 0
                    },
                    if (flag) {
                        0
                    } else {
                        it.applicableTime[0] ?: 0
                    },
                    if (flag) {
                        true
                    } else {
                        it.applicableTime[1] == null
                    },
                    if (flag) {
                        true
                    } else {
                        it.applicableTime[0] == null
                    },
                    PassengerTypeEnum.fr24ValueToEnum(it.paxType),
                    RefundChangeRuleTypeEnum.CHANGE,
                    NumberUtil.parseInt(it.changeFee, 0),
                    0,
                    "",
                    0,
                )
            }
                    +
                    item.refund.map {
                        val refundDisplay = FR24PolicyEnum.valueOf(it.refundPolicy).refundDisplay()
                        if (refundDisplay.isNotBlank()) {
                            refundDec += "${refundDisplay}\n"
                        }
                        val flag = it.applicableTime.isEmpty()
                        ReverseInfoItem(
                            "CNY",
                            if (flag) {
                                0
                            } else {
                                it.applicableTime[1] ?: 0
                            },
                            if (flag) {
                                0
                            } else {
                                it.applicableTime[0] ?: 0
                            },
                            if (flag) {
                                true
                            } else {
                                it.applicableTime[1] == null
                            },
                            if (flag) {
                                true
                            } else {
                                it.applicableTime[0] == null
                            },
                            PassengerTypeEnum.fr24ValueToEnum(it.paxType),
                            RefundChangeRuleTypeEnum.CHANGE,
                            NumberUtil.parseInt(it.refundFee, 0),
                            0,
                            "",
                            0,
                        )
                    }
        );
        return result.copy(changeDec, refundDec)
    }

    private fun initBagInfo(info: ExtraInfo, segmentIdMap: Map<String, Int>): BaggageInfo {
        var dec = ""
        val bagList = mutableListOf<BaggageInfoItem>()
        info.freeBaggageAllowance.map {
            val handPc = parseEEE(it.cabinBagPc)
            val handSize = parseEEE(it.checkedBagSize)
            val handWeight = parseEEE(it.cabinBagWeight)
            val checkPc = parseEEE(it.checkedBagPc)
            val checkSize = parseEEE(it.checkedBagSize)
            val checkWeight = parseEEE(it.cabinBagWeight)
            for (c in 0 until 3) {
                val s1 = formatBagStr(
                    handPc[c],
                    handSize[c],
                    handWeight[c],
                    PassengerTypeEnum.entries[c],
                    BaggageInfoEnum.HAND
                )
                val s2 = formatBagStr(
                    checkPc[c],
                    checkSize[c],
                    checkWeight[c],
                    PassengerTypeEnum.entries[c],
                    BaggageInfoEnum.CHECKED
                )
                if (s1.isNotBlank() && s2.isNotBlank()) {
                    dec += "$s1\n$s2\n"
                } else if (s1.isNotBlank()) {
                    dec += "$s1\n"
                } else if (s2.isNotBlank()) {
                    dec += "$s2\n"
                }
                bagList.add(
                    BaggageInfoItem(
                        PassengerTypeEnum.entries[c],
                        BaggageInfoEnum.HAND,
                        listOf(
                            BaggageRuleItem(
                                segmentIndex = segmentIdMap[it.segmentId] ?: 0,
                                volume = handSize[c],
                                weight = handWeight[c]
                            )
                        )
                    )
                );
                bagList.add(
                    BaggageInfoItem(
                        PassengerTypeEnum.entries[c],
                        BaggageInfoEnum.CHECKED,
                        listOf(
                            BaggageRuleItem(
                                segmentIndex = segmentIdMap[it.segmentId] ?: 0,
                                volume = checkSize[c],
                                weight = checkWeight[c]
                            )
                        )
                    )
                );

            }
        }
        return BaggageInfo(dec, bagList)
    }

    private fun formatBagStr(
        pc: String,
        size: String,
        weight: String,
        pax: PassengerTypeEnum = PassengerTypeEnum.ADULT,
        type: BaggageInfoEnum = BaggageInfoEnum.HAND,
    ): String {
        val pcStr = if (pc.isNotBlank()) {
            return ", 限${pc}件"
        } else {
            pc
        }
        return if (size.isNotBlank() && weight.isNotBlank()) {
            "${pax.display()}, ${type.display}每件${weight}KG, 行李体积限制为：${size}cm${pcStr};"
        } else {
            ""
        }
    }

    private fun parseEEE(value: String): List<String> {
        val eee = value.split(CommonValFunc.TC_SERVICE_TIME_SPLIT_3)
        if (eee.isEmpty() || eee.size != 3) {
            return listOf("", "", "")
        }
        return eee.map {
            if (it == CommonValFunc.FR24_E.toString()) {
                ""
            } else {
                it
            }
        }
    }

    private fun formatDateTime(date: String): String {
        return DateUtil.parse(date, CommonValFunc.FR24_DATE_TIME_FORMAT).toString()
    }

    private fun post(data: String, uriEnum: FR24UriEnum): String {
        val contentType = "application/json"
        val request = Request.Builder()
            .url(fR24Config.baseUrl + uriEnum.uri)
            .headers(getHeaders())
            .post(data.toRequestBody(contentType.toMediaTypeOrNull()))
            .build()
        log.info("url: {}, data: {}", request.url.toString(), data)
        try {
            val response = okHttpClient.newCall(request).execute()
            val result = response.body?.string()
            val r = result.into<FR24Response<String>>()
            log.info("url: {}, request body: {}, response: {}", request.url.toString(), data, r.notDataStr())
            if (response.isSuccessful) {
                log.info("success api: {}", uriEnum)
                return result ?: ""
            }
            log.info(
                "fail api: {}, code: {}, message: {}, data: {}",
                uriEnum, response.code, response.message, r.notDataStr()
            )
        } catch (e: Exception) {
            log.error("error api: {}, {}", uriEnum, request, e)
        }
        return ""
    }

    private fun getHeaders(): Headers {
        return Headers.Builder()
            .add("appKey", fR24Config.appKey)
            .add("content-type", "application/json")
            .build()
    }

    private fun getI18nMsg(locale: Locale): String {
        return commonService.getI18nMsg("all.product-type.24", locale)
    }
}