package com.somytrip.provider.flight.utils

import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.JSONObject
import com.somytrip.model.flight.common.ProductInfo
import com.somytrip.model.flight.enums.PassengerTypeEnum
import com.somytrip.model.flight.enums.ProductTagEnum
import com.somytrip.model.flight.tc.TCShoppingSearchRequest
import com.somytrip.model.po.QueryShoppingPriceItem
import com.somytrip.model.po.QueryShoppingPriceItemTaxDetail
import com.somytrip.model.po.QueryShoppingResponse
import com.somytrip.model.po.QueryShoppingSegmentItem
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.slf4j.LoggerFactory

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-07-09 11:36
 */
class XCFlightUtils(
    builder: Builder
) {
    private val log = LoggerFactory.getLogger(this::class.java)
    private val okHttpClient = builder.okHttpClient

    class Builder {
        internal var okHttpClient: OkHttpClient = OkHttpClient()
        fun okHttpClient(okHttpClient: OkHttpClient) = apply {
            this.okHttpClient = okHttpClient
        }

        fun builder(): XCFlightUtils {
            return XCFlightUtils(this)
        }
    }

    private val baseUrl = "https://m.ctrip.com/restapi/soa2"
    private val domesticUrl = "$baseUrl/14488/flightList?subEnv=fat128&_fxpcqlniredt=52271129211233184941"
    private val internationalUrl = "$baseUrl/14780/listSearchForH5?subEnv=fat128&_fxpcqlniredt=52271129211233184941"
    private val mediaType: MediaType? = "application/json".toMediaTypeOrNull()
    private val request: Request by lazy {
        Request.Builder()
            .url(domesticUrl)
            .method("POST", "{}".toRequestBody(mediaType))
            .addHeader("x-ctx-locale", "zh-CN")
            .addHeader("x-ctx-group", "ctrip")
            .addHeader("x-ctx-personal-recommend", "1")
            .addHeader("xweb_xhr", "1")
            .addHeader("x-ctx-region", "CN")
            .addHeader("x-ctx-currency", "CNY")
            .addHeader("Content-Type", "application/json")
            .build()
    }

    private val iata_code_map by lazy {
        mapOf(
            "NRT" to "TYO",
            "ITM" to "OSA",
            "UKB" to "OSA",
            "CTS" to "SPK",
            "OKD" to "SPK",
            "GMP" to "SEL",
            "DMK" to "BKK",
            "XSP" to "SIN",
            "BVA" to "PAR",
            "ORY" to "PAR",
            "XCR" to "PAR",
            "LCY" to "LON",
            "LHR" to "LON",
            "SEN" to "LON",
            "STN" to "LON",
            "ONT" to "LAX",
            "VNY" to "LAX",
            "DWC" to "DXB",
            "EWR" to "NYC",
            "JFK" to "NYC",
            "LGA" to "NYC",
            "NYS" to "NYC",
            "SWF" to "NYC",
            "DME" to "MOW",
            "SVO" to "MOW",
            "VKO" to "MOW",
            "CIA" to "ROM",
            "DIA" to "DOH",
            "MDW" to "CHI",
            "ORD" to "CHI",
            "RFD" to "CHI",
            "AUH" to "AUH",
            "YHM" to "YTO",
            "YTZ" to "YTO",
            "YYZ" to "YTO",
            "TXL" to "BER",
            "BLD" to "LAS",
            "DCA" to "WAS",
            "IAD" to "WAS",
            "SAW" to "IST",
            "PEK" to "BJS",
            "PKX" to "BJS",
            "JS2" to "SHA",
            "PVG" to "SHA",
            "BGY" to "MIL",
            "LIN" to "MIL",
            "MXP" to "MIL",
            "PMF" to "MIL",
            "CLD" to "SAN",
            "BNH" to "BOS",
            "PSM" to "BOS",
            "IAH" to "HOU",
            "BFI" to "SEA",
            "LKE" to "SEA",
            "CGH" to "SAO",
            "GRU" to "SAO",
            "VCP" to "CPQA",
            "AGB" to "MUC",
            "SFB" to "ORL",
            "PDK" to "ATL",
            "HHN" to "FRA",
            "RKE" to "CPH",
            "CRL" to "BRU",
            "CXH" to "YVR",
            "DAL" to "DFW",
            "YHU" to "YMQ",
            "YMX" to "YMQ",
            "YUL" to "YMQ",
            "TTN" to "PHL",
            "SDV" to "TLV",
            "AEP" to "BUE",
            "EZE" to "BUE",
            "XFW" to "HAM",
            "TRF" to "OSL",
            "ARN" to "STO",
            "BMA" to "STO",
            "NYO" to "STO",
            "VST" to "STO",
            "YND" to "YOW",
            "YEG" to "YEA",
            "GNB" to "LYS",
            "GSE" to "GOT",
            "NLU" to "MEX",
            "TLC" to "MEX",
            "TFU" to "CTU",
            "SZB" to "KUL",
            "TSF" to "VCE",
            "CGK" to "JKT",
            "HLP" to "JKT",
            "HLA" to "JNB",
            "ESB" to "ANK",
            "DTW" to "DTT",
            "MCI" to "MKC",
            "EMA" to "NQT",
            "IKA" to "THR",
            "JBQ" to "SDQ",
            "PAC" to "PTY",
            "PIK" to "GLA",
            "UBN" to "ULN",
            "HBE" to "DTT",
            "ZH1" to "ZUH",
            "SH1" to "JMU",
            "HSJ" to "CGO",
            "PF1" to "HRB",
            "JG1" to "JGD",
            "WHA" to "WUH",
            "PQ1" to "CDE",
            "YD1" to "CDE",
            "DDR" to "RKZ",
            "ZZ3" to "DHU",
            "HXD" to "KRL",
            "DEQ" to "HZC",
            "AEQ" to "CIF",
            "WMT" to "ZYI",
            "AZJ" to "ZUJ",
            "AVV" to "MEL",
            "MEB" to "MEL",
            "GIG" to "RIO",
            "SDU" to "RIO",
            "WIL" to "NBO",
            "LCK" to "CMH",
            "LUK" to "CVG",
            "SIG" to "SJU",
            "NRN" to "DUS",
            "CNF" to "BHZ",
            "PLU" to "BHZ",
            "MMX" to "MMA",
            "BHD" to "BFS",
            "ADB" to "IZM",
            "BBU" to "BUH",
            "OTP" to "BUH",
            "SCK" to "SAC",
            "SMF" to "SAC",
            "AMM" to "AMM",
            "EOH" to "MDE",
            "OLF" to "WOLF",
            "LUH" to "LUH",
            "DSS" to "DKR",
            "BDL" to "HFD"
        )
    }

    private fun getIataCode(code: String): String {
        return iata_code_map.getOrDefault(code, code)
    }

    private fun getRequest(req: TCShoppingSearchRequest): Request {
        return if (req.businessLineType == 0) {
            request.newBuilder().url(domesticUrl)
                .post(
                    getRequestBodyDomestic(
                        getIataCode(req.segments.first().depAirport),
                        getIataCode(req.segments.first().arrAirport),
                        req.segments.first().depDate
                    ).toRequestBody(mediaType)
                )
                .build()
        } else {
            request.newBuilder().url(internationalUrl)
                .post(
                    getRequestBodyInternational(
                        getIataCode(req.segments.first().depAirport),
                        getIataCode(req.segments.first().arrAirport),
                        req.segments.first().depDate
                    ).toRequestBody(mediaType)
                )
                .build()
        }
    }

    fun main(req: TCShoppingSearchRequest): List<QueryShoppingResponse> {
        val result = post(getRequest(req))
        if (result.isBlank()) {
            throw RuntimeException("get data error")
        }
//        log.info("xc result: {}", result)
        val jsonResponse = JSONObject.parseObject(result)
        return if (req.businessLineType == 0) {
            dealDomesticData(jsonResponse)
        } else {
            dealInternationalData(jsonResponse)
        }
    }

    private fun dealDomesticData(json: JSONObject): List<QueryShoppingResponse> {
        log.info("response status: {}", json.getJSONObject("ResponseStatus"))
        return json.getJSONObject("data").getJSONArray("listCards").filter {
            val item = it as JSONObject
            item.getJSONArray("airlines").size < 2
        }.map {
            val item = it as JSONObject
            log.info("dealDomesticData: {}", item.getString("totalDuration"))
            QueryShoppingResponse(
                prices = listOf(
                    QueryShoppingPriceItem(
                        PassengerTypeEnum.ADULT,
                        "",
                        item.getJSONObject("prices").getIntValue("ECONOMY"),
                        QueryShoppingPriceItemTaxDetail(
                            "0",
                            "0"
                        )
                    )
                ),
                segments = item.getJSONArray("airlines").mapIndexed() { index, airline ->
                    val segment = airline as JSONObject
                    QueryShoppingSegmentItem(
                        segment.getString("code"),
                        segment.getString("flightNo"),
                        segment.getString("code"),
                        0,
                        segment.getBoolean("shared"),
                        segment.getString("flightNo"),
                        segment.getString("craft"),
                        segment.getString("craft"),
                        item.getString("arrivalAirportCode"),
                        item.getString("arrivalName"),
                        item.getString("arriveDateTime"),
                        item.getString("departureAirportCode"),
                        item.getString("departureName"),
                        item.getString("departDateTime"),
                        item.getJSONObject("flightTags").getString("MEAT") ?: "",
                        "",
                        index + 1,
                        listOf(),
                        "0",
                        "0",
                        "0",
                        item.getString("arrivalAirportName"),
                        item.getString("departureAirportName"),
                        null,
                        listOf(),
                        item.getString("transitDuration") ?: ""
                    )

                },
                cabins = listOf(),
                productInfo = ProductInfo(
                    "NORMAL_PRODUCT",
                ),
                durationTotal = item.getString("totalDuration"),
                sourceType = 0
            )
        }
    }

    private fun dealInternationalData(json: JSONObject): List<QueryShoppingResponse> {
        log.info("response status: {}", json.getJSONObject("ResponseStatus"))
        return json.getJSONArray("segs").map {
            val item = it as JSONObject
            log.info("dealInternationalData: {}", item.getString("transitDuration"))
            QueryShoppingResponse(
                prices = item.getJSONArray("policys").map { policy ->
                    val policyJson = policy as JSONObject
                    QueryShoppingPriceItem(
                        PassengerTypeEnum.ADULT,
                        policyJson.getJSONArray("prices").getJSONObject(0).getString("tax"),
                        policyJson.getJSONArray("prices").getJSONObject(0).getIntValue("price"),
                        QueryShoppingPriceItemTaxDetail(
                            "0", "0"
                        )
                    )
                },
                segments = item.getJSONArray("flgs").map { airline ->
                    val segment = airline as JSONObject
                    QueryShoppingSegmentItem(
                        segment.getJSONObject("basinfo").getString("aircode"),
                        segment.getJSONObject("basinfo").getString("flgno"),
                        segment.getJSONObject("basinfo").getString("aircode"),
                        0,
                        false,
                        segment.getJSONObject("basinfo").getString("flgno"),
                        segment.getJSONObject("craftinfo").getString("cname"),
                        segment.getJSONObject("craftinfo").getString("craft"),
                        segment.getJSONObject("aportinfo").getString("aport"),
                        segment.getJSONObject("aportinfo").getString("bsname"),
                        dateFormat(segment.getJSONObject("dateinfo").getString("adate")),
                        segment.getJSONObject("dportinfo").getString("aport"),
                        segment.getJSONObject("dportinfo").getString("bsname"),
                        dateFormat(segment.getJSONObject("dateinfo").getString("ddate")),
                        "",
                        "0",
                        segment.getIntValue("sequenceNo"),
                        listOf(),
                        "0",
                        minFormat(segment.getJSONObject("dateinfo").getIntValue("jtime")),
                        minFormat(item.getJSONObject("seginfo").getIntValue("durtion")),
                        segment.getJSONObject("aportinfo").getString("aportsname"),
                        segment.getJSONObject("dportinfo").getString("aportsname"),
                        null,
                        listOf(),
                        ""
                    )

                },
                cabins = listOf(),
                productInfo = ProductInfo(
                    "NORMAL_PRODUCT",
                    ProductTagEnum.NORMAL_PRODUCT,
                ),
                durationTotal = minFormat(item.getJSONObject("seginfo").getIntValue("durtion")),
                sourceType = 1
            )
        }
    }

    /**
     * params: m Int 分钟
     * return "1d2h3m"
     */
    private fun minFormat(m: Int): String {
        val days = m / (24 * 60)
        val hours = (m % (24 * 60)) / 60
        val minutes = m % 60

        return buildString {
            if (days > 0) append("${days}d")
            if (hours > 0) append("${hours}h")
            if (minutes > 0) append("${minutes}m")

            // 如果没有任何时间单位大于0，则返回空字符串
            if (isEmpty()) {
                append("")
            }
        }
    }

    private fun dateFormat(date: String): String {
        return "${date.substring(0..4)}-${date.substring(4..6)}-${date.substring(6..8)} ${date.substring(8..10)}:${
            date.substring(
                10..12
            )
        }"
    }


    private fun post(request: Request): String {
        try {
            val response = okHttpClient.newCall(request).execute()
            if (response.isSuccessful) {
                return JSON.toJSONString(response.body?.string())
            }
            log.error("response code: ${response.code} body: ${response.body?.string()}")
        } catch (e: Exception) {
            log.error("e: {}", request, e)
        }
        return ""
    }

    private fun getRequestBodyDomestic(depCode: String, arrCode: String, depDate: String): String {
        val result = """{
    "data": "{\"searchParameter\":{\"searchFlightItems\":[{\"departureCode\":\"${depCode}\",\"departureCityId\":\"\",\"arrivalCode\":\"${arrCode}\",\"arrivalCityId\":\"\",\"departureDate\":\"${depDate}\",\"cityType\":\"code\"}],\"tripType\":\"ONE_WAY\",\"regionType\":\"DOMESTIC\",\"passengers\":{\"ADULT\":1,\"CHILD\":0,\"INFANT\":0}}}",
    "head": {
        "auth": "",
        "cid": "52271076296482655181",
        "ctok": "",
        "cver": "101.272",
        "extension": [
            {
                "name": "pageName",
                "value": "HOME"
            },
            {
                "name": "aId",
                "value": "262684"
            },
            {
                "name": "sId",
                "value": "711465"
            },
            {
                "name": "sourceId",
                "value": "55552689"
            },
            {
                "name": "env",
                "value": "weapp"
            },
            {
                "name": "version",
                "value": "2024.02.19"
            },
            {
                "name": "platform.source",
                "value": "H5"
            },
            {
                "name": "appId",
                "value": "wx0e6ed4f51db9d078"
            },
            {
                "name": "scene",
                "value": "1145"
            }
        ],
        "lang": "01",
        "sauth": "",
        "sid": "",
        "syscode": "30"
    }
}"""
        log.info("getRequestBodyDomestic body: {}", JSONObject.parseObject(result))
        return result
    }

    private fun getRequestBodyInternational(depCode: String, arrCode: String, depDate: String): String {
        val result = """{
    "segs": [
        {
            "dcode": "$depCode",
            "acode": "$arrCode",
            "ddate": "$depDate"
        }
    ],
    "tripType": 1,
    "psglst": [
        {
            "psgtype": 1,
            "psgcnt": 1
        },
        {
            "psgtype": 2,
            "psgcnt": 0
        },
        {
            "psgtype": 3,
            "psgcnt": 0
        }
    ],
    "segno": 1,
    "grade": 4,
    "sortinfo": {
        "idx": 1,
        "size": 0,
        "ordby": 105,
        "meyordby": 2,
        "dir": 2,
        "token": ""
    },
    "polid": "",
    "prdid": "",
    "head": {
        "cid": "52271129211233184941",
        "ctok": "",
        "cver": "856.006",
        "lang": "01",
        "sid": "",
        "syscode": "30",
        "sauth": "",
        "extension": [
            {
                "name": "pageName",
                "value": "INTER_FIRST"
            },
            {
                "name": "aId",
                "value": "1023020"
            },
            {
                "name": "sId",
                "value": "1625630"
            },
            {
                "name": "sourceId",
                "value": "55552689"
            },
            {
                "name": "env",
                "value": "weapp"
            },
            {
                "name": "version",
                "value": "2024.02.19"
            },
            {
                "name": "platform.source",
                "value": "H5"
            },
            {
                "name": "appId",
                "value": "wx0e6ed4f51db9d078"
            },
            {
                "name": "scene",
                "value": "1256"
            }
        ]
    }
}""";
        log.info("getRequestBodyInternational body: {}", JSONObject.parseObject(result))
        return result
    }

}