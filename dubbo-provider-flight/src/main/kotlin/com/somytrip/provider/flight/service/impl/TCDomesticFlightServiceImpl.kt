package com.somytrip.provider.flight.service.impl

import cn.hutool.core.date.DateField
import cn.hutool.core.date.DateUnit
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.DesensitizedUtil
import cn.hutool.core.util.NumberUtil
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson2.JSONObject
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.somytrip.api.service.order.OrderBaseService
import com.somytrip.api.service.order.OrderExtService
import com.somytrip.api.service.order.OrderStateService
import com.somytrip.entity.dto.order.FlightOrderDetail
import com.somytrip.entity.dto.order.OrderBigFieldDTO
import com.somytrip.entity.dto.order.OrderMasterExt
import com.somytrip.entity.enums.order.OrderBusinessTypeEnum
import com.somytrip.entity.enums.order.OrderSettleModeEnums
import com.somytrip.entity.enums.order.OrderTypeEnum
import com.somytrip.entity.enums.order.OrderUserTypeEnum
import com.somytrip.entity.enums.pay.CollectionAgencyEnums
import com.somytrip.entity.enums.pay.CurrencyEnums
import com.somytrip.entity.enums.pay.PaymentChannelEnums
import com.somytrip.entity.enums.pay.PaymentFromEnums
import com.somytrip.entity.enums.thirdparty.ThirdServiceTypeEnum
import com.somytrip.entity.response.ResponseResult
import com.somytrip.entity.vo.CreateOrderFinanceVo
import com.somytrip.entity.vo.order.*
import com.somytrip.exception.BusinessException
import com.somytrip.model.dto.TicketChangeOrder
import com.somytrip.model.flight.common.*
import com.somytrip.model.flight.enums.*
import com.somytrip.model.flight.tc.*
import com.somytrip.model.flight.vo.*
import com.somytrip.model.flight.vo.OrderBaseVo
import com.somytrip.provider.flight.config.FlightConfig
import com.somytrip.provider.flight.config.IDConfig
import com.somytrip.provider.flight.config.PriceConfig
import com.somytrip.provider.flight.mapper.TicketChangeOrderMapper
import com.somytrip.provider.flight.service.CommonService
import com.somytrip.provider.flight.service.FlightService
import com.somytrip.provider.flight.utils.TCFlightUtil
import jakarta.annotation.Resource
import okhttp3.OkHttpClient
import org.apache.dubbo.config.annotation.DubboReference
import org.apache.dubbo.rpc.RpcContext
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * @Description: 同程flight service 实现
 * @author: pigeon
 * @created: 2024-08-29 9:16
 */
@Service("tcDomesticFlightService")
class TCDomesticFlightServiceImpl : FlightService {
    private val log = LoggerFactory.getLogger(this::class.java)
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(5, TimeUnit.MINUTES)
        .readTimeout(5, TimeUnit.MINUTES)
        .writeTimeout(5, TimeUnit.MINUTES)
        .callTimeout(5, TimeUnit.MINUTES)
        .build()
    private val flightUtil by lazy {
        TCFlightUtil.Builder()
            .okHttpClient(okHttpClient)
            .tcConfig(flightConfig.tcDomestic())
            .builder()
    };

    @Resource
    private lateinit var idConfig: IDConfig

    @Resource
    private lateinit var flightConfig: FlightConfig

    @Resource
    private lateinit var priceConfig: PriceConfig

    @Resource
    private lateinit var ticketChangeOrderMapper: TicketChangeOrderMapper

    @Resource
    private lateinit var commonService: CommonService

    @DubboReference(check = false)
    private lateinit var orderBaseService: OrderBaseService

    @DubboReference(check = false)
    private lateinit var orderStateService: OrderStateService;

    @DubboReference(check = false)
    private lateinit var orderExtService: OrderExtService;
    override fun support(provider: ProviderSourceEnum): Boolean {
        return provider() == provider
    }

    override fun provider(): ProviderSourceEnum {
        return ProviderSourceEnum.TC_DOMESTIC
    }

    override fun shoppingList(req: ShopListReq): Map<String, List<ShopListRsp>> {
        try {
            val lang = RpcContext.getServerAttachment().getAttachment("lang");
            val l = StrUtil.split(lang, StrUtil.C_UNDERLINE)
            val locale = Locale(l[0], l[1])
            val productType = "tc.product-type."
            val cacheValue = commonService.getCacheShoppingList(req, provider())
            if (cacheValue.isNotEmpty()) {
                return cacheValue
            }
            val request = TCShoppingSearchRequest(
                "1",
                0,
                null,
                req.screenCondition.airline,
                listOf(
                    TCShoppingSearchPassenger(req.adultNum(), PassengerTypeEnum.ADULT.tcValue()),
                    TCShoppingSearchPassenger(req.childNum(), PassengerTypeEnum.CHILD.tcValue()),
                    TCShoppingSearchPassenger(req.infantNum(), PassengerTypeEnum.INFANT.tcValue()),
                ).filter { it.count > 0 },
                req.segments.mapIndexed { index, it ->
                    TCShoppingSearchSegmentItem(
                        index + 1,
                        it.departCode,
                        "",
                        it.arriveCode,
                        "",
                        it.departTime,
                    )
                },
                null,
            )
            val rsp = shopping(request)
            if (rsp.productList.isEmpty()) {
                return emptyMap()
            }
            val result = mutableMapOf<String, MutableList<ShopListRsp>>()
            rsp.productList.forEach {
                var segDurationTotal = 0
                val item = ShopListRsp(
                    it.prices.map { p ->
                        PriceItem(
                            PassengerTypeEnum.tcValueToEnums(p.value.passengerType),
                            p.value.taxB.toString(),
                            priceConfig.getChangePrice(p.value.priceB).toString(),
                            TaxDetail(
                                p.value.taxDetail.yqB.toString(),
                                p.value.taxDetail.taxB.toString()
                            )
                        )
                    },
                    it.segments.sortedBy { s -> s.segmentIndex }.map { s ->
                        var stopTimeTotal: Int = 0;
                        var durationTotal: Int = 0;
                        durationTotal += s.duration
                        stopTimeTotal += s.stopTime
                        segDurationTotal += durationTotal + stopTimeTotal
                        SegmentItem(
                            s.operatingAirline,
                            s.operatingFlightNo,
                            s.marketingAirline,
                            when (s.segmentType) {
                                2 -> 1
                                else -> 0
                            },
                            s.flightShare,
                            s.marketingFlightNo,
                            s.aircraft,
                            s.aircraftType.toString(),
                            s.arrAirport,
                            s.arrAirportTerm,
                            s.arrDateTime,
                            s.depAirport,
                            s.depAirportTerm,
                            s.depDateTime,
                            when (s.meal) {
                                1 -> "有餐食" // 有
                                2 -> "无餐食" // 无
                                else -> "" // 默认未知
                            },
                            s.mileage.toString(),
                            s.segmentIndex,
                            s.stops.map { sp ->
                                SegmentStopItem(
                                    sp.stopAirport,
                                    sp.stopTime,
                                )
                            },
                            CommonValFunc.formatIntToDuration(s.stopTime),
                            CommonValFunc.formatIntToDuration(s.duration),
                            CommonValFunc.formatIntToDuration(s.duration + s.stopTime),
                            commonService.getAirportName(s.arrAirport),
                            commonService.getAirportName(s.depAirport),
                            commonService.getAirline(s.marketingAirline),
                            s.stops.map { sp -> sp.stopAirport },
                            CommonValFunc.formatIntToDuration(s.stopTime),
                            s.cabinCode,
                        )
                    },
                    it.cabins.map { c ->
                        CabinItem(
                            CabinClassEnum.tcValueToEnums(c.cabinClass),
                            c.cabinCode,
                            c.cabinCount,
                            c.segmentIndex
                        )
                    },
                    ProductInfo(
                        it.productCode.toString(),
                        ProductTagEnum.tcValueToEnums(it.productTag),
                        provider().display,
                        provider(),
                        it.data,
                        getI18nMsg("$productType${it.productCode}", locale)
                    ),
                    0,
                    CommonValFunc.formatIntToDuration(segDurationTotal)
                )
                val key = item.segments.joinToString { s -> s.flightNo }
                if (result.containsKey(key)) {
                    result[key]?.add(item)
                } else {
                    result[key] = mutableListOf(item)
                }
            }
            commonService.cacheShoppingList(req, result, provider())
            return result
        } catch (e: Exception) {
            log.error("get data error: {}", req.toJSONString(), e)
        }
        return emptyMap()
    }

    private fun shopping(request: TCShoppingSearchRequest): TCShoppingBusinessResponse {
        try {
            val requestId = idConfig.getStrID()
            val response = flightUtil.flightShoppingSearch(request, requestId)
            return response
        } catch (e: Exception) {
            log.error("error: ${request.toJSONString()}\n", e)
            throw BusinessException("${e.message}")
        }
    }

    override fun accSearch(req: AccurateReq): AccurateRsp {
        val result = accSearchMarkUpFlag(req, true)
        log.info("acc search result: {}", result.toJSONString())
        return result
    }

    private fun accSearchMarkUpFlag(req: AccurateReq, markUpFlag: Boolean = false): AccurateRsp {
        log.info("accSearchMarkUpFlag req: {}", req.toJSONString())
        val lang = RpcContext.getServerAttachment().getAttachment("lang");
        val l = StrUtil.split(lang, StrUtil.C_UNDERLINE)
        val locale = Locale(l[0], l[1])
        val productType = "tc.product-type."
        val request = TCAccurateSearchRequest(
            0,
            req.tripType + 1,
            req.productInfos.map { it.productCode.toInt() },
            1,
            req.adultNum,
            req.childNum,
            req.infantNum,
            req.mainAirline,
            "0",
            req.segments.map {
                TCAccurateSearchSegment(
                    req.tripType + 1,
                    it.segmentIndex,
                    "",
                    it.departAirport,
                    it.departAirportTerm,
                    "",
                    it.arriveAirport,
                    it.arriveAirportTerm,
                    it.departDateTime,
                    it.arriveDateTime,
                    it.airline,
                    it.flightNo,
                    it.cabinCode,
                    "",
                    it.flightShare,
                    it.aircraft,
                    it.operationFlightNo,
                    it.operationAirline,
                )
            }
        )
        val requestId = idConfig.getStrID()
        try {
            val response = flightUtil.flightAccurateSearch(request, requestId)
            log.info("acc search response: {}", response.toJSONString())
            val result = AccurateRsp(
                0,
                response.flightUnits.filter { it.issueTicketInfo.changePnrCode == 1 }.map {
                    var segDurationTotal = 0
                    val item = AccRuleItem(
                        it.prices.values.map { p ->
                            PriceItem(
                                PassengerTypeEnum.tcValueToEnums(p.passengerType),
                                p.taxB.toString(),
                                p.priceB.toString(),
                                TaxDetail(p.taxDetail.yqB.toString(), p.taxDetail.taxB.toString())
                            )
                        },
                        it.segments.sortedBy { s -> s.segmentIndex }.map { s ->
                            var stopTimeTotal: Int = 0;
                            var durationTotal: Int = 0;
                            durationTotal += s.duration
                            stopTimeTotal += s.stopTime
                            segDurationTotal += durationTotal + stopTimeTotal
                            SegmentItem(
                                s.operatingAirline,
                                s.operatingFlightNo,
                                s.marketingAirline,
                                when (s.segmentType) {
                                    2 -> 1
                                    else -> 0
                                },
                                s.flightShare,
                                s.marketingFlightNo,
                                s.aircraft,
                                s.aircraftType.toString(),
                                s.arrAirport,
                                s.arrAirportTerm,
                                s.arrDateTime,
                                s.depAirport,
                                s.depAirportTerm,
                                s.depDateTime,
                                when (s.meal) {
                                    1 -> "有餐食" // 有
                                    2 -> "无餐食" // 无
                                    else -> "" // 默认未知
                                },
                                s.mileage.toString(),
                                s.segmentIndex,
                                s.stops.map { sp ->
                                    SegmentStopItem(
                                        sp.stopAirport,
                                        sp.stopTime,
                                    )
                                },
                                CommonValFunc.formatIntToDuration(s.stopTime),
                                CommonValFunc.formatIntToDuration(s.duration),
                                CommonValFunc.formatIntToDuration(s.duration + s.stopTime),
                                s.arrCityName,
                                s.depCityName,
                                commonService.getAirline(s.marketingAirline),
                                s.stops.map { sp -> sp.stopAirport },
                                CommonValFunc.formatIntToDuration(s.stopTime),
                                s.cabinCode,
                            )
                        },
                        ProductInfo(
                            it.productCode.toString(),
                            ProductTagEnum.tcValueToEnums(it.productTag),
                            provider().display,
                            provider(),
                            it.data,
                            getI18nMsg("$productType${it.productCode}", locale)
                        ),
                        ReverseInformation(
                            it.refundChangeInfo.changeText,
                            it.refundChangeInfo.refundText,
                            it.refundChangeInfo.refundChangeRules.map { r ->
                                ReverseInfoItem(r)
                            },
                        ),
                        ServiceTime(it.serviceTime),
                        BaggageInfo(it.baggageInfo),
                        IssueTicketInfo(it.issueTicketInfo, it.invoiceWay, it.invoiceType),
                        LimitInfo(it.limitInfo)
                    )
                    cacheTCAccurate(item)
                    item
                }
            )
            return result.copy(
                rules = result.rules
                    .map {
                        it.copy(
                            prices = it.prices
                                .map { p ->
                                    p.copy(
                                        price = if (markUpFlag) {
                                            commonService.getMarkUpPrice(p.price).toString()
                                        } else {
                                            p.price
                                        }
                                    )
                                })
                    }
            )
        } catch (e: Exception) {
            log.error("精确询价接口报错: requestID: $requestId, request: ${request.toJSONString()}", e)
            return AccurateRsp()
        }
    }

    override fun bookingOrder(req: CreateOrderReq): CreateOrderRsp {
        log.info("tc create order req: {}", req.toJSONString())
        var orderSerialNo: String = "";
        try {
            var ruleItem = getCacheValue(req.productInfo.orderInfo)
            log.info("cache get value: {}", ruleItem.productInfo.orderInfo)
            if (ruleItem.segments.isEmpty()) {
                val accRsp = accSearchMarkUpFlag(req.accurateRequest)
                if (accRsp.rules.isEmpty()) {
                    return CreateOrderRsp(false, 500, "未找到对应航班详细信息，请重新挑选！")
                }
                if (accRsp.rules.none {
                        it.productInfo.orderInfo == req.productInfo.orderInfo
                    }) {
                    log.info("req order info: {}", req.productInfo.toJSONString())
                    log.info("acc rsp order info: {}", accRsp.rules.joinToString { it.productInfo.orderInfo })
                    cacheTCAccurate(accRsp.rules.first())
                    log.info("flash order info: {}", accRsp.rules.map { it.productInfo }.toJSONString())
                    return CreateOrderRsp(
                        false, 306,
                        "价格已刷新，请核对价格后重试", "",
                        accRsp.copy(
                            rules = accRsp.rules
                                .map {
                                    it.copy(
                                        prices = it.prices
                                            .map { p ->
                                                p.copy(
                                                    price = commonService.getMarkUpPrice(p.price).toString()
                                                )
                                            })
                                })
                    )
                }
                ruleItem = accRsp.rules.first()
            }
            val passengers = commonService.getPassengerInfos(req.userId, req.passengers)
            // 判断年龄
            if (ruleItem.limitInfo.ages.isNotEmpty()) {
                for (passenger in passengers) {
                    if (passenger.birthday.isBlank()) {
                        continue
                    }
                    val age = DateUtil.ageOfNow(passenger.birthday)
                    for (ageLimit in ruleItem.limitInfo.ages) {
                        if (age < ageLimit.min || age > ageLimit.max) {
                            log.info("年龄校验未通过: {}", passenger.toJSONString())
                            return CreateOrderRsp(
                                false,
                                400,
                                "乘客-${passenger.lastName + passenger.firstName}年龄不符,最小:${ageLimit.min},最大:${ageLimit.max}"
                            )
                        }
                    }
                }
                log.info("年龄校验通过")
            }
            val orderRequest = initTCOrderRequest(passengers, ruleItem, req)
            val requestId = idConfig.getStrID();
            log.info("tc create order req: {}, {}", requestId, req.toJSONString())
            val orderResponse = flightUtil.flightCreateOrder(orderRequest, requestId)
            log.info("tc create order rsp: {}, {}", requestId, orderResponse.toJSONString())
            if (orderResponse.orderSerialNo.isBlank()) {
                log.error(
                    "tc create order fail: {}, {}, {}",
                    requestId,
                    orderRequest.toJSONString(),
                    orderResponse.toJSONString()
                )
                val messages = CommonValFunc.TC_MESSAGE_REGEX.findAll(orderResponse.toJSONString())
                if (!messages.none()) {
                    val message = messages.last()
                    log.warn("tc create order fail message: {}", message.value)
                    return CreateOrderRsp(false, 400, message.value)
                }
                return CreateOrderRsp(false, 500, "TC provider create order fail")
            }
            orderSerialNo = orderResponse.orderSerialNo
            val createOrderVo = initCreateOrderVo(req, orderRequest, ruleItem, orderResponse)
            log.info("init create order vo success")
            try {
                log.info("TC_Domestic create order: {}", createOrderVo.toJSONString())
                val response = orderBaseService.createOrderBaseV2(createOrderVo.toJSONString())
                log.info("order service return: {}", response.toJSONString())
                if (response.isSuccess) {
                    orderSerialNo = "";
                    return CreateOrderRsp(true, 200, response.message, response.data.toString())
                } else {
                    return CreateOrderRsp(false, response.code, response.message)
                }
            } catch (e: Exception) {
                log.error("rpc order service fail: {}", createOrderVo.toJSONString())
            } finally {
                if (orderSerialNo.isNotBlank()) {
                    val request = TCOrderCancelRequest(
                        orderSerialNo,
                        createOrderVo.orderBigFiled.field2.toJSONString().into<TCOrderBusinessResponse>()
                            .productItems.map {
                                TCOrderCancelProduct(
                                    it.productDetail
                                        .into<TCOrderCancelProductDetail>(), it.productType
                                )
                            }, "系统自动取消"
                    )
                    if (notPayCancelOrder(request)) {
                        log.info("orderSerialNo:${orderSerialNo} system cancel success")
                    } else {
                        log.error("orderSerialNo:${orderSerialNo} system cancel fail")
                    }
                }
            }
        } catch (e: Exception) {
            log.error("create order fail: {}", req.toJSONString())
            log.error("", e)
        }
        return CreateOrderRsp(false, 500, "TC provider create order fail")
    }

    override fun tcPay(req: TCPayInfo): TCPayResponse {
        try {
            val payCheckId = idConfig.getStrID()
            var totalPrice = BigDecimal.ZERO
            req.products.forEach { totalPrice = NumberUtil.add(totalPrice, NumberUtil.parseNumber(it.price)) }
            val totalPriceStr = NumberUtil.roundStr(totalPrice.toString(), 2)
            val payCheckReq = TCPayCheckRequest(
                req.orderSerialNo,
                totalPriceStr,
                req.orderNo,
                req.products
            )
            log.info("tc tcPay check req:{}, {}", payCheckId, payCheckReq.toJSONString())
            val payCheckRsp = flightUtil.flightPayCheck(payCheckReq, payCheckId)
            log.info("tc tcPay check rsp:{}, {}", payCheckId, payCheckRsp.toJSONString())
            if (!payCheckRsp.success) {
                log.info("tc tcPay check fail: {}", req.toJSONString())
                return TCPayResponse(payCheckRsp.message, payCheckRsp.orderSerialNo)
            }
            log.info("tc tcPay check success")
            val payId = idConfig.getStrID()
            val payReq = TCPayRequest(
                req.orderSerialNo,
                totalPriceStr,
            )
            log.info("tc tcPay req:{}, {}", payId, payReq.toJSONString())
            val payRsp = flightUtil.flightPay(payReq, payId)
            log.info("tc tcPay rsp:{}, {}", payId, payRsp.toJSONString())
            if (payRsp.success) {
                return TCPayResponse(payRsp.message, payRsp.orderSerialNo, true)
            }
        } catch (e: Exception) {
            log.error("tc tcPay order fail: {}", req.toJSONString())
            log.error("", e)
        }
        return TCPayResponse("error", req.orderSerialNo, false)
    }

    override fun orderDetail(orderNo: String): String {
        val orderExt = orderExtService.queryOrderMasterExtByOrderNo(orderNo)
        if (orderExt == null) {
            log.warn("get order ext fail: {}", orderNo)
            throw BusinessException("not found order ext info")
        }
        val requestId = idConfig.getStrID()
        log.info("query order detail: {}", orderExt.toJSONString())
        val request = if (orderExt.outOrderNo.startsWith("E")) {
            val changeOrderDetail = flightUtil.changeOrderDetail(
                TCQueryChangeOrderDetailRequest(orderExt.outOrderNo),
                idConfig.getStrID()
            )
            log.info("query order detail first change: {}", changeOrderDetail.toJSONString())
            TCOrderDetailRequest(changeOrderDetail.data.orderSerialNo, requestId)
        } else {
            TCOrderDetailRequest(orderExt.outOrderNo, requestId)
        }
        log.info("query order detail req: {}", request.toJSONString())
        val response = flightUtil.flightOrderDetail(request, request.traceId);
        log.info(
            "orderNo: {} query order detail requestId: {}, response: {}",
            orderNo,
            requestId,
            response.toJSONString()
        )
        return response.toJSONString()
    }

    override fun orderDetail(orderMasterExt: OrderMasterExt): String {
        val requestId = idConfig.getStrID()
        log.info("query order detailV2: {}", orderMasterExt.toJSONString())
        val request = if (orderMasterExt.outOrderNo.startsWith("E")) {
            val changeOrderDetail = flightUtil.changeOrderDetail(
                TCQueryChangeOrderDetailRequest(orderMasterExt.outOrderNo),
                idConfig.getStrID()
            )
            log.info("query order detailV2 first change: {}", changeOrderDetail.toJSONString())
            return changeOrderDetail.toJSONString()
        } else {
            TCOrderDetailRequest(orderMasterExt.outOrderNo, requestId)
        }
        log.info("query order detailV2 req: {}", request.toJSONString())
        val response = flightUtil.flightOrderDetail(request, request.traceId);
        log.info(
            "orderNo: {} query order detailV2 requestId: {}, response: {}",
            orderMasterExt.orderNo,
            requestId,
            response.toJSONString()
        )
        return response.toJSONString()
    }

    override fun uploadFiles(vo: UploadFilesVo): List<String> {
        log.info("upload refund files vo: {}", vo.orderNo)
        if (vo.type == 1) {
            val orderDetail = orderDetail(vo.orderNo).into<TCOrderDetailQueryResponse>()
            val request = TCRefundFileUploadRequest(
                orderDetail.products.first().orderSerialNo,
                "",
                vo.orderNo,
                vo.files.map {
                    TCRefundFileUploadFile(
                        it.file,
                        it.fileName
                    )
                }
            )
            val requestId = idConfig.getStrID()
            val response = flightUtil.flightRefundFileUpload(request, requestId);
            log.info("upload refund files response: {}", response.toJSONString())
            return response.fileIds
        } else {
            val orderDetail = orderDetail(vo.orderNo).into<TCOrderDetailQueryResponse>()
            val request = TCEndorseFileUploadRequest(
                orderDetail.products.first().orderSerialNo,
                vo.files.map {
                    TCEndorseQueryUploadFile(it.file, it.fileName)
                }
            )
            val requestId = idConfig.getStrID()
            val response = flightUtil.flightEndorseUploadFile(request, requestId);
            log.info("upload change files response: {}", response.toJSONString())
            return response.fileIds
        }
    }

    override fun queryRefundFee(vo: QueryRefundFeeVo): QueryRefundFeeResultVo {
        log.info("query refund fee vo: {}", vo.toJSONString())
        val orderMasterExt = orderExtService.queryOrderMasterExtByOrderNo(vo.orderNo)
            ?: throw BusinessException(400, "order not found")
        if (orderMasterExt.outOrderNo.startsWith("E")) {
            val orderDetail = orderDetail(orderMasterExt).into<TCQueryChangeOrderDetailResponse>()
            log.info("query change refund fee get order detail success: {}", orderDetail.toJSONString())
            val ticketMap: MutableMap<String, TCEndorseDetail2QueryTicketInfo> = mutableMapOf()
            orderDetail.data.ticketInfos.forEach { t ->
                t.newTicket.ticketNos.forEach { n ->
                    ticketMap[n.ticketNo] = t
                }
            }
            val ableRefundRequest = TCRefundAbleRequest(
                orderDetail.data.orderSerialNo,
                vo.type.tcValue,
                vo.ticketNos.map {
                    val t = ticketMap[it]
                    TCRefundAblePassengerInfo(
                        t?.newTicket?.creditNo ?: "",
                        t?.newTicket?.firstName + t?.newTicket?.lastName,
                        it,
                    )
                },
                idConfig.getStrID()
            )
            log.info("query change refund fee request: {}", ableRefundRequest.toJSONString())
            val ableRefundResponse = flightUtil.flightRefundable(ableRefundRequest, ableRefundRequest.traceId)
            log.info("query change refund fee response: {}", ableRefundResponse.toJSONString())
            val feeList = mutableListOf<RefundFeeResultVo>()
            ableRefundResponse.routePassengerInfos.forEach {
                it.passengerRefundInfo.forEach { p ->
                    p.refundProducts.forEach { r ->
                        feeList.add(RefundFeeResultVo(r.purchasePenalty.toString()))
                    }
                }
            }

            log.info("able change refund response: {}", ableRefundResponse.toJSONString())
            return QueryRefundFeeResultVo(
                "0",
                feeList,
                "0"
            )
        } else {
            val orderDetail = orderDetail(orderMasterExt).into<TCOrderDetailQueryResponse>()
            log.info("query refund fee get order detail success: {}", orderDetail.toJSONString())
            val passengerMap = mutableMapOf<String, TCOrderDetailQueryPassenger>()
            val ticketMap = mutableMapOf<String, TCOrderDetailQueryTicket>()
            orderDetail.products.forEach {
                it.passengers.forEach { passenger ->
                    val key = "${passenger.firstName}${passenger.lastName}"
                    passengerMap[key] = passenger
                }
                it.tickets.forEach { ticket ->
                    ticketMap[ticket.ticketNo] = ticket
                }
            }
            log.info("query refund fee get passenger success: {}", ticketMap.toJSONString())
            val ableRefundRequest = TCRefundAbleRequest(
                orderDetail.products.first().orderSerialNo,
                vo.type.tcValue,
                vo.ticketNos.map {
                    val key = ticketMap[it]?.firstName + ticketMap[it]?.lastName
                    TCRefundAblePassengerInfo(
                        passengerMap[key]?.creditNo ?: "",
                        key,
                        it
                    )
                },
                idConfig.getStrID()
            )
            log.info("query refund fee request: {}", ableRefundRequest.toJSONString())
            val ableRefundResponse = flightUtil.flightRefundable(ableRefundRequest, ableRefundRequest.traceId)
            log.info("query refund fee response: {}", ableRefundResponse.toJSONString())
            val feeList = mutableListOf<RefundFeeResultVo>()
            ableRefundResponse.routePassengerInfos.forEach {
                it.passengerRefundInfo.forEach { p ->
                    p.refundProducts.forEach { r ->
                        feeList.add(RefundFeeResultVo(r.purchasePenalty.toString()))
                    }
                }
            }

            log.info("able refund response: {}", ableRefundResponse.toJSONString())
            return QueryRefundFeeResultVo(
                "0",
                feeList,
                "0"
            )
        }
    }

    override fun applyRefund(vo: ApplyRefundVo): ApplyRefundResultVo {
        log.info("apply refund vo: {}", vo.toJSONString())
        val orderMasterExt = orderExtService.queryOrderMasterExtByOrderNo(vo.orderNo)
            ?: throw BusinessException(400, "order not found")
        if (orderMasterExt.outOrderNo.startsWith("E")) {
            val orderDetail = orderDetail(orderMasterExt).into<TCQueryChangeOrderDetailResponse>()
            log.info("apply change refund fee get order detail success: {}", orderDetail.toJSONString())
            val ticketMap: MutableMap<String, TCEndorseDetail2QueryTicketInfo> = mutableMapOf()
            orderDetail.data.ticketInfos.forEach { t ->
                t.newTicket.ticketNos.forEach { n ->
                    ticketMap[n.ticketNo] = t
                }
            }
            val request = TCRefundApplyRequest(
                orderDetail.data.orderSerialNo,
                vo.orderNo,
                vo.type.tcValue,
                vo.remark,
                vo.fileIds,
                vo.reason.tcValue,
                vo.ticketNos.map {
                    val t = ticketMap[it]
                    TCRefundApplyPassengerInfo(
                        t?.newTicket?.creditNo ?: "",
                        t?.newTicket?.firstName + t?.newTicket?.lastName,
                        it,
                    )
                }
            )
            log.info("apply change refund fee request: {}", request.toJSONString())
            val requestId = idConfig.getStrID()
            val response = flightUtil.flightRefundApply(request, requestId)
            log.info("apply change refund requestId: {}, response: {}", requestId, response.toJSONString())
            return ApplyRefundResultVo(true, response.toJSONString())
        } else {
            val orderDetail = orderDetail(orderMasterExt).into<TCOrderDetailQueryResponse>()
            log.info("apply refund get order detail success: {}", orderDetail.toJSONString())
            val passengerMap = mutableMapOf<String, TCOrderDetailQueryPassenger>()
            val ticketMap = mutableMapOf<String, TCOrderDetailQueryTicket>()
            orderDetail.products.forEach {
                it.passengers.forEach { passenger ->
                    val key = "${passenger.firstName}${passenger.lastName}"
                    passengerMap[key] = passenger
                }
                it.tickets.forEach { ticket ->
                    ticketMap[ticket.ticketNo] = ticket
                }
            }
            val request = TCRefundApplyRequest(
                orderDetail.products.first().orderSerialNo,
                vo.orderNo,
                vo.type.tcValue,
                vo.remark,
                vo.fileIds,
                vo.reason.tcValue,
                vo.ticketNos.map {
                    val key = ticketMap[it]?.firstName + ticketMap[it]?.lastName
                    TCRefundApplyPassengerInfo(
                        passengerMap[key]?.creditNo ?: "",
                        key,
                        it
                    )
                }
            )
            val requestId = idConfig.getStrID()
            log.info("apply refund: {}", requestId)
            val response = flightUtil.flightRefundApply(request, requestId)
            log.info("apply refund requestId: {}, response: {}", requestId, response.toJSONString())
            return ApplyRefundResultVo(true, response.toJSONString())
        }

    }

    override fun tcNotify(body: NotifyBody<TCNotifyBody>): NotifyResult<String> {
        val data = body.data
        log.info("tc notifyNew body: {}", body.toJSONString())
        when (TCNotifyEnum.codeToEnum(data.serviceCode)) {
            TCNotifyEnum.PAY_NOTIFY -> {
                orderStateService.flightTicketPayHandler(tcPayNotify(data))
            }

            TCNotifyEnum.TICKET_RESULT_NOTIFY -> {
                log.info("tc notify TICKET_RESULT_NOTIFY");
                orderStateService.flightTicketStatusModify(tcTicketNotify(data));
            }

            TCNotifyEnum.REFUND_RESULT_NOTIFY -> {
                log.info("tc notify REFUND_RESULT_NOTIFY");
                orderStateService.flightRefundHandler(refundResultNotify(data));
            }

            TCNotifyEnum.REFUND_FEE_NOTIFY -> {
                val notify = refundFeeNotify(data)
                log.info("tc notify REFUND_FEE_NOTIFY: {}", notify.toJSONString());
            }

            TCNotifyEnum.ENDORSE_CONFIRM_NOTIFY -> {
                log.info("tc notify ENDORSE_CONFIRM_NOTIFY");
                changeOrderHandler(changeTicketConfirmNotify(data))
            }

            TCNotifyEnum.ENDORSE_RESULT_NOTIFY -> {
                log.info("tc notify ENDORSE_RESULT_NOTIFY");
                changeOrderResultHandler(changeTicketResultNotify(data))
            }

            else -> {
                log.error("not support type: {}", body.toJSONString())
                return NotifyResult("ERROR")
            }
        }
        return NotifyResult("SUCCESS")
    }

    override fun cancelOrder(orderBigDto: OrderBigFieldDTO, reason: String): Boolean {
        log.info("TC_Domestic order cancel dto: {}", orderBigDto.toJSONString())
        // 构造取消订单信息
//        val orderBaseVo = orderBigDto.field1.toJSONString().into<OrderBaseVo>()
        val tcResponse = orderBigDto.field2.toJSONString().into<TCOrderBusinessResponse>()
        val request = TCOrderCancelRequest(
            orderSerialNo = tcResponse.orderSerialNo,
            cancelProducts = tcResponse.productItems.map {
                TCOrderCancelProduct(
                    productType = it.productType,
                    cancelProductDetail = TCOrderCancelProductDetail(
                        it.productDetail.into<TCOrderCancelProductDetail>().pnr
                    )
                )
            },
            reason = reason
        )
        val requestId = idConfig.getStrID();
        log.info("tc cancel order request: {}", request.toJSONString())
        val cancelResponse = flightUtil.flightCancelOrder(request, requestId)
        log.info("request: {}, response: {}", request.toJSONString(), cancelResponse.toJSONString())
        return cancelResponse.success
    }

    override fun queryChangeList(req: ChangeShopReq, bigField: OrderBigFieldDTO): Map<String, List<ReShopRsp>> {
        val lang = RpcContext.getServerAttachment().getAttachment("lang");
        val l = StrUtil.split(lang, StrUtil.C_UNDERLINE)
        val locale = Locale(l[0], l[1])
        val productType = "tc.product-type."
        val orderDetail = orderDetail(bigField.orderNo).into<TCOrderDetailQueryResponse>()
        val orderBaseVo = bigField.field1.toString().into<OrderBaseVo>()
        val orderResponse = bigField.field2.toString().into<TCOrderBusinessResponse>()
        val passengerMap = mutableMapOf<String, TCOrderDetailQueryPassenger>()
        val ticketMap = mutableMapOf<String, TCOrderDetailQueryTicket>()
        orderDetail.products.forEach { p ->
            p.passengers.forEach {
                passengerMap[it.firstName + it.lastName] = it
            }
            p.tickets.forEach {
                ticketMap[it.ticketNo] = it
            }
        }
        val request = TCChangeReShopRequest(
            0,
            1,
            req.segments.first().departTime,
            orderResponse.orderSerialNo,
            req.orderNo,
            orderBaseVo.accurate.ruleUnit.segments.first().flightNo,
            ticketMap.keys.mapNotNull {
                if (req.tickets.contains(it)) {
                    val key = (ticketMap[it]?.firstName ?: "") + (ticketMap[it]?.lastName ?: "")
                    TCChangeReShopPassengerItem(passengerMap[key]?.passengerType ?: 1, it)
                } else {
                    null
                }
            }
        )
        val requestId = idConfig.getStrID();
        val changeReShop = flightUtil.changeReShop(request, requestId)
        log.info("req: {}, tc response: {}", req.toJSONString(), changeReShop.toJSONString())
        val result = mutableMapOf<String, List<ReShopRsp>>()
        changeReShop.endorseFlightInfos.forEach {
            var stopTimeTotal = 0
            val prices = mutableListOf<ReShopPriceInfo>()
            val reShop = ReShopRsp(
                listOf(
                    SegmentItem(
                        it.realCarrier,
                        it.flightNo,
                        it.carrier,
                        0,
                        it.flightShare,
                        it.flightNo,
                        it.aircraft,
                        it.aircraftType,
                        it.arrivalAirportCode,
                        it.arrivalTerminal,
                        it.arrivalDateTime,
                        it.departureAirportCode,
                        it.departureTerminal,
                        it.takeOffDateTime,
                        when (it.meal) {
                            0 -> "";
                            1 -> "有餐食";
                            else -> "无餐食"
                        },
                        it.distance,
                        1,
                        it.stopInfoList.map { s ->
                            val min = DateUtil.between(
                                DateUtil.parseDate(s.arrivalDateTime),
                                DateUtil.parseDate(s.departureDateTime),
                                DateUnit.MINUTE
                            )
                            stopTimeTotal += min.toInt()
                            SegmentStopItem(
                                s.stopAirportCode,
                                CommonValFunc.formatIntToDuration(min.toInt()),
                                s.departureDateTime,
                                s.arrivalDateTime,
                            )
                        },
                        CommonValFunc.formatIntToDuration(stopTimeTotal),
                        CommonValFunc.formatIntToDuration(it.duration.toInt()),
                        CommonValFunc.formatIntToDuration(it.duration.toInt() + stopTimeTotal),
                        commonService.getAirportName(it.arrivalAirportCode),
                        commonService.getAirportName(it.departureAirportCode),
                        commonService.getAirline(it.carrier),
                        it.stopInfoList.map { s ->
                            commonService.getAirportName(s.stopAirportCode)
                        },
                        "",
                        it.cabinInfos.first().cabinCode
                    )
                ),
                it.cabinInfos
                    // 无需筛选是否自动计价成功，由同程通知创建改签订单
//                    .filter { c -> c.priceInfo.calcEndorseFee }
                    .map { c ->
                        c.priceInfo.ticketPrice
                        prices.add(
                            ReShopPriceInfo(
                                NumberUtil.roundStr(
                                    NumberUtil.add(
                                        c.priceInfo.upgradeFee,
                                        c.priceInfo.endorseFee,
                                        c.priceInfo.agencyFee
                                    ).toString(),
                                    2
                                ),
                                c.priceInfo.taxDiff,
                                c.priceInfo.ticketPriceDiff,
                                "0",
                                TaxDetail(
                                    c.priceInfo.taxDetail.getOrDefault("yq", "0"),
                                    c.priceInfo.taxDetail.getOrDefault("tax", "0")
                                ),
                                PassengerTypeEnum.tcValueToEnums(c.passengerType),
                                CabinClassEnum.tcValueToEnums(c.cabinClass),
                                c.cabinCode
                            )
                        )
                        ReCabinItem(
                            PassengerTypeEnum.tcValueToEnums(c.passengerType),
                            prices.last(),
                            CabinClassEnum.tcValueToEnums(c.cabinClass),
                            CabinClassEnum.tcValueToEnums(c.cabinClass).display,
                            c.cabinCode,
                            when (c.seatCount) {
                                "A" -> 10
                                in "1".."9" -> c.seatCount.toInt()
                                else -> 0
                            }
                        )
                    },
                ProductInfo(
                    "0",
                    ProductTagEnum.TC_RE_SHOP_PRODUCT,
                    ProductTagEnum.TC_RE_SHOP_PRODUCT.display,
                    provider(),
                    it.data,
                    getI18nMsg("${productType}0", locale)
                ),
                0,
                CommonValFunc.formatIntToDuration(it.duration.toInt() + stopTimeTotal),
                prices
            )
            if (result.containsKey(it.flightNo)) {
                result[it.flightNo] = result[it.flightNo]!! + listOf(reShop)
            } else {
                result[it.flightNo] = listOf(reShop)
            }
        }
        log.info("change shop result: {}", result.toJSONString())
        return result
    }

    override fun applyChange(req: ChangeApplyReq): Boolean {
        log.info("apply change req: {}", req.toJSONString())
        val queryWrapper = QueryWrapper<TicketChangeOrder>()
        queryWrapper.eq("order_no", req.orderNo)
            .eq("out_order_no", "")
            .eq("source", provider())
        if (ticketChangeOrderMapper.exists(queryWrapper)) {
            log.info("改签流程进行中,请勿重复提交: {}", req.orderNo)
            throw BusinessException("改签流程进行中,请勿重复提交")
        }

        val orderDetail = orderDetail(req.orderNo).into<TCOrderDetailQueryResponse>()
        val ticketChangeOrder = TicketChangeOrder(
            orderNo = req.orderNo,
            outOrderNo = orderDetail.products.first().orderSerialNo,
            changeNo = "",
            outChangeNo = "",
            source = provider()
        )
        log.info("save change order result: {}", ticketChangeOrderMapper.insert(ticketChangeOrder) > 0)
        val passengerMap = mutableMapOf<String, TCOrderDetailQueryPassenger>()
        val ticketMap = mutableMapOf<String, TCOrderDetailQueryTicket>()
        orderDetail.products.forEach {
            it.passengers.forEach { passenger ->
                val key = "${passenger.firstName}${passenger.lastName}"
                passengerMap[key] = passenger
            }
            it.tickets.forEach { ticket ->
                ticketMap[ticket.ticketNo] = ticket
            }
        }
        val request = TCEndorseApplyRequest(
            1,
            orderDetail.products.first().orderSerialNo,
            req.orderNo,
            orderDetail.products.first().segments.first().operatingFlightNo,
            orderDetail.products.first().segments.map {
                TCEndorseApplyFlightInfo(
                    it.segmentType,
                    it.sequence,
                    it.flightNo,
                    it.airline,
                    it.cabinCode,
                    if (it.flightShare) {
                        1
                    } else {
                        0
                    },
                    it.depAirport,
                    it.arrAirport,
                    it.depTime,
                    it.arrTime,
                )
            },
            req.changeSegments.map {
                TCEndorseApplyFlightInfo(
                    1,
                    it.segmentIndex,
                    it.flightNo,
                    it.airline,
                    it.cabinCode,
                    if (it.flightShare) {
                        1
                    } else {
                        0
                    },
                    it.departAirport,
                    it.arriveAirport,
                    it.departDateTime,
                    it.arriveDateTime,
                )
            },
            req.passengers.map {
                val t = ticketMap[it]?.ticketNo ?: ""
                val name = ticketMap[it]?.firstName + ticketMap[it]?.lastName
                val passengerType = passengerMap[name]?.passengerType ?: 0
                TCEndorseApplyPassengerInfo(
                    t,
                    "",
                    name,
                    passengerType,
                )
            },
            null,
            "",
            req.fileKeys,
            0,
            1,
            req.productInfo.orderInfo
        )
        log.info("apply change ticket request: {}", request.toJSONString())
        val requestId = idConfig.getStrID();
        val applyResponse = flightUtil.flightEndorseApply(request, requestId)
        log.info("apply change ticket response: {}", applyResponse.toJSONString())
        if (applyResponse.endorseSerialNo.isBlank()) {
            log.warn("apply change ticket fail: {}", req.toJSONString())
            return false;
        }
        log.info("save ticket change order: {}", ticketChangeOrder.toJSONString())
        return ticketChangeOrderMapper.update(
            ticketChangeOrder.copy(outChangeNo = applyResponse.endorseSerialNo, updateTime = LocalDateTime.now()),
            QueryWrapper<TicketChangeOrder>()
                .eq("order_no", req.orderNo)
                .eq("out_order_no", orderDetail.products.first().orderSerialNo)
                .eq("source", provider())
        ) > 0;
    }

    override fun changeOrderFlush(orderNo: String): ResponseResult<ChangeOrderFlushResult?> {
        val queryWrapper = QueryWrapper<TicketChangeOrder>()
        queryWrapper.eq("order_no", orderNo)
        queryWrapper.eq("source", provider())
        val ticketChangeOrder = ticketChangeOrderMapper.selectOne(queryWrapper)
        val orderFinance = orderBaseService.queryOrderFinancePublic(ticketChangeOrder.changeNo)
        if (ticketChangeOrder.changeNo.isNotBlank()) {
            val flag = orderFinance.estimatedPrice > 0
            if (!flag) {
                val response = changeOrderPay(ticketChangeOrder.changeNo)
                log.info("pay order response: {}", response.toJSONString())
                if (!response.isSuccess) {
                    return ResponseResult.fail(500, "请联系客服处理!!!")
                }
            }
            return ResponseResult.ok(ChangeOrderFlushResult(ticketChangeOrder.changeNo, flag));
        }
        return ResponseResult.fail(306, "正在加急处理中...")
    }

    override fun changeOrderPay(orderNo: String): ResponseResult<Boolean> {
        try {
            val queryWrapper = QueryWrapper<TicketChangeOrder>()
            queryWrapper.eq("change_no", orderNo)
            queryWrapper.eq("source", provider())
            val ticketChangeOrder = ticketChangeOrderMapper.selectOne(queryWrapper)
            val orderBigField = orderBaseService.queryOrderBigFieldPublic(orderNo)
            val notifyBody = orderBigField.field1.toString().into<TCEndorseConfirmNotify>()
            val requestId = idConfig.getStrID()
            val request = TCEndorsePayRequest(ticketChangeOrder.outChangeNo, notifyBody.totalPrice)
            log.info("changeOrderPay request: {}", request.toJSONString())
            val response = flightUtil.flightEndorsePay(request, requestId)
            log.info("changeOrderPay response: {}", response.toJSONString())
            return ResponseResult.ok(true)
        } catch (e: Exception) {
            log.error("changeOrderPay fail: {}", orderNo, e)
        }
        return ResponseResult.fail(500, "改签支付失败")
    }

    override fun changeOrderDetail(bigField: OrderBigFieldDTO): FlightOrderDetail.Detail {
        val result = FlightOrderDetail.Detail()
        val notify = bigField.field1.toJSONString().into<TCEndorseConfirmNotify>()
        val req = TCQueryChangeOrderDetailRequest(notify.endorseSerialNo)
        val reqId = idConfig.getStrID()
        log.info("query TC change order req: {}", req.toJSONString())
        val changeOrderDetail = flightUtil.changeOrderDetail(req, reqId)
        log.info("query TC change order detail: {}", changeOrderDetail.toJSONString())
        val segments = changeOrderDetail.data.segmentInfo.multiSegmentChangeFlightInfo
        val ticketInfos = changeOrderDetail.data.ticketInfos
        result.segments = segments.map {
            SegmentItem(
                segmentIndex = it.segmentIndex,
                departAirport = it.departureAirportCode,
                arriveAirport = it.arriveAirportCode,
                departAirportTerm = it.departureTerminal ?: "",
                arriveAirportTerm = it.arriveTerminal ?: "",
                departDateTime = it.gmtTakeOffTime,
                arriveDateTime = it.gmtArriveTime,
                flightNo = it.flightNo,
                airline = it.airways,
                cabinCode = it.cabinCode,
                departAirportDisplay = commonService.getAirportName(it.departureAirportCode),
                arriveAirportDisplay = commonService.getAirportName(it.arriveAirportCode),
                airlineDisplay = commonService.getAirline(it.airways)
            )
        }
        log.info("init segments success")
        result.guests = FlightOrderDetail.Guest(
            "",
            ticketInfos.map {
                val item = it.newTicket
                log.info("guest ticket: {}", item.toJSONString())
                FlightOrderDetail.Passenger(
                    "${item.firstName}${item.lastName}",
                    "${PassengerTypeEnum.tcValueToEnums(item.passengerType)}",
                    "${GenderEnum.tcValueToEnum(item.passengerGende)}",
                    "${CreditTypeEnum.tcCreateToEnum(item.creditType)}",
                    DesensitizedUtil.idCardNum(item.creditNo, 1, 2),
                    StrUtil.join(StrUtil.COMMA, item.ticketNos.map { t -> t.ticketNo })
                )
            }.toList()
        )
        log.info("init guests success")
        result.ruleInfo = AccRuleItem(
            segments = result.segments,
        )
        log.info("change result: {}", result.toJSONString())
        return result
    }

    private fun changeTicketConfirmNotify(body: TCNotifyBody): TCEndorseConfirmNotify {
        val result = flightUtil.uncompress(body.businessRequest)
        log.info("changeTicketConfirmNotify uncompress data: $result")
        return result.into<TCEndorseConfirmNotify>()
    }

    private fun changeTicketResultNotify(body: TCNotifyBody): TCEndorseResultNotify {
        val result = flightUtil.uncompress(body.businessRequest)
        log.info("changeTicketResultNotify uncompress data: $result")
        return result.into<TCEndorseResultNotify>()
    }

    private fun refundFeeNotify(body: TCNotifyBody): TCRefundFeeNotify {
        val result = flightUtil.uncompress(body.businessRequest)
        log.info("refundFeeNotify uncompress data: $result")
        return result.into<TCRefundFeeNotify>()
    }

    private fun refundResultNotify(body: TCNotifyBody): TCRefundResultNotify {
        val result = flightUtil.uncompress(body.businessRequest)
        log.info("refundResultNotify uncompress data: $result")
        return result.into<TCRefundResultNotify>()
    }

    private fun tcTicketNotify(body: TCNotifyBody): TCTicketNotify {
        val result = flightUtil.uncompress(body.businessRequest)
        log.info("tcTicketNotify uncompress data: $result")
        return result.into<TCTicketNotify>()
    }

    private fun tcPayNotify(body: TCNotifyBody): TCPayNotify {
        val result = flightUtil.uncompress(body.businessRequest)
        log.info("tcPayNotify uncompress data: $result")
        return result.into<TCPayNotify>()
    }

    private fun notPayCancelOrder(req: TCOrderCancelRequest): Boolean {
        val requestId = idConfig.getStrID()
        log.info("tc cancel order req: {}", req.toJSONString())
        try {
            val response = flightUtil.flightCancelOrder(req, requestId)
            log.info("tc cancel order rsp: {}", response.toJSONString())
            return response.success
        } catch (e: Exception) {
            log.error("tc cancel order req fail: {}", req.toJSONString())
            log.error("", e)
        }
        return false
    }

    private fun changeOrderResultHandler(notify: TCEndorseResultNotify) {
        // 收款-成功 - 完成
        // 收款-失败 - 退款
        // 退款-成功 - 退款
        // 退款-失败 - 完成
        val queryWrapper = QueryWrapper<TicketChangeOrder>()
        queryWrapper.eq("out_change_no", notify.endorseSerialNo)
        queryWrapper.eq("source", provider())
        val ticketChangeOrder = ticketChangeOrderMapper.selectOne(queryWrapper)
        log.info(
            "changeOrderResultHandler: {}, notify value: {}",
            ticketChangeOrder.toJSONString(),
            notify.toJSONString()
        )
        orderStateService.flightChangeTicketHandler(notify, ticketChangeOrder)
    }

    private fun changeOrderHandler(notify: TCEndorseConfirmNotify) {
        val queryWrapper = QueryWrapper<TicketChangeOrder>()
        queryWrapper.eq("out_change_no", notify.endorseSerialNo)
            .eq("source", provider())
            .eq("order_no", notify.purchaserSerialNo)
            .eq("change_no", "")

        val ticketChangeOrder = ticketChangeOrderMapper.selectOne(queryWrapper)
        val orderMaster = orderBaseService.queryOrderMasterPublic(ticketChangeOrder.orderNo)
        if (ticketChangeOrder == null) {
            log.error("changeOrderHandler not found change order: {}", notify.toJSONString())
        }
        val estimatedPrice = commonService.getMovePoint2(notify.totalPrice)
        val masterVo = CreateOrderMasterVo(
            OrderTypeEnum.CHANGE.name,
            OrderBusinessTypeEnum.FLIGHT.name,
            orderMaster.userSign,
            orderMaster.userType,
            ""
        )
        // 财务信息
        val financeVo = CreateOrderFinanceVo(
            OrderSettleModeEnums.electronic.name,
            estimatedPrice,
            estimatedPrice,
            estimatedPrice,
            0,
            orderMaster.userSign,
            PaymentChannelEnums.wechat.name,
            PaymentFromEnums.application.name,
            DateUtil.offset(Date(), DateField.SECOND, flightConfig.system().payTimeoutSeconds),
            CollectionAgencyEnums.YEE_PAY.name,
            CurrencyEnums.CNY.name
        )

        // 商品信息
        val productVo = CreateOrderProductVo(
            notify.endorseSerialNo,
            "TC Change Service",
            3,
            notify.priceInfos.size,
            estimatedPrice,
            estimatedPrice,
            0,
            0,
            "",
            notify.priceInfos.toJSONString(),
            ""
        )
        // 附加数据
        val fieldVo = OrderBigFiledVo(
            ThirdServiceTypeEnum.flight_tc_change,
            JSONObject.from(notify),
            null,
            null,
        )
        val sonOrder = CreateSonOrderMasterVo(
            orderMaster.orderNo,
            CreateOrderVo(
                masterVo,
                financeVo,
                productVo,
                fieldVo,
                notify.endorseSerialNo,
                "",
                "",
                emptyList(),
            )
        )
        log.info("son order json: {}", sonOrder.toJSONString())
        val createOrderRpcResponse = orderBaseService.createOrderBaseV2(sonOrder)
        log.info("create son order response: {}", createOrderRpcResponse.toJSONString())
        if (createOrderRpcResponse.isSuccess) {
            val flag = ticketChangeOrderMapper.updateById(
                ticketChangeOrder.copy(
                    changeNo = createOrderRpcResponse.data.toString(),
                    updateTime = LocalDateTime.now()
                )
            )
            log.info("update ticket order state: {}", flag > 0)
        } else {
            log.error("rpc create order ticket order fail: {}", createOrderRpcResponse.toJSONString())
        }
    }

    private fun initCreateOrderVo(
        req: CreateOrderReq,
        orderRequest: TCOrderRequest,
        accItem: AccRuleItem,
        orderResponse: TCOrderBusinessResponse
    ): CreateOrderVo {
        val masterVo = CreateOrderMasterVo(
            OrderTypeEnum.NORMAL.name,
            OrderBusinessTypeEnum.FLIGHT.name,
            req.userId,
            OrderUserTypeEnum.admin.name,
            ""
        )
        var totalPrice = BigDecimal.ZERO
        var totalTax = BigDecimal.ZERO
        var totalPriceNotMarkUp = BigDecimal.ZERO
        orderRequest.products.forEach {
            it.productDetail.prices.forEach { p ->
                totalTax = NumberUtil.add(totalTax.toString(), p.tax);
                totalPrice = NumberUtil.add(commonService.getMarkUpPrice(p.price), totalPrice);
                totalPriceNotMarkUp = NumberUtil.add(totalPriceNotMarkUp.toString(), p.price);
            }
        }
        // 财务信息
        val price = NumberUtil.roundStr(
            NumberUtil.add(totalPrice, totalTax).toString(), 2
        )
        log.info("uid: {}, tcPay price yuan: {}, not markup price: {}", req.userId, price, totalPriceNotMarkUp)
        val estimatedPrice = commonService.getMovePoint2(price)
        log.info("uid: {}, tcPay price fen: {}", req.userId, estimatedPrice)
        val beforeDiscountPrice = commonService.getMovePoint2(
            NumberUtil.roundStr(
                NumberUtil.add(totalPriceNotMarkUp, totalTax).toString(), 2
            )
        )
        val financeVo = CreateOrderFinanceVo(
            OrderSettleModeEnums.electronic.name,
            estimatedPrice,
            beforeDiscountPrice,
            estimatedPrice,
            beforeDiscountPrice - estimatedPrice,
            req.userId,
            PaymentChannelEnums.wechat.name,
            PaymentFromEnums.application.name,
            DateUtil.offset(Date(), DateField.SECOND, flightConfig.system().payTimeoutSeconds),
            CollectionAgencyEnums.YEE_PAY.name,
            CurrencyEnums.CNY.name
        )

        // 商品信息
        val productVo = CreateOrderProductVo(
            orderResponse.orderSerialNo,
            "FLIGHT",
            1,
            orderRequest.products.size,
            estimatedPrice,
            beforeDiscountPrice,
            0,
            0,
            "",
            "{}",
            ""
        )
        val vo = OrderBaseVo(
            price, orderResponse.orderSerialNo, OrderReqBase(
                totalTax.toString(),
                totalPrice.toString(),
                req,
                accItem
            ),
            orderResponse.productItems
        )
        // 附加数据
        val fieldVo = OrderBigFiledVo(
            ThirdServiceTypeEnum.flight,
            JSONObject.from(vo),
            JSONObject.from(orderResponse),
            null,
        )

        return CreateOrderVo(
            masterVo,
            financeVo,
            productVo,
            fieldVo,
            orderResponse.orderSerialNo,
            req.insuranceSequenceNo,
            req.sessionId,
            emptyList(),
        )
    }

    private fun initTCOrderRequest(
        passengers: List<PassengerInfo>,
        accItem: AccRuleItem,
        req: CreateOrderReq,
    ): TCOrderRequest {
        var priceTotal = BigDecimal.ZERO
        val priceMap = accItem.prices.associateBy { it.passengerType }
        val request = TCOrderRequest(
            buisinessLineType = 0,
            contact = TCOrderContact(
                req.contactInfo.name,
                null,
                null,
                req.contactInfo.email,
                req.contactInfo.mobile,
                null
            ),
            products = listOf(
                TCOrderProduct(
                    accItem.productInfo.orderInfo,
                    "0",
                    productType = ProductOrderTypeEnum.FLIGHT.tcValue,
                    productDetail = TCOrderProductDetail(
                        officeNo = "",
                        passengers = passengers.map {
                            TCOrderPassenger(
                                "",
                                it.firstName + it.lastName,
                                it.gender.tcValue,
                                it.type.tcIntValue(),
                                it.creditType.tcCreate(),
                                it.notion,
                                it.notionCode,
                                it.creditNo,
                                it.creditValidDate,
                                it.creditIssueNotion,
                                it.birthday,
                                it.linkPhone
                            )
                        },
                        prices = passengers.map {
                            val price = TCOrderPrice(
                                priceMap[it.type]?.price ?: "0",
                                priceMap[it.type]?.taxTotal ?: "0",
                                it.type.tcIntValue()
                            )
                            val itemPriceTotal = NumberUtil.add(price.price, price.tax)
                            priceTotal = NumberUtil.add(itemPriceTotal, priceTotal)
                            price
                        },
                        bigPnrCode = "",
                    )
                )
            ).map {
                it.copy(price = NumberUtil.roundStr(priceTotal.toString(), 2))
            },
            totalPrice = NumberUtil.roundStr(priceTotal.toString(), 2),
        )
        return request
    }

    private fun getI18nMsg(key: String, locale: Locale): String {
        val v = commonService.getI18nMsg(key, locale)
        if (StrUtil.isBlank(v)) {
            return commonService.getI18nMsg("all.product-type.24", locale)
        }
        return v
    }

    private fun cacheTCAccurate(value: AccRuleItem) {
        commonService.cacheAccRuleItem(value.productInfo.orderInfo, value)
    }

    private fun getCacheValue(data: String): AccRuleItem {
        return commonService.getCacheAccRuleItem(data)
    }
}