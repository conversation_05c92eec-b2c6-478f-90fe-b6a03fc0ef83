package com.somytrip.provider.flight

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo
import org.mybatis.spring.annotation.MapperScan
import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.scheduling.annotation.EnableScheduling

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-20 9:42
 */

@SpringBootApplication
@MapperScan("com.somytrip.provider.flight.mapper")
@EnableDubbo
@EnableScheduling
open class DubboProviderFlightApplication {
}

fun main(args: Array<String>) {
    SpringApplication.run(DubboProviderFlightApplication::class.java, *args)
}