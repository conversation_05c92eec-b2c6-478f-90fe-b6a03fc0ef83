package com.somytrip.provider.flight.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.annotation.Configuration

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-10-25 10:42
 */
@Configuration
@ConfigurationProperties(prefix = "cache")
@RefreshScope
open class CacheConfig {
    //    private val log = LoggerFactory.getLogger(CacheConfig::class.java)
    open lateinit var timeout: Timeout

    data class Timeout(
        val shopSeconds: Long = 0,
        val accSeconds: Long = 0,
        val airlineSeconds: Long = 0,
        val airportCityMapSeconds: Long = 0,
        val airportMapSeconds: Long = 0,
    )

    fun timeout() = timeout
}