package com.somytrip.provider.flight.config

import com.somytrip.common.util.MinioUtil
import io.minio.MinioClient
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-10-25 10:42
 */
@Configuration
@ConfigurationProperties(prefix = "minio")
@RefreshScope
open class MinioConfig {
    private val log = LoggerFactory.getLogger(MinioConfig::class.java)
    open lateinit var config: Config

    data class Config(
        val endpoint: String = "",
        val accessKey: String = "",
        val secretKey: String = "",
    )

    private fun getMinioClient(): MinioClient {
        return MinioClient.Builder()
            .endpoint(config.endpoint)
            .credentials(config.accessKey, config.secretKey)
            .build()
    }

    @Bean("minioUtil")
    open fun minioUtil(): MinioUtil {
        return MinioUtil(getMinioClient())
    }
}