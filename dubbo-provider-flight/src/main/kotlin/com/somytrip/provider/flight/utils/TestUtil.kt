package com.somytrip.provider.flight.utils

import cn.hutool.core.io.FileUtil
import cn.hutool.core.util.ZipUtil

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-12-04 10:12
 */
object TestUtil {
    @JvmStatic
    fun main(args: Array<String>) {
        val data =
            FileUtil.readUtf8String("D:\\code\\somytrip\\dubbo-somytrip\\dubbo-provider-flight\\src\\main\\kotlin\\com\\somytrip\\provider\\flight\\utils\\xx.json")
        val res = ZipUtil.gzip(data, "UTF-8")
        FileUtil.writeBytes(
            res,
            "D:\\code\\somytrip\\dubbo-somytrip\\dubbo-provider-flight\\src\\main\\kotlin\\com\\somytrip\\provider\\flight\\utils\\xx.txt"
        )
    }
}