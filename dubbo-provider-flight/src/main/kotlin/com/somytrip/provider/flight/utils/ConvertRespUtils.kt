package com.somytrip.provider.flight.utils

import cn.hutool.core.date.DateField
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.NumberUtil
import com.alibaba.fastjson2.JSONObject
import com.alibaba.fastjson2.into
import com.somytrip.entity.enums.order.OrderBusinessTypeEnum
import com.somytrip.entity.enums.order.OrderSettleModeEnums
import com.somytrip.entity.enums.order.OrderTypeEnum
import com.somytrip.entity.enums.order.OrderUserTypeEnum
import com.somytrip.entity.enums.pay.CollectionAgencyEnums
import com.somytrip.entity.enums.pay.CurrencyEnums
import com.somytrip.entity.enums.pay.PaymentChannelEnums
import com.somytrip.entity.enums.pay.PaymentFromEnums
import com.somytrip.entity.enums.thirdparty.ThirdServiceTypeEnum
import com.somytrip.entity.vo.CreateOrderFinanceVo
import com.somytrip.entity.vo.flight.*
import com.somytrip.entity.vo.flight.QueryFlightShoppingRequest.Passenger
import com.somytrip.entity.vo.order.CreateOrderMasterVo
import com.somytrip.entity.vo.order.CreateOrderProductVo
import com.somytrip.entity.vo.order.CreateOrderVo
import com.somytrip.entity.vo.order.OrderBigFiledVo
import com.somytrip.model.flight.common.ProductInfo
import com.somytrip.model.flight.enums.*
import com.somytrip.model.flight.tc.*
import com.somytrip.model.po.*
import com.somytrip.util.FlightTCUtils
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.util.stream.IntStream

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-02-27 15:36
 */
object ConvertRespUtils {
    private val log = LoggerFactory.getLogger(this::class.java)

    fun toCreateOrderVo(
        userId: String,
        repPo: TCOrderResponsePo,
        raisePrice: (x: String) -> Int,
        movePointRight: (x: String) -> Int
    ): CreateOrderVo {
        // 创建主单信息
        val masterVo = CreateOrderMasterVo(
            OrderTypeEnum.NORMAL.name,
            OrderBusinessTypeEnum.FLIGHT.name,
            userId,
            OrderUserTypeEnum.admin.name,
            ""
        )
        // 财务信息
        // 未加税费金额
        val flightPrice =
            NumberUtil.add(raisePrice(repPo.accurate.totalPrice).toString(), repPo.accurate.totalTax).toString()
        log.info("userID: $userId need tcPay price point 2: $flightPrice")
        val estimatedPrice = movePointRight(NumberUtil.roundStr(flightPrice, 2))
        log.info("userID: $userId need tcPay price point 0: $estimatedPrice")
        val beforeDiscountPrice = movePointRight(repPo.totalPrice)
        val financeVo = CreateOrderFinanceVo(
            OrderSettleModeEnums.electronic.name,
            estimatedPrice,
            beforeDiscountPrice,
            estimatedPrice,
            beforeDiscountPrice - estimatedPrice,
            userId,
            PaymentChannelEnums.wechat.name,
            PaymentFromEnums.application.name,
            DateUtil.date().offset(DateField.MINUTE, 30),
            CollectionAgencyEnums.YEE_PAY.name,
            CurrencyEnums.CNY.name
        )
        // 商品信息
        val productVo = CreateOrderProductVo(
            repPo.orderSerialNo,
            "FLIGHT",
            1,
            repPo.products.size,
            estimatedPrice,
            beforeDiscountPrice,
            0,
            0,
            "",
            "{}",
            ""
        )
        // 附加数据
        val fieldVo = OrderBigFiledVo(
            ThirdServiceTypeEnum.flight,
            JSONObject.from(repPo),
            null,
            null,
        )
        return CreateOrderVo(
            masterVo,
            financeVo, productVo,
            fieldVo, repPo.orderSerialNo,
            "",
            "",
            emptyList()
        )
    }

    fun convertVoToTCOrderRequest(
        acc: TCAccurateSearchItem,
        vo: CreateFlightOrderVo,
        func: (x: Double) -> Int
    ): TCOrderRequestPo {
        var totalPrice = BigDecimal.ZERO;
        var totalTax = BigDecimal.ZERO;
        val passengerMaps: MutableMap<String, MutableList<TCOrderPassenger>> = mutableMapOf()
        val priceMaps: MutableMap<String, MutableList<TCOrderPrice>> = mutableMapOf()
        vo.passengers.forEach {
            totalPrice = totalPrice.add(BigDecimal.valueOf(acc.prices.getValue(it.type.tcValue()).priceB))
            totalTax = totalTax.add(BigDecimal.valueOf(acc.prices.getValue(it.type.tcValue()).taxB))
            if (!passengerMaps.containsKey(it.type.tcValue())) {
                passengerMaps[it.type.tcValue()] = mutableListOf()
                priceMaps[it.type.tcValue()] = mutableListOf()
            }
            passengerMaps[it.type.tcValue()]?.add(
                TCOrderPassenger(
                    if (vo.sourceType == 0) "" else it.lastName,
                    if (vo.sourceType == 0) it.firstName + it.lastName else it.firstName,
                    it.gender.tcValue,
                    it.type.tcIntValue(),
                    it.creditType.tcCreate(),
                    it.notion,
                    it.notionCode,
                    it.creditNo,
                    it.creditValidDate,
                    it.creditIssueNotion,
                    it.birthday,
                    it.linkPhone
                )
            )
            priceMaps[it.type.tcValue()]?.add(
                TCOrderPrice(
                    acc.prices.getValue(it.type.tcValue()).priceB.toString(),
                    acc.prices.getValue(it.type.tcValue()).tax, it.type.tcIntValue()
                )
            )
        }
        val total = NumberUtil.roundStr(NumberUtil.add(totalPrice + totalTax).toString(), 2)
        return TCOrderRequestPo(
            TCOrderRequest(
                buisinessLineType = vo.sourceType,
                contact = TCOrderContact(
                    vo.contactInfo.name,
                    null,
                    null,
                    vo.contactInfo.email,
                    vo.contactInfo.mobile,
                    null
                ),

                products = passengerMaps.map {
                    TCOrderProduct(
                        acc.data,
                        getTotalPrice(priceMaps.getValue(it.key)),
                        productType = ProductOrderTypeEnum.FLIGHT.tcValue,
                        productDetail = TCOrderProductDetail(
                            officeNo = null,
                            passengers = it.value,
                            prices = priceMaps.getValue(it.key),
                            bigPnrCode = null,
                        )
                    )
                },
                totalPrice = total,
            ),
            ruleUnit = QueryFlightAccurateResponse.FlightUnitRuleItem(acc, func),
            totalPrice = totalPrice.toString(),
            totalTax = totalTax.toString()
        )
    }

    private fun getTotalPrice(prices: List<TCOrderPrice>): String {
        var total = BigDecimal.ZERO
        prices.forEach {
            total = NumberUtil.add(NumberUtil.add(it.tax, it.price), total)
        }
        return NumberUtil.round(total, 2).toString()
    }

    fun convertVoToTCAccurateSearchRequest(request: QueryFlightAccurateRequest): TCAccurateSearchRequest {
        return TCAccurateSearchRequest(
            businessLineType = request.sourceType,
            tripType = request.tripType + 1,
            productCodes = request.productInfos?.map { it.productCode.toInt() },
            changePnrCode = 0,
            adultNum = request.adultNum,
            chdNum = request.chdNum,
            infNum = request.infNum,
            mainAirline = request.mainAirline,
            recommend = request.recommend.toString(),
            segments = request.segments.map {
                TCAccurateSearchSegment(
                    segmentType = it.tripType + 1,
                    segmentIndex = it.segmentIndex,
                    depCity = null,
                    depAirport = it.depAirport,
                    depTerminal = null,
                    depDate = it.depDate,
                    arrCity = null,
                    arrAirport = it.arrAirport,
                    arrTerminal = null,
                    arrDate = it.arrDate,
                    airline = it.airline,
                    aircraftCode = null,
                    flightNo = it.flightNo,
                    flightShare = it.isFlightShare,
                    operatingAirline = it.operationAirline,
                    operatingFlightNo = it.operationFlightNo,
                    cabinClass = null,
                    cabinCode = it.cabinCode
                )
            }
        )
    }

    fun convertVoToTCShoppingSearchRequest(request: QueryFlightShoppingRequest): TCShoppingSearchRequest {
        val shoppingSearchRequest: TCShoppingSearchRequest
        val switchKey = "${request.tripType}${request.sourceType}"
        when (switchKey) {
            "00", "01", "11" ->                 // 往返
                shoppingSearchRequest = TCShoppingSearchRequest(
                    (request.tripType + 1).toString(),
                    request.sourceType,
                    null,
                    null,
                    request.passengers.stream().map { passenger: Passenger ->
                        TCShoppingSearchPassenger(
                            passenger.num,
                            passenger.type.tcValue()
                        )
                    }.toList(),
                    IntStream.range(0, request.segments.size).mapToObj { index: Int ->
                        TCShoppingSearchSegmentItem(
                            index + 1,
                            request.segments[index].departCode,
                            null,
                            request.segments[index].arriveCode,
                            null,
                            request.segments[index].departTime
                        )
                    }.toList(),
                    null
                )

            "10" -> shoppingSearchRequest = TCShoppingSearchRequest(
                (request.tripType).toString(),
                request.sourceType,
                null,
                null,
                request.passengers.stream().map { passenger: Passenger ->
                    TCShoppingSearchPassenger(
                        passenger.num,
                        passenger.type.tcValue()
                    )
                }.toList(),
                IntStream.range(0, request.segments.size).mapToObj { index: Int ->
                    TCShoppingSearchSegmentItem(
                        index + 1,
                        request.segments[index].departCode,
                        null,
                        request.segments[index].arriveCode,
                        null,
                        request.segments[index].departTime
                    )
                }.toList(),
                null
            )

            else -> shoppingSearchRequest = "{}".into<TCShoppingSearchRequest>()
        }
        log.info("convertVoToTCShoppingSearchRequest: {}", JSONObject.from(shoppingSearchRequest).toString())
        return shoppingSearchRequest
    }


    fun convertQueryShoppingResponseV2(
        resp: TCShoppingBusinessResponse,
        fn: (x: Double) -> Int,
        req: QueryFlightShoppingRequest,
        fnAirport: (x: String) -> String,
        fnAirline: (x: String) -> AirLineCodeVo,
        flightSource: ProviderSourceEnum
    ): Map<String, MutableList<QueryShoppingResponse>> {
        val productList = resp.productList
        val flightMap = mutableMapOf<String, MutableList<QueryShoppingResponse>>()
        for (item in productList) {
            val key = getSegmentItemKey(item)
            if (!checkScreen(req.screenCondition, item)) {
                continue
            }
            if (!flightMap.containsKey(key)) {
                flightMap[key] = mutableListOf()
            }
            flightMap[key]?.add(
                itemToQueryShoppingResponse(
                    item,
                    fn,
                    req.sourceType,
                    fnAirport,
                    fnAirline,
                    flightSource
                )
            )
        }
        if (req.screenCondition != null && req.screenCondition.sort != null) {
            val customPriceComparator = compareBy<QueryShoppingResponse> { queryShoppingResponse ->
                queryShoppingResponse.prices.minBy { it.price }
            }
            val customDepTimeComparator = compareBy<QueryShoppingResponse> { queryShoppingResponse ->
                queryShoppingResponse.segments.minBy { segment -> segment.departDateTime }.departDateTime
            };
            val comparator = when (req.screenCondition.sort) {
                SortEnum.PRICE_HIGH_LOW -> compareByDescending {
                    it.value.sortWith(customPriceComparator)
                    it.value.first().prices.first().price
                }

                SortEnum.START_LOW_HIGH -> compareBy {
                    it.value.sortWith(customDepTimeComparator)
                    it.value.first().segments.first().departDateTime
                }

                SortEnum.START_HIGH_LOW -> compareByDescending {
                    it.value.sortWith(customDepTimeComparator.reversed())
                    it.value.first().segments.first().departDateTime
                }

                else -> compareBy<Map.Entry<String, MutableList<QueryShoppingResponse>>> {
                    it.value.sortWith(customPriceComparator)
                    it.value.first().prices.first().price
                }
            }
            return flightMap.entries.sortedWith(comparator).associateBy({ it.key }, { it.value })
        }
        return flightMap;
    }

    private fun isTimeInRange(dateTimeStr: String, timeRangeStr: String): Boolean {
        val time = dateTimeStr.split(":").let { it[0].toInt() * 60 + it[1].toInt() }
        val (startStr, endStr) = timeRangeStr.split("-")
        val startTime = startStr.split(":").let { it[0].toInt() * 60 + it[1].toInt() }
        val endTime = endStr.split(":").let { it[0].toInt() * 60 + it[1].toInt() }
        return time in startTime..endTime
    }

    private fun checkScreen(rules: QueryFlightShoppingRequest.ScreenCondition?, item: TCShoppingProductItem): Boolean {
        var flag = true;
        if (rules == null) {
            return true
        }
        val flagStyle = !rules.stylePreference.isNullOrEmpty()
        val flagTime = !rules.timeRange.isNullOrEmpty()
        val flagCabin = !rules.cabin.isNullOrEmpty()
        val flagAirline = !rules.airline.isNullOrEmpty()
        val airline: MutableList<Boolean> = mutableListOf()
        for (s in item.segments) {
            if (flagStyle) {
                // 校验风格偏好
                for (style in rules.stylePreference) {
                    if (StylePreferenceEnum.NOT_STOP == style) {
                        flag = s.stops.isNullOrEmpty()
                    } else if (StylePreferenceEnum.NOT_SHARE == style) {
                        flag = s.flightShare
                    }
                    if (flag) {
                        continue
                    }
                    return false
                }
            }
            if (flagTime) {
                // 校验时间
                for (t in rules.timeRange) {
                    flag = isTimeInRange(
                        s.depDateTime.substring(s.depDateTime.length - 8, s.depDateTime.length - 3),
                        t.display
                    )
                    if (flag) {
                        continue
                    }
                    return false;
                }
            }

            if (flagAirline) {
                // 航空公司
                airline.add(rules.airline.any { it == s.marketingAirline })
            }
        }
        if (airline.isNotEmpty()) {
            flag = airline.any { it }
            if (!flag) {
                return false;
            }
        }
        // 校验舱等
        if (flagCabin) {
            val cabin: String = rules.cabin.joinToString(",") { it.tcValue }
            flag = item.cabins.map { it.cabinClass }.any { c -> c in cabin }
        }
        return flag
    }


    fun itemToQueryShoppingResponse(
        item: TCShoppingProductItem,
        fn: (x: Double) -> Int,
        sourceType: Int,
        fnAirport: (x: String) -> String,
        fnAirline: (x: String) -> AirLineCodeVo,
        flightSource: ProviderSourceEnum
    ): QueryShoppingResponse {
        val transitTime = arrayOf(item.segments.first().depDateTime, item.segments.first().depDateTime, "0", "0")
        return QueryShoppingResponse(
            item.prices.map { p ->
                QueryShoppingPriceItem(
                    PassengerTypeEnum.tcValueToEnums(p.value.passengerType),
                    p.value.taxB.toString(),
                    fn(p.value.priceB),
                    QueryShoppingPriceItemTaxDetail(
                        p.value.taxDetail.yqB.toString(),
                        p.value.taxDetail.taxB.toString()
                    )
                )
            },
            item.segments.map { s ->
                QueryShoppingSegmentItem(
                    s,
                    fnAirport(s.arrAirport),
                    fnAirport(s.depAirport),
                    fnAirline(s.marketingAirline),
                    fnAirport,
                    transitTime
                )
            },
            item.cabins.map { c ->
                QueryShoppingCabinItem(
                    CabinClassEnum.tcValueToEnums(c.cabinClass).name,
                    CabinClassEnum.tcValueToEnums(c.cabinClass).display,
                    c.cabinCode,
                    c.cabinCount,
                    c.segmentIndex
                )
            },
            ProductInfo(
                item.productCode.toString(),
                ProductTagEnum.tcValueToEnums(item.productTag),
                flightSource.display,
                flightSource
            ),
            FlightTCUtils.convertMin(transitTime[2].toInt()),
            sourceType,
        )
    }

    private fun getSegmentItemKey(item: TCShoppingProductItem): String {
        val keyMap = mutableMapOf<Int, String>()
        for (s in item.segments) {
            keyMap[s.segmentIndex] = s.marketingFlightNo
        }
        return keyMap.keys.sorted().map { keyMap[it] }.joinToString()
    }
}