package com.somytrip.provider.flight.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.connection.RedisConnectionFactory
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer


/**
 * @Description: redis config
 * @author: pigeon
 * @created: 2024-06-05 17:39
 */
@Configuration
open class RedisConfig {
    @Bean
    open fun redisTemplate(connectionFactory: RedisConnectionFactory): RedisTemplate<String, Any> {
        val template = RedisTemplate<String, Any>()
        val jackson2JsonRedisSerializer = Jackson2JsonRedisSerializer(Any::class.java)
        template.connectionFactory = connectionFactory
        template.keySerializer = jackson2JsonRedisSerializer
        template.valueSerializer = jackson2JsonRedisSerializer
        template.hashKeySerializer = jackson2JsonRedisSerializer
        template.hashValueSerializer = jackson2JsonRedisSerializer
        template.afterPropertiesSet();
        return template
    }
}