package com.somytrip.provider.flight.service

import com.somytrip.entity.dto.flight.AirportCodeNameDTO
import com.somytrip.entity.vo.flight.AirLineCodeVo
import com.somytrip.model.flight.common.AccRuleItem
import com.somytrip.model.flight.common.ShopListRsp
import com.somytrip.model.flight.enums.ProviderSourceEnum
import com.somytrip.model.flight.tc.TCAccurateSearchItem
import com.somytrip.model.flight.vo.PassengerInfo
import com.somytrip.model.flight.vo.ShopListReq
import java.time.ZoneId
import java.util.Locale

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-08-20 16:44
 */
interface CommonService {
    fun getAirportName(airCode: String): String;
    fun getAirportCodeNameDTOByCityId(cityId: Int): AirportCodeNameDTO;
    fun getAirline(airlineCode: String): AirLineCodeVo

    /**
     * @param airportList airport iata code list
     * [SZX, SHA]
     * 判断是否有国际机场
     */
    fun isInternational(airportList: List<String>): Boolean;

    /**
     * 单位：元 加价
     */
    fun getMarkUpPrice(price: String): Int

    /**
     * 获取用户信息统一封装
     */
    fun getPassengerInfos(uid: String, ids: List<Int>): List<PassengerInfo>

    /**
     * 移动小数点
     */
    fun getMovePoint2(price: String): Int;

    fun cacheShoppingList(
        req: ShopListReq,
        result: Map<String, List<ShopListRsp>>,
        source: ProviderSourceEnum
    );

    fun delCacheShoppingList(req: ShopListReq, source: ProviderSourceEnum);

    fun getCacheShoppingList(
        req: ShopListReq,
        source: ProviderSourceEnum = ProviderSourceEnum.DEFAULT
    ): Map<String, List<ShopListRsp>>;

    fun getCacheShopUnZip(
        req: ShopListReq,
        source: ProviderSourceEnum = ProviderSourceEnum.DEFAULT
    ): Map<String, List<ShopListRsp>>;

    fun cacheAccRuleItem(key: String, value: AccRuleItem);
    fun getCacheAccRuleItem(key: String): AccRuleItem;
    fun cacheTCAccurateSearchItem(item: TCAccurateSearchItem);
    fun getCacheTCAccurateSearchItem(key: String): TCAccurateSearchItem?;
    fun getAirportTimezone(airport: String): ZoneId

    fun getI18nMsg(key: String, locale: Locale = Locale("zh", "CN")): String
}