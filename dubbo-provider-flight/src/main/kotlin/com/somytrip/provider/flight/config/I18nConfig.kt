package com.somytrip.provider.flight.config

import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.support.ReloadableResourceBundleMessageSource

/**
 * @Description:
 * @author: pigeon
 * @created: 2025-07-03 15:25
 */
@Configuration
open class I18nConfig {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Bean("i18nMessageSource")
    open fun messageSource(): ReloadableResourceBundleMessageSource {
        var messageSource = ReloadableResourceBundleMessageSource()
        messageSource.setBasename("classpath:i18n/messages")
        messageSource.setDefaultEncoding("UTF-8")
        log.info("init i18nMessageSource complete")
        return messageSource
    }
}