package com.somytrip.provider.flight.service.impl

import cn.hutool.core.date.DateUtil
import cn.hutool.core.date.LocalDateTimeUtil
import cn.hutool.core.map.MapUtil
import cn.hutool.core.util.EnumUtil
import cn.hutool.core.util.IdUtil
import cn.hutool.core.util.NumberUtil
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.somytrip.api.service.flight.ManyFlightService
import com.somytrip.api.service.order.OrderBaseService
import com.somytrip.common.util.MinioUtil
import com.somytrip.entity.dto.order.FlightOrderDetail
import com.somytrip.entity.enums.order.OrderTypeEnum
import com.somytrip.entity.enums.thirdparty.ThirdServiceTypeEnum
import com.somytrip.entity.response.Response
import com.somytrip.entity.response.ResponseResult
import com.somytrip.exception.BusinessException
import com.somytrip.model.dto.FileRecord
import com.somytrip.model.dto.TicketChangeOrder
import com.somytrip.model.flight.common.AccurateRsp
import com.somytrip.model.flight.common.CommonValFunc
import com.somytrip.model.flight.common.ReShopRsp
import com.somytrip.model.flight.common.ShopListRsp
import com.somytrip.model.flight.enums.*
import com.somytrip.model.flight.fr24.BookingData
import com.somytrip.model.flight.fr24.FR24NotifyResponse
import com.somytrip.model.flight.fr24.OrderChangeInfoNotify
import com.somytrip.model.flight.fr24.TicketingData
import com.somytrip.model.flight.tc.TCNotifyBody
import com.somytrip.model.flight.tc.TCPayCheckProduct
import com.somytrip.model.flight.tc.TCPayCheckProductDetail
import com.somytrip.model.flight.tc.TCPayResponse
import com.somytrip.model.flight.vo.*
import com.somytrip.provider.flight.config.FlightConfig
import com.somytrip.provider.flight.mapper.FileRecordMapper
import com.somytrip.provider.flight.mapper.TicketChangeOrderMapper
import com.somytrip.provider.flight.service.CommonService
import com.somytrip.provider.flight.service.FlightService
import jakarta.annotation.Resource
import kotlinx.coroutines.*
import org.apache.dubbo.config.annotation.DubboReference
import org.apache.dubbo.config.annotation.DubboService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.ZoneId
import java.time.ZonedDateTime

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-08-15 14:57
 */
@Service
@DubboService
class ManyFlightServiceImpl : ManyFlightService {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Resource
    private lateinit var flightServices: List<FlightService>

    @Resource
    private lateinit var commonService: CommonService

    @Resource
    private lateinit var fileRecordMapper: FileRecordMapper

    @Resource
    private lateinit var minioUtil: MinioUtil

    @DubboReference(check = false)
    private lateinit var orderBaseService: OrderBaseService

    @Resource
    private lateinit var flightConfig: FlightConfig

    @Resource
    private lateinit var ticketChangeOrderMapper: TicketChangeOrderMapper

    /**
     * 国际 true
     */
    private fun tcSource(codes: List<String>): Boolean {
        return commonService.isInternational(codes)
    }

    override fun tripPlanStr(vo: TripPlanVo): String {
        return tripPlanV2(vo).toJSONString()
    }

    private fun tripPlanV2(vo: TripPlanVo): TripPlanRsp {
        log.info("trip plan v2 start: {}", vo.toJSONString())
        val req = ShopListReq()
        val reqs = vo.dates.mapIndexed() { index, it ->
            req.copy(
                segments = listOf(
                    SegmentVo(
                        commonService.getAirportCodeNameDTOByCityId(vo.cityPairs[index][0]).code,
                        commonService.getAirportCodeNameDTOByCityId(vo.cityPairs[index][1]).code,
                        it
                    )
                ),
                adultNum = 1
            )
        }
        try {
            log.info("trip plan reqs: {}", reqs.toJSONString())
            return TripPlanRsp(
                tripPlanAsync(reqs),
                true,
            )
        } catch (e: Exception) {
            log.error("trip plan error: {}", vo.toJSONString(), e)
        }
        return TripPlanRsp(
            emptyList(),
            false,
        )
    }

    private fun tripPlanAsync(reqs: List<ShopListReq>): List<TripPlanItem> = runBlocking {
        val deferredResults = reqs.map {
            async { tripPlanData(it) }
        }
        return@runBlocking deferredResults.awaitAll()
    }

    private fun tripPlanData(req: ShopListReq): TripPlanItem {
        val data = shopV2(req)
        log.info("tripPlanData result data size: {}", data.size)
        val values = data.values
        if (values.isEmpty()) {
            log.info("tripPlanData values is empty: {}", req.toJSONString())
            return TripPlanItem()
        }
        val lowFilter = values.filter {
            if (it.isEmpty()) {
                false
            } else {
                it.first().segments.first().departDateTime.substring(CommonValFunc.DATE_HOUR_SUBSTRING_RANGE)
                    .toInt() in DateHourRangeEnum.LOW.range
            }
        }
        if (lowFilter.isEmpty()) {
            log.info("lowFilter is empty")
            return TripPlanItem(values.first().first(), values.first().first(), values.last().first())
        }
        val midFilter = lowFilter.filter {
            it.first().segments.first().departDateTime.substring(CommonValFunc.DATE_HOUR_SUBSTRING_RANGE)
                .toInt() in DateHourRangeEnum.MID.range
        }
        if (midFilter.isEmpty()) {
            log.info("midFilter is empty")
            return TripPlanItem(lowFilter.first().first(), lowFilter.first().first(), lowFilter.last().first())
        }
        val highFilter = midFilter.filter {
            it.first().segments.first().departDateTime.substring(CommonValFunc.DATE_HOUR_SUBSTRING_RANGE)
                .toInt() in DateHourRangeEnum.HIGH.range
                    &&
                    it.first().segments.size == 1
        }
        if (highFilter.isEmpty()) {
            log.info("highFilter is empty")
            return TripPlanItem(lowFilter.first().first(), midFilter.first().first(), midFilter.last().first())
        }

        val result = TripPlanItem(lowFilter.first().first(), midFilter.first().first(), highFilter.last().first())
        log.info("low mid high has value: {}", result.toJSONString())
        return result
    }

    override fun shopV2(req: ShopListReq): Map<String, List<ShopListRsp>> {
        try {
            if (!req.check()) {
                log.warn("req is wrong: {}", req.toJSONString())
                return MapUtil.empty<String, List<ShopListRsp>>()
            }
            for (provider in flightConfig.system().providers) {
                for (service in flightServices) {
                    if (service.support(provider)) {
                        val shop = service.shoppingList(req);
                        if (shop.isEmpty()) {
                            log.info("shop list is empty, skipping...: {}", provider)
                            continue
                        }
                        val keys = req.screenCondition.flightNos.ifEmpty {
                            shop.keys
                        }
                        return formatShopResult(req, shop, keys);
                    }
                }
            }
        } catch (e: Exception) {
            log.error("shop req: {}", req.toJSONString(), e)
        }
        return MapUtil.empty<String, List<ShopListRsp>>()
    }

    private fun formatShopResult(
        req: ShopListReq,
        data: Map<String, List<ShopListRsp>>,
        keys: Iterable<String>
    ): Map<String, List<ShopListRsp>> {
        val result = mutableMapOf<String, List<ShopListRsp>>()
        val cNow = ZonedDateTime.now(ZoneId.of(req.timezone))
        for (key in keys) {
            val list = data[key] ?: emptyList()
            if (list.isEmpty()) {
                log.info("formatShopResult key is empty: {}", key)
                continue
            }
            val item = list.asSequence().sortedWith { u1, u2 ->
                u1.prices.first().price.toInt().compareTo(u2.prices.first().price.toInt())
            }.filter { it ->
                if (req.screenCondition.airports.isEmpty()) {
                    true
                } else {
                    req.screenCondition.airports.intersect(it.segments.map { it.arriveAirport }.toSet()).isNotEmpty()
                }
            }.filter {
                if (req.screenCondition.airline.isEmpty()) {
                    true
                } else {
                    req.screenCondition.airline.contains(it.segments.first().airline)
                }
            }.filter {
                if (req.screenCondition.cabin.isEmpty()) {
                    true
                } else {
                    req.screenCondition.cabin.contains(it.cabins.first().cabinClass)
                }
            }.filter {
                if (req.screenCondition.stylePreference.isEmpty()) {
                    true
                } else {
                    req.screenCondition.stylePreference.any { s ->
                        if (s == StylePreferenceEnum.NOT_STOP) {
                            it.segments.size == 1
                        } else if (s == StylePreferenceEnum.NOT_SHARE) {
                            !it.segments.any { !it.flightShare }
                        } else {
                            true
                        }
                    }
                }
            }.filter {
                if (req.screenCondition.timeRange.isEmpty()) {
                    true
                } else {
                    req.screenCondition.timeRange.any { t ->
                        val hour = it.segments.first().departDateTime.substring(CommonValFunc.DATE_HOUR_SUBSTRING_RANGE)
                            .toInt()
                        hour in t.timeInterval()
                    }
                }
            }.filter {
                try {
                    val depTime = ZonedDateTime.of(
                        LocalDateTimeUtil.parse(it.segments.first().departDateTime, "yyyy-MM-dd HH:mm"),
                        commonService.getAirportTimezone(it.segments.first().departAirport)
                    )
                    depTime.isAfter(cNow) || depTime.isEqual(cNow)
                } catch (e: Exception) {
                    log.warn("filter airport timezone fail: {}", it.toJSONString(), e)
                    true
                }
            }.toList()
            if (item.isNotEmpty()) {
                result[key] = item
            }
        }
        return result.entries.sortedWith { u1, u2 ->
            when (req.screenCondition.sort) {
                SortEnum.PRICE_LOW_HIGH -> {
                    NumberUtil.compare(
                        NumberUtil.parseInt(u1.value.first().prices.first().price),
                        NumberUtil.parseInt(u2.value.first().prices.first().price)
                    )
                }

                SortEnum.PRICE_HIGH_LOW -> {
                    NumberUtil.compare(
                        NumberUtil.parseInt(u2.value.first().prices.first().price),
                        NumberUtil.parseInt(u1.value.first().prices.first().price)
                    )
                }

                SortEnum.START_LOW_HIGH -> {
                    // departDateTime String yyyy-mm-dd HH:MM
                    val d1 =
                        DateUtil.parse(u1.value.first().segments.first().departDateTime, CommonValFunc.DATE_TIME_FORMAT)
                    val d2 =
                        DateUtil.parse(u2.value.first().segments.first().departDateTime, CommonValFunc.DATE_TIME_FORMAT)
                    DateUtil.compare(d1, d2)
                }

                SortEnum.START_HIGH_LOW -> {
                    val d1 =
                        DateUtil.parse(u1.value.first().segments.first().departDateTime, CommonValFunc.DATE_TIME_FORMAT)
                    val d2 =
                        DateUtil.parse(u2.value.first().segments.first().departDateTime, CommonValFunc.DATE_TIME_FORMAT)
                    DateUtil.compare(d2, d1)
                }
            }
        }.filterIndexed { index, _ ->
            index < 100
        }.associateBy({ it.key }, { it.value })
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun shoppingAsync(req: ShopListReq): List<Map<String, List<ShopListRsp>>> = runBlocking {
        coroutineScope {
            val provider = flightConfig.system().activeProvider
            log.info("shopping list active provider: {}", provider)
            val tcList = GlobalScope.async {
                val flag = tcSource(req.segments.map { it.departCode } + req.segments.map { it.arriveCode })
                val flightService = flightServices.find {
                    it.support(
                        if (flag) {
                            ProviderSourceEnum.TC_INTERNATIONAL
                        } else {
                            ProviderSourceEnum.TC_DOMESTIC
                        }
                    )
                }
                if (flightService == null || flag) {
                    emptyMap()
                } else {
                    flightService.shoppingList(req)
                }

            }
            val fr24List = GlobalScope.async {
                if (provider.contains(ProviderSourceEnum.FR24.name)) {
                    val flightService = flightServices.find { it.support(ProviderSourceEnum.FR24) }
                    flightService?.shoppingList(req) ?: emptyMap()
                } else {
                    emptyMap()
                }
            }
            val zfList = GlobalScope.async {
                if (provider.contains(ProviderSourceEnum.ZF.name)) {
                    val flightService = flightServices.find { it.support(ProviderSourceEnum.ZF) }
                    flightService?.shoppingList(req) ?: emptyMap()
                } else {
                    emptyMap()
                }
            }
            return@coroutineScope awaitAll(tcList, fr24List, zfList)
        }
    }

    override fun accurateSearch(req: AccurateReq): ResponseResult<AccurateRsp> {
        try {
            for (flightService in flightServices) {
                if (flightService.support(req.productInfos.first().productSource)) {
                    return ResponseResult.ok(flightService.accSearch(req))
                }
            }
            log.error("not support provider: {}", JSON.toJSONString(req))
        } catch (e: Exception) {
            log.error("acc rule: {}", req.toJSONString(), e)
            if (e is BusinessException) {
                return ResponseResult.fail<AccurateRsp?>(e.code, e.message ?: "", AccurateRsp())
            }
        }
        return ResponseResult.ok(AccurateRsp())
    }

    override fun booking(req: CreateOrderReq): CreateOrderRsp {
        log.info("booking order: {}", req.toJSONString())
        if (orderBaseService.existsBlackListFlag(NumberUtil.parseLong(req.userId))) {
            log.warn("this user is black list: {}", req.toJSONString())
            return CreateOrderRsp(false, 400, "api.order.blacklist")
        }
        if (orderBaseService.maxUnpaidOrderCountLimit(NumberUtil.parseLong(req.userId))) {
            log.warn("this user unpaid max limit : {}", req.toJSONString())
            return CreateOrderRsp(false, 400, "api.order.unpaid.limit")
        }
        for (flightService in flightServices) {
            if (flightService.support(req.productInfo.productSource)) {
                return flightService.bookingOrder(req)
            }
        }
        log.error("not support ${req.productInfo}, {}", req.toJSONString())
        throw BusinessException("not support ${req.productInfo}")
    }

    override fun booking(req: String): CreateOrderRsp {
        return booking(req.into<CreateOrderReq>())
    }

    override fun payFR24(req: PayReq<FR24PayInfo>): PayResponse<TicketingData> {
        for (flightService in flightServices) {
            if (flightService.support(ProviderSourceEnum.FR24)) {
                val result = flightService.fr24Pay(req.payInfo)
                return PayResponse(result, result.orderNo.isNotBlank());
            }
        }
        throw RuntimeException("not support ${req.payInfo}")
    }

    override fun payTC(req: PayReq<TCPayInfo>): PayResponse<TCPayResponse> {
        val info = req.payInfo
        val provider = if (info.source == 0) {
            ProviderSourceEnum.TC_DOMESTIC
        } else {
            ProviderSourceEnum.TC_INTERNATIONAL
        }
        for (flightService in flightServices) {
            if (flightService.support(provider)) {
                return PayResponse(flightService.tcPay(info))
            }
        }
        throw RuntimeException("not support ${req.payInfo}")
    }

    override fun fr24Notify(body: NotifyBody<OrderChangeInfoNotify>): NotifyResult<FR24NotifyResponse> {
        log.info("FR24 notify body: {}", body.toJSONString())
        for (flightService in flightServices) {
            if (flightService.support(ProviderSourceEnum.FR24)) {
                return flightService.fr24Notify(body)
            }
        }
        return NotifyResult(FR24NotifyResponse())
    }

    override fun tcNotify(body: NotifyBody<TCNotifyBody>): NotifyResult<String> {
        val provider = if (body.data.businessType == 0) {
            ProviderSourceEnum.TC_DOMESTIC
        } else {
            ProviderSourceEnum.TC_INTERNATIONAL
        }
        for (flightService in flightServices) {
            if (flightService.support(provider)) {
                return flightService.tcNotify(body)
            }
        }
        return NotifyResult("not support ${provider.name}");
    }

    override fun notifyHandler(body: String, source: ProviderSourceEnum): String {
        for (flightService in flightServices) {
            if (flightService.support(source)) {
                log.info("source: {}, notify: {}", source, body)
                return flightService.notifyHandler(body)
            }
        }
        log.warn("source: {}, notify: {}", source, body)
        return "";
    }

    override fun noticeChangeHandler(
        body: String,
        source: ProviderSourceEnum
    ): String {
        for (flightService in flightServices) {
            if (flightService.support(source)) {
                log.info("noticeChangeHandler source: {}, notify: {}", source, body)
                return flightService.notifyChangeHandler(body)
            }
        }
        log.warn("noticeChangeHandler source: {}, notify: {}", source, body)
        return "";
    }

    override fun notPayCancelOrder(orderNo: String, reason: String): Boolean {
        try {
            val orderMaster = orderBaseService.queryOrderMasterPublic(orderNo)
            if (EnumUtil.equals(OrderTypeEnum.CHANGE, orderMaster.orderType)) {
                log.info("改签订单没有取消接口: {}", orderNo)
                return true;
            }
            val orderBigDto = orderBaseService.queryOrderBigFieldPublic(orderNo);
            val orderBaseVo = orderBigDto.field1.toJSONString().into<OrderBaseVo>()
            log.info("wait cancel not paid order entity success: {}", orderNo)
            log.info("orderNo: {}, {}", orderNo, orderBaseVo.accurate.request.productInfo.productSource)
            for (flightService in flightServices) {
                if (flightService.support(orderBaseVo.accurate.request.productInfo.productSource)) {
                    log.info("support cancel order: {}", orderBaseVo.accurate.ruleUnit.productInfo.productSource)
                    return flightService.cancelOrder(orderBigDto, reason)
                }
            }
            log.info("wait cancel not paid order entity fail: {}", orderBaseVo.toJSONString())
        } catch (e: Exception) {
            log.error("cancel order error: {}", orderNo, e)
        }
        log.warn("not support cancel: {}", orderNo)
        return false
    }

    override fun pay(orderNo: String): Boolean {
        try {
            log.info("system pay flight order start")
            val dto = orderBaseService.queryOrderBigFieldPublic(orderNo)
            log.info("system pay flight order get bigField success")
            if (EnumUtil.equals(ThirdServiceTypeEnum.flight_fr24, dto.thirdServiceType)) {
                log.info("航路支付: {}", orderNo)
                val bookingData = JSON.parseObject(
                    dto.field2.toJSONString(),
                    BookingData::class.java
                )
                val payInfo = FR24PayInfo(
                    bookingData.orderNo,
                    bookingData.partnerOrderNo,
                    bookingData.currency,
                    bookingData.totalPrice,
                    "0"
                )
                log.info("flight order payInfo: {}", JSON.toJSONString(payInfo))
                val payReq = PayReq(payInfo)
                val payResult: PayResponse<TicketingData> = payFR24(payReq)
                log.info("flight order payResult: {}", JSON.toJSONString(payResult))
                return payResult.success
            } else if (EnumUtil.equals(ThirdServiceTypeEnum.flight, dto.thirdServiceType)) {
                log.info("同程支付: {}", orderNo)
                val vo = dto.field1.toJSONString().into<OrderBaseVo>()
                val products = vo.products.map {
                    TCPayCheckProduct(
                        "0",
                        it.prdoductTotalPrice.ifBlank {
                            it.productTotalPrice
                        },
                        it.productDetail.into<TCPayCheckProductDetail>()
                    )
                }
                val payReq = PayReq(
                    TCPayInfo(
                        vo.accurate.request.sourceType,
                        vo.orderSerialNo,
                        vo.totalPrice,
                        orderNo,
                        products
                    )
                )
                log.info("同程支付 flight order payReq: {}", JSON.toJSONString(payReq))
                val payResult: PayResponse<TCPayResponse> = payTC(payReq)
                log.info("同程支付 flight order payResult: {}", JSON.toJSONString(payResult))
                return payResult.success
            } else if (EnumUtil.equals(ThirdServiceTypeEnum.flight_tc_change, dto.thirdServiceType)) {
                log.info("change order: {}", orderNo)
                return payChangeOrder(orderNo)
            } else if (EnumUtil.equals(ThirdServiceTypeEnum.flight_zf, dto.thirdServiceType)) {
                for (flightService in flightServices) {
                    if (flightService.support(ProviderSourceEnum.ZF)) {
                        log.info("智飞科技 flight pay: {}", orderNo)
                        return flightService.pay(dto)
                    }
                }
                log.error("智飞科技 flight pay not found service: {}", orderNo)
                return false;
            } else {
                log.error("not support pay flight order: {}", orderNo)
                return false
            }
        } catch (e: Exception) {
            log.error("进行机票支付报错: {}", orderNo, e)
            return false
        }
    }

    override fun payChangeOrder(orderNo: String): Boolean {
        val queryWrapper = QueryWrapper<TicketChangeOrder>()
        queryWrapper.eq("change_no", orderNo)
        val ticketChangeOrder = ticketChangeOrderMapper.selectOne(queryWrapper)
        log.info("pay change order: {}", ticketChangeOrder.toJSONString())
        for (flightService in flightServices) {
            if (flightService.support(ticketChangeOrder.source)) {
                log.info("success support pay change order: {}", orderNo)
                return flightService.changeOrderPay(orderNo).data
            }
        }
        log.error("not support pay change order: {}", ticketChangeOrder.toJSONString())
        return false;
    }

    override fun queryRefundFee(vo: ComputePriceVo): ResponseResult<QueryRefundFeeResultVo> {
        log.info("query refund fee: {}", vo.toJSONString())
        val orderFinanceDTO = orderBaseService.queryOrderFinancePublic(vo.orderNo)
            ?: return ResponseResult.fail(400, "not found order: ${vo.orderNo}")
        val bigField = orderBaseService.queryOrderBigFieldPublic(vo.orderNo)
            ?: return ResponseResult.fail(400, "not found order: ${vo.orderNo}")
        val provider = if (bigField.thirdServiceType == ThirdServiceTypeEnum.flight_tc_change.name) {
            ProviderSourceEnum.TC_DOMESTIC
        } else {
            bigField.field1.toJSONString().into<OrderBaseVo>().accurate.request.productInfo.productSource
        }
        val req = QueryRefundFeeVo(vo.tickets, vo.orderNo, provider, vo.type)
        log.info("query refund fee req: {}", req.toJSONString())
        for (flightService in flightServices) {
            if (flightService.support(req.provider)) {
                val result = flightService.queryRefundFee(req)
                log.info("query refund fee result: {}", result.toJSONString())
                val price = NumberUtil.roundStr(
                    NumberUtil.div(orderFinanceDTO.estimatedPrice, BigDecimal.TEN.pow(2)).toString(), 2
                )
                log.info("query refund fee result: {}, origin price: {}", result.toJSONString(), price)
                return Response.ok(
                    result.copy(
                        payPrice = price
                    )
                );
            }
        }
        return ResponseResult.fail(500, "Not yet implemented")
    }

    override fun applyRefund(vo: ApplyRefundNVo): ResponseResult<Boolean> {
        val bigField = orderBaseService.queryOrderBigFieldPublic(vo.orderNo)
            ?: return ResponseResult.fail(400, "not found order: ${vo.orderNo}")
        log.info("apply refund order: {}", vo.orderNo)
        val provider = if (bigField.thirdServiceType == ThirdServiceTypeEnum.flight_tc_change.name) {
            ProviderSourceEnum.TC_DOMESTIC
        } else {
            bigField.field1.toJSONString().into<OrderBaseVo>().accurate.request.productInfo.productSource
        }
        val req = ApplyRefundVo(
            vo.tickets,
            vo.orderNo,
            vo.type,
            vo.remark,
            vo.fileKeys,
            when (vo.type) {
                RefundTypeEnum.VOLUNTARY -> {
                    RefundReasonEnum.VOLUNTARY
                }

                else -> {
                    RefundReasonEnum.OTHER
                }
            }
        )
        log.info("apply refund req: {}", req.toJSONString())
        for (flightService in flightServices) {
            if (flightService.support(provider)) {
                log.info("apply refund req: {}, {}", provider, req.toJSONString())
                val result = flightService.applyRefund(req)
                log.info("apply refund req: {}, result: {}", req.toJSONString(), result)
                return if (result.success) {
                    ResponseResult.ok("ok", true)
                } else {
                    ResponseResult.fail(500, result.data)
                }
            }
        }
        return ResponseResult.fail(500, "Not yet implemented")
    }

    override fun uploadFiles(vo: UploadFilesVo): ResponseResult<List<String>> {
        log.info("uploadFiles upload files: {}, {}", vo.orderNo, vo.files.size)
        val bigField = orderBaseService.queryOrderBigFieldPublic(vo.orderNo)
            ?: return ResponseResult.fail(400, "not found order: ${vo.orderNo}")
        log.info("uploadFiles order: {}", vo.orderNo)
        val provider = if (bigField.thirdServiceType == ThirdServiceTypeEnum.flight_tc_change.name) {
            ProviderSourceEnum.TC_DOMESTIC
        } else {
            bigField.field1.toJSONString().into<OrderBaseVo>().accurate.request.productInfo.productSource
        }
        var result: List<String> = emptyList()
        for (flightService in flightServices) {
            if (flightService.support(provider)) {
                result = flightService.uploadFiles(vo)
            }
        }
        if (result.isNotEmpty()) {
            try {
                val fileInputStream = ByteArrayInputStream(vo.files.first().file)
                val fileName = IdUtil.objectId() + StrUtil.subSuf(
                    vo.files.first().fileName,
                    StrUtil.indexOf(vo.files.first().fileName, '.', 0)
                )
                val folder = if (vo.type == 1) {
                    "refund"
                } else {
                    "change"
                }
                val objectName = folder + StrUtil.SLASH + fileName;
                minioUtil.uploadFile(fileInputStream, CommonValFunc.BUCKET, objectName)
                val fileRecord = FileRecord(vo.orderNo, result.first(), CommonValFunc.BUCKET, objectName)
                log.info("upload file record: {}", fileRecord.toJSONString())
                fileRecordMapper.insert(fileRecord)
                return ResponseResult.ok(
                    result + listOf(
                        minioUtil.getPresignedObjectUrl(
                            fileRecord.bucket,
                            fileRecord.filePath
                        )
                    )
                )
            } catch (e: Exception) {
                log.error("upload file to minio fail: {}, {}", vo.orderNo, result, e)
            }
        }
        return ResponseResult.fail(500, "error")
    }

    override fun queryChangeShoppingList(req: ChangeShopListVo): ResponseResult<Map<String, List<ReShopRsp>>> {
        val bigField = orderBaseService.queryOrderBigFieldPublic(req.orderNo)
            ?: return ResponseResult.fail(400, "not found order: ${req.orderNo}")
        log.info("query change shop list: {}", req.toJSONString())
        val provider = bigField.field1.toJSONString().into<OrderBaseVo>().accurate.request.productInfo.productSource
        val changeShopReq = ChangeShopReq(req.orderNo, req.segments, req.tickets)
        for (flightService in flightServices) {
            if (flightService.support(provider)) {
                val result = flightService.queryChangeList(changeShopReq, bigField)
                if (result.isEmpty()) {
                    return ResponseResult.fail(500, "Please contact customer service")
                }
                return ResponseResult.ok(result)
            }
        }
        return ResponseResult.fail(500, "error")
    }

    override fun applyChange(req: ApplyChangeVo): ResponseResult<Boolean> {
        log.info("apply change req: {}", req.toJSONString())
        val vo = ChangeApplyReq(req.orderNo, req.productInfo, req.tickets, req.changeSegments, req.fileKeys)
        var result = false
        for (flightService in flightServices) {
            if (flightService.support(ProviderSourceEnum.TC_DOMESTIC)) {
                result = flightService.applyChange(vo)
            }
        }
        log.info("apply change result: {}", result)
        return if (result) {
            ResponseResult.ok(true)
        } else {
            ResponseResult.ok(false)
        }
    }

    override fun changeOrderPay(orderNo: String): ResponseResult<Boolean> {
        for (flightService in flightServices) {
            if (flightService.support(ProviderSourceEnum.TC_DOMESTIC)) {
                return flightService.changeOrderPay(orderNo)
            }
        }
        throw BusinessException(500, "Not yet implemented")
    }

    override fun changeOrderDetail(orderNo: String, uid: String): FlightOrderDetail.Detail {
        log.info("query change order detail: {}", orderNo)
        val bigField = orderBaseService.queryOrderBigFieldPublic(orderNo)
        for (flightService in flightServices) {
            if (flightService.support(ProviderSourceEnum.TC_DOMESTIC)) {
                return flightService.changeOrderDetail(bigField)
            }
        }
        throw BusinessException(500, "Not yet implemented")
    }

    override fun canRefund(orderNo: String): Boolean {
        log.info("start can refund: {}", orderNo)
        val bigField = orderBaseService.queryOrderBigFieldPublic(orderNo)
        val provider = bigField.field1.toJSONString().into<OrderBaseVo>().accurate.request.productInfo.productSource
        for (flightService in flightServices) {
            if (flightService.support(provider)) {
                log.info("start can refund: {}, {}", orderNo, provider)
                return flightService.canRefund(bigField)
            }
        }
        log.warn("cannot refund not match flight service: {}", orderNo)
        return false;
    }
}