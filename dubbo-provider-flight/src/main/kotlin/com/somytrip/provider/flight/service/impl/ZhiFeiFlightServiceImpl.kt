package com.somytrip.provider.flight.service.impl

import cn.hutool.core.date.DateField
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.IdUtil
import cn.hutool.core.util.NumberUtil
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson2.JSONObject
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.somytrip.api.service.order.OrderBaseService
import com.somytrip.api.service.order.OrderStateService
import com.somytrip.entity.dto.order.FlightOrderDetail
import com.somytrip.entity.dto.order.OrderBigFieldDTO
import com.somytrip.entity.dto.order.OrderMasterExt
import com.somytrip.entity.enums.order.OrderBusinessTypeEnum
import com.somytrip.entity.enums.order.OrderSettleModeEnums
import com.somytrip.entity.enums.order.OrderTypeEnum
import com.somytrip.entity.enums.order.OrderUserTypeEnum
import com.somytrip.entity.enums.pay.CollectionAgencyEnums
import com.somytrip.entity.enums.pay.CurrencyEnums
import com.somytrip.entity.enums.pay.PaymentChannelEnums
import com.somytrip.entity.enums.pay.PaymentFromEnums
import com.somytrip.entity.enums.thirdparty.ThirdServiceTypeEnum
import com.somytrip.entity.response.ResponseResult
import com.somytrip.entity.vo.CreateOrderFinanceVo
import com.somytrip.entity.vo.order.CreateOrderMasterVo
import com.somytrip.entity.vo.order.CreateOrderProductVo
import com.somytrip.entity.vo.order.CreateOrderVo
import com.somytrip.entity.vo.order.OrderBigFiledVo
import com.somytrip.exception.BusinessException
import com.somytrip.model.dto.ZfApplyRecord
import com.somytrip.model.flight.common.AccRuleItem
import com.somytrip.model.flight.common.AccurateRsp
import com.somytrip.model.flight.common.ReShopRsp
import com.somytrip.model.flight.common.ShopListRsp
import com.somytrip.model.flight.enums.*
import com.somytrip.model.flight.vo.*
import com.somytrip.model.flight.zf.*
import com.somytrip.model.flight.zf.PayReq
import com.somytrip.provider.flight.config.FlightConfig
import com.somytrip.provider.flight.mapper.ZfApplyRecordMapper
import com.somytrip.provider.flight.service.CommonService
import com.somytrip.provider.flight.service.FlightService
import com.somytrip.provider.flight.utils.ZfFlightUtil
import jakarta.annotation.Resource
import org.apache.dubbo.config.annotation.DubboReference
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.*

/**
 * @Description: 智飞科技Service Impl
 * @author: pigeon
 * @created: 2024-08-15 15:08
 */
@Service(value = "zhiFeiFlightService")
class ZhiFeiFlightServiceImpl : FlightService {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Resource
    private lateinit var flightConfig: FlightConfig

    @Resource
    private lateinit var commonService: CommonService

    @DubboReference(check = false)
    private lateinit var orderBaseService: OrderBaseService

    @DubboReference(check = false)
    private lateinit var orderStateService: OrderStateService;

    @Resource
    private lateinit var zfApplyRecordMapper: ZfApplyRecordMapper

    private val zfFlightUtil by lazy {
        ZfFlightUtil.Builder()
            .config(flightConfig.zf())
            .commonService(commonService)
            .builder()
    }

    override fun support(provider: ProviderSourceEnum): Boolean {
        return provider() == provider
    }

    override fun provider(): ProviderSourceEnum {
        return ProviderSourceEnum.ZF
    }

    override fun shoppingList(req: ShopListReq): Map<String, List<ShopListRsp>> {
        try {
            log.info("shop list req: {}", req.toJSONString())
            val cacheValue = commonService.getCacheShoppingList(req, provider())
            if (cacheValue.isNotEmpty()) {
                return cacheValue
            }
            val result = zfFlightUtil.shop(req)
            log.info("shop list req: {}, result size: {}", req.toJSONString(), result.size)
            if (result.isNotEmpty()) {
                commonService.cacheShoppingList(req, result, provider())
            }
            return result
        } catch (e: Exception) {
            log.error("shop list: {}", req.toJSONString(), e)
            return emptyMap()
        }
    }

    override fun accSearch(req: AccurateReq): AccurateRsp {
        log.info("acc rule req: {}", req.toJSONString())
        try {
            val result = zfFlightUtil.accRule(req)
            log.info("acc rule req: {}, result: {}", req.toJSONString(), result.toJSONString())
            return result
        } catch (e: BusinessException) {
            log.error("get acc rule req: {}", req.toJSONString(), e)
        }
        val pReq = PricingReq(
            Passenger(
                req.adultNum,
                req.childNum,
                req.infantNum
            ),
            req.segments.mapIndexed { index, s ->
                val cabin = if (req.cabins.isEmpty()) {
                    CabinClassEnum.ECONOMY_CLASS.zfValue()
                } else {
                    try {
                        req.cabins[index].cabinClass.zfValue()
                    } catch (e: Exception) {
                        log.warn("not match index: {}, {}", index, req.cabins.toJSONString(), e)
                        CabinClassEnum.ECONOMY_CLASS.zfValue()
                    }
                }
                PricingSeg(
                    1,
                    s.departAirport,
                    s.arriveAirport,
                    s.departDateTime.substring(0..9),
                    s.flightNo,
                    cabinClass = cabin
                )
            }
        )
        log.info("pricing req: {}", pReq.toJSONString())
        val pResult = zfFlightUtil.pricing(pReq, req.sourceType)
        val key = StrUtil.join(StrUtil.COMMA, req.segments.map { it.flightNo }.toList())
        log.info("pResult keys: {}, match key: {}", pResult.keys.toJSONString(), key)
        if (pResult.contains(key)) {
            log.info("get shop list value has key: {}", key)
            val reqNew =
                req.copy(productInfos = listOf(pResult[key]?.first()?.productInfo ?: req.productInfos.first()))
            val result = zfFlightUtil.accRule(reqNew)
            log.info("acc rule req: {}, result: {}", reqNew.toJSONString(), result.toJSONString())
            return result
        }
        log.warn("matching acc rule fail: {}, {}", pResult.keys.toJSONString(), key)
        return AccurateRsp(req.sourceType)
    }

    override fun bookingOrder(req: CreateOrderReq): CreateOrderRsp {
        try {
            val passengers = commonService.getPassengerInfos(req.userId, req.passengers)
            var adult = 0
            var child = 0
            var inf = 0
            val p = passengers.map { p ->
                val t = PassengerTypeEnum.birthDayToEnum(p.birthday)
                when (t) {
                    PassengerTypeEnum.ADULT -> adult++;
                    PassengerTypeEnum.CHILD -> child++;
                    PassengerTypeEnum.INFANT -> inf++;
                }
                val r = OrderPassenger(
                    t.zfValue(),
                    p.firstName,
                    p.lastName,
                    p.birthday,
                    p.gender.zfValue(),
                    p.notionCode,
                    p.creditType.zfValue(),
                    p.creditNo,
                    p.creditValidDate,
                )
                r
            }
            val solution = zfFlightUtil.accRuleOrigin(req.accurateRequest.copy(adult, child, inf))
            val reqNew = if (req.contactInfo.email.isBlank()) {
                req.copy(contactInfo = req.contactInfo.copy(email = flightConfig.system().defaultEmail))
            } else {
                req
            }
            val booking = zfFlightUtil.booking(reqNew, solution, p)
            log.info("booking order result: {}", booking.toJSONString())
            val createOrderVo =
                initCreateOrderVo(reqNew, booking, zfFlightUtil.accRuleFormat(solution, reqNew.accurateRequest))
            val response = orderBaseService.createOrderBaseV2(createOrderVo.toJSONString())
            log.info("create order response: {}", response.toJSONString())
            if (!response.isSuccess) {
                log.error("create order fail: {}", createOrderVo.toJSONString())
                return CreateOrderRsp().copy(code = 500, message = "create order failed")
            }
            log.info("create order success: {}", createOrderVo.toJSONString())
            return CreateOrderRsp().copy(orderNo = response.data.toString(), message = "create order success")
        } catch (e: Exception) {
            log.error("bookingOrder error: {}", req.toJSONString(), e)
            if (e is BusinessException) {
                return CreateOrderRsp().copy(false, 500, message = e.message ?: "create order fail")
            }
        }
        return CreateOrderRsp().copy(false, 400, message = "create order fail")

    }

    override fun orderDetail(orderNo: String): String {
        TODO("Not yet implemented")
    }

    override fun orderDetail(orderMasterExt: OrderMasterExt): String {
        return zfFlightUtil.detail(orderMasterExt.outOrderNo).toJSONString()
    }

    override fun uploadFiles(vo: UploadFilesVo): List<String> {
        log.info("upload files order: {}, type: {}, size: {}", vo.orderNo, vo.type, vo.files.size)
        return vo.files.map {
            IdUtil.fastUUID()
        }
    }

    override fun pay(bigField: OrderBigFieldDTO): Boolean {
        try {
            val booingRsp = bigField.field2.toJSONString().into<OrderDetailRsp>()
            val req = PayReq(booingRsp.id, booingRsp.priceDetail.priceTotal)
            val payResponse = zfFlightUtil.pay(req)
            log.info("pay order success: {}", payResponse.toJSONString())
            payResponse.ticketPassengers()
            return true
        } catch (e: Exception) {
            log.error("pay error: {}", bigField.orderNo, e)
            return false
        }
    }

    override fun cancelOrder(orderBigDto: OrderBigFieldDTO, reason: String): Boolean {
        try {
            val booingRsp = orderBigDto.field2.toJSONString().into<OrderDetailRsp>()
            val cancelRsp = zfFlightUtil.cancel(booingRsp.id)
            log.info("cancel order success: {}", cancelRsp.toJSONString())
            return ZfOrderStatus.CANCELED.eq(cancelRsp.status)
        } catch (e: Exception) {
            log.error("cancelOrder error: {}", orderBigDto.orderNo, e)
            return false
        }
    }

    override fun queryChangeList(req: ChangeShopReq, bigField: OrderBigFieldDTO): Map<String, List<ReShopRsp>> {
//        val orderDetailRsp = bigField.field2.toJSONString().into<OrderDetailRsp>()
//        val request = ChangeListReq(
//            orderDetailRsp.id,
//            orderDetailRsp.journeys.first().segments.first().id,
//            req.segments.first().departTime,
//            orderDetailRsp.orderPassengers().map {
//                it.travelDocumentNumber
//            }
//        )
//        val changeListRsp = zfFlightUtil.changeList(request)
        return emptyMap()
    }

    override fun applyChange(req: ChangeApplyReq): Boolean {
        val zfApplyRecord = getApplyRecordByOrderNo(req.orderNo, ZfOrderType.REFUND) ?: return false
        val confirmRsp = zfFlightUtil.confirmRefund(zfApplyRecord.outNewId)
        log.info("config refund order rsp: {}", confirmRsp.toJSONString())
        when (confirmRsp.status) {
            ZfOrderStatus.RFD_REJECTED.value() -> {
                return false
            }
        }
        log.info("config refund order success: {}", confirmRsp.toJSONString())
        return true
    }

    override fun changeOrderFlush(orderNo: String): ResponseResult<ChangeOrderFlushResult?> {
        TODO("Not yet implemented")
    }

    override fun queryRefundFee(vo: QueryRefundFeeVo): QueryRefundFeeResultVo {
        val bigField = orderBaseService.queryOrderBigFieldPublic(vo.orderNo)
        log.info("init order bigField success: {}", bigField.orderNo)
        val bookingRsp = bigField.field2.toJSONString().into<OrderDetailRsp>()
        log.info("init order bigField field2 success: {}", bookingRsp.id)
        var zfApplyRecord = getApplyRecordByOrderNo(bigField.orderNo, ZfOrderType.REFUND)
        if (zfApplyRecord != null) {
            val refundRsp = zfFlightUtil.detail(zfApplyRecord.outNewId)
            log.info("detail refundRsp success: {}", refundRsp.toJSONString())
            if (!ZfOrderStatus.CLOSED.eq(refundRsp.status)) {
                log.info("order is open: {}", refundRsp.toJSONString())
                return QueryRefundFeeResultVo(
                    "0",
                    refundRsp.priceDetail.refundPrices().map {
                        RefundFeeResultVo(
                            NumberUtil.toStr(NumberUtil.add(it.refundFee, it.serviceFee, it.serviceFee))
                        )
                    },
                    "0"
                )

            }
            zfApplyRecordMapper.updateById(zfApplyRecord.copy(deleteFlag = true))
            log.info("refund order is close: {}", refundRsp.toJSONString())
        }
        val req = RefundReq(
            bookingRsp.id,
            bookingRsp.journeys.first().segments.map {
                it.id
            },
            bookingRsp.orderPassengers().map {
                it.travelDocumentNumber
            },
            vo.type.zfValue(),
        )
        log.info("req refund: {}", req.toJSONString())
        val refundRsp = zfFlightUtil.refund(req)
        log.info("new refundRsp success: {}", refundRsp.toJSONString())
        zfApplyRecord = ZfApplyRecord(ZfOrderType.REFUND, bigField.orderNo, bookingRsp.id, refundRsp.id)
        val saveValue = zfApplyRecordMapper.insert(zfApplyRecord)
        log.info("saveValue success: {}", saveValue > 0)
        return QueryRefundFeeResultVo(
            "0",
            refundRsp.priceDetail.refundPrices().map {
                RefundFeeResultVo(
                    NumberUtil.toStr(NumberUtil.add(it.refundFee, it.serviceFee, it.serviceFee))
                )
            },
            "0"
        )

    }

    override fun applyRefund(vo: ApplyRefundVo): ApplyRefundResultVo {
        val zfApplyRecord = getApplyRecordByOrderNo(vo.orderNo, ZfOrderType.REFUND) ?: return ApplyRefundResultVo()
        try {
            val confirmRsp = zfFlightUtil.confirmRefund(zfApplyRecord.outNewId)
            log.info("confirm refund order rsp: {}", confirmRsp.toJSONString())
            when (confirmRsp.status) {
                ZfOrderStatus.RFD_REJECTED.value() -> {
                    log.warn("confirm refund order success: {}", confirmRsp.toJSONString())
                    return ApplyRefundResultVo()
                }
            }
            log.info("confirm refund order success: {}", confirmRsp.toJSONString())
            return ApplyRefundResultVo(true, "")
        } catch (e: BusinessException) {
            return ApplyRefundResultVo(false, e.message ?: "")
        }
    }

    override fun notifyHandler(body: String): String {
        log.info("notify handler body: {}", body)
        val notifyBody = body.into<Resp<NotifyReq>>()
        val data = notifyBody.data ?: return notifyBody.message
        when (data.orderType) {
            ZfOrderType.ORIGIN.value -> {
                originHandler(data)
                return ""
            }

            ZfOrderType.CHANGE.value -> {
                changeHandler(data)
                return ""
            }

            ZfOrderType.REFUND.value -> {
                refundHandler(data)
                return ""
            }

            ZfOrderType.VOID.value -> {
                voidHandler(data)
                return ""
            }
        }
        log.warn("notify handler body: {}", notifyBody.toJSONString())
        return ""
    }

    override fun notifyChangeHandler(body: String): String {
        log.info("notify change handler body: {}", body)
        val data = body.into<ChangeNoticeBody>()
        if (data.privateFlight == null) {
            log.warn("change flight info is empty: {}", body)
            return ""
        }
        log.info("start notify change handler body: {}", body)
        orderStateService.flightChangeHandler(data)
        log.info("complete notify change handler body: {}", body)
        return ""
    }

    override fun changeOrderPay(orderNo: String): ResponseResult<Boolean> {
        TODO("Not yet implemented")
    }

    override fun changeOrderDetail(bigField: OrderBigFieldDTO): FlightOrderDetail.Detail {
        TODO("Not yet implemented")
    }

    override fun canRefund(bigField: OrderBigFieldDTO): Boolean {
        log.info("query can refund: {}", bigField.orderNo)
        try {
            val bookingRsp = bigField.field2.toJSONString().into<OrderDetailRsp>()
            val data = zfFlightUtil.detail(bookingRsp.id)
            when (data.status) {
                ZfOrderStatus.REVIEWING.value() -> {
                    val result = StrUtil.contains(data.rejectReason, "创建订单失败");
                    log.info("审核中订单是否可以退款: {}, {}", bigField.orderNo, result)
                    return result;
                }

                ZfOrderStatus.FAILED.value() -> {
                    log.info("failed, can refund: {}, {}", bigField.orderNo, bookingRsp.id)
                    return true;
                }
            }
            log.warn("cannot refund. not match status: {}", data.toJSONString())
            return false
        } catch (e: Exception) {
            log.error("query can refund order: {}", bigField.orderNo, e)
        }
        return false;
    }

    private fun originHandler(data: NotifyReq) {
        log.info("origin notify handler: {}", data.toJSONString())
        when (data.status) {
            ZfOrderStatus.REVIEWING.value() -> {
                // 审核中
                log.info("审核中 zf create order success: {}", data.toJSONString())
            }

            ZfOrderStatus.TO_BE_PAID.value() -> {
                // 待支付
                log.info("待支付 zf create order success: {}", data.toJSONString())
            }

            ZfOrderStatus.PROCEEDING.value(), ZfOrderStatus.RESERVED.value() -> {
                // 出票中
                orderStateService.flightTicketPayHandler(data)
            }

            ZfOrderStatus.ISSUED.value() -> {
                // 已出票
                orderStateService.flightTicketStatusModify(data)
            }

            else -> {
                log.warn("not support order status: {}", data.toJSONString())
            }
        }
    }

    private fun changeHandler(data: NotifyReq) {
        log.info("change notify handler: {}", data.toJSONString())
    }

    private fun refundHandler(data: NotifyReq) {
        log.info("refund notify handler: {}", data.toJSONString())
        val zfApplyRecord = getApplyRecordByOriginId(data.id, data.originOrderId, ZfOrderType.REFUND)
        if (zfApplyRecord == null) {
            log.error("not found order: {}", data.toJSONString())
            return
        }
        orderStateService.flightRefundHandler(data, zfApplyRecord.orderNo)
    }

    private fun voidHandler(data: NotifyReq) {
        log.info("void notify handler: {}", data.toJSONString())
    }

    private fun getApplyRecordByOrderNo(orderNo: String, type: ZfOrderType): ZfApplyRecord? {
        val queryWrapper = QueryWrapper<ZfApplyRecord>()
        queryWrapper.eq("order_no", orderNo)
            .eq("type", type)
            .eq("delete_flag", false);
        return zfApplyRecordMapper.selectOne(queryWrapper)
    }

    private fun getApplyRecordByOriginId(newId: String, originId: String, type: ZfOrderType): ZfApplyRecord? {
        val queryWrapper = QueryWrapper<ZfApplyRecord>()
        queryWrapper.eq("out_origin_id", originId)
            .eq("out_new_id", newId)
            .eq("type", type);
        return zfApplyRecordMapper.selectOne(queryWrapper)
    }

    private fun getApplyRecordByOriginId(originId: String, type: ZfOrderType): ZfApplyRecord? {
        val queryWrapper = QueryWrapper<ZfApplyRecord>()
        queryWrapper.eq("out_origin_id", originId)
            .eq("type", type);
        return zfApplyRecordMapper.selectOne(queryWrapper)
    }

    private fun initCreateOrderVo(
        req: CreateOrderReq,
        booking: OrderDetailRsp,
        ruleItem: AccRuleItem,
    ): CreateOrderVo {
        // 创建主单信息
        val masterVo = CreateOrderMasterVo(
            OrderTypeEnum.NORMAL.name,
            OrderBusinessTypeEnum.FLIGHT.name,
            req.userId,
            OrderUserTypeEnum.admin.name,
            ""
        )
        var totalPrice = BigDecimal.ZERO
        var totalTax = BigDecimal.ZERO
        var totalNotMarkUpPrice = BigDecimal.ZERO
        booking.priceDetail.originPrices().forEach {
            totalPrice = NumberUtil.add(
                totalPrice,
                NumberUtil.mul(commonService.getMarkUpPrice(it.price), it.num)
            )
            totalNotMarkUpPrice = NumberUtil.add(
                totalNotMarkUpPrice,
                NumberUtil.mul(BigDecimal(it.price), it.num)
            )
            totalTax = NumberUtil.add(totalTax, NumberUtil.mul(BigDecimal(it.tax), it.num))
        }

        val price = NumberUtil.roundStr(
            NumberUtil.add(totalPrice, totalTax).toString(), 2
        )
        val estimatedPrice = commonService.getMovePoint2(price)
        val beforeDiscountPrice = commonService.getMovePoint2(
            NumberUtil.roundStr(
                NumberUtil.add(
                    totalNotMarkUpPrice,
                    totalTax
                ).toString(), 2
            )
        )
        log.info(
            "total price: {}, total: {}, tax: {}", estimatedPrice,
            totalPrice, totalTax
        )
        log.info(
            "total price: {}, markupPrice: {}, tax: {}", beforeDiscountPrice,
            estimatedPrice, totalTax
        )
        // 财务信息
        val financeVo = CreateOrderFinanceVo(
            OrderSettleModeEnums.electronic.name,
            estimatedPrice,
            beforeDiscountPrice,
            estimatedPrice,
            beforeDiscountPrice - estimatedPrice,
            req.userId,
            PaymentChannelEnums.wechat.name,
            PaymentFromEnums.application.name,
            DateUtil.offset(Date(), DateField.SECOND, flightConfig.system().payTimeoutSeconds),
            CollectionAgencyEnums.YEE_PAY.name,
            CurrencyEnums.CNY.name
        )
        // 商品信息
        val productVo = CreateOrderProductVo(
            booking.id,
            "FLIGHT",
            1,
            1,
            estimatedPrice,
            beforeDiscountPrice,
            0,
            0,
            "",
            booking.priceDetail.toJSONString(),
            ""
        )
        val vo = OrderBaseVo(
            price, booking.id, OrderReqBase(
                totalTax.toString(),
                totalPrice.toString(),
                req,
                ruleItem
            )
        )
        // 附加数据
        val fieldVo = OrderBigFiledVo(
            ThirdServiceTypeEnum.flight_zf,
            JSONObject.from(vo),
            JSONObject.from(booking),
            null,
        )
        return CreateOrderVo(
            masterVo,
            financeVo,
            productVo,
            fieldVo,
            booking.id,
            req.insuranceSequenceNo,
            req.sessionId,
            emptyList(),
        )
    }
}