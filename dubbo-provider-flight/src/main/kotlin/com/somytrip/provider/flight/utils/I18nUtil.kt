package com.somytrip.provider.flight.utils

import cn.hutool.core.util.StrUtil
import com.somytrip.model.flight.enums.I18n
import org.slf4j.LoggerFactory.getLogger
import java.util.Locale

/**
 * @Description: 多语言工具类
 * @author: pigeon
 * @created: 2025-06-13 16:35
 */
object I18nUtil {
    private val log = getLogger(this::class.java)

    fun getValue(t: I18n, locale: Locale, v: Array<String> = emptyArray()): String {
        return when (locale.toString()) {
            "zh_CN" -> {
                StrUtil.format(t.cn(), *v)
            }

            "zh_TW" -> {
                StrUtil.format(t.tw(), *v)
            }

            "en_US" -> {
                StrUtil.format(t.en(), *v)
            }

            else -> {
                log.warn("not support locale: {}", locale.toString())
                StrUtil.format(t.cn(), *v)
            }
        }
    }
}

fun main() {
    println(
        I18nUtil.getValue(
            I18n.CHANGE,
            Locale.CHINA,
            emptyArray()
        )
    )

    print(
        I18nUtil.getValue(
            I18n.FREE_CARRY_LUGGAGE,
            Locale.CHINA,
            arrayOf("1", "1", "2")
        )
    )
}