package com.somytrip.provider.flight.service.impl

import cn.hutool.core.date.DateField
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.NumberUtil
import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.JSONObject
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.somytrip.api.service.order.OrderBaseService
import com.somytrip.api.service.order.OrderExtService
import com.somytrip.api.service.order.OrderStateService
import com.somytrip.entity.dto.order.FlightOrderDetail
import com.somytrip.entity.dto.order.OrderBigFieldDTO
import com.somytrip.entity.enums.order.OrderBusinessTypeEnum
import com.somytrip.entity.enums.order.OrderSettleModeEnums
import com.somytrip.entity.enums.order.OrderTypeEnum
import com.somytrip.entity.enums.order.OrderUserTypeEnum
import com.somytrip.entity.enums.pay.CollectionAgencyEnums
import com.somytrip.entity.enums.pay.CurrencyEnums
import com.somytrip.entity.enums.pay.PaymentChannelEnums
import com.somytrip.entity.enums.pay.PaymentFromEnums
import com.somytrip.entity.enums.thirdparty.ThirdServiceTypeEnum
import com.somytrip.entity.response.Response
import com.somytrip.entity.vo.CreateOrderFinanceVo
import com.somytrip.entity.vo.order.CreateOrderMasterVo
import com.somytrip.entity.vo.order.CreateOrderProductVo
import com.somytrip.entity.vo.order.CreateOrderVo
import com.somytrip.entity.vo.order.OrderBigFiledVo
import com.somytrip.exception.BusinessException
import com.somytrip.model.flight.common.*
import com.somytrip.model.flight.enums.*
import com.somytrip.model.flight.tc.*
import com.somytrip.model.flight.vo.*
import com.somytrip.provider.flight.config.FlightConfig
import com.somytrip.provider.flight.config.IDConfig
import com.somytrip.provider.flight.config.PriceConfig
import com.somytrip.provider.flight.service.CommonService
import com.somytrip.provider.flight.service.FlightService
import com.somytrip.provider.flight.utils.TCFlightUtil
import jakarta.annotation.Resource
import okhttp3.OkHttpClient
import org.apache.dubbo.config.annotation.DubboReference
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * @Description: 同程flight service 实现
 * @author: pigeon
 * @created: 2024-08-29 9:16
 */
@Service(value = "tcInternationalFlightService")
class TCInternationalFlightServiceImpl : FlightService {
    private val log = LoggerFactory.getLogger(this::class.java)
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(5, TimeUnit.MINUTES)
        .readTimeout(5, TimeUnit.MINUTES)
        .writeTimeout(5, TimeUnit.MINUTES)
        .callTimeout(5, TimeUnit.MINUTES)
        .build()
    private val flightUtil by lazy {
        TCFlightUtil.Builder()
            .okHttpClient(okHttpClient)
            .tcConfig(flightConfig.tcInternational())
            .builder()
    };

    @Resource
    private lateinit var idConfig: IDConfig

    @Resource
    private lateinit var flightConfig: FlightConfig

    @Resource
    private lateinit var priceConfig: PriceConfig

    @Resource
    private lateinit var commonService: CommonService

    @DubboReference(check = false)
    private lateinit var orderBaseService: OrderBaseService

//    @Resource(name = "cacheAccRuleItem")
//    private lateinit var caffeineAccRuleItemCache: Cache<String, AccRuleItem>

    @DubboReference(check = false)
    private lateinit var orderStateService: OrderStateService;

    @DubboReference(check = false)
    private lateinit var orderExtService: OrderExtService;

    private fun cacheTCAccurate(value: AccRuleItem) {
        commonService.cacheAccRuleItem(value.productInfo.orderInfo, value)
    }

    private fun getCacheValue(data: String): AccRuleItem {
        return commonService.getCacheAccRuleItem(data)
    }

    override fun support(provider: ProviderSourceEnum): Boolean {
        return provider() == provider
    }

    override fun provider(): ProviderSourceEnum {
        return ProviderSourceEnum.TC_INTERNATIONAL
    }

    override fun shoppingList(req: ShopListReq): Map<String, List<ShopListRsp>> {
        val cacheValue = commonService.getCacheShoppingList(req, provider())
        if (cacheValue.isNotEmpty()) {
            return cacheValue
        }
        val request = TCShoppingSearchRequest(
            "1",
            1,
            null,
            req.screenCondition.airline,
            listOf(
                TCShoppingSearchPassenger(req.adultNum(), PassengerTypeEnum.ADULT.tcValue()),
                TCShoppingSearchPassenger(req.childNum(), PassengerTypeEnum.CHILD.tcValue()),
                TCShoppingSearchPassenger(req.infantNum(), PassengerTypeEnum.INFANT.tcValue()),
            ).filter { it.count > 0 },
            req.segments.mapIndexed { index, it ->
                TCShoppingSearchSegmentItem(
                    index + 1,
                    it.departCode,
                    "",
                    it.arriveCode,
                    "",
                    it.departTime,
                )
            },
            null,
        )
        val rsp = shopping(request)
        if (rsp.productList.isEmpty()) {
            return emptyMap()
        }
        val result = mutableMapOf<String, MutableList<ShopListRsp>>()
        rsp.productList.forEach {
            var segDurationTotal = 0
            val item = ShopListRsp(
                it.prices.map { p ->
                    PriceItem(
                        PassengerTypeEnum.tcValueToEnums(p.value.passengerType),
                        p.value.taxB.toString(),
                        priceConfig.getChangePrice(p.value.priceB).toString(),
                        TaxDetail(
                            p.value.taxDetail.yqB.toString(),
                            p.value.taxDetail.taxB.toString()
                        )
                    )
                },
                it.segments.sortedBy { s -> s.segmentIndex }.map { s ->
                    var stopTimeTotal: Int = 0;
                    var durationTotal: Int = 0;
                    durationTotal += s.duration
                    stopTimeTotal += s.stopTime
                    segDurationTotal += durationTotal + stopTimeTotal
                    SegmentItem(
                        s.operatingAirline,
                        s.operatingFlightNo,
                        s.marketingAirline,
                        when (s.segmentType) {
                            2 -> 1
                            else -> 0
                        },
                        s.flightShare,
                        s.marketingFlightNo,
                        s.aircraft,
                        s.aircraftType.toString(),
                        s.arrAirport,
                        s.arrAirportTerm,
                        s.arrDateTime,
                        s.depAirport,
                        s.depAirportTerm,
                        s.depDateTime,
                        when (s.meal) {
                            1 -> "有餐食" // 有
                            2 -> "无餐食" // 无
                            else -> "" // 默认未知
                        },
                        s.mileage.toString(),
                        s.segmentIndex,
                        s.stops.map { sp ->
                            SegmentStopItem(
                                sp.stopAirport,
                                sp.stopTime,
                            )
                        },
                        CommonValFunc.formatIntToDuration(s.stopTime),
                        CommonValFunc.formatIntToDuration(s.duration),
                        CommonValFunc.formatIntToDuration(s.duration + s.stopTime),
                        commonService.getAirportName(s.arrAirport),
                        commonService.getAirportName(s.depAirport),
                        commonService.getAirline(s.marketingAirline),
                        s.stops.map { sp -> sp.stopAirport },
                        CommonValFunc.formatIntToDuration(s.stopTime),
                        s.cabinCode,
                    )
                },
                it.cabins.map { c ->
                    CabinItem(
                        CabinClassEnum.tcValueToEnums(c.cabinClass),
                        c.cabinCode,
                        c.cabinCount,
                        c.segmentIndex
                    )
                },
                ProductInfo(
                    it.productCode.toString(),
                    ProductTagEnum.tcValueToEnums(it.productTag),
                    provider().display,
                    provider(),
                ),
                1,
                CommonValFunc.formatIntToDuration(segDurationTotal)
            )
            val key = item.segments.joinToString { s -> s.flightNo }
            if (result.containsKey(key)) {
                result[key]?.add(item)
            } else {
                result[key] = mutableListOf(item)
            }
        }
        commonService.cacheShoppingList(req, result, provider())
        return result
    }

    private fun shopping(request: TCShoppingSearchRequest): TCShoppingBusinessResponse {
        try {
            val requestId = idConfig.getStrID()
            val response = flightUtil.flightShoppingSearch(request, requestId)
            return response
        } catch (e: Exception) {
            log.error("error: ${request.toJSONString()}\n", e)
        }
        return TCShoppingBusinessResponse()
    }

    override fun accSearch(req: AccurateReq): AccurateRsp {
        return accSearchMarkUpFlag(req, true)
    }

    private fun accSearchMarkUpFlag(req: AccurateReq, markUpFlag: Boolean = false): AccurateRsp {
        log.info("accSearchMarkUpFlag req: {}", req.toJSONString())
        val request = TCAccurateSearchRequest(
            1,
            req.tripType + 1,
            req.productInfos.map { it.productCode.toInt() },
            1,
            req.adultNum,
            req.childNum,
            req.infantNum,
            req.mainAirline,
            "0",
            req.segments.map {
                TCAccurateSearchSegment(
                    req.tripType + 1,
                    it.segmentIndex,
                    "",
                    it.departAirport,
                    it.departAirportTerm,
                    "",
                    it.arriveAirport,
                    it.arriveAirportTerm,
                    it.departDateTime,
                    it.arriveDateTime,
                    it.airline,
                    it.flightNo,
                    it.cabinCode,
                    "",
                    it.flightShare,
                    it.aircraft,
                    it.operationFlightNo,
                    it.operationAirline,
                )
            }
        )
        val requestId = idConfig.getStrID()
        try {
            val response = flightUtil.flightAccurateSearch(request, requestId)
            val result = AccurateRsp(
                1,
                response.flightUnits.filter { it.issueTicketInfo.changePnrCode == 1 }.map {
                    var segDurationTotal = 0
                    val item = AccRuleItem(
                        it.prices.values.map { p ->
                            PriceItem(
                                PassengerTypeEnum.tcValueToEnums(p.passengerType),
                                p.taxB.toString(),
                                p.priceB.toString(),
                                TaxDetail(p.taxDetail.yqB.toString(), p.taxDetail.taxB.toString())
                            )
                        },
                        it.segments.sortedBy { s -> s.segmentIndex }.map { s ->
                            var stopTimeTotal: Int = 0;
                            var durationTotal: Int = 0;
                            durationTotal += s.duration
                            stopTimeTotal += s.stopTime
                            segDurationTotal += durationTotal + stopTimeTotal
                            SegmentItem(
                                s.operatingAirline,
                                s.operatingFlightNo,
                                s.marketingAirline,
                                when (s.segmentType) {
                                    2 -> 1
                                    else -> 0
                                },
                                s.flightShare,
                                s.marketingFlightNo,
                                s.aircraft,
                                s.aircraftType.toString(),
                                s.arrAirport,
                                s.arrAirportTerm,
                                s.arrDateTime,
                                s.depAirport,
                                s.depAirportTerm,
                                s.depDateTime,
                                when (s.meal) {
                                    1 -> "有餐食" // 有
                                    2 -> "无餐食" // 无
                                    else -> "" // 默认未知
                                },
                                s.mileage.toString(),
                                s.segmentIndex,
                                s.stops.map { sp ->
                                    SegmentStopItem(
                                        sp.stopAirport,
                                        sp.stopTime,
                                    )
                                },
                                CommonValFunc.formatIntToDuration(s.stopTime),
                                CommonValFunc.formatIntToDuration(s.duration),
                                CommonValFunc.formatIntToDuration(s.duration + s.stopTime),
                                s.arrCityName,
                                s.depCityName,
                                commonService.getAirline(s.marketingAirline),
                                s.stops.map { sp -> sp.stopAirport },
                                CommonValFunc.formatIntToDuration(s.stopTime),
                                s.cabinCode,
                            )
                        },
                        ProductInfo(
                            it.productCode.toString(),
                            ProductTagEnum.tcValueToEnums(it.productTag),
                            provider().display,
                            provider(),
                            it.data,
                        ),
                        ReverseInformation(
                            it.refundChangeInfo.changeText,
                            it.refundChangeInfo.refundText,
                            it.refundChangeInfo.refundChangeRules.map { r ->
                                ReverseInfoItem(r)
                            },
                        ),
                        ServiceTime(it.serviceTime),
                        BaggageInfo(it.baggageInfo),
                        IssueTicketInfo(it.issueTicketInfo, it.invoiceWay, it.invoiceType),
                        LimitInfo(it.limitInfo)
                    )
                    cacheTCAccurate(item)
                    item
                }
            )
            return result.copy(
                rules = result.rules
                    .map {
                        it.copy(
                            prices = it.prices
                                .map { p ->
                                    p.copy(
                                        price = if (markUpFlag) {
                                            commonService.getMarkUpPrice(p.price).toString()
                                        } else {
                                            p.price
                                        }
                                    )
                                })
                    }
            )
        } catch (e: Exception) {
            log.error("精确询价接口报错: requestID: $requestId, request: ${JSON.toJSONString(request)}")
            log.error("", e)
            return AccurateRsp()
        }
    }

    override fun bookingOrder(req: CreateOrderReq): CreateOrderRsp {
        log.info("tc create order req: {}", req.toJSONString())
        var orderSerialNo: String = "";
        try {
            var ruleItem = getCacheValue(req.productInfo.orderInfo)
            log.info("cache get value: {}", ruleItem.productInfo.orderInfo)
            if (ruleItem.segments.isEmpty()) {
                val accRsp = accSearchMarkUpFlag(req.accurateRequest)
                if (accRsp.rules.isEmpty()) {
                    return CreateOrderRsp(false, 500, "未找到对应航班详细信息，请重新挑选！")
                }
                if (accRsp.rules.none {
                        it.productInfo.orderInfo == req.productInfo.orderInfo
                    }) {
                    log.info("req order info: {}", req.productInfo.toJSONString())
                    log.info("acc rsp order info: {}", accRsp.rules.joinToString { it.productInfo.orderInfo })
                    cacheTCAccurate(accRsp.rules.first())
                    log.info("flash order info: {}", accRsp.rules.map { it.productInfo }.toJSONString())
                    return CreateOrderRsp(
                        false, 306,
                        "价格已刷新，请核对价格后重试", "",
                        accRsp.copy(
                            rules = accRsp.rules
                                .map {
                                    it.copy(
                                        prices = it.prices
                                            .map { p ->
                                                p.copy(
                                                    price = commonService.getMarkUpPrice(p.price).toString()
                                                )
                                            })
                                })
                    )
                }
                ruleItem = accRsp.rules.first()
            }
            val passengers = commonService.getPassengerInfos(req.userId, req.passengers)
            // 判断年龄
            if (ruleItem.limitInfo.ages.isNotEmpty()) {
                for (passenger in passengers) {
                    if (passenger.birthday.isBlank()) {
                        continue
                    }
                    val age = DateUtil.ageOfNow(passenger.birthday)
                    for (ageLimit in ruleItem.limitInfo.ages) {
                        if (age < ageLimit.min || age > ageLimit.max) {
                            log.info("年龄校验未通过: {}", passenger.toJSONString())
                            return CreateOrderRsp(
                                false,
                                400,
                                "乘客-${passenger.lastName + passenger.firstName}年龄不符,最小:${ageLimit.min},最大:${ageLimit.max}"
                            )
                        }
                    }
                }
                log.info("年龄校验通过")
            }
            val orderRequest = initTCOrderRequest(passengers, ruleItem, req)
            val requestId = idConfig.getStrID();
            log.info("tc create order req: {}, {}", requestId, req.toJSONString())
            val orderResponse = flightUtil.flightCreateOrder(orderRequest, requestId)
            log.info("tc create order rsp: {}, {}", requestId, orderResponse.toJSONString())
            if (orderResponse.orderSerialNo.isBlank()) {
                log.error(
                    "tc create order fail: {}, {}, {}",
                    requestId,
                    orderRequest.toJSONString(),
                    orderResponse.toJSONString()
                )
                val messages = CommonValFunc.TC_MESSAGE_REGEX.findAll(orderResponse.toJSONString())
                if (!messages.none()) {
                    val message = messages.last()
                    log.warn("tc create order fail message: {}", message.value)
                    return CreateOrderRsp(false, 400, message.value)
                }
                return CreateOrderRsp(false, 500, "TC provider create order fail")
            }
            orderSerialNo = orderResponse.orderSerialNo
            val createOrderVo = initCreateOrderVo(req, orderRequest, ruleItem, orderResponse)
            log.info("init create order vo success")
            try {
                log.info("TC_Internation create order: {}", createOrderVo.toJSONString())
                val response = orderBaseService.createOrderBaseV2(createOrderVo.toJSONString())
                log.info("order service return: {}", response.toJSONString())
                if (response.isSuccess) {
                    orderSerialNo = "";
                    return CreateOrderRsp(true, 200, response.message, response.data.toString())
                } else {
                    return CreateOrderRsp(false, response.code, response.message)
                }
            } catch (e: Exception) {
                log.error("rpc order service fail: {}", createOrderVo.toJSONString())
            } finally {
                if (orderSerialNo.isNotBlank()) {
                    val request = TCOrderCancelRequest(
                        orderSerialNo,
                        createOrderVo.orderBigFiled.field2.toJSONString().into<TCOrderBusinessResponse>()
                            .productItems.map {
                                TCOrderCancelProduct(
                                    it.productDetail
                                        .into<TCOrderCancelProductDetail>(), it.productType
                                )
                            }, "系统自动取消"
                    )
                    if (notPayCancelOrder(request)) {
                        log.info("orderSerialNo:${orderSerialNo} system cancel success")
                    } else {
                        log.error("orderSerialNo:${orderSerialNo} system cancel fail")
                    }
                }
            }
        } catch (e: Exception) {
            log.error("create order fail: {}", req.toJSONString())
            log.error("", e)
        }
        return CreateOrderRsp(false, 500, "TC provider create order fail")
    }

    private fun notPayCancelOrder(req: TCOrderCancelRequest): Boolean {
        val requestId = idConfig.getStrID()
        log.info("tc cancel order req: {}", req.toJSONString())
        try {
            val response = flightUtil.flightCancelOrder(req, requestId)
            log.info("tc cancel order rsp: {}", response.toJSONString())
            return response.success
        } catch (e: Exception) {
            log.error("tc cancel order req fail: {}", req.toJSONString())
            log.error("", e)
        }
        return false
    }

    private fun initCreateOrderVo(
        req: CreateOrderReq,
        orderRequest: TCOrderRequest,
        accItem: AccRuleItem,
        orderResponse: TCOrderBusinessResponse
    ): CreateOrderVo {
        val masterVo = CreateOrderMasterVo(
            OrderTypeEnum.NORMAL.name,
            OrderBusinessTypeEnum.FLIGHT.name,
            req.userId,
            OrderUserTypeEnum.admin.name,
            ""
        )
        var totalPrice = BigDecimal.ZERO
        var totalTax = BigDecimal.ZERO
        var totalPriceNotMarkUp = BigDecimal.ZERO
        orderRequest.products.forEach {
            it.productDetail.prices.forEach { p ->
                totalTax = NumberUtil.add(totalTax.toString(), p.tax)
                totalPrice = NumberUtil.add(commonService.getMarkUpPrice(p.price), totalPrice);
                totalPriceNotMarkUp = NumberUtil.add(totalPriceNotMarkUp.toString(), p.price)
            }
        }
        // 财务信息
        val price = NumberUtil.roundStr(
            NumberUtil.add(totalPrice, totalTax).toString(), 2
        )
        log.info("uid: {}, tcPay price yuan: {}, not markup price: {}", req.userId, price, totalPriceNotMarkUp)
        val estimatedPrice = commonService.getMovePoint2(price)
        log.info("uid: {}, tcPay price fen: {}", req.userId, estimatedPrice)
        val beforeDiscountPrice = commonService.getMovePoint2(
            NumberUtil.roundStr(
                NumberUtil.add(
                    totalPriceNotMarkUp,
                    totalTax
                ).toString(), 2
            )
        )
        val financeVo = CreateOrderFinanceVo(
            OrderSettleModeEnums.electronic.name,
            estimatedPrice,
            beforeDiscountPrice,
            estimatedPrice,
            beforeDiscountPrice - estimatedPrice,
            req.userId,
            PaymentChannelEnums.wechat.name,
            PaymentFromEnums.application.name,
            DateUtil.offset(Date(), DateField.SECOND, flightConfig.system().payTimeoutSeconds),
            CollectionAgencyEnums.YEE_PAY.name,
            CurrencyEnums.CNY.name
        )

        // 商品信息
        val productVo = CreateOrderProductVo(
            orderResponse.orderSerialNo,
            "FLIGHT",
            1,
            orderRequest.products.size,
            estimatedPrice,
            beforeDiscountPrice,
            0,
            0,
            "",
            "{}",
            ""
        )
        val vo = OrderBaseVo(
            price, orderResponse.orderSerialNo, OrderReqBase(
                totalTax.toString(),
                totalPrice.toString(),
                req,
                accItem
            ),
            orderResponse.productItems
        )
        // 附加数据
        val fieldVo = OrderBigFiledVo(
            ThirdServiceTypeEnum.flight,
            JSONObject.from(vo),
            JSONObject.from(orderResponse),
            null,
        )

        return CreateOrderVo(
            masterVo,
            financeVo,
            productVo,
            fieldVo,
            orderResponse.orderSerialNo,
            req.insuranceSequenceNo,
            req.sessionId,
            emptyList(),
        )

    }

    private fun initTCOrderRequest(
        passengers: List<PassengerInfo>,
        accItem: AccRuleItem,
        req: CreateOrderReq,
    ): TCOrderRequest {
        val priceMap = accItem.prices.associateBy { it.passengerType }
        var priceTotal = BigDecimal.ZERO
        val request = TCOrderRequest(
            buisinessLineType = 1,
            contact = TCOrderContact(
                req.contactInfo.name,
                null,
                null,
                req.contactInfo.email,
                req.contactInfo.mobile,
                null
            ),
            products = listOf(
                TCOrderProduct(
                    accItem.productInfo.orderInfo,
                    "0",
                    productType = ProductOrderTypeEnum.FLIGHT.tcValue,
                    productDetail = TCOrderProductDetail(
                        officeNo = "",
                        passengers = passengers.map {
                            TCOrderPassenger(
                                it.lastName,
                                it.firstName,
                                it.gender.tcValue,
                                it.type.tcIntValue(),
                                it.creditType.tcCreate(),
                                it.notion,
                                it.notionCode,
                                it.creditNo,
                                it.creditValidDate,
                                it.creditIssueNotion,
                                it.birthday,
                                it.linkPhone
                            )
                        },
                        prices = passengers.map {
                            val price = TCOrderPrice(
                                priceMap[it.type]?.price ?: "0",
                                priceMap[it.type]?.taxTotal ?: "0",
                                it.type.tcIntValue()
                            )
                            val itemPriceTotal = NumberUtil.add(price.price, price.tax)
                            priceTotal = NumberUtil.add(itemPriceTotal, priceTotal)
                            price
                        },
                        bigPnrCode = "",
                    )
                )
            ).map {
                it.copy(price = NumberUtil.roundStr(priceTotal.toString(), 2))
            },
            totalPrice = NumberUtil.roundStr(priceTotal.toString(), 2),
        )
        return request
    }

    override fun tcPay(req: TCPayInfo): TCPayResponse {
        try {
            val payCheckId = idConfig.getStrID()
            var totalPrice = BigDecimal.ZERO
            req.products.forEach { totalPrice = NumberUtil.add(totalPrice, NumberUtil.parseNumber(it.price)) }
            val totalPriceStr = NumberUtil.roundStr(totalPrice.toString(), 2)
            val payCheckReq = TCPayCheckRequest(
                req.orderSerialNo,
                totalPriceStr,
                req.orderNo,
                req.products
            )
            log.info("tc tcPay check req:{}, {}", payCheckId, payCheckReq.toJSONString())
            val payCheckRsp = flightUtil.flightPayCheck(payCheckReq, payCheckId)
            log.info("tc tcPay check rsp:{}, {}", payCheckId, payCheckRsp.toJSONString())
            if (!payCheckRsp.success) {
                log.info("tc tcPay check fail: {}", req.toJSONString())
                return TCPayResponse(payCheckRsp.message, payCheckRsp.orderSerialNo)
            }
            log.info("tc tcPay check success")
            val payId = idConfig.getStrID()
            val payReq = TCPayRequest(
                req.orderSerialNo,
                totalPriceStr,
            )
            log.info("tc tcPay req:{}, {}", payId, payReq.toJSONString())
            val payRsp = flightUtil.flightPay(payReq, payId)
            log.info("tc tcPay rsp:{}, {}", payId, payRsp.toJSONString())
            if (payRsp.success) {
                return TCPayResponse(payRsp.message, payRsp.orderSerialNo, true)
            }
        } catch (e: Exception) {
            log.error("tc tcPay order fail: {}", req.toJSONString())
            log.error("", e)
        }
        return TCPayResponse("error", req.orderSerialNo, false)
    }

    override fun orderDetail(orderNo: String): String {
        val orderExt = orderExtService.queryOrderMasterExtByOrderNo(orderNo)
        if (orderExt == null) {
            log.warn("get order ext fail: {}", orderNo)
            throw BusinessException("not found order ext info")
        }
        val requestId = idConfig.getStrID()
        log.info("orderNo: {} query order detail requestId: {}", orderNo, requestId)
        val request = TCOrderDetailRequest(orderExt.outOrderNo, requestId)
        val response = flightUtil.flightOrderDetail(request, request.traceId);
        log.info(
            "orderNo: {} query order detail requestId: {}, response: {}",
            orderNo,
            requestId,
            response.toJSONString()
        )
        return response.toJSONString()
    }

    override fun uploadFiles(vo: UploadFilesVo): List<String> {
        log.info("upload refund files vo: {}", vo.orderNo)
        val orderDetail = orderDetail(vo.orderNo).into<TCOrderDetailQueryResponse>()
        val request = TCRefundFileUploadRequest(
            orderDetail.products.first().orderSerialNo,
            "",
            vo.orderNo,
            vo.files.map {
                TCRefundFileUploadFile(
                    it.file,
                    it.fileName
                )
            }
        )
        val requestId = idConfig.getStrID()
        val response = flightUtil.flightRefundFileUpload(request, requestId);
        log.info("upload refund files response: {}", response.toJSONString())
        return response.fileIds
    }

    override fun queryRefundFee(vo: QueryRefundFeeVo): QueryRefundFeeResultVo {
        val orderDetail = orderDetail(vo.orderNo).into<TCOrderDetailQueryResponse>()
        val passengerMap = mutableMapOf<String, TCOrderDetailQueryPassenger>()
        val ticketMap = mutableMapOf<String, TCOrderDetailQueryTicket>()
        orderDetail.products.forEach {
            it.passengers.forEach { passenger ->
                val key = "${passenger.firstName}${passenger.lastName}"
                passengerMap[key] = passenger
            }
            it.tickets.forEach { ticket ->
                ticketMap[ticket.ticketNo] = ticket
            }
        }
        val ableRefundRequest = TCRefundAbleRequest(
            orderDetail.products.first().orderSerialNo,
            vo.type.tcValue,
            vo.ticketNos.map {
                val key = ticketMap[it]?.firstName + ticketMap[it]?.lastName
                TCRefundAblePassengerInfo(
                    passengerMap[key]?.creditNo ?: "",
                    key,
                    it
                )
            },
            idConfig.getStrID()
        )
        val ableRefundResponse = flightUtil.flightRefundable(ableRefundRequest, ableRefundRequest.traceId)
        val feeList = mutableListOf<RefundFeeResultVo>()
        ableRefundResponse.routePassengerInfos.forEach {
            it.passengerRefundInfo.forEach { p ->
                p.refundProducts.forEach { r ->
                    feeList.add(RefundFeeResultVo(r.purchasePenalty.toString()))
                }
            }
        }

        log.info("able refund response: {}", ableRefundResponse.toJSONString())
        return QueryRefundFeeResultVo(
            "",
            feeList,
            "0"
        )
    }

    override fun applyRefund(vo: ApplyRefundVo): ApplyRefundResultVo {
        val orderDetail = orderDetail(vo.orderNo).into<TCOrderDetailQueryResponse>()
        val passengerMap = mutableMapOf<String, TCOrderDetailQueryPassenger>()
        val ticketMap = mutableMapOf<String, TCOrderDetailQueryTicket>()
        orderDetail.products.forEach {
            it.passengers.forEach { passenger ->
                val key = "${passenger.firstName}${passenger.lastName}"
                passengerMap[key] = passenger
            }
            it.tickets.forEach { ticket ->
                ticketMap[ticket.ticketNo] = ticket
            }
        }
        val request = TCRefundApplyRequest(
            orderDetail.products.first().orderSerialNo,
            vo.orderNo,
            vo.type.tcValue,
            vo.remark,
            vo.fileIds,
            vo.reason.tcValue,
            vo.ticketNos.map {
                val key = ticketMap[it]?.firstName + ticketMap[it]?.lastName
                TCRefundApplyPassengerInfo(
                    passengerMap[key]?.creditNo ?: "",
                    key,
                    it
                )
            }
        )
        val requestId = idConfig.getStrID()
        log.info("apply refund: {}", requestId)
        val response = flightUtil.flightRefundApply(request, requestId)
        log.info("apply refund requestId: {}, response: {}", requestId, response.toJSONString())
        return ApplyRefundResultVo(true, response.toJSONString())
    }

    override fun tcNotify(body: NotifyBody<TCNotifyBody>): NotifyResult<String> {
        val data = body.data
        log.info("tc notifyNew body: {}", body.toJSONString())
        when (TCNotifyEnum.codeToEnum(data.serviceCode)) {
            TCNotifyEnum.PAY_NOTIFY -> {
                orderStateService.flightTicketPayHandler(tcPayNotify(data))
            }

            TCNotifyEnum.TICKET_RESULT_NOTIFY -> {
                orderStateService.flightTicketStatusModify(tcTicketNotify(data))
            }

            TCNotifyEnum.REFUND_RESULT_NOTIFY -> {
                orderStateService.flightRefundHandler(refundResultNotify(data));
            }

            else -> {
                log.error("not support type: {}", body.toJSONString())
                return NotifyResult("ERROR")
            }
        }
        return NotifyResult("SUCCESS")
    }

    override fun cancelOrder(orderBigDto: OrderBigFieldDTO, reason: String): Boolean {
        log.info("TC_INTERNATIONAL order cancel dto: {}", orderBigDto.toJSONString())
        // 构造取消订单信息
//        val orderBaseVo = orderBigDto.field1.toJSONString().into<OrderBaseVo>()
        val tcResponse = orderBigDto.field2.toJSONString().into<TCOrderBusinessResponse>()
        val request = TCOrderCancelRequest(
            orderSerialNo = tcResponse.orderSerialNo,
            cancelProducts = tcResponse.productItems.map {
                TCOrderCancelProduct(
                    productType = it.productType,
                    cancelProductDetail = TCOrderCancelProductDetail(
                        it.productDetail.into<TCOrderCancelProductDetail>().pnr
                    )
                )
            },
            reason = reason
        )
        val requestId = idConfig.getStrID();
        log.info("tc cancel order request: {}", request.toJSONString())
        val cancelResponse = flightUtil.flightCancelOrder(request, requestId)
        log.info("request: {}, response: {}", request.toJSONString(), cancelResponse.toJSONString())
        return cancelResponse.success
    }

    override fun queryChangeList(req: ChangeShopReq, bigField: OrderBigFieldDTO): Map<String, List<ReShopRsp>> {
        return emptyMap()
    }

    override fun applyChange(req: ChangeApplyReq): Boolean {
        TODO("Not yet implemented")
    }

    override fun changeOrderFlush(orderNo: String): Response<ChangeOrderFlushResult?> {
        TODO("Not yet implemented")
    }

    override fun changeOrderPay(orderNo: String): Response<Boolean> {
        TODO("Not yet implemented")
    }

    override fun changeOrderDetail(bigField: OrderBigFieldDTO): FlightOrderDetail.Detail {
        val result = FlightOrderDetail.Detail()
        return result
    }

    private fun refundResultNotify(body: TCNotifyBody): TCRefundResultNotify {
        val result = flightUtil.uncompress(body.businessRequest)
        log.info("refundResultNotify uncompress data: $result")
        return result.into<TCRefundResultNotify>()
    }

    private fun tcTicketNotify(body: TCNotifyBody): TCTicketNotify {
        val result = flightUtil.uncompress(body.businessRequest)
        log.info("tcTicketNotify uncompress data: $result")
        return result.into<TCTicketNotify>()
    }

    private fun tcPayNotify(body: TCNotifyBody): TCPayNotify {
        val result = flightUtil.uncompress(body.businessRequest)
        log.info("tcPayNotify uncompress data: $result")
        return result.into<TCPayNotify>()
    }
}