package com.somytrip.provider.flight.service.impl

import cn.hutool.core.date.DateField
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.NumberUtil
import cn.hutool.crypto.digest.DigestAlgorithm
import cn.hutool.crypto.digest.Digester
import com.alibaba.fastjson2.JSONObject
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.somytrip.api.service.order.OrderBaseService
import com.somytrip.api.service.order.OrderStateService
import com.somytrip.entity.dto.order.FlightOrderDetail
import com.somytrip.entity.dto.order.OrderBigFieldDTO
import com.somytrip.entity.enums.order.OrderBusinessTypeEnum
import com.somytrip.entity.enums.order.OrderSettleModeEnums
import com.somytrip.entity.enums.order.OrderTypeEnum
import com.somytrip.entity.enums.order.OrderUserTypeEnum
import com.somytrip.entity.enums.pay.CollectionAgencyEnums
import com.somytrip.entity.enums.pay.CurrencyEnums
import com.somytrip.entity.enums.pay.PaymentChannelEnums
import com.somytrip.entity.enums.pay.PaymentFromEnums
import com.somytrip.entity.enums.thirdparty.ThirdServiceTypeEnum
import com.somytrip.entity.response.Response
import com.somytrip.entity.vo.CreateOrderFinanceVo
import com.somytrip.entity.vo.order.CreateOrderMasterVo
import com.somytrip.entity.vo.order.CreateOrderProductVo
import com.somytrip.entity.vo.order.CreateOrderVo
import com.somytrip.entity.vo.order.OrderBigFiledVo
import com.somytrip.model.flight.common.*
import com.somytrip.model.flight.enums.*
import com.somytrip.model.flight.fr24.*
import com.somytrip.model.flight.vo.*
import com.somytrip.provider.flight.config.FlightConfig
import com.somytrip.provider.flight.config.IDConfig
import com.somytrip.provider.flight.service.CommonService
import com.somytrip.provider.flight.service.FlightService
import com.somytrip.provider.flight.utils.FR24Util
import jakarta.annotation.Resource
import okhttp3.Credentials
import okhttp3.OkHttpClient
import org.apache.dubbo.config.annotation.DubboReference
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.net.InetSocketAddress
import java.net.Proxy
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-08-15 15:08
 */
@Service(value = "fR24FlightService")
class FR24FlightServiceImpl : FlightService {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Resource
    private lateinit var idConfig: IDConfig

    @Resource
    private lateinit var commonService: CommonService;

    @DubboReference(check = false)
    private lateinit var orderBaseService: OrderBaseService

    @DubboReference(check = false)
    private lateinit var orderStateService: OrderStateService;

    @Resource
    private lateinit var flightConfig: FlightConfig

    @Value("\${spring.profiles.active}")
    private lateinit var activeProfile: String
    private val okHttpClient by lazy {
        val builder = OkHttpClient.Builder()
            .connectTimeout(5, TimeUnit.MINUTES)
            .readTimeout(5, TimeUnit.MINUTES)
            .writeTimeout(5, TimeUnit.MINUTES)
            .callTimeout(5, TimeUnit.MINUTES)
        if (activeProfile == "dev" || activeProfile == "local") {
            builder.proxy(Proxy(Proxy.Type.HTTP, InetSocketAddress("*************", 31280)))
            builder.proxyAuthenticator { _, response ->
                val credential = Credentials.basic("smt-proxy", "@@smt\$\$proxy##password");
                return@proxyAuthenticator response.request.newBuilder().header("Proxy-Authorization", credential)
                    .build()
            }
        }
        builder.build()
    }
    private val fR24Util by lazy {
        FR24Util.Builder()
            .okHttpClient(okHttpClient)
            .commonService(commonService)
            .fR24Config(flightConfig.fR24())
            .authenticationSignDigester(Digester(DigestAlgorithm.SHA512))
            .build()
    }

    override fun tripPlan(req: ShopListReq): TripPlanRsp {
        try {
            val shopRequest = ShoppingRequest(
                fR24Util.getAuthentication(),
                req.segments.map {
                    SearchLegItem(
                        it.departCode,
                        it.arriveCode,
                        it.departTime
                    )
                },
                req.adultNum(),
                req.childNum(),
                req.infantNum(),
            )
            val fR24Response = fR24Util.shoppingList(shopRequest)
            log.info("tripPlan get data success: {}", fR24Response.notDataStr())
            val sourceType =
                sourceType(shopRequest.searchLegs.map { it.origin } + shopRequest.searchLegs.map { it.destination })
            val tripPlanRsp = fR24Util.shoppingTripPlan(fR24Response.data, req, sourceType)
            log.info("tripPlan deal data: {}", tripPlanRsp.success)
            return tripPlanRsp
        } catch (e: Exception) {
            log.error("tripPlan error", e)
            log.error("tripPlan request: {}", req.toJSONString())
        }
        return TripPlanRsp()
    }

    override fun cancelOrder(orderBigDto: OrderBigFieldDTO, reason: String): Boolean {
        log.info("FR24 cancel data: {}", reason)
        return true
    }

    override fun queryChangeList(req: ChangeShopReq, bigField: OrderBigFieldDTO): Map<String, List<ReShopRsp>> {
//        val bigFieldDto = orderBaseService.queryOrderBigFieldPublic(req.orderNo) ?: return emptyMap()
//        val baseVo = bigFieldDto.field1.toJSONString().into<OrderBaseVo>()
//        val bookingData = bigFieldDto.field2.toJSONString().into<BookingData>()
//        baseVo.accurate.request.passengers
//        val passengers = commonService.getPassengerInfos(baseVo.accurate.request.userId, req.passengers.map {
//            baseVo.accurate.request.passengers[it]
//        })
//        val request = ChangeReShopRequest(
//            fR24Util.getAuthentication(),
//            bookingData.orderNo,
//            bookingData.segments.map {
//                OriginalSegmentItem(
//                    it.carrier,
//                    it.flightNo,
//                    it.depTime
//                )
//            },
//            req.segments.map {
//                NewLegItem(
//                    it.departCode,
//                    it.arriveCode,
//                    it.departTime
//                )
//            },
//            passengers.count { it.type == PassengerTypeEnum.ADULT },
//            passengers.count { it.type == PassengerTypeEnum.CHILD },
//            passengers.count { it.type == PassengerTypeEnum.INFANT },
//            preferences = Preferences("")
//        )
//        log.info("re shop request: {}", request.toJSONString())
//        val reShop = fR24Util.changeReShop(request)
//        fR24Util.changeReShopData(reShop)
        // TODO
//        return
        return emptyMap()
    }

    override fun applyChange(req: ChangeApplyReq): Boolean {
        TODO("Not yet implemented")
    }

    override fun changeOrderFlush(orderNo: String): Response<ChangeOrderFlushResult?> {
        TODO("Not yet implemented")
    }

    override fun changeOrderPay(orderNo: String): Response<Boolean> {
        TODO("Not yet implemented")
    }

    override fun changeOrderDetail(bigField: OrderBigFieldDTO): FlightOrderDetail.Detail {
        TODO("Not yet implemented")
    }

    override fun support(provider: ProviderSourceEnum): Boolean {
        return provider() == provider
    }

    override fun provider(): ProviderSourceEnum {
        return ProviderSourceEnum.FR24
    }

    override fun shoppingList(req: ShopListReq): Map<String, List<ShopListRsp>> {
        try {
            val cacheValue = commonService.getCacheShoppingList(req, provider())
            if (cacheValue.isNotEmpty()) {
                return cacheValue
            }
            val shopRequest = ShoppingRequest(
                fR24Util.getAuthentication(),
                req.segments.map {
                    SearchLegItem(
                        it.departCode,
                        it.arriveCode,
                        it.departTime
                    )
                },
                req.adultNum(),
                req.childNum(),
                req.infantNum(),
            )
            val fR24Response = fR24Util.shoppingList(shopRequest)
            log.debug("fr24 response: {}", fR24Response.notDataStr())
            val sourceType =
                sourceType(shopRequest.searchLegs.map { it.origin } + shopRequest.searchLegs.map { it.destination })
            val shoppingData = fR24Util.shoppingData(fR24Response.data, sourceType)
            log.info("shop data keys: {}", shoppingData.keys.toJSONString())
            commonService.cacheShoppingList(req, shoppingData, provider())
            return shoppingData
        } catch (e: Exception) {
            log.error("shoppingList error: {}", req.toJSONString(), e)
        }
        return emptyMap()
    }

    override fun accSearch(req: AccurateReq): AccurateRsp {
        try {
            val request = PricingRequest(
                fR24Util.getAuthentication(),
                req.productInfos.first().orderInfo.into<FR24OrderInfo>().offerId,
                req.adultNum,
                req.childNum,
                req.infantNum,
            )
            val fR24Response = fR24Util.pricing(request)
            val sourceType = sourceType(req.segments.map { it.departAirport } + req.segments.map { it.arriveAirport })
            val pricingData = fR24Util.pricingData(fR24Response.data, sourceType)
            return pricingData
        } catch (e: Exception) {
            log.error("accSearch error", e)
            log.error("accSearch request: {}", req.toJSONString())
        }
        return AccurateRsp()
    }

    override fun bookingOrder(req: CreateOrderReq): CreateOrderRsp {
        try {
            val passengerNum = mutableListOf(0, 0, 0)
            val adultList = mutableListOf<PassengerNumLimit>()
            val passengers = commonService.getPassengerInfos(req.userId, req.passengers).mapIndexed { index, it ->
                val name = if (it.creditType == CreditTypeEnum.ID_CARD) {
                    it.firstName + CommonValFunc.EN_NAME_SPLIT + it.lastName
                } else {
                    it.lastName + CommonValFunc.EN_NAME_SPLIT + it.firstName
                }
                val paxId = when (it.type) {
                    PassengerTypeEnum.ADULT -> {
                        passengerNum[0]++
                        adultList.add(PassengerNumLimit("${index + 1}", 2, 1))
                        ""
                    }

                    PassengerTypeEnum.CHILD -> {
                        passengerNum[1]++
                        val passengerNumLimit = adultList.first { a -> a.childNum > 0 }
                        passengerNumLimit.childNum--
                        passengerNumLimit.key
                    }

                    PassengerTypeEnum.INFANT -> {
                        passengerNum[2]++
                        val passengerNumLimit = adultList.first { a -> a.infNum > 0 }
                        passengerNumLimit.infNum--
                        passengerNumLimit.key
                    }

                    else -> {
                        log.warn("not support passenger type: {}", req.toJSONString())
                        ""
                    }
                }
                PassengerItem(
                    index + 1,
                    name,
                    it.type.fr24Value(),
                    it.birthday,
                    it.gender.fr24Value,
                    it.creditNo,
                    it.creditType.fr24Value(),
                    it.creditIssueNotion,
                    it.creditValidDate,
                    it.notionCode,
                    req.contactInfo.email,
                    it.linkPhone,
                    "+86",
                    paxId
                )
            }
            val request = PricingRequest(
                fR24Util.getAuthentication(),
                req.accurateRequest.productInfos.first().orderInfo.into<FR24OrderInfo>().offerId,
                passengerNum[0],
                passengerNum[1],
                passengerNum[2],
            )
            val pricingResponse = fR24Util.pricing(request)
            if (!pricingResponse.success()) {
                return CreateOrderRsp().copy(message = "请重新挑选航班", success = false, code = 500)
            }
            log.info("FR24 order pricing: {}", pricingResponse.data.toJSONString())
            val pricingData = fR24Util.pricingData(pricingResponse.data)
            val reqBody = initBookingOrderReq(req, pricingResponse.data.offer.first().offerId, passengers)
            val bookingResponse = fR24Util.booking(reqBody)
            if (!bookingResponse.success()) {
                return CreateOrderRsp().copy(code = 400, message = bookingResponse.message, success = false)
            }
            log.info("FR24 booking rsp: {}", bookingResponse.data.toJSONString())
            val createOrderReq = initCreateOrderVo(req, bookingResponse.data, pricingData, reqBody)
            log.info("FR24 create order: {}", createOrderReq.toJSONString())
            val createOrderRsp = orderBaseService.createOrderBaseV2(createOrderReq.toJSONString())
            if (!createOrderRsp.isSuccess) {
                return CreateOrderRsp().copy(code = 500, message = "Somytrip create order failed")
            }
            return CreateOrderRsp().copy(orderNo = createOrderRsp.data.toString(), message = "create order success")
        } catch (e: Exception) {
            log.error("bookingOrder error: {}", req.toJSONString(), e)
            if (e is NoSuchElementException) {
                return CreateOrderRsp().copy(message = "儿童|婴儿必须跟随成人下单", success = false, code = 400)
            }
            if (e is RuntimeException) {
                return CreateOrderRsp().copy(false, 400, message = e.message ?: "create order fail")
            }
        }
        return CreateOrderRsp().copy(false, 400, message = "create order fail")
    }

    override fun fr24Pay(req: FR24PayInfo): TicketingData {
        log.info("fr24Pay order: {}", req.toJSONString())
        try {
            val request = TicketingRequest(
                fR24Util.getAuthentication(),
                req.orderNo,
                req.partnerOrderNo,
                req.currency,
                req.totalPrice,
            )
            val ticketingResponse = fR24Util.ticketing(request);
            if (ticketingResponse.success()) {
                return ticketingResponse.data
            }
        } catch (e: Exception) {
            log.error("fr24Pay order error", e)
            log.error("fr24Pay order error: {}", req.toJSONString())
        }
        return TicketingData()
    }

    override fun orderDetail(orderNo: String): String {
        TODO("Not yet implemented")
    }

    override fun uploadFiles(vo: UploadFilesVo): List<String> {
        TODO("Not yet implemented")
    }

    private fun fr24Notify(notify: OrderChangeInfoNotify): FR24NotifyResponse {
        log.info("notifyNew body: {}", notify.toJSONString())
        val result = FR24NotifyResponse(
            notify.traceId,
            CommonValFunc.FR24_CODE_SUCCESS,
            CommonValFunc.FR24_MESSAGE_SUCCESS,
            0,
            fR24Util.getAuthentication(),
        )
        log.info("notifyNew result: {}", result.toJSONString())
        return result
    }

    override fun fr24Notify(body: NotifyBody<OrderChangeInfoNotify>): NotifyResult<FR24NotifyResponse> {
        log.info("fr24 notifyNew body: {}", body.toJSONString())
        val data = body.data
        val response = fr24Notify(body.data)
        when (FR24NotifyTypeEnum.codeToEnum(data.type)) {
            FR24NotifyTypeEnum.ORDER_STATUS -> {
                log.info("order status notify: {}", body.toJSONString())
                when (FR24OrderStatusEnum.codeToEnum(data.orderStatus)) {
                    FR24OrderStatusEnum.CREATED,
                    FR24OrderStatusEnum.TICKETING,
//                    FR24OrderStatusEnum.AWAITING_CONFIRMATION,
                    FR24OrderStatusEnum.PAYMENT_SUCCESSFUL -> {
                        orderStateService.flightTicketPayHandler(data)
                    }

                    FR24OrderStatusEnum.AWAITING_CONFIRMATION -> {
                        log.warn("order status AWAITING_CONFIRMATION notify: {}", data.toJSONString())
                    }

                    FR24OrderStatusEnum.TICKETED -> {
                        orderStateService.flightTicketStatusModify(data)
                    }

                    else -> {
                        log.error("not support orderStatus: {}", body.toJSONString())
                    }
                }
            }

            FR24NotifyTypeEnum.TICKET_NO, FR24NotifyTypeEnum.TICKET_NO_UPDATE -> {
                log.info("order ticket notify: {}", body.toJSONString())
                orderStateService.flightTicketStatusModify(data)
            }

            else -> {
                log.error("fr24 not support type: {}", body.toJSONString())
                return NotifyResult(response.copy(message = "ERROR", code = "500"))
            }
        }
        log.info("fr24 notifyNew result: {}", response.toJSONString())
        return NotifyResult(response)
    }

    private fun sourceType(iataCodes: List<String>): Int {
        return if (commonService.isInternational(iataCodes)) {
            1
        } else {
            0
        }
    }

    private fun initCreateOrderVo(
        req: CreateOrderReq,
        fr24BookingRsp: BookingData,
        pricingData: AccurateRsp,
        bookingBody: BookingRequestBody,
    ): CreateOrderVo {
        // 创建主单信息
        val masterVo = CreateOrderMasterVo(
            OrderTypeEnum.NORMAL.name,
            OrderBusinessTypeEnum.FLIGHT.name,
            req.userId,
            OrderUserTypeEnum.admin.name,
            ""
        )
        var totalPrice = BigDecimal.ZERO
        var totalTax = BigDecimal.ZERO
        var totalNotMarkUpPrice = BigDecimal.ZERO
        fr24BookingRsp.offer.pricePerPax.forEach {
            val num = bookingBody.passengers.count { p ->
                p.paxType == it.paxType
            }
            totalPrice = NumberUtil.add(totalPrice, NumberUtil.mul(commonService.getMarkUpPrice(it.baseFare), num))
            totalNotMarkUpPrice = NumberUtil.add(totalNotMarkUpPrice, NumberUtil.mul(BigDecimal(it.baseFare), num))
            totalTax = NumberUtil.add(totalTax, NumberUtil.mul(BigDecimal(it.totalTax), num))
        }

        val price = NumberUtil.roundStr(
            NumberUtil.add(totalPrice, totalTax).toString(), 2
        )
        val estimatedPrice = commonService.getMovePoint2(price)
        val beforeDiscountPrice = commonService.getMovePoint2(
            NumberUtil.roundStr(
                NumberUtil.add(
                    totalNotMarkUpPrice,
                    totalTax
                ).toString(), 2
            )
        )
        log.info(
            "FR24 total price: {}, total: {}, tax: {}", fr24BookingRsp.totalPrice,
            totalPrice, totalTax
        )
        log.info(
            "FR24 total price: {}, markupPrice: {}, tax: {}", fr24BookingRsp.totalPrice,
            estimatedPrice, beforeDiscountPrice
        )
        // 财务信息
        val financeVo = CreateOrderFinanceVo(
            OrderSettleModeEnums.electronic.name,
            estimatedPrice,
            beforeDiscountPrice,
            estimatedPrice,
            beforeDiscountPrice - estimatedPrice,
            req.userId,
            PaymentChannelEnums.wechat.name,
            PaymentFromEnums.application.name,
            DateUtil.offset(Date(), DateField.SECOND, flightConfig.system().payTimeoutSeconds),
            CollectionAgencyEnums.YEE_PAY.name,
            CurrencyEnums.CNY.name
        )
        // 商品信息
        val productVo = CreateOrderProductVo(
            fr24BookingRsp.orderNo,
            "FLIGHT",
            1,
            1,
            estimatedPrice,
            beforeDiscountPrice,
            0,
            0,
            "",
            "{}",
            ""
        )
        val vo = OrderBaseVo(
            price, fr24BookingRsp.orderNo, OrderReqBase(
                totalTax.toString(),
                totalPrice.toString(),
                req,
                pricingData.rules.first()
            )
        )
        // 附加数据
        val fieldVo = OrderBigFiledVo(
            ThirdServiceTypeEnum.flight_fr24,
            JSONObject.from(vo),
            JSONObject.from(fr24BookingRsp),
            null,
        )
        return CreateOrderVo(
            masterVo,
            financeVo,
            productVo,
            fieldVo,
            fr24BookingRsp.orderNo,
            req.insuranceSequenceNo,
            req.sessionId,
            emptyList(),
        )
    }

    private fun initBookingOrderReq(
        req: CreateOrderReq,
        offerId: String,
        passengers: List<PassengerItem>
    ): BookingRequestBody {
        return BookingRequestBody(
            fR24Util.getAuthentication(),
            offerId,
            idConfig.getStrID(),
            passengers,
            agentContact = AgentContact(
                req.contactInfo.name,
                req.contactInfo.email,
                req.contactInfo.mobile,
                "+86"
            )
        )
    }
}