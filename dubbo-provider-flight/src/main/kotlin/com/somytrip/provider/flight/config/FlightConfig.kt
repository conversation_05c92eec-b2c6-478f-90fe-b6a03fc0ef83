package com.somytrip.provider.flight.config

import com.somytrip.model.flight.config.FR24Config
import com.somytrip.model.flight.config.TCConfig
import com.somytrip.model.flight.config.ZfConfig
import com.somytrip.model.flight.enums.ProviderSourceEnum
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.annotation.Configuration

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-05 18:00
 */
@Configuration
@ConfigurationProperties(prefix = "flight.config")
@RefreshScope
open class FlightConfig {
    private val log = LoggerFactory.getLogger(this::class.java)

    data class Common(
        val activeProvider: String,
        val payTimeoutSeconds: Int,
        val providers: List<ProviderSourceEnum>,
        val defaultEmail: String = "<EMAIL>",
    )

    open lateinit var tcDomestic: TCConfig
    open lateinit var tcInternational: TCConfig
    open lateinit var fR24: FR24Config
    open lateinit var zf: ZfConfig
    open lateinit var system: Common
    open fun tcDomestic() = tcDomestic
    open fun tcInternational() = tcInternational
    open fun fR24() = fR24
    open fun system() = system
    open fun zf() = zf
}