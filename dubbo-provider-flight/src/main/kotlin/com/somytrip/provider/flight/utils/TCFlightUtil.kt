package com.somytrip.provider.flight.utils

import com.alibaba.fastjson2.JSONException
import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString
import com.somytrip.model.flight.common.CommonValFunc
import com.somytrip.model.flight.config.TCConfig
import com.somytrip.model.flight.enums.TCRestApiEnum
import com.somytrip.model.flight.tc.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.slf4j.LoggerFactory
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.security.MessageDigest
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream
import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

/**
 * @Description: 机票util
 * @author: pigeon
 * @created: 2023-11-25 14:31
 */

class TCFlightUtil(
    builder: Builder
) {
    private val log = LoggerFactory.getLogger(this::class.java)
    private var tcConfig = builder.tcConfig
    private val okHttpClient = builder.okHttpClient

    class Builder {
        internal var okHttpClient: OkHttpClient = OkHttpClient()
        internal var tcConfig: TCConfig = TCConfig("", "", "")
        fun okHttpClient(okHttpClient: OkHttpClient) = apply {
            this.okHttpClient = okHttpClient
        }

        fun tcConfig(tcConfig: TCConfig) = apply {
            this.tcConfig = tcConfig
        }

        private fun check() {
            // 检查config设置
            if (tcConfig.pid.isBlank()
                || tcConfig.pidSecret.isBlank()
                || tcConfig.baseUrl.isBlank()
            ) {
                throw RuntimeException("tcConfig init error")
            }
        }

        fun builder(): TCFlightUtil {
            check()
            return TCFlightUtil(this)
        }
    }

    /**
     * 重设对应config配置
     */
    fun resetConfig(tcConfig: TCConfig) {
        this.tcConfig = tcConfig
    }

    private fun flightRequestHandler(request: Any, requestId: String, restApiEnum: TCRestApiEnum): FlightResponse? {
        log.info("serviceCode: ${restApiEnum.value}, pid: {}", tcConfig.pid)
        val flightRequest = getPostBody(request, requestId, restApiEnum.value)
        log.info("request-not-sign-gzip: ${flightRequest.toJSONString()}")
        val responseStr: String = if (restApiEnum == TCRestApiEnum.ORDER_API) {
            AESUtils.decrypt(postFlightRequest(flightRequest), tcConfig.pidSecret)
        } else {
            postFlightRequest(flightRequest)
        }
        if (responseStr.isBlank()) {
            log.error("request-error: (request: ${request.toJSONString()}, response:), pid: {}", tcConfig.pid)
            return null
        }
        log.debug(
            "[TCFlight-req-rsp]: (request: {};response: {})",
            flightRequest.toJSONString(),
            responseStr
        )
        try {
            val flightResponse = responseStr.into<FlightResponse>()
            if (flightResponse.code == CommonValFunc.TC_REST_API_SUCCESS_CODE) {
                log.info("success req: ${request.toJSONString()}, pid: ${tcConfig.pid}")
                log.info("response: {}, {}, {}", flightResponse.code, flightResponse.msg, flightResponse.status)
                return flightResponse
            }
            log.error("error response: {}, pid: {}", responseStr, tcConfig.pid)
            throw RuntimeException(flightResponse.msg)
        } catch (e: JSONException) {
            log.error("req: {}", request.toJSONString(), e)
        }
        log.error("request-error(request: $request, response: $responseStr), pid: ${tcConfig.pid}")
        return null
    }

    /**
     * 模糊询价查询
     */
    fun flightShoppingSearch(
        request: TCShoppingSearchRequest,
        requestId: String
    ): TCShoppingBusinessResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.FLIGHT_SHOPPING_SEARCH_API)
        if (response != null) {
            log.debug("TC Flight Shop list: {}", response.businessResponse)
            return response.businessResponse.into<TCShoppingBusinessResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 客规
     */
    fun flightGuestRule(request: TCGuestRuleRequest, requestId: String): TCGuestBusinessResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.FLIGHT_GUEST_RULE_API)
        if (response != null) {
            return response.businessResponse.into<TCGuestBusinessResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 生单
     */
    fun flightCreateOrder(request: TCOrderRequest, requestId: String): TCOrderBusinessResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.ORDER_API)
        log.info("flightCreateOrder: {}", response.toJSONString())
        if (response != null) {
            return response.businessResponse.into<TCOrderBusinessResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 取消订单
     */
    fun flightCancelOrder(request: TCOrderCancelRequest, requestId: String): TCOrderCancelResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.ORDER_CANCEL_API)
        if (response != null) {
            return response.businessResponse.into<TCOrderCancelResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 支付校验
     */
    fun flightPayCheck(request: TCPayCheckRequest, requestId: String): TCPayCheckBusinessResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.PAY_CHECK_API)
        if (response != null) {
            return response.businessResponse.into<TCPayCheckBusinessResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 支付
     */
    fun flightPay(request: TCPayRequest, requestId: String): TCPayResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.PAY_API)
        if (response != null) {
            return response.businessResponse.into<TCPayResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 精确报价查询
     */
    fun flightAccurateSearch(request: TCAccurateSearchRequest, requestId: String): TCAccurateSearchResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.FLIGHT_ACCURATE_SEARCH_API);
        if (response != null) {
            return response.businessResponse.into<TCAccurateSearchResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 订单列表查询
     */
    fun flightOrderListQuery(request: TCOrderListQueryRequest, requestId: String): TCOrderListQueryResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.ORDER_LIST_QUERY)
        if (response != null) {
            return response.businessResponse.into<TCOrderListQueryResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 订单详情查询
     */
    fun flightOrderDetail(request: TCOrderDetailRequest, requestId: String): TCOrderDetailQueryResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.ORDER_DETAIL_QUERY)
        if (response != null) {
            return response.businessResponse.into<TCOrderDetailQueryResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 取消出票
     */
    fun flightTicketCancel(request: TCTicketCancelRequest, requestId: String): TCTicketCancelResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.TICKET_CANCEL)
        if (response != null) {
            return response.businessResponse.into<TCTicketCancelResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 确认出票
     */
    fun flightTicketConfirm(request: TCTicketConfirmRequest, requestId: String): TCTicketConfirmResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.TICKET_CONFIRM)
        if (response != null) {
            return response.businessResponse.into<TCTicketConfirmResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 可退查询
     */
    fun flightRefundable(request: TCRefundAbleRequest, requestId: String): TCRefundAbleResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.REFUNDABLE_API)
        if (response != null) {
            return response.businessResponse.into<TCRefundAbleResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 退票申请
     */
    fun flightRefundApply(request: TCRefundApplyRequest, requestId: String): TCRefundApplyResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.REFUND_APPLY_API)
        if (response != null) {
            return response.businessResponse.into<TCRefundApplyResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 退票-附件上传
     */
    fun flightRefundFileUpload(request: TCRefundFileUploadRequest, requestId: String): TCRefundFileUploadResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.REFUND_FILE_UPLOAD)
        if (response != null) {
            return response.businessResponse.into<TCRefundFileUploadResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 退票列表
     */
    fun flightRefundListQuery(request: TCRefundListRequest, requestId: String): TCRefundListResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.REFUND_LIST_QUERY)
        if (response != null) {
            return response.businessResponse.into<TCRefundListResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 退票详情
     */
    fun flightRefundOrderDetailSupplier(request: TCRefundDetailRequest, requestId: String): TCRefundDetailResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.REFUND_DETAIL2_QUERY)
        if (response != null) {
            return response.businessResponse.into<TCRefundDetailResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 改签申请
     */
    fun flightEndorseApply(request: TCEndorseApplyRequest, requestId: String): TCEndorseApplyResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.ENDORSE_APPLY)
        if (response != null) {
            return response.businessResponse.into<TCEndorseApplyResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 改签支付
     */
    fun flightEndorsePay(request: TCEndorsePayRequest, requestId: String): TCEndorsePayResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.ENDORSE_PAY)
        if (response != null) {
            return response.businessResponse.into<TCEndorsePayResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 改签取消
     */
    fun flightEndorseCancel(request: TCEndorseCancelRequest, requestId: String): TCEndorseCancelResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.ENDORSE_CANCEL)
        if (response != null) {
            return response.businessResponse.into<TCEndorseCancelResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 改签列表
     */
    fun flightEndorseListQuery(request: TCEndorseListQueryRequest, requestId: String): TCEndorseListQueryResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.ENDORSE_LIST_QUERY)
        if (response != null) {
            return response.businessResponse.into<TCEndorseListQueryResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 改签查询
     */
    fun changeReShop(request: TCChangeReShopRequest, requestId: String): TCChangeReShopResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.ENDORSE_QUERY)
        log.info("change shop list: {}", response.toJSONString())
        if (response != null) {
            return response.businessResponse.into<TCChangeReShopResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    /**
     * 改签详情
     */
    fun changeOrderDetail(
        request: TCQueryChangeOrderDetailRequest,
        requestId: String
    ): TCQueryChangeOrderDetailResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.ENDORSE_DETAIL2_QUERY)
        if (response != null) {
            return response.businessResponse.into<TCQueryChangeOrderDetailResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    fun uncompress(content: String): String {
        return GzipUtils.uncompress(content);
    }

    /**
     * 改签附件上传
     */
    fun flightEndorseUploadFile(request: TCEndorseFileUploadRequest, requestId: String): TCEndorseFileUploadResponse {
        val response = flightRequestHandler(request, requestId, TCRestApiEnum.ENDORSE_UPLOAD)
        if (response != null) {
            return response.businessResponse.into<TCEndorseFileUploadResponse>()
        }
        throw RuntimeException("requestID: $requestId, getDataError")
    }

    private fun getPostBody(params: Any, requestId: String, serviceCode: String): TCFlightRequest {
        val tongChengFlightRequest = TCFlightRequest()
        tongChengFlightRequest.pid = tcConfig.pid
        tongChengFlightRequest.serviceCode = serviceCode
        tongChengFlightRequest.timestamp = getTimestamp()
        tongChengFlightRequest.requestID = requestId
        tongChengFlightRequest.businessRequest = params.toJSONString()
        return tongChengFlightRequest
    }

    private fun postFlightRequest(request: TCFlightRequest): String {
        val flightRequest = request.copy()
        if (TCRestApiEnum.ORDER_API.value == request.serviceCode) {
            flightRequest.businessRequest =
                GzipUtils.compress(AESUtils.encrypt(request.businessRequest, tcConfig.pidSecret))
        } else {
            flightRequest.businessRequest = GzipUtils.compress(request.businessRequest)
        }
        flightRequest.sign = sign(request, tcConfig.pidSecret)
        val requestBody = flightRequest.toJSONString().toRequestBody("application/json".toMediaTypeOrNull())
        val clientRequest: Request = Request.Builder()
            .url(tcConfig.baseUrl)
            .post(requestBody)
            .build()
        log.info("request-data-sign-gzip: ${flightRequest.toJSONString()}")
        try {
            val response = okHttpClient.newCall(clientRequest).execute()
            val result: String?
            if (response.isSuccessful && response.body != null) {
                result = response.body?.string()
                if (!result.isNullOrBlank()) {
                    val res = GzipUtils.uncompress(result)
                    log.info("request-success: ${request.requestID}")
                    return res
                }
            }
        } catch (e: Exception) {
            log.error("request-error: $request")
            e.printStackTrace()
        }
        return ""
    }

    private fun getTimestamp(): String {
        val zoneId = ZoneId.of("GMT+8")
        val now = ZonedDateTime.now(zoneId)
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        return now.format(formatter)
    }

    private fun sign(request: TCFlightRequest, secret: String): String {
        val content = "${request.businessRequest}${request.timestamp}${secret}${request.pid}"
        return MD5Utils.md5(content).uppercase(Locale.getDefault())
    }

    fun sign(businessRequest: String, timestamp: String, pid: String): String {
        val content = "${businessRequest}${timestamp}${tcConfig.pidSecret}${pid}"
        return MD5Utils.md5(content).uppercase(Locale.getDefault())
    }

    private object MD5Utils {
        private val md5String =
            charArrayOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f')

        @Throws(Exception::class)
        fun md5(pwd: String): String {
            val btInput = pwd.toByteArray()
            val mdInst = MessageDigest.getInstance("MD5")
            mdInst.update(btInput)
            val md = mdInst.digest()
            val j = md.size
            val str = CharArray(j * 2)
            var k = 0
            for (i in 0 until j) {
                val byte0 = md[i].toInt()
                str[k++] = md5String[byte0 ushr 4 and 0xf]
                str[k++] = md5String[byte0 and 0xf]
            }
            return String(str)
        }
    }

    private object AESUtils {
        @Throws(Exception::class)
        fun encrypt(content: String, password: String): String {
            val raw = password.toByteArray(Charsets.UTF_8)
            if (raw.size != 16) {
                throw Exception("Invalid md5Key size. $password, 密钥token长度不是16位")
            }
            return try {
                val secretSpec = SecretKeySpec(raw, "AES")
                val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
                cipher.init(Cipher.ENCRYPT_MODE, secretSpec, IvParameterSpec(ByteArray(16))) // zero IV
                val finalCode = cipher.doFinal(content.toByteArray(Charsets.UTF_8))
                String(Base64.getEncoder().encode(finalCode))
            } catch (e: Exception) {
                throw Exception("加密参数错误,原始参数=$content", e)
            }
        }

        @Throws(Exception::class)
        fun decrypt(content: String, password: String): String {
            val raw = password.toByteArray(Charsets.UTF_8)
            if (raw.size != 16) {
                throw Exception("Invalid md5Key size. $password, 密钥token长度不是16位")
            }
            return try {
                val secretSpec = SecretKeySpec(raw, "AES")
                val cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
                cipher.init(Cipher.DECRYPT_MODE, secretSpec, IvParameterSpec(ByteArray(16)))
                val toDecrypt = Base64.getDecoder().decode(content.toByteArray())
                val original = cipher.doFinal(toDecrypt)
                String(original, Charsets.UTF_8)
            } catch (e: Exception) {
                throw Exception("解密参数错误,原始参数=$content", e)
            }
        }
    }

    private object GzipUtils {
        @Throws(Exception::class)
        fun compress(str: String): String {
            if (str.isBlank()) {
                return str
            }
            val out = ByteArrayOutputStream()
            val gzip = GZIPOutputStream(out)
            gzip.write(str.toByteArray())
            gzip.close()
            val bytes = out.toByteArray()
            out.close()
            return Base64.getEncoder().encodeToString(bytes)
        }

        @Throws(Exception::class)
        fun uncompress(str: String): String {
            if (str.isBlank()) {
                return str
            }
            val bytes = Base64.getDecoder().decode(str)
            val out = ByteArrayOutputStream()
            val `in` = ByteArrayInputStream(bytes)
            val gunzip = GZIPInputStream(`in`)
            val buffer = ByteArray(256)
            var n: Int
            while (gunzip.read(buffer).also { n = it } >= 0) {
                out.write(buffer, 0, n)
            }
            return out.toString(Charsets.UTF_8.name())
        }
    }
}

