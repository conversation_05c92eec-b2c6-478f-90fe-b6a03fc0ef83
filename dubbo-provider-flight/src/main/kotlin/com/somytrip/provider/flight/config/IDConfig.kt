package com.somytrip.provider.flight.config

import com.github.yitter.contract.IdGeneratorOptions
import com.github.yitter.idgen.YitIdHelper
import org.springframework.beans.factory.annotation.Value
import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.annotation.Configuration

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-05 17:52
 */
@Configuration
@RefreshScope
open class IDConfig {

    @Value("\${idgenerator.option.workId}")
    private lateinit var workId: String

    @Value("\${spring.profiles.active}")
    private lateinit var profile: String
    private var isInit: Boolean = true

    private fun initYitIdHelper() {
        if (isInit) {
            YitIdHelper.setIdGenerator(IdGeneratorOptions(workId.toShort()))
            isInit = false
        }
    }

    open fun getStrID(): String {
        if (::workId.isInitialized && isInit) {
            initYitIdHelper()
        }
        return """${profile}${YitIdHelper.nextId()}"""
    }

    open fun getLongID(): Long {
        if (::workId.isInitialized && isInit) {
            initYitIdHelper()
        }
        return YitIdHelper.nextId()
    }
}
