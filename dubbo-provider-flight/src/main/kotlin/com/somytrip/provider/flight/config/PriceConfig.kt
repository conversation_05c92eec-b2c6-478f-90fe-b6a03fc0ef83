package com.somytrip.provider.flight.config

import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.cloud.context.config.annotation.RefreshScope
import org.springframework.context.annotation.Configuration
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-05 17:52
 */
@Configuration
@ConfigurationProperties(prefix = "price")
@RefreshScope
open class PriceConfig {
    private val log = LoggerFactory.getLogger(this::class.java)

    data class Config(
        val coefficient: String = "1.01",
        val surcharge: String = "0"
    )

    open lateinit var config: Config

    open fun getChangePrice(price: Double): Int {
        val initialPrice = BigDecimal(price).add(BigDecimal(config.surcharge))
        val finalPrice = initialPrice.multiply(BigDecimal(config.coefficient)).setScale(0, RoundingMode.CEILING)
        return finalPrice.toInt()
    }

    open fun getChangePrice(price: String): Int {
        val initialPrice = BigDecimal(price).add(BigDecimal(config.surcharge))
        val finalPrice = initialPrice.multiply(BigDecimal(config.coefficient)).setScale(0, RoundingMode.CEILING)
        return finalPrice.toInt()
    }

    open fun movePointRight(price: String, n: Int = 2): Int {
        return BigDecimal(price).multiply(BigDecimal("1.00")).movePointRight(n).toInt()
    }
}
