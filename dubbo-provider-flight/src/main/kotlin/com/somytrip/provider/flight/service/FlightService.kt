package com.somytrip.provider.flight.service

import com.somytrip.entity.dto.order.FlightOrderDetail
import com.somytrip.entity.dto.order.OrderBigFieldDTO
import com.somytrip.entity.dto.order.OrderMasterExt
import com.somytrip.entity.response.ResponseResult
import com.somytrip.model.flight.common.AccurateRsp
import com.somytrip.model.flight.common.ReShopRsp
import com.somytrip.model.flight.common.ShopListRsp
import com.somytrip.model.flight.enums.ProviderSourceEnum
import com.somytrip.model.flight.fr24.FR24NotifyResponse
import com.somytrip.model.flight.fr24.OrderChangeInfoNotify
import com.somytrip.model.flight.fr24.TicketingData
import com.somytrip.model.flight.tc.TCNotifyBody
import com.somytrip.model.flight.tc.TCPayResponse
import com.somytrip.model.flight.vo.*

/**
 * @Description: 定义通用service
 * @author: pigeon
 * @created: 2024-08-06 17:45
 */
interface FlightService {

    fun support(provider: ProviderSourceEnum): Boolean
    fun provider(): ProviderSourceEnum

    /**
     * 模糊询价
     */
    fun shoppingList(req: ShopListReq): Map<String, List<ShopListRsp>>;

    /**
     * 实时价格
     */
    fun accSearch(req: AccurateReq): AccurateRsp;

    /**
     * 预订
     */
    fun bookingOrder(req: CreateOrderReq): CreateOrderRsp;

    /**
     * 订单详情
     */
    fun orderDetail(orderNo: String): String;
    fun orderDetail(orderMasterExt: OrderMasterExt): String = run { "" };

    /**
     * 查询是否可退
     */
    fun queryRefundFee(vo: QueryRefundFeeVo): QueryRefundFeeResultVo = run { QueryRefundFeeResultVo() }

    /**
     * 申请退款
     */
    fun applyRefund(vo: ApplyRefundVo): ApplyRefundResultVo = run { ApplyRefundResultVo() }

    /**
     * 上传退款文件
     * 限制一个文件
     */
    fun uploadFiles(vo: UploadFilesVo): List<String>

    /**
     * 支付
     */
    fun fr24Pay(req: FR24PayInfo): TicketingData = run { TicketingData() };

    /**
     * 支付
     */
    fun tcPay(req: TCPayInfo): TCPayResponse = run { TCPayResponse() };

    fun pay(bigField: OrderBigFieldDTO): Boolean = run { false };

    /**
     * 通知
     */
    fun fr24Notify(body: NotifyBody<OrderChangeInfoNotify>): NotifyResult<FR24NotifyResponse> =
        run { NotifyResult(FR24NotifyResponse()) };

    /**
     * 同程通知
     */
    fun tcNotify(body: NotifyBody<TCNotifyBody>): NotifyResult<String> = run { NotifyResult("") };

    fun notifyHandler(body: String): String = run { "" }

    /**
     * 航变通知处理
     */
    fun notifyChangeHandler(body: String): String = run { "" }

    /**
     * 攻略特供
     */
    fun tripPlan(req: ShopListReq): TripPlanRsp = run { TripPlanRsp() }

    /**
     * 未支付取消订单
     */
    fun cancelOrder(orderBigDto: OrderBigFieldDTO, reason: String): Boolean

    /**
     * 查询改签航班列表
     */
    fun queryChangeList(req: ChangeShopReq, bigField: OrderBigFieldDTO): Map<String, List<ReShopRsp>>

    /**
     * 改签申请
     */
    fun applyChange(req: ChangeApplyReq): Boolean

    /**
     * 改签订单刷新接口
     */
    fun changeOrderFlush(orderNo: String): ResponseResult<ChangeOrderFlushResult?>

    /**
     * 改签订单支付
     */
    fun changeOrderPay(orderNo: String): ResponseResult<Boolean>

    fun changeOrderDetail(bigField: OrderBigFieldDTO): FlightOrderDetail.Detail

    fun canRefund(bigField: OrderBigFieldDTO): Boolean = run { false }
}