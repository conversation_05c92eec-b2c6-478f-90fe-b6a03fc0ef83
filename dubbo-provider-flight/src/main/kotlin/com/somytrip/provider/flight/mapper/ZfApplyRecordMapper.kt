package com.somytrip.provider.flight.mapper

import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.somytrip.model.dto.ZfApplyRecord
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select

/**
 * @Description: 智飞科技申请记录表Mapper
 * @author: pigeon
 * @created: 2024-12-03 17:39
 */
@Mapper
interface ZfApplyRecordMapper : BaseMapper<ZfApplyRecord> {
    @Select("select * from zf_apply_record where order_no=#{orderNo} and type=#{type} and delete_flag=false")
    fun selectOneByOrderNo(
        @Param("orderNo") orderNo: String,
        @Param("type") type: String,
    ): ZfApplyRecord?

    @Select("select * from zf_apply_record where out_origin_id=#{outOriginId} and type=#{type} and delete_flag=false")
    fun selectOneByOriginId(
        @Param("outOriginId") outOriginId: String,
        @Param("type") type: String,
    ): ZfApplyRecord?
}