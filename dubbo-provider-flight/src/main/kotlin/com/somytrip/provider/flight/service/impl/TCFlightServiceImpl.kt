package com.somytrip.provider.flight.service.impl

import cn.hutool.core.collection.ListUtil
import cn.hutool.core.date.DateField
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.IdcardUtil
import com.alibaba.fastjson2.*
import com.somytrip.api.service.FlightV2Service
import com.somytrip.api.service.flight.TCFlightService
import com.somytrip.api.service.order.OrderBaseService
import com.somytrip.entity.response.ResponseResult
import com.somytrip.entity.vo.flight.*
import com.somytrip.entity.vo.flight.QueryFlightAccurateResponse.FlightUnitRuleItem
import com.somytrip.entity.vo.order.QueryOrderVo
import com.somytrip.model.flight.common.*
import com.somytrip.model.flight.enums.*
import com.somytrip.model.flight.tc.*
import com.somytrip.model.flight.vo.AccurateReq
import com.somytrip.model.flight.vo.ShopListReq
import com.somytrip.model.po.QueryShoppingResponse
import com.somytrip.model.po.TCOrderResponsePo
import com.somytrip.model.po.TCOrderResponsePoProduct
import com.somytrip.model.vo.flight.FlightPayCheckVo
import com.somytrip.model.vo.flight.FlightPayOrderVo
import com.somytrip.model.vo.flight.OrderCancelVo
import com.somytrip.provider.flight.config.FlightConfig
import com.somytrip.provider.flight.config.IDConfig
import com.somytrip.provider.flight.config.PriceConfig
import com.somytrip.provider.flight.service.CommonService
import com.somytrip.provider.flight.utils.ConvertRespUtils
import com.somytrip.provider.flight.utils.TCFlightUtil
import com.somytrip.provider.flight.utils.XCFlightUtils
import com.somytrip.utils.JSONResultUtils
import jakarta.annotation.Resource
import kotlinx.coroutines.*
import okhttp3.OkHttpClient
import org.apache.dubbo.config.annotation.DubboReference
import org.apache.dubbo.config.annotation.DubboService
import org.apache.dubbo.rpc.RpcException
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * @Description: 同程机票
 * @author: pigeon
 * @created: 2024-01-20 14:17
 */
@DubboService
@Service
class TCFlightServiceImpl : TCFlightService {
    @Resource
    private lateinit var idConfig: IDConfig

    @Resource
    private lateinit var flightConfig: FlightConfig

    @Resource
    private lateinit var priceConfig: PriceConfig

    @Resource
    private lateinit var commonService: CommonService


    @DubboReference(check = false)
    private lateinit var orderBaseService: OrderBaseService

    @DubboReference(check = false)
    private lateinit var flightV2Service: FlightV2Service
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(5, TimeUnit.MINUTES)
        .readTimeout(5, TimeUnit.MINUTES)
        .writeTimeout(5, TimeUnit.MINUTES)
        .callTimeout(5, TimeUnit.MINUTES)
        .build()

    private val tcFlightUtil by lazy {
        TCFlightUtil.Builder()
            .okHttpClient(okHttpClient)
            .tcConfig(
                flightConfig.tcDomestic()
            )
            .builder()
    }
    private val log = LoggerFactory.getLogger(this::class.java)
    private val AIRPORT_LIST by lazy {
        flightV2Service.getAirportList()
    }
    private val AIRLINE_LIST by lazy {
        flightV2Service.getAirlineList()
    }

    private val xcFlightUtils by lazy {
        XCFlightUtils.Builder()
            .okHttpClient(okHttpClient)
            .builder()
    }

    private fun cacheData(item: TCAccurateSearchItem): TCAccurateSearchItem {
        commonService.cacheTCAccurateSearchItem(item)
        return item;
    }

    private fun getCacheData(key: String): TCAccurateSearchItem? {
        return commonService.getCacheTCAccurateSearchItem(key)
    }

    private fun getAirport(airCode: String): String {
        val airportList = AIRPORT_LIST
        return airportList.find { it.code == airCode }?.nameCn ?: ""
    }

    private fun getAirline(airlineCode: String): AirLineCodeVo {
        val airlineList = AIRLINE_LIST
        return airlineList.find { it.code == airlineCode } ?: AirLineCodeVo()
    }

    private fun filter(response: TCShoppingBusinessResponse): List<TCShoppingProductItem?> {
        if (response.productList.isEmpty()) {
            return listOf(null, null, null)
        }
        val result: TCShoppingProductItem = response.productList.first()
        val lowFilter = response.productList.filter {
            val hour = it.segments.first().depDateTime.substring(11..12).toInt()
            hour in 6..20
        }
        if (lowFilter.isEmpty()) {
            return listOf(result, result, result)
        }
        val midFilter = lowFilter.filter {
            val hour = it.segments.first().depDateTime.substring(11..12).toInt()
            hour in 8..20
        }
        if (midFilter.isEmpty()) {
            return listOf(lowFilter.first(), lowFilter.first(), lowFilter.last())
        }
        val highFilter = midFilter.filter {
            val hour = it.segments.first().depDateTime.substring(11..12).toInt()
            hour in 10..20 && !it.segments.all { s -> s.stops.isEmpty() }
        }
        if (highFilter.isEmpty()) {
            return listOf(lowFilter.first(), midFilter.first(), midFilter.last())
        }
        return listOf(lowFilter.first(), midFilter.first(), highFilter.last())
    }

    private fun filterV2(shopList: List<QueryShoppingResponse?>): List<QueryShoppingResponse?> {
        if (shopList.isEmpty()) {
            return listOf(null, null, null)
        }
        val result: QueryShoppingResponse? = shopList.first()
        val lowFilter = shopList.filter {
            val hour = it?.segments?.first()?.departDateTime?.substring(11..12)?.toInt() ?: 0
            hour in 6..20
        }
        if (lowFilter.isEmpty()) {
            return listOf(result, result, result)
        }
        val midFilter = lowFilter.filter {
            val hour = it?.segments?.first()?.departDateTime?.substring(11..12)?.toInt() ?: 0
            hour in 8..20
        }
        if (midFilter.isEmpty()) {
            return listOf(lowFilter.first(), lowFilter.first(), lowFilter.last())
        }
        val highFilter = midFilter.filter {
            val hour = it?.segments?.first()?.departDateTime?.substring(11..12)?.toInt() ?: 0
            hour in 10..20
                    &&
                    it?.segments?.all { s -> s.stops.isNullOrEmpty() } == false
        }
        if (highFilter.isEmpty()) {
            return listOf(lowFilter.first(), midFilter.first(), midFilter.last())
        }
        return listOf(lowFilter.first(), midFilter.first(), highFilter.last())
    }

    fun searchV2(
        go: TCShoppingSearchRequest,
        back: TCShoppingSearchRequest,
    ): List<List<QueryShoppingResponse?>> = runBlocking {
        val goId = idConfig.getStrID()
        val backId = idConfig.getStrID()
        val that = this@TCFlightServiceImpl
        coroutineScope {
            val goResult = async {
                log.info("async go: {}, {}", goId, go)
                val res = filter(tcFlightUtil.flightShoppingSearch(go, goId))
                val flightSource = ProviderSourceEnum.TC
                res.map {
                    if (it != null) {
                        ConvertRespUtils.itemToQueryShoppingResponse(
                            it, priceConfig::getChangePrice,
                            go.businessLineType,
                            that::getAirport, that::getAirline,
                            flightSource
                        )
                    } else {
                        null
                    }
                }
            }
            val backResult = async {
                log.info("async back: {}, {}", backId, back)
                val res = filter(tcFlightUtil.flightShoppingSearch(back, backId))
                val flightSource = ProviderSourceEnum.TC
                res.map {
                    if (it != null) {
                        ConvertRespUtils.itemToQueryShoppingResponse(
                            it, priceConfig::getChangePrice,
                            go.businessLineType,
                            that::getAirport, that::getAirline,
                            flightSource
                        )
                    } else {
                        null
                    }
                }
            }
            return@coroutineScope awaitAll(goResult, backResult)
        }
    }

    fun searchV2(
        request: TCShoppingSearchRequest
    ): List<QueryShoppingResponse?> {
        val id = idConfig.getStrID()
        log.info("async request: {}, {}", id, request)
        resetConfig(request.businessLineType)
        val res = filter(tcFlightUtil.flightShoppingSearch(request, id))
        val flightSource = ProviderSourceEnum.TC
        return res.map {
            if (it != null) {
                ConvertRespUtils.itemToQueryShoppingResponse(
                    it, priceConfig::getChangePrice,
                    request.businessLineType,
                    this::getAirport, this::getAirline,
                    flightSource
                )
            } else {
                null
            }
        }
    }

    override fun getFlightOneRoundTripV3(vo: RoundOneFlight): ResponseResult<Any> {
        val shoppingRequest = ConvertRespUtils.convertVoToTCShoppingSearchRequest(vo.request)
        val go = shoppingRequest.copy(tripType = "1", segments = listOf(shoppingRequest.segments.first().copy()))
        val back = shoppingRequest.copy(
            tripType = "1",
            segments = listOf(shoppingRequest.segments.last().copy(segmentNo = 1))
        )
        try {
            val result = searchV3XC(go, back)
            log.info("getFlightOneRoundTripV2 success: {}", vo)
            return JSONResultUtils.success(result)
        } catch (e: Exception) {
            log.error("getFlightOneRoundTripV2 error: {}", vo, e)
        }
        try {
            val result = searchV2(go, back)
            return JSONResultUtils.success(result)
        } catch (e: Exception) {
            log.error("getFlightOneRoundTripV2 error: {}", vo, e)
        }
        return JSONResultUtils.fail();
    }

    private fun searchV3XC(
        go: TCShoppingSearchRequest,
        back: TCShoppingSearchRequest,
    ): List<List<QueryShoppingResponse?>> = runBlocking {
        coroutineScope {
            val goResult = async {
                searchV3XC(go)
            }
            val backResult = async {
                searchV3XC(back)
            }
            return@coroutineScope awaitAll(goResult, backResult)
        }
    }

    override fun searchV3XC(
        request: TCShoppingSearchRequest,
    ): List<QueryShoppingResponse?> {
        return filterV2(xcFlightUtils.main(request))
    }

    override fun searchV3TC(request: TCShoppingSearchRequest): List<QueryShoppingResponse?> {
        return searchV2(request)
    }

    override fun getShopFlightListV2(request: TCShoppingSearchRequest): TCShoppingBusinessResponse {
        try {
            resetConfig(request.businessLineType)
            val requestId = idConfig.getStrID()
            val response = tcFlightUtil.flightShoppingSearch(request, requestId)
            return response
        } catch (e: Exception) {
            log.error("error: $request\n", e)
        }
        return TCShoppingBusinessResponse()
    }

    override fun shopping(req: ShopListReq): Map<String, List<ShopListRsp>> {
        val request = TCShoppingSearchRequest(
            "1",
            req.sourceType,
            null,
            req.screenCondition.airline,
            req.passengers.map {
                TCShoppingSearchPassenger(
                    it.num,
                    it.type.tcValue(),

                    )
            },
            req.segments.mapIndexed { index, it ->
                TCShoppingSearchSegmentItem(
                    index + 1,
                    it.departCode,
                    "",
                    it.arriveCode,
                    "",
                    it.departTime,
                )
            },
            null,
        )
        val rsp = getShopFlightListV2(request)
        if (rsp.productList.isEmpty()) {
            return emptyMap()
        }
        val result = mutableMapOf<String, MutableList<ShopListRsp>>()
        rsp.productList.forEach {
            var segDurationTotal = 0
            val item = ShopListRsp(
                it.prices.map { p ->
                    PriceItem(
                        PassengerTypeEnum.tcValueToEnums(p.value.passengerType),
                        p.value.taxB.toString(),
                        priceConfig.getChangePrice(p.value.priceB).toString(),
                        TaxDetail(
                            p.value.taxDetail.yqB.toString(),
                            p.value.taxDetail.taxB.toString()
                        )
                    )
                },
                it.segments.sortedBy { s -> s.segmentIndex }.map { s ->
                    var stopTimeTotal: Int = 0;
                    var durationTotal: Int = 0;
                    durationTotal += s.duration
                    stopTimeTotal += s.stopTime
                    segDurationTotal += durationTotal + stopTimeTotal
                    SegmentItem(
                        s.operatingAirline,
                        s.operatingFlightNo,
                        s.marketingAirline,
                        when (s.segmentType) {
                            2 -> 1
                            else -> 0
                        },
                        s.flightShare,
                        s.marketingFlightNo,
                        s.aircraft,
                        s.aircraftType.toString(),
                        s.arrAirport,
                        s.arrAirportTerm,
                        s.arrDateTime,
                        s.depAirport,
                        s.depAirportTerm,
                        s.depDateTime,
                        when (s.meal) {
                            1 -> "有餐食" // 有
                            2 -> "无餐食" // 无
                            else -> "" // 默认未知
                        },
                        s.mileage.toString(),
                        s.segmentIndex,
                        s.stops.map { sp ->
                            SegmentStopItem(
                                sp.stopAirport,
                                sp.stopTime,
                            )
                        },
                        CommonValFunc.formatIntToDuration(s.stopTime),
                        CommonValFunc.formatIntToDuration(s.duration),
                        CommonValFunc.formatIntToDuration(s.duration + s.stopTime),
                        s.arrCityName,
                        s.depCityName,
                        commonService.getAirline(s.marketingAirline),
                        s.stops.map { sp -> sp.stopAirport },
                        CommonValFunc.formatIntToDuration(s.stopTime),
                        s.cabinCode,
                    )
                },
                it.cabins.map { c ->
                    CabinItem(
                        CabinClassEnum.tcValueToEnums(c.cabinClass),
                        c.cabinCode,
                        c.cabinCount,
                        c.segmentIndex
                    )
                },
                ProductInfo(
                    it.productCode.toString(),
                    ProductTagEnum.tcValueToEnums(it.productTag),
                    ProviderSourceEnum.TC.display,
                    ProviderSourceEnum.TC,
                ),
                req.sourceType,
                CommonValFunc.formatIntToDuration(segDurationTotal)
            )
            val key = item.segments.joinToString { s -> s.flightNo }
            if (result.containsKey(key)) {
                result[key]?.add(item)
            } else {
                result[key] = mutableListOf(item)
            }
        }
        return result
    }

    override fun getShopFlightListV2(request: QueryFlightShoppingRequest): ResponseResult<Any> {
        val shoppingRequest = ConvertRespUtils.convertVoToTCShoppingSearchRequest(request)
        try {
            log.info("获取模糊数据start request: ${JSONObject.toJSONString(shoppingRequest)}")
            val response = getShopFlightListV2(shoppingRequest)
            val flightSource = if (shoppingRequest.businessLineType == 0) {
                ProviderSourceEnum.TC
            } else {
                ProviderSourceEnum.TC_TEST
            }
            val result = ConvertRespUtils.convertQueryShoppingResponseV2(
                response,
                priceConfig::getChangePrice,
                request,
                this::getAirport,
                this::getAirline,
                flightSource
            )
            return JSONResultUtils.success(JSONObject.from(result));
        } catch (e: Exception) {
            log.error("获取模糊数据error, request: ${JSONObject.toJSONString(shoppingRequest)}")
            log.error("", e)
            if (e is RuntimeException) {
                return JSONResultUtils.fail(e.message);
            }
        }
        return JSONResultUtils.fail()
    }

    override fun flightAccurateSearchV2(request: QueryFlightAccurateRequest): QueryFlightAccurateResponse {
        val tcAccurateSearchRequest = ConvertRespUtils.convertVoToTCAccurateSearchRequest(request)
        resetConfig(tcAccurateSearchRequest.businessLineType)
        log.info("[TCFlight]----flightAccurateSearch params: {}", tcAccurateSearchRequest.toJSONString())
        try {
            val response = flightAccurateSearch(tcAccurateSearchRequest)
            if (response.flightUnits.isEmpty()) {
                val queryFlightAccurateResponse = QueryFlightAccurateResponse();
                queryFlightAccurateResponse.sourceType = request.sourceType
                queryFlightAccurateResponse.rules = emptyList()
                return queryFlightAccurateResponse
            }
            val first = response.flightUnits.first()
            val queryFlightAccurateResponse = QueryFlightAccurateResponse(cacheData(first), priceConfig::getChangePrice)
            queryFlightAccurateResponse.sourceType = request.sourceType
            log.info("acc result size: ${queryFlightAccurateResponse.rules.size}")
            return queryFlightAccurateResponse
        } catch (e: Exception) {
            log.error("flightAccurateSearch params: {}", request.toJSONString(), e)
            throw RuntimeException("error")
        }
    }

    override fun flightAccurateSearch(request: QueryFlightAccurateRequest): ResponseResult<Any> {
        val tcAccurateSearchRequest = ConvertRespUtils.convertVoToTCAccurateSearchRequest(request)
        resetConfig(tcAccurateSearchRequest.businessLineType)
        log.info("[TCFlight]----flightAccurateSearch params: {}", JSONObject.from(tcAccurateSearchRequest).toString())
        try {
            val response = flightAccurateSearch(tcAccurateSearchRequest)
            if (response.flightUnits.isEmpty()) {
                val queryFlightAccurateResponse = QueryFlightAccurateResponse();
                queryFlightAccurateResponse.sourceType = request.sourceType
                queryFlightAccurateResponse.rules = ListUtil.empty()
                return JSONResultUtils.success(queryFlightAccurateResponse);
            }
            val first = response.flightUnits.first()
            val queryFlightAccurateResponse = QueryFlightAccurateResponse(cacheData(first), priceConfig::getChangePrice)
            queryFlightAccurateResponse.sourceType = request.sourceType
            log.info("acc result size: ${queryFlightAccurateResponse.rules.size}")
            return JSONResultUtils.success(JSONObject.from(queryFlightAccurateResponse));
        } catch (e: RuntimeException) {
            log.error("", e)
        }
        return JSONResultUtils.fail()
    }

    @OptIn(DelicateCoroutinesApi::class)
    suspend fun flightAccurateSearchAsync(request: QueryFlightAccurateRequest): Deferred<Any> {
        // 异步执行 flightAccurateSearch 方法
        return GlobalScope.async {
            flightAccurateSearch(request).data
        }
    }

    override fun flightAccurateSearchList(request: QueryFlightAccurateListRequest): ResponseResult<Any> = runBlocking {
        if (request.products == null) {
            return@runBlocking JSONResultUtils.fail(400, "params init fail");
        }
        val listSize = request.products!!.size % 5
        val deferredResults = List(listSize) { index ->
            // 使用 async 启动异步计算
            flightAccurateSearchAsync(QueryFlightAccurateRequest(request, index))
        }
        // 等待所有异步计算完成
        val result = deferredResults.awaitAll()
        return@runBlocking JSONResultUtils.success(JSONArray.from(result))
    }

    override fun accSearch(req: AccurateReq): AccurateRsp {
        val request = TCAccurateSearchRequest(
            req.sourceType,
            req.tripType + 1,
            req.productInfos.map { it.productCode.toInt() },
            0,
            req.adultNum,
            req.childNum,
            req.infantNum,
            req.mainAirline,
            "0",
            req.segments.map {
                TCAccurateSearchSegment(
                    req.tripType + 1,
                    it.segmentIndex,
                    "",
                    it.departAirport,
                    it.departAirportTerm,
                    "",
                    it.arriveAirport,
                    it.arriveAirportTerm,
                    it.departDateTime,
                    it.arriveDateTime,
                    it.airline,
                    it.flightNo,
                    it.cabinCode,
                    "",
                    it.flightShare,
                    it.aircraft,
                    it.operationFlightNo,
                    it.operationAirline,
                )
            }
        )
        val requestId = idConfig.getStrID()
        try {
            val response = tcFlightUtil.flightAccurateSearch(request, requestId)
            return AccurateRsp(
                req.sourceType,
                response.flightUnits.map {
                    var segDurationTotal = 0
                    AccRuleItem(
                        it.prices.values.map { p ->
                            PriceItem(
                                PassengerTypeEnum.tcValueToEnums(p.passengerType),
                                p.taxB.toString(),
                                commonService.getMarkUpPrice(p.priceB.toString()).toString(),
                                TaxDetail(p.taxDetail.yqB.toString(), p.taxDetail.taxB.toString())
                            )
                        },
                        it.segments.sortedBy { s -> s.segmentIndex }.map { s ->
                            var stopTimeTotal: Int = 0;
                            var durationTotal: Int = 0;
                            durationTotal += s.duration
                            stopTimeTotal += s.stopTime
                            segDurationTotal += durationTotal + stopTimeTotal
                            SegmentItem(
                                s.operatingAirline,
                                s.operatingFlightNo,
                                s.marketingAirline,
                                when (s.segmentType) {
                                    2 -> 1
                                    else -> 0
                                },
                                s.flightShare,
                                s.marketingFlightNo,
                                s.aircraft,
                                s.aircraftType.toString(),
                                s.arrAirport,
                                s.arrAirportTerm,
                                s.arrDateTime,
                                s.depAirport,
                                s.depAirportTerm,
                                s.depDateTime,
                                when (s.meal) {
                                    1 -> "有餐食" // 有
                                    2 -> "无餐食" // 无
                                    else -> "" // 默认未知
                                },
                                s.mileage.toString(),
                                s.segmentIndex,
                                s.stops.map { sp ->
                                    SegmentStopItem(
                                        sp.stopAirport,
                                        sp.stopTime,
                                    )
                                },
                                CommonValFunc.formatIntToDuration(s.stopTime),
                                CommonValFunc.formatIntToDuration(s.duration),
                                CommonValFunc.formatIntToDuration(s.duration + s.stopTime),
                                s.arrCityName,
                                s.depCityName,
                                commonService.getAirline(s.marketingAirline),
                                s.stops.map { sp -> sp.stopAirport },
                                CommonValFunc.formatIntToDuration(s.stopTime),
                                s.cabinCode,
                            )
                        },
                        ProductInfo(
                            it.productCode.toString(),
                            ProductTagEnum.tcValueToEnums(it.productTag),
                            ProviderSourceEnum.TC.display,
                            ProviderSourceEnum.TC,
                            it.data,
                        ),
                        ReverseInformation(
                            it.refundChangeInfo.changeText,
                            it.refundChangeInfo.refundText,
                            it.refundChangeInfo.refundChangeRules.map { r ->
                                ReverseInfoItem(r)
                            },
                        ),
                        ServiceTime(it.serviceTime),
                        BaggageInfo(it.baggageInfo),
                        IssueTicketInfo(it.issueTicketInfo, it.invoiceWay, it.invoiceType),
                        LimitInfo(it.limitInfo)
                    )
                }
            )
        } catch (e: Exception) {
            log.error("精确询价接口报错: requestID: $requestId, request: ${JSON.toJSONString(request)}")
            log.error("", e)
            return AccurateRsp()
        }
    }

    private fun flightAccurateSearch(tcAccurateSearchRequest: TCAccurateSearchRequest): TCAccurateSearchResponse {
        val requestId = idConfig.getStrID()
        try {
            val response = tcFlightUtil.flightAccurateSearch(tcAccurateSearchRequest, requestId)
            return response;
        } catch (e: Exception) {
            log.error("精确询价接口报错: requestID: $requestId, request: $tcAccurateSearchRequest")
            log.error("", e)
        }
        throw RuntimeException("error")
    }

    override fun createOrder(vo: CreateFlightOrderVo): ResponseResult<Any> {
        resetConfig(vo.sourceType)
        log.info("创建订单vo: {}", JSONObject.from(vo))
        var orderSerialNo: String = ""
        try {
            var flightUnitItem = getCacheData(vo.accurateRequest.productInfos.first().orderInfo)
            log.info("cache 缓存取值: {}", flightUnitItem?.data)
            if (flightUnitItem == null) {
                log.info("cache 缓存没有值")
                val accurateSearch =
                    flightAccurateSearch(ConvertRespUtils.convertVoToTCAccurateSearchRequest(vo.accurateRequest))
                if (accurateSearch.flightUnits.isEmpty()) {
                    return JSONResultUtils.fail("已经没有库存了");
                }
                val flightUnit = accurateSearch.flightUnits.filter {
                    it.data == vo.accurateRequest.productInfos.first().orderInfo
                }
                if (flightUnit.isEmpty()) {
                    log.info("flightUnits productInfo: {}", vo.accurateRequest.productInfos)
                    log.info(
                        "flightUnits productInfo orderInfos: {}",
                        accurateSearch.flightUnits.map { it.data }.toList()
                    )
                    cacheData(accurateSearch.flightUnits.first())
                    val s = JSONObject.from(
                        FlightUnitRuleItem(
                            accurateSearch.flightUnits.first(),
                            priceConfig::getChangePrice
                        )
                    ).toString()
                    log.info("306 response: {}", s)
                    return JSONResultUtils.fail(
                        306, "价格已刷新，请重试",
                        s
                    );
                }
                flightUnitItem = flightUnit.first()
            }
            // 判断年龄
            if (flightUnitItem.limitInfo.agePairs.isNotEmpty()) {
                flightUnitItem.limitInfo.agePairs.forEach {
                    val ageLimit = JSON.toJSONString(it).parseObject()
                    val flag = vo.passengers.filter { passengerInfo ->
                        if (passengerInfo.creditType == CreditTypeEnum.ID_CARD) {
                            val age = IdcardUtil.getAgeByIdCard(passengerInfo.creditNo)
                            age !in ageLimit.getInteger("min")..ageLimit.getInteger("max")
                        } else {
                            false;
                        }
                    }.isNotEmpty()
                    if (flag) {
                        return JSONResultUtils.fail(
                            400,
                            "年龄不符,最小:${ageLimit.getInteger("min")},最大:${ageLimit.getInteger("")}"
                        )
                    }
                }
            }
            val requestPo =
                ConvertRespUtils.convertVoToTCOrderRequest(flightUnitItem, vo, priceConfig::getChangePrice)
            val requestId = idConfig.getStrID();
            log.info("TC创建订单请求:{}, request: {}", requestId, JSONObject.toJSONString(requestPo))
            val response = tcFlightUtil.flightCreateOrder(requestPo.request, requestId)
            log.info("TC创建订单响应:{}, response: {}", requestId, JSONObject.toJSONString(response))
            if (response.orderSerialNo.isBlank()) {
                log.warn("TC创建订单失败: ${requestId}, ${JSONObject.toJSONString(response)}")
                val re = Regex("\"message\":\"(.*?)\"[,)}]")
                val messages = re.findAll(JSONObject.toJSONString(response))
                if (messages.count() > 0) {
                    val message = messages.last().groupValues.last()
                    log.info("截取到message返回为: $message")
                    return JSONResultUtils.fail(400, message);
                }
                return JSONResultUtils.fail(400, "第三方订单信息同步失败");
            }
            orderSerialNo = response.orderSerialNo
            val responsePo = TCOrderResponsePo(
                response.orderSerialNo,
                response.totalPrice,
                response.productItems.map {
                    TCOrderResponsePoProduct(
                        it.productType,
                        it.state,
                        it.unitKey,
                        it.productDetail,
                        it.productTotalPrice.ifBlank {
                            it.prdoductTotalPrice
                        }
                    )
                },
                requestPo
            )
            val createOrderVo = ConvertRespUtils.toCreateOrderVo(
                vo.userId, responsePo,
                priceConfig::getChangePrice, priceConfig::movePointRight
            )
            createOrderVo.insuranceSequenceNo = vo.insuranceSequenceNo;
            createOrderVo.orderFinance.paymentDeadline =
                DateUtil.offset(Date(), DateField.SECOND, flightConfig.system().payTimeoutSeconds);
            try {
                val result = orderBaseService.createOrderBaseV2(JSON.toJSONString(createOrderVo))
                log.info("订单上游戏返回: {}", result)
                if (result.isSuccess) {
                    orderSerialNo = "";
                    return JSONResultUtils.success(result.message, result.data)
                } else {
                    return JSONResultUtils.fail(result.code, result.message)
                }
            } catch (e: RpcException) {
                log.error("", e)
                val request = TCOrderCancelRequest(
                    responsePo.orderSerialNo,
                    responsePo.products
                        .map {
                            TCOrderCancelProduct(
                                JSONObject.from(it.productDetail)
                                    .into<TCOrderCancelProductDetail>(), it.productType
                            )
                        }, "系统自动取消"
                )
                cancelOrder(OrderCancelVo(vo.sourceType, request))
                orderSerialNo = ""
                return JSONResultUtils.fail("创建订单失败");
            }
        } catch (e: RuntimeException) {
            log.error("", e)
            return JSONResultUtils.fail("创建订单失败，航班信息已更新");
        } catch (e: Exception) {
            log.error("", e)
            return JSONResultUtils.fail("创建订单失败");
        } finally {
            if (orderSerialNo.isNotBlank()) {
                val request = TCOrderCancelRequest(
                    orderSerialNo,
                    listOf(TCOrderCancelProduct(null, "0")), "系统自动取消"
                )
                cancelOrder(OrderCancelVo(vo.sourceType, request))
//                return JSONResultUtils.fail("创建订单失败");
            }
        }
    }

    override fun cancelOrder(vo: CancelFlightOrderVo): ResponseResult<Any> {
        try {
            log.info("cancel order: {}", vo.toJSONString())
            // 查询订单信息
            val queryOrderBaseResp = orderBaseService.queryOrderBase(vo.orderNo)
            if (!queryOrderBaseResp.isSuccess) {
                return queryOrderBaseResp
            }
            log.info("order query rsp: {}", queryOrderBaseResp.toJSONString())
            val queryOrderVo = queryOrderBaseResp.data.toJSONString().into<QueryOrderVo>()
            val po = queryOrderVo.orderBigField.field1.toJSONString().into<TCOrderResponsePo>()
            log.info("tc order cancel po: {}", po.toJSONString())
            // 构造取消订单信息
            resetConfig(po.accurate.request.buisinessLineType)
            val request = TCOrderCancelRequest(
                orderSerialNo = po.orderSerialNo,
                cancelProducts = po.products.map {
                    TCOrderCancelProduct(
                        productType = it.productType,
                        cancelProductDetail = TCOrderCancelProductDetail(
                            JSONObject.parseObject(it.productDetail).getString("pnr")
                        )
                    )
                },
                reason = vo.reason
            )
            val requestId = idConfig.getStrID();
            log.info("tc cancel order request: {}", request.toJSONString())
            val cancelResponse = tcFlightUtil.flightCancelOrder(request, requestId)
            log.info("request: {}, response: {}", JSONObject.from(request), JSONObject.from(cancelResponse))
            if (!cancelResponse.success) {
                log.info("cancel response: {}", cancelResponse)
                return JSONResultUtils.fail(cancelResponse.message);
            }
            return JSONResultUtils.success();
        } catch (e: Exception) {
            log.error("tc cancel order error: {}\n", vo.toJSONString(), e)
            return JSONResultUtils.fail()
        }
    }

    private fun resetConfig(type: Int = 0) {
        when (type) {
            0 -> {
                log.info("reset config in china")
                tcFlightUtil.resetConfig(flightConfig.tcDomestic())
//                tcFlightUtil.resetConfig(testConfig)
            }

            1 -> {
                log.info("reset config in global")
                tcFlightUtil.resetConfig(flightConfig.tcInternational())
            }

            2 -> {
                log.info("reset config is test")
                tcFlightUtil.resetConfig(flightConfig.tcDomestic())
            }

            else -> {
                log.warn("不支持的类型")
            }
        }
    }

    override fun cancelOrder(vo: OrderCancelVo): ResponseResult<Any> {
        resetConfig(vo.locationType)
        val requestId = idConfig.getStrID()
        log.info("取消订单参数: {}", vo.request)
        try {
            val response = tcFlightUtil.flightCancelOrder(vo.request, requestId)
            return JSONResultUtils.success(response);
        } catch (e: Exception) {
            log.error("取消订单失败: {}", vo)
            log.error("", e)
        }
        return JSONResultUtils.fail();
    }

    override fun pay(vo: FlightPayOrderVo): ResponseResult<Any> {
        resetConfig(vo.locationType)
        val requestId = idConfig.getStrID()
        log.info("支付订单参数: {}", vo.request)
        try {
            val response = tcFlightUtil.flightPay(vo.request, requestId)
            return JSONResultUtils.success(response);
        } catch (e: Exception) {
            log.error("支付订单失败: {}", vo)
            log.error("", e)
        }
        return JSONResultUtils.fail();
    }

    override fun payCheck(vo: FlightPayCheckVo): ResponseResult<Any> {
        resetConfig(vo.locationType)
        val requestId = idConfig.getStrID()
        try {
            val businessResponse = tcFlightUtil.flightPayCheck(vo.request, requestId)
            log.info("paycheck response: {}", businessResponse)
            if (businessResponse.success) {
                return JSONResultUtils.success(businessResponse);
            }
        } catch (e: Exception) {
            log.error("payCheck error $vo ---", e)
        }

        return JSONResultUtils.fail();
    }

    override fun queryOrderList(request: TCOrderListQueryRequest, businessType: Int): ResponseResult<Any> {
        resetConfig(businessType)
        TODO("Not yet implemented")
    }

    override fun queryOrderDetail(orderSerialNo: String, businessType: Int): ResponseResult<Any> {
        TODO("Not yet implemented")
    }

//    override fun queryOrderDetail(request: TCOrderDetailQueryRequest, businessType: Int): ResponseResult<Any> {
//        resetConfig(businessType)
//        val requestId = idConfig.getStrID()
//        val flightOrderDetailQuery =
//            tcFlightUtil.flightOrderDetailQuery(TCOrderDetailQueryRequest(request.orderSerialNo, requestId), requestId)
//        log.info("response message: {}", flightOrderDetailQuery.message)
//        return JSONResultUtils.success(flightOrderDetailQuery);
//    }
//
//    override fun queryOrderDetail(orderSerialNo: String, businessType: Int): ResponseResult<Any> {
//        return this.queryOrderDetail(TCOrderDetailQueryRequest(orderSerialNo, ""), businessType)
//    }

    override fun queryRefundable(request: TCRefundAbleRequest, businessType: Int): ResponseResult<Any> {
        resetConfig(businessType)
        val requestId = idConfig.getStrID()
        val flightRefundable = tcFlightUtil.flightRefundable(request, requestId)
        log.info("queryRefundable response: {}", flightRefundable)
        return JSONResultUtils.success(flightRefundable)
    }

    override fun applyRefundOrder(request: TCRefundApplyRequest, businessType: Int): ResponseResult<Any> {
        resetConfig(businessType)
        TODO("Not yet implemented")
    }

    override fun refundUploadFile(request: TCRefundFileUploadRequest, businessType: Int): ResponseResult<Any> {
        resetConfig(businessType)
        TODO("Not yet implemented")
    }

    override fun confirmRefund(request: TCRefundOrderConfirmRequest, businessType: Int): ResponseResult<Any> {
        resetConfig(businessType)
        TODO("Not yet implemented")
    }

    override fun queryRefundOrderList(request: TCRefundListRequest, businessType: Int): ResponseResult<Any> {
        resetConfig(businessType)
        TODO("Not yet implemented")
    }

//    override fun queryRefundOrderDetail(request: TCOrderDetailQueryRequest, businessType: Int): ResponseResult<Any> {
//        resetConfig(businessType)
//        TODO("Not yet implemented")
//    }

//    override fun queryEndorse(request: TCEndorseQueryRequest, businessType: Int): ResponseResult<Any> {
//        resetConfig(businessType)
//        TODO("Not yet implemented")
//    }

    override fun endorseUploadFile(request: TCEndorseFileUploadRequest, businessType: Int): ResponseResult<Any> {
        resetConfig(businessType)
        TODO("Not yet implemented")
    }

    override fun applyEndorseOrder(request: TCEndorseApplyRequest, businessType: Int): ResponseResult<Any> {
        resetConfig(businessType)
        TODO("Not yet implemented")
    }

    override fun payEndorseOrder(request: TCEndorsePayRequest, businessType: Int): ResponseResult<Any> {
        resetConfig(businessType)
        TODO("Not yet implemented")
    }

    override fun cancelEndorseOrder(request: TCEndorseCancelRequest, businessType: Int): ResponseResult<Any> {
        resetConfig(businessType)
        TODO("Not yet implemented")
    }

    override fun queryEndorseOrderList(request: TCEndorseListQueryRequest, businessType: Int): ResponseResult<Any> {
        resetConfig(businessType)
        TODO("Not yet implemented")
    }

    override fun queryEndorseOrderDetail(
        request: TCQueryChangeOrderDetailRequest,
        businessType: Int
    ): ResponseResult<Any> {
        resetConfig(businessType)
        TODO("Not yet implemented")
    }

    override fun tcNotify(body: TCNotifyBody): ResponseResult<Any> {
//        resetConfig(businessType)
        return JSONResultUtils.success(body)
    }

    override fun tcPayNotify(body: TCNotifyBody): TCPayNotify {
        val result = JSON.parseObject(tcFlightUtil.uncompress(body.businessRequest))
        log.info("uncompress data: $result")
        return result.into<TCPayNotify>()
    }

    override fun tcTicketNotify(body: TCNotifyBody): TCTicketNotify {
        val result = JSON.parseObject(tcFlightUtil.uncompress(body.businessRequest))
        return result.into<TCTicketNotify>()
    }

    override fun tcRefundResultNotify(body: TCNotifyBody): TCRefundResultNotify {
        val result = JSON.parseObject(tcFlightUtil.uncompress(body.businessRequest))
        return result.into<TCRefundResultNotify>()
    }

    override fun tcRefundFeeNotify(body: TCNotifyBody): TCRefundFeeNotify {
        val result = JSON.parseObject(tcFlightUtil.uncompress(body.businessRequest))
        return result.into<TCRefundFeeNotify>()
    }

    override fun tcEndorseConfirmNotify(body: TCNotifyBody): TCEndorseConfirmNotify {
        val result = JSON.parseObject(tcFlightUtil.uncompress(body.businessRequest))
        return result.into<TCEndorseConfirmNotify>()
    }

    override fun tcEndorseResultNotify(body: TCNotifyBody): TCEndorseResultNotify {
        val result = JSON.parseObject(tcFlightUtil.uncompress(body.businessRequest))
        return result.into<TCEndorseResultNotify>()
    }

    override fun tcChangeNotify(body: TCNotifyBody): TCFlightChangeNotify {
        val result = JSON.parseObject(tcFlightUtil.uncompress(body.businessRequest))
        return result.into<TCFlightChangeNotify>()
    }


    private fun tcNotifyCheck(body: TCNotifyBody): Boolean {
        resetConfig(body.businessType)
        return body.sign == tcFlightUtil.sign(body.businessRequest, body.timestamp, body.pid);
    }
}