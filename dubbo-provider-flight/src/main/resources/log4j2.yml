Appenders:
  Console: #输出到控制台
    name: CONSOLE #Appender命名
    target: SYSTEM_OUT
    PatternLayout:
      pattern: "%style{%d{yyyy-MM-dd HH:mm:ss.SSS}}{cyan} [%15.15t] %highlight{%-5level} %style{%pid}{magenta} %style{%-40.40logger{39}}{cyan} - %msg%n%style{%throwable}{red}"
      #      pattern: "%style{%d{yyyy-MM-dd HH:mm:ss.SSS}}{bright,green} | %highlight{%-5p} [%t] %style{%c}{bright,yellow} %style{%L}{bright,blue} -| %highlight{%m}%n%style{%throwable}{red}"
      disableAnsi: false
  RollingFile: # 输出到文件，超过10MB归档
    - name: ROLLING_FILE
      ignoreExceptions: false
      fileName: "logs/flight_service.log"
      filePattern: "logs/$${date:yyyy-MM}/flight_service-%d{yyyy-MM-dd}-%i.log"
      PatternLayout:
        #        pattern: "%d{yyyy-MM-dd HH:mm:ss,SSS}:%4p %t (%F:%L) - %m%n"
        pattern: "%highlight{%d{yyyy-MM-dd HH:mm:ss.SSS}} [%t] %highlight{%-5level} %style{%pid}{magenta} %style{%-50.50logger{39}}{cyan} - %msg%n"
      Policies:
        SizeBasedTriggeringPolicy:
          size: "10 MB"
      DefaultRolloverStrategy:
        max: 1000
Loggers:
  Root:
    level: info
    AppenderRef:
      - ref: CONSOLE
      - ref: ROLLING_FILE
  Logger: # 单独设置某些包的输出级别
    - name: com.somytrip.provider.flight
      additivity: false # 去除重复的log
      level: info
      AppenderRef:
        - ref: CONSOLE
        - ref: ROLLING_FILE
