package com.somytrip.api.service.flight

import com.somytrip.entity.dto.order.FlightOrderDetail
import com.somytrip.entity.response.ResponseResult
import com.somytrip.model.flight.common.AccurateRsp
import com.somytrip.model.flight.common.ReShopRsp
import com.somytrip.model.flight.common.ShopListRsp
import com.somytrip.model.flight.enums.ProviderSourceEnum
import com.somytrip.model.flight.fr24.FR24NotifyResponse
import com.somytrip.model.flight.fr24.OrderChangeInfoNotify
import com.somytrip.model.flight.fr24.TicketingData
import com.somytrip.model.flight.tc.TCNotifyBody
import com.somytrip.model.flight.tc.TCPayResponse
import com.somytrip.model.flight.vo.*

/**
 * @Description: 多供应商机票实现
 * @author: pigeon
 * @created: 2024-08-15 14:38
 */
interface ManyFlightService {

    /**
     * 攻略使用
     */
    fun tripPlanStr(vo: TripPlanVo): String

    /**
     * 航班列表
     */
    fun shopV2(req: ShopListReq): Map<String, List<ShopListRsp>>;

    /**
     * 精确询价
     * @param req json
     * @return [AccurateRsp] json
     */
    fun accurateSearch(req: AccurateReq): ResponseResult<AccurateRsp>

    /**
     * 下单
     * @param req json
     * @return [CreateOrderRsp] json
     */
    fun booking(req: CreateOrderReq): CreateOrderRsp;

    /**
     * 下单
     * @param req json
     * @return [CreateOrderRsp] json
     */
    fun booking(req: String): CreateOrderRsp;

    /**
     * 支付订单
     * @param req json
     * @return PayResponse<TicketingData> json
     */
    fun payFR24(req: PayReq<FR24PayInfo>): PayResponse<TicketingData>

    /**
     * 支付订单
     * @param req json
     * @return PayResponse<TCPayResponse> json
     */
    fun payTC(req: PayReq<TCPayInfo>): PayResponse<TCPayResponse>

    /**
     * 通知处理 FR24
     * @param body json
     * @return NotifyBody<OrderChangeInfoNotify> json
     */
    fun fr24Notify(body: NotifyBody<OrderChangeInfoNotify>): NotifyResult<FR24NotifyResponse>

    /**
     * 通知处理 TC
     * @param body json
     * @return NotifyBody<TCNotifyBody> json
     */
    fun tcNotify(body: NotifyBody<TCNotifyBody>): NotifyResult<String>

    /**
     * 普通订单通知
     */
    fun notifyHandler(body: String, source: ProviderSourceEnum): String

    /**
     * 航变通知
     */
    fun noticeChangeHandler(body: String, source: ProviderSourceEnum): String

    /**
     * 未支付取消订单
     * @param orderNo String
     * @return Boolean true false
     */
    fun notPayCancelOrder(orderNo: String, reason: String): Boolean

    /**
     * 支付
     */
    fun pay(orderNo: String): Boolean

    /**
     * 支付改签订单
     */
    fun payChangeOrder(orderNo: String): Boolean

    /**
     * 查询退款费用
     */
    fun queryRefundFee(vo: ComputePriceVo): ResponseResult<QueryRefundFeeResultVo>

    /**
     * 申请退款
     */
    fun applyRefund(vo: ApplyRefundNVo): ResponseResult<Boolean>

    /**
     * 上传证明文件
     */
    fun uploadFiles(vo: UploadFilesVo): ResponseResult<List<String>>

    /**
     * 查询改签航班列表
     */
    fun queryChangeShoppingList(req: ChangeShopListVo): ResponseResult<Map<String, List<ReShopRsp>>>

    /**
     * 改签申请
     */
    fun applyChange(req: ApplyChangeVo): ResponseResult<Boolean>


    /**
     * 改签订单支付
     */
    fun changeOrderPay(orderNo: String): ResponseResult<Boolean>

    /**
     * 订单详情
     */
    fun changeOrderDetail(orderNo: String, uid: String): FlightOrderDetail.Detail

    fun canRefund(orderNo: String): Boolean
}