package com.somytrip.api.service.flight

import com.somytrip.entity.response.ResponseResult
import com.somytrip.entity.vo.flight.*
import com.somytrip.model.flight.common.AccurateRsp
import com.somytrip.model.flight.common.ShopListRsp
import com.somytrip.model.flight.tc.*
import com.somytrip.model.flight.vo.AccurateReq
import com.somytrip.model.flight.vo.ShopListReq
import com.somytrip.model.po.QueryShoppingResponse
import com.somytrip.model.vo.flight.FlightPayCheckVo
import com.somytrip.model.vo.flight.FlightPayOrderVo
import com.somytrip.model.vo.flight.OrderCancelVo


/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-20 9:38
 */
interface TCFlightService {

    fun searchV3XC(
        request: TCShoppingSearchRequest,
    ): List<QueryShoppingResponse?>

    fun searchV3TC(
        request: TCShoppingSearchRequest,
    ): List<QueryShoppingResponse?>

    fun getFlightOneRoundTripV3(vo: RoundOneFlight): ResponseResult<Any>

    /**
     * 模糊询价列表
     */
    fun getShopFlightListV2(request: QueryFlightShoppingRequest): ResponseResult<Any>

    //    fun getShopFlightListTestV2(request: QueryFlightShoppingRequest):  ResponseResult<Any>
    fun getShopFlightListV2(request: TCShoppingSearchRequest): TCShoppingBusinessResponse

    fun shopping(req: ShopListReq): Map<String, List<ShopListRsp>>

    /**
     * 精确询价
     */
    fun flightAccurateSearch(request: QueryFlightAccurateRequest): ResponseResult<Any>
    fun flightAccurateSearchV2(request: QueryFlightAccurateRequest): QueryFlightAccurateResponse
    fun flightAccurateSearchList(request: QueryFlightAccurateListRequest): ResponseResult<Any>

    /**
     * 精确询价
     */
    fun accSearch(req: AccurateReq): AccurateRsp

    /**
     * 取消订单
     */

    fun cancelOrder(vo: CancelFlightOrderVo): ResponseResult<Any>

    /**
     * 取消订单
     */
    fun cancelOrder(vo: OrderCancelVo): ResponseResult<Any>

    /**
     * 支付
     */
    fun pay(vo: FlightPayOrderVo): ResponseResult<Any>

    /**
     * 支付校验
     */
    fun payCheck(vo: FlightPayCheckVo): ResponseResult<Any>

    /**
     * 订单列表查询
     */
    fun queryOrderList(request: TCOrderListQueryRequest, businessType: Int): ResponseResult<Any>

    /**
     * 订单详情查询
     */
//    fun queryOrderDetail(request: TCOrderDetailQueryRequest, businessType: Int): ResponseResult<Any>
    fun queryOrderDetail(orderSerialNo: String, businessType: Int): ResponseResult<Any>

    /**
     * 可退查询
     */
    fun queryRefundable(request: TCRefundAbleRequest, businessType: Int): ResponseResult<Any>

    /**
     * 退票申请
     */
    fun applyRefundOrder(request: TCRefundApplyRequest, businessType: Int): ResponseResult<Any>

    /**
     * 退票上传附件
     */
    fun refundUploadFile(request: TCRefundFileUploadRequest, businessType: Int): ResponseResult<Any>

    /**
     * 确认提交退票接口（国际）
     */
    fun confirmRefund(request: TCRefundOrderConfirmRequest, businessType: Int): ResponseResult<Any>

    /**
     * 查询退票订单列表
     */
    fun queryRefundOrderList(request: TCRefundListRequest, businessType: Int): ResponseResult<Any>

    /**
     * 查询退票订单详情
     */
//    fun queryRefundOrderDetail(request: TCOrderDetailQueryRequest, businessType: Int): ResponseResult<Any>

    /**
     * 改签查询
     */
//    fun queryEndorse(request: TCEndorseQueryRequest, businessType: Int): ResponseResult<Any>

    /**
     * 改签上传附件
     */
    fun endorseUploadFile(request: TCEndorseFileUploadRequest, businessType: Int): ResponseResult<Any>

    /**
     * 改签申请
     */
    fun applyEndorseOrder(request: TCEndorseApplyRequest, businessType: Int): ResponseResult<Any>

    /**
     * 改签支付
     */
    fun payEndorseOrder(request: TCEndorsePayRequest, businessType: Int): ResponseResult<Any>

    /**
     * 取消改签
     */
    fun cancelEndorseOrder(request: TCEndorseCancelRequest, businessType: Int): ResponseResult<Any>

    /**
     * 改签订单列表
     */
    fun queryEndorseOrderList(request: TCEndorseListQueryRequest, businessType: Int): ResponseResult<Any>

    /**
     * 改签订单详情
     */
    fun queryEndorseOrderDetail(request: TCQueryChangeOrderDetailRequest, businessType: Int): ResponseResult<Any>

    /**
     * 通知
     */
    fun tcNotify(body: TCNotifyBody): ResponseResult<Any>

    /**
     * 通知
     */
//    fun tcNotify(body: JSONObject): String
    fun tcPayNotify(body: TCNotifyBody): TCPayNotify
    fun tcTicketNotify(body: TCNotifyBody): TCTicketNotify
    fun tcRefundResultNotify(body: TCNotifyBody): TCRefundResultNotify
    fun tcRefundFeeNotify(body: TCNotifyBody): TCRefundFeeNotify
    fun tcEndorseConfirmNotify(body: TCNotifyBody): TCEndorseConfirmNotify
    fun tcEndorseResultNotify(body: TCNotifyBody): TCEndorseResultNotify
    fun tcChangeNotify(body: TCNotifyBody): TCFlightChangeNotify

    fun createOrder(vo: CreateFlightOrderVo): ResponseResult<Any>
}