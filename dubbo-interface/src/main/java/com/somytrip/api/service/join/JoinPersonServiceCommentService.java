package com.somytrip.api.service.join;

import com.baomidou.mybatisplus.extension.service.IService;
import com.somytrip.bean.dto.JoinPersonServiceAddCommentDto;
import com.somytrip.bean.entity.JoinPersonServiceCommentEntity;
import com.somytrip.bean.resp.JoinPersonServiceCommentResp;
import com.somytrip.bean.vo.JoinPersonServiceCommentVo;
import com.somytrip.bean.vo.JoinPersonServiceProviderCommentVo;
import com.somytrip.entity.dto.hotel.PaginationDto;

import java.util.List;

/**
 * @ClassName: JoinPersonServiceCommentService
 * @Description: 个人加盟服务 评论Service层
 * @Author: shadow
 * @Date: 2023/11/22 11:43
 */
public interface JoinPersonServiceCommentService extends IService<JoinPersonServiceCommentEntity> {

    /**
     * 发布评论
     *
     * @param uid 用户ID
     * @param dto 评论Dto
     * @return Boolean true: 成功, false: 失败
     */
    Boolean addComment(Long uid, JoinPersonServiceAddCommentDto dto);

    /**
     * 查询评论列表
     *
     * @param uid       用户ID
     * @param serviceSn 服务序列号
     * @param pageDto   分页dto
     * @return JoinPersonServiceCommentResp 评论数据
     */
    JoinPersonServiceCommentResp commentList(Long uid, String serviceSn, PaginationDto pageDto);

    /**
     * 查询子评论列表
     *
     * @param commentSn 评论序列号
     * @param pageDto   分页dto
     * @return Item<JoinPersonServiceCommentVo> 子评论列表
     */
    List<JoinPersonServiceCommentVo> subCommentList(String commentSn, PaginationDto pageDto);

    /**
     * 查询评论列表(B端)
     *
     * @param uid        用户ID
     * @param scoreLevel 评分等级
     * @param pageDto    分页dto
     * @return Item<JoinPersonServiceProviderCommentVo> B端评论列表
     */
    List<JoinPersonServiceProviderCommentVo> providerCommentList(Long uid, Integer scoreLevel, PaginationDto pageDto);

    /**
     * 删除评论
     *
     * @param uid       用户ID
     * @param commentSn 评论序列号
     * @return Boolean true: 成功, false: 失败
     */
    Boolean deleteComment(Long uid, String commentSn);

    /**
     * 点赞/踩 评论
     *
     * @param uid       用户ID
     * @param commentSn 评论序列号
     * @param type      操作类型 1: 点赞, 0: 踩
     * @return Boolean true: 成功, false: 失败
     */
    Boolean voteComment(Long uid, String commentSn, Integer type);
}
