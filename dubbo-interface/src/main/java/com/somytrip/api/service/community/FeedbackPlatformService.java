package com.somytrip.api.service.community;

import com.baomidou.mybatisplus.extension.service.IService;
import com.somytrip.entity.community.FeedbackPlatform;
import com.somytrip.entity.dto.community.feedback.FeedbackCommentDto;
import com.somytrip.entity.dto.community.feedback.FeedbackCommentListReq;
import com.somytrip.entity.dto.community.feedback.FeedbackPlatformDto;
import com.somytrip.entity.dto.community.feedback.FeedbackPlatformListReq;
import com.somytrip.entity.vo.community.FeedbackCommentListRes;
import com.somytrip.entity.vo.community.FeedbackPlatformListRes;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.api.service.community
 * @className: FeedbackPlatformService
 * @author: lijunqi
 * @description:
 * @date: 2025/8/4 14:58
 * @version: 1.0
 */
public interface FeedbackPlatformService extends IService<FeedbackPlatform> {

    /**
     * 提交反馈建议
     *
     * @param dto       提交参数
     * @return 反馈信息
     */
    Long submitFeedback(FeedbackPlatformDto dto);

    /**
     * 发布反馈评论
     *
     * @param dto 评论参数
     * @return 评论ID
     */
    Long publishComment(FeedbackCommentDto dto);


    /**
     * 查询反馈列表
     *
     * @param req 查询请求参数
     * @return 反馈列表响应结果
     */
    FeedbackPlatformListRes getFeedbackList(FeedbackPlatformListReq req);

    /**
     * 查询反馈留言列表
     *
     * @param req 查询请求参数
     * @return 留言列表响应结果
     */
    FeedbackCommentListRes getFeedbackCommentList(FeedbackCommentListReq req);
}
