package com.somytrip.api.service.common;

import com.somytrip.entity.dto.common.IpLocationInfo;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.api.service.common
 * @interfaceName: IpLocationService
 * @author: li<PERSON><PERSON>
 * @description: IP地址位置解析服务接口
 * @date: 2025-01-04 16:00:00
 * @Version: 1.0
 */
public interface IpLocationService {

    /**
     * 根据IP地址获取位置信息
     *
     * @param ipAddress IP地址
     * @return IP位置信息
     */
    IpLocationInfo getLocationByIp(String ipAddress);

    /**
     * 批量获取IP地址位置信息
     *
     * @param ipAddresses IP地址数组
     * @return IP位置信息数组
     */
    IpLocationInfo[] getLocationsByIps(String[] ipAddresses);

    /**
     * 检查IP地址是否有效
     *
     * @param ipAddress IP地址
     * @return 是否有效
     */
    boolean isValidIp(String ipAddress);
}
