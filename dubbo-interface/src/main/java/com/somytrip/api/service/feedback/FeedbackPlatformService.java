package com.somytrip.api.service.feedback;

import com.baomidou.mybatisplus.extension.service.IService;
import com.somytrip.entity.community.FeedbackPlatform;
import com.somytrip.entity.dto.feedback.SubmitFeedbackDto;
import com.somytrip.entity.vo.feedback.SubmitFeedbackVo;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.api.service.feedback
 * @interfaceName: FeedbackPlatformService
 * @author: lijun<PERSON>
 * @description: 反馈平台服务接口
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
public interface FeedbackPlatformService extends IService<FeedbackPlatform> {

    /**
     * 提交反馈建议
     *
     * @param userId 用户ID
     * @param dto    提交反馈建议请求DTO
     * @param userIp 用户IP地址
     * @return 提交反馈建议响应VO
     */
    SubmitFeedbackVo submitFeedback(String userId, SubmitFeedbackDto dto, String userIp);

    /**
     * 增加支持数
     *
     * @param feedbackId 反馈ID
     * @return 是否成功
     */
    Boolean increaseSupportCount(Integer feedbackId);

    /**
     * 更新反馈状态
     *
     * @param feedbackId 反馈ID
     * @param status     状态
     * @return 是否成功
     */
    Boolean updateFeedbackStatus(Integer feedbackId, Integer status);

    /**
     * 设置反馈为已采纳
     *
     * @param feedbackId 反馈ID
     * @return 是否成功
     */
    Boolean adoptFeedback(Integer feedbackId);
}
