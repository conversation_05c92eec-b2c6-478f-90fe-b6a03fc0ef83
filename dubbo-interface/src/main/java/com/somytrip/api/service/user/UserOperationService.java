package com.somytrip.api.service.user;

import com.alibaba.fastjson2.JSONObject;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.user.LoginResultVo;
import com.somytrip.entity.vo.user.UserOpVo;

import java.util.Map;

/**
 * @Description: 用户操作Service
 * @author: pigeon
 * @created: 2024-12-27 15:52
 */
public interface UserOperationService {

    /**
     * 获取配置信息
     *
     * @return json
     */
    ResponseResult<JSONObject> configs(String language);

    /**
     * 校验验证码是否正确
     *
     * @param vo 入参
     * @return true false
     */
    ResponseResult<Boolean> checkMessage(UserOpVo<?> vo);

    /**
     * 生成验证码的消息
     * 手机号-发送验证码
     * 邮箱-发送验证码
     *
     * @param userOpVo 入参
     * @return true false
     */
    ResponseResult<Boolean> generateMessage(UserOpVo<?> userOpVo);

    /**
     * 用户登陆
     *
     * @param userOpVo 登陆参数
     * @return user json
     */
    ResponseResult<LoginResultVo> login(UserOpVo<?> userOpVo);

    /**
     * 用户注册
     *
     * @param userOpVo 注册参数
     * @return user json
     */
    ResponseResult<LoginResultVo> register(UserOpVo<?> userOpVo);

    /**
     * 用户是否已经注册
     *
     * @param userOpVo 判断参数
     * @return true false
     */
    ResponseResult<Boolean> exists(UserOpVo<?> userOpVo);

    /**
     * 绑定第三方渠道
     *
     * @param userOpVo 绑定参数
     * @return true false 绑定是否成功
     */
    ResponseResult<Boolean> binding(UserOpVo<?> userOpVo);

    /**
     * 解绑第三方渠道
     *
     * @param userOpVo 解绑参数
     * @return true false 解绑是否成功
     */
    ResponseResult<Boolean> unbind(UserOpVo<?> userOpVo);

    /**
     * 重新设置-邮箱|邮箱密码等
     *
     * @param userOpVo 入参
     * @return true false
     */
    ResponseResult<Boolean> reset(UserOpVo<?> userOpVo);

    /**
     * 逻辑删除用户
     *
     * @param userId 用户ID
     * @return true false
     */
    ResponseResult<Boolean> deleteUserById(String userId);

    /**
     * 获取字段状态，比如是否注册，是否设置密码
     * exists方法的补充
     *
     * @param userOpVo 入参
     * @return 0 未注册 1 已经注册-没有设置密码 2 已经注册-设置密码
     */
    ResponseResult<Integer> state(UserOpVo<?> userOpVo);

    /**
     * 获取绑定的第三方渠道
     *
     * @param uid 用户ID
     * @return {we: true, ali: false}
     */
    ResponseResult<Map<String, Boolean>> bindingOauth2Map(String uid);

    boolean checkBlackUser(String uid);
}
