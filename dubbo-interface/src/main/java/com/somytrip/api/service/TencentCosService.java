package com.somytrip.api.service;

import com.qcloud.cos.model.Bucket;
import com.qcloud.cos.model.COSObjectSummary;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectResult;
import com.somytrip.entity.dto.common.UploadInPo;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * @Description: 腾讯云COS
 * @author: pigeon
 * @created: 2024-06-22 15:28
 */
public interface TencentCosService {
    /**
     * 创建桶
     *
     * @param bucketName 桶名称
     */
    void createBucket(String bucketName);

    /**
     * 判断桶是否存在
     *
     * @param bucketName 桶名称
     * @return boolean
     */
    boolean bucketExists(String bucketName);

    /**
     * 获得Bucket的策略
     *
     * @param bucketName 桶名称
     * @return 策略
     */
    String getBucketPolicy(String bucketName);

    /**
     * 获得所有Bucket列表
     *
     * @return List<Bucket>
     */
    List<Bucket> getAllBuckets();

    /**
     * 判断文件是否存在
     *
     * @param bucketName 桶名
     * @param objectName 文件夹名称
     * @return boolean
     */
    boolean isObjectExist(String bucketName, String objectName);

    /**
     * 判断文件夹是否存在
     *
     * @param bucketName 桶名
     * @param objectName 文件夹名称
     * @return boolean
     */
    boolean isFolderExist(String bucketName, String objectName);

    /**
     * 获取文件流
     *
     * @param bucketName 存储桶
     * @param objectName 文件名
     * @return 二进制流
     */
    InputStream getObject(String bucketName, String objectName);

    /**
     * 获取路径下文件列表
     *
     * @param bucketName 存储桶
     * @param prefix     文件名称
     * @param recursive  是否递归查找，false：模拟文件夹结构查找
     * @return 二进制流
     */

    List<COSObjectSummary> listObjects(String bucketName, String prefix, boolean recursive);

    /**
     * 使用MultipartFile进行文件上传
     *
     * @param bucketName  存储桶
     * @param file        文件名
     * @param objectName  对象名
     * @param contentType 类型
     * @return PutObjectResult
     */
    PutObjectResult uploadFile(String bucketName, MultipartFile file, String objectName, String contentType);

    /**
     * 通过流上传文件
     *
     * @param bucketName  存储桶
     * @param objectName  文件对象
     * @param inputStream 文件流
     * @return PutObjectResult
     */
    PutObjectResult uploadFile(String bucketName, String objectName, InputStream inputStream);

    /**
     * 通过流上传文件
     *
     * @param po 存储桶
     * @return PutObjectResult
     */
    PutObjectResult uploadFile(UploadInPo po);

    /**
     * 通过url上传文件
     *
     * @param bucketName
     * @param objectName
     * @param url
     * @return
     */
    PutObjectResult uploadFile(String bucketName, String objectName, String url);

    /**
     * 删除文件
     *
     * @param bucketName 存储桶
     * @param objectName 文件名称
     */
    void removeFile(String bucketName, String objectName);

    /**
     * 批量删除文件
     *
     * @param bucketName 存储桶
     * @param keys       需要删除的文件列表
     */
    void removeFiles(String bucketName, List<String> keys);

    /**
     * 获取外链
     *
     * @param bucketName 桶名称
     * @param objectName 对象名称
     * @return url
     */
    String getUrl(String bucketName, String objectName);

    /**
     * 获取用户头像
     *
     * @param path 文件路径
     * @return url
     */
    String getAuthImage(String path);

    /**
     * 获取文件信息
     *
     * @param bucketName bucket名称
     * @param objectName 文件名称
     * @return ObjectMetadata
     * @throws Exception <a href="https://docs.minio.io/cn/java-client-api-reference.html#statObject">...</a>
     */
    ObjectMetadata getObjectInfo(String bucketName, String objectName) throws Exception;
}
