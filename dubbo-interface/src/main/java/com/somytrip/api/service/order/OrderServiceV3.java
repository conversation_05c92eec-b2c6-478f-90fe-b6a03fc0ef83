package com.somytrip.api.service.order;

import com.somytrip.entity.dto.order.CheckUserNotArriveStateOrderDTO;
import com.somytrip.entity.dto.order.OrderFinishEstimate;
import com.somytrip.entity.dto.order.QueryUserOrderListDTO;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.visa.NotifyVo;

/**
 * @Description:
 * @author: pigeon
 * @created: 2023-11-30 17:51
 */
public interface OrderServiceV3 {
    /**
     * 获取查询订单列表对应的配置信息
     *
     * @return []
     */
    ResponseResult queryOrderListConfig();

    /**
     * 查询订单主单信息
     *
     * @param orderNo 父订单订单号
     * @return ResponseBean
     */
    ResponseResult queryOrderMasterList(String orderNo);

    /**
     * 根据通知修改上上签订单
     *
     * @param vo 通知实体
     * @return ResponseBean
     */
    ResponseResult updateVisaOrder(NotifyVo vo);

    /**
     * 查询用户订单列表
     *
     * @param dto 查询参数
     * @return []
     */
    ResponseResult queryOrderListByUserId(QueryUserOrderListDTO dto);

    /**
     * 查询指定业务未到达指定状态前的订单
     *
     * @param dto 请求参数
     * @return orderNo
     */
    ResponseResult checkUserNotArriveStateOrder(CheckUserNotArriveStateOrderDTO dto);

    /**
     * 查询订单详情
     *
     * @param orderNo 订单号
     * @param uid     用户ID
     * @return 订单详情
     */
    ResponseResult queryOrderDetail(String orderNo, String uid);

    /**
     * 查询订单详情
     *
     * @param orderNo
     * @param uid
     * @return
     */
    ResponseResult queryOrderDetailV2(String orderNo, String uid);


    /**
     * 插入评价
     *
     * @param vo
     * @return
     */
    ResponseResult insertOrderFinishEstimate(OrderFinishEstimate.Vo vo);

    /**
     * test
     */
    void test();
}
