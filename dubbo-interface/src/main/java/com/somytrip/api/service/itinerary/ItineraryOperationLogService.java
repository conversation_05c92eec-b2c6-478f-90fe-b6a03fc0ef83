package com.somytrip.api.service.itinerary;

import com.baomidou.mybatisplus.extension.service.IService;
import com.somytrip.entity.itinerary.ItineraryOperationLogDto;
import com.somytrip.entity.itinerary.ItineraryOperationLogEntity;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.api.service.itinerary
 * @interfaceName: ItineraryOperationLogService
 * @author: shadow
 * @description: 攻略操作日志Service
 * @date: 2024/9/24 16:41
 * @version: 1.0
 */
public interface ItineraryOperationLogService extends IService<ItineraryOperationLogEntity> {

    /**
     * 记录操作日志
     *
     * @param dto 参数
     * <AUTHOR>
     * @date 2024/9/24 16:50
     */
    void log(ItineraryOperationLogDto dto);

    /**
     * 持久化操作日志
     *
     * <AUTHOR>
     * @date 2024/9/24 17:25
     */
    void persistLogs();
}
