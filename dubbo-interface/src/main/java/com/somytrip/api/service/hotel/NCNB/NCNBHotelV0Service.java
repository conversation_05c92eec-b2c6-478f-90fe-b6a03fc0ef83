package com.somytrip.api.service.hotel.NCNB;

import com.somytrip.entity.dto.hotel.CreateOrderDto;
import com.somytrip.entity.dto.hotel.HotelDateRange;
import com.somytrip.entity.dto.hotel.QueryHotelListDto;
import com.somytrip.entity.dto.hotel.QueryRoomListDto;
import com.somytrip.entity.hotel.HotelOrderEntity;
import com.somytrip.entity.hotel.NCNB.NCNBBookingResp;
import com.somytrip.entity.hotel.NCNB.NCNBOrderSearchResp;
import com.somytrip.entity.resp.ListResp;
import com.somytrip.entity.vo.hotel.HotelVo;
import com.somytrip.entity.vo.hotel.RatePlanVo;
import com.somytrip.entity.vo.hotel.RoomVo;

import java.util.List;

/**
 * @ClassName: NCNBHotelService
 * @Description: 捷旅酒店Service
 * @Author: shadow
 * @Date: 2023/11/29 15:20
 */
public interface NCNBHotelV0Service {

    /**
     * 根据酒店ID查询酒店静态数据并转为HotelVo
     *
     * @param hotelId 酒店ID
     * @return HotelVo 酒店vo
     */
    HotelVo getHotelByHotelId(String hotelId, HotelDateRange dateRange);

    /**
     * 根据dto查询酒店列表
     *
     * @param dto 查询酒店列表dto
     * @return ListResp<HotelVo> 酒店列表
     */
    ListResp<HotelVo> getHotelListByDto(QueryHotelListDto dto);

    /**
     * 根据酒店ID列表批量查询酒店起价信息
     *
     * @param hotelIds  酒店ID列表
     * @param dateRange 入住时间区间
     * @return Item<HotelVo> 酒店列表
     */
    List<HotelVo> getHotelListByIds(List<String> hotelIds, HotelDateRange dateRange);

    /**
     * 根据房间ID获取房间静态数据并转为RoomVo
     *
     * @param hotelId 酒店ID
     * @param roomId  房间ID
     * @return RoomVo 房间Vo
     */
    RoomVo getRoomVoByRoomId(String hotelId, String roomId);

    /**
     * 根据dto查询房间列表
     *
     * @param dto 查询房间列表dto
     * @return Item<RoomVo> 房间列表
     */
    List<RoomVo> getRoomListByDto(QueryRoomListDto dto);

    /**
     * 根据价格计划ID查询价格计划
     *
     * @param hotelId    酒店ID
     * @param roomId     房间ID
     * @param ratePlanId 价格计划ID
     * @param dateRange  入住时间区间
     */
    RatePlanVo getRatePlanById(String hotelId, String roomId, String ratePlanId, HotelDateRange dateRange);

    /**
     * 可订检查
     *
     * @param dto 创建订单dto
     */
    void preBookingCheck(CreateOrderDto dto);

    /**
     * 下单
     *
     * @param orderEntity 酒店订单对象
     */
    NCNBBookingResp payOrder(HotelOrderEntity orderEntity);

    /**
     * 取消订单
     *
     * @param orderId 订单ID
     */
    void cancelOrder(String orderId);

    /**
     * 根据捷旅OrderId查询订单
     *
     * @param orderId 捷旅OrderId
     * @return NCNBOrderSearchResp.OrderSearchInfo
     */
    NCNBOrderSearchResp.OrderSearchInfo getOrderDetailByOrderId(String orderId);

    void getAllList();
}
