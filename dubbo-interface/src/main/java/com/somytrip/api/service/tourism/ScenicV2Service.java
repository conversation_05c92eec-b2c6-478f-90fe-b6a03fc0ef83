package com.somytrip.api.service.tourism;

import com.somytrip.entity.tourism.QueryTourismParam;
import com.somytrip.entity.vo.ScenicVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ScenicV2Service
 * @Description:
 * @Author: shadow
 * @Date: 2023/10/10 16:43
 */

@Deprecated
public interface ScenicV2Service {

    /**
     * 分析景区价格
     */
    BigDecimal analysisScenicPrice(Map<String, Object> visitMap, List<Map<String, Object>> scenicList);

//    /**
//     * 查询好玩指数最高的几个景区
//     */
//    Item<ScenicVo> selectTopThiScenicCombined(QueryTourismParam param);
//
//    /**
//     * 景区按定位排序
//     */
//    Item<ScenicVo> orderTopThiScenicList(Item<ScenicVo> scenicList);

    /**
     * 按照酒店与景区的距离排序
     */
    List<Map<String, Object>> orderTopThiScenicListByHotel(
            Map<String, Object> hotel,
            List<Map<String, Object>> scenicList
    );

    /**
     * 查询距离指定景区最近的10个景区
     */
    List<ScenicVo> queryNearByScenic(
            QueryTourismParam param,
            int levelValue,
            String cityId,
            ScenicVo dayScenic,
            List<String> selectedScenicIdList
    );


}
