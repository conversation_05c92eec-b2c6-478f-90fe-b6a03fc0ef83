package com.somytrip.api.service.community;

import com.baomidou.mybatisplus.extension.service.IService;
import com.somytrip.entity.community.UserPoint;
import com.somytrip.entity.vo.user.UserPointVo;

import java.util.Map;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.api.service.community
 * @className: UserPointService
 * @author: li<PERSON><PERSON>
 * @description: 用户积分服务接口
 * @date: 2025/8/5 15:12
 * @version: 1.0
 */
public interface UserPointService extends IService<UserPoint> {

    /**
     * 处理反馈提交积分奖励
     * 当日首次发表反馈获得积分奖励
     *
     * @param userId     用户ID
     * @param feedbackId 反馈ID
     * @return 是否获得奖励
     */
    Boolean handleFeedbackSubmitReward(String userId, String feedbackId);

    /**
     * 查询用户积分信息
     *
     * @param userId 用户ID
     * @return 用户积分信息
     */
    UserPointVo getUserPointInfo(String userId);


    /**
     * 检查用户今日是否已获得反馈奖励
     *
     * @param userId 用户ID
     * @return 是否已获得奖励
     */
    Boolean checkTodayFeedbackRewarded(String userId);

    /**
     * 获取积分奖励配置信息
     *
     * @return 积分奖励配置
     */
    Map<String, Object> getPointRewardConfig();

}
