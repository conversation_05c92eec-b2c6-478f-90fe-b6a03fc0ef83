package com.somytrip.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.somytrip.entity.dto.HotEntity;
import com.somytrip.entity.dto.hotel.PaginationDto;
import com.somytrip.entity.resp.ListResp;
import com.somytrip.entity.vo.CityVo;
import com.somytrip.entity.vo.HotScenicVo;
import com.somytrip.entity.vo.HotVo;

import java.util.List;

/**
 * @ClassName: HotService
 * @Description: 热度Service层
 * @Author: shadow
 * @Date: 2023/12/23 14:32
 */
public interface HotService extends IService<HotEntity> {

    /**
     * 查询热门城市列表
     *
     * @param pageNum 页码
     * @return java.util.List<com.somytrip.entity.vo.CityVo>
     * <AUTHOR>
     * @date 2024/7/20 11:47
     */
    ListResp<CityVo> queryHotCityList(Integer pageNum);

    /**
     * 写入redis
     *
     * @param hotScenicId 景区ID
     * @return Boolean true: 成功, false: 失败
     */
    Boolean insertCache(String hotScenicId);

    /**
     * 批量写入热门景区ID到redis
     *
     * @param hotScenicIds 热门景区ID列表
     * <AUTHOR>
     * @date 2024/4/22 11:14
     */
    void insertCacheBatch(List<String> hotScenicIds);

    /**
     * 热度数据持久化
     *
     * @return Boolean true: 成功, false: 失败
     */
    Boolean saveHotData();

    /**
     * 查询热度列表
     *
     * @param dimension 维度()
     * @return Item<HotVo> 热度vo列表
     */
    List<HotVo> queryList(Integer dimension);

    /**
     * 查询热门景区列表
     *
     * @param dimension   维度(1: 城市, 2: 国家)
     * @param dimensionId 维度ID(如: 城市ID)
     * @param pagination  分页参数
     * @return Item<ScenicSimpleVo> 热门景区列表
     */
    ListResp<HotScenicVo> queryHotScenicList(Integer dimension, Integer dimensionId, PaginationDto pagination);
}
