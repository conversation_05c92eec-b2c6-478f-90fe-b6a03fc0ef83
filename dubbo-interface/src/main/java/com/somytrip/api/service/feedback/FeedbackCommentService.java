package com.somytrip.api.service.feedback;

import com.baomidou.mybatisplus.extension.service.IService;
import com.somytrip.entity.community.FeedbackComment;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.api.service.feedback
 * @interfaceName: FeedbackCommentService
 * @author: li<PERSON><PERSON>
 * @description: 反馈评论服务接口
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
public interface FeedbackCommentService extends IService<FeedbackComment> {

    /**
     * 添加评论
     *
     * @param feedbackId 反馈ID
     * @param userId     用户ID
     * @param userType   用户类型
     * @param content    评论内容
     * @param userIp     用户IP
     * @return 是否成功
     */
    Boolean addComment(Integer feedbackId, String userId, Integer userType, String content, String userIp);

    /**
     * 回复评论
     *
     * @param feedbackId      反馈ID
     * @param userId          用户ID
     * @param userType        用户类型
     * @param content         评论内容
     * @param parentId        父评论ID
     * @param replyToUserId   被回复用户ID
     * @param replyToUserType 被回复用户类型
     * @param userIp          用户IP
     * @return 是否成功
     */
    Boolean replyComment(Integer feedbackId, String userId, Integer userType, String content,
                        Integer parentId, String replyToUserId, Integer replyToUserType, String userIp);
}
