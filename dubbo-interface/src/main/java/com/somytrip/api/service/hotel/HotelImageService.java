package com.somytrip.api.service.hotel;

import com.baomidou.mybatisplus.extension.service.IService;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import com.somytrip.entity.hotel.HotelImageEntity;

import java.util.List;

/**
 * 酒店图片表(HotelImages)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-02 15:34:25
 */
public interface HotelImageService extends IService<HotelImageEntity> {

    /**
     * 更新酒店图片信息(同程艺龙 列表)
     *
     * @param images  同程艺龙酒店图片信息列表
     * @param hotelId 酒店ID
     * @return boolean true: 成功, false: 失败
     */
    boolean updateHotelImage(List<ELongStaticHotelInfoResp.Image> images, String hotelId);

    /**
     * 根据酒店ID查询图片列表
     *
     * @param hotelId     酒店ID
     * @param hotelOrigin 酒店来源
     * @return Item<HotelImageEntity> 酒店图片Entity列表
     */
    List<HotelImageEntity> queryImageListByHotelId(String hotelId, HotelOrigin hotelOrigin);

    /**
     * 根据酒店ID查询酒店详情图片列表
     *
     * @param hotelId     酒店ID
     * @param hotelOrigin 酒店来源
     * @return Item<String> 图片url列表
     */
    List<String> queryHotelDetailImageList(String hotelId, HotelOrigin hotelOrigin);
}
