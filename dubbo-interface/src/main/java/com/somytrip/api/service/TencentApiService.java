package com.somytrip.api.service;

import com.somytrip.entity.dto.yy.Asr;
import com.somytrip.entity.tencent.AsrFastParams;

/**
 * @Description: 腾讯API
 * @author: pigeon
 * @created: 2024-07-16 9:44
 */
public interface TencentApiService {
    /**
     * 腾讯asr极速版
     *
     * @param request 参数
     * @param data    音频二进制数据
     * @return response json
     */
    String asrFast(AsrFastParams request, byte[] data);

    /**
     * 腾讯asr一句话 file
     *
     * @param req 参数
     * @return data
     */
    String asrOne(Asr.Req req);

    /**
     * 腾讯asr一句话 base64
     *
     * @param req 参数
     * @return data
     */
    String asrOne(Asr.ReqB64 req);
}
