package com.somytrip.api.service.hotel.v2;

import com.somytrip.entity.dto.hotel.CancelOrderDto;
import com.somytrip.entity.dto.hotel.CreateOrderDtoV2;
import com.somytrip.entity.dto.hotel.QueryHotelPriceForBookingDto;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.hotel.OrderDetailVoV2;

/**
 * @ClassName: HotelOrderV2Service
 * @Description: 酒店订单ServiceV2
 * @Author: shadow
 * @Date: 2024/3/16 11:29
 */
public interface HotelOrderV2Service {

    /**
     * 创建订单
     *
     * @param createOrderDto 创建订单参数dto
     * @return com.somytrip.entity.response.ResponseResult
     * <AUTHOR>
     * @date 2024/4/26 10:58
     */
    ResponseResult createOrder(CreateOrderDtoV2 createOrderDto);

    /**
     * webbeds 查询酒店价格
     *
     * @param queryHotelPriceForBookingDto
     * @return
     */
    ResponseResult queryHotelFee(QueryHotelPriceForBookingDto queryHotelPriceForBookingDto);

    /**
     * 支付订单(捷旅创建订单)
     *
     * @param orderNo 订单号
     * @return boolean: 成功, false: 失败
     */
    boolean payOrder(String orderNo);

    /**
     * 取消订单
     *
     * @param cancelOrderDto 取消订单dto
     * @return boolean true: 成功, false: 失败
     */
    boolean cancelOrder(CancelOrderDto cancelOrderDto);

    /**
     * 查询订单详情
     *
     * @param uid     用户ID
     * @param orderNo 订单号
     * @return OrderDetailVoV2 订单详情voVo
     */
    OrderDetailVoV2 orderDetail(Long uid, String orderNo);
}
