package com.somytrip.api.service.itinerary;

import com.baomidou.mybatisplus.extension.service.IService;
import com.somytrip.entity.itinerary.ItinerarySearchParam;
import com.somytrip.entity.itinerary.ItineraryUserConditionEntity;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.api.service.itinerary
 * @interfaceName: ItineraryUserConditionService
 * @author: shadow
 * @description: 攻略用户查询条件Service
 * @date: 2024/11/12 14:55
 * @version: 1.0
 */
public interface ItineraryUserConditionService extends IService<ItineraryUserConditionEntity> {

    /**
     * 保存攻略用户查询条件
     *
     * @param searchParam 查询条件
     * <AUTHOR>
     * @date 2024/11/12 14:59
     */
    void saveUserCondition(ItinerarySearchParam searchParam);

    /**
     * 获取指定用户最新的查询条件
     *
     * @param uid 用户ID
     * @return com.somytrip.entity.itinerary.ItinerarySearchParam
     * <AUTHOR>
     * @date 2024/11/12 15:03
     */
    ItinerarySearchParam getLatestByUid(String uid);
}
