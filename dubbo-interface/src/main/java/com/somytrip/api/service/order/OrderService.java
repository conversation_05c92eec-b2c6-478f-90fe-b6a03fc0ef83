package com.somytrip.api.service.order;

import com.somytrip.entity.dto.order.CancelOrder;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.yeepay.PayVo;

/**
 * @Description: 第一版订单操作
 * @author: pigeon
 * @created: 2023-11-11 9:22
 */
public interface OrderService {
    /**
     * 取消订单
     *
     * @param vo 参数
     * @return 成功|失败
     */
    ResponseResult cancelOrder(CancelOrder.Vo vo);

    /**
     * 未支付取消订单
     *
     * @param orderNo
     * @param uid
     * @return
     */
    ResponseResult unpaidCancelOrder(String orderNo, String uid);

    /**
     * 已支付取消
     *
     * @param applyReasonInfo
     * @return
     */
    ResponseResult paidCancelOrder(CancelOrder.ApplyReasonInfo applyReasonInfo);

    /**
     * 取消订单退款申请资金明细
     *
     * @param orderNo
     * @param uid
     * @return
     */
    ResponseResult cancelOrderRefundApply(String orderNo, String uid);

    /**
     * 获取支付参数
     *
     * @param vo  请求参数
     * @param ip  用户IP
     * @param uid 用户Id
     * @return JSONResultUtils
     */
    ResponseResult getPrePayParams(PayVo vo, String ip, String uid);

    /**
     * 申请退款
     *
     * @param orderNo 订单ID
     * @param uid     用户ID
     * @return JSONResultUtils
     */
    ResponseResult createRefundOrder(String orderNo, String uid);

    /**
     * 支付收银台页面返回
     *
     * @param orderNo
     * @param uid
     * @return
     */
    ResponseResult payCashDesk(String orderNo, String uid);

    /**
     * 查询支付状态
     *
     * @param orderNo
     * @param uid
     * @return
     */
    ResponseResult payResult(String orderNo, String uid);
}
