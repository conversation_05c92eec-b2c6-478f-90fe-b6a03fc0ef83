package com.somytrip.provider.community;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.somytrip.api.service.community.FeedbackPlatformService;
import com.somytrip.entity.FileInfo;
import com.somytrip.entity.dto.community.feedback.FeedbackCommentDto;
import com.somytrip.entity.dto.community.feedback.FeedbackCommentListReq;
import com.somytrip.entity.dto.community.feedback.FeedbackPlatformDto;
import com.somytrip.entity.dto.community.feedback.FeedbackPlatformListReq;
import com.somytrip.entity.vo.community.FeedbackCommentListRes;
import com.somytrip.entity.vo.community.FeedbackPlatformListRes;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @projectName: Test-feedback_platform
 * @package: com.somytrip.provider.community
 * @className: TestFeedbackPlatformService
 * @author: lijunqi
 * @description:
 * @date: 2025/8/5 16:29
 * @version: 1.0
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class TestFeedbackPlatformService {

    @Resource
    private FeedbackPlatformService feedbackPlatformService;

    /**
     * 测试提交反馈建议
     */
    @Test
    public void testSubmitFeedback() {
        log.info("开始测试：提交反馈建议");

        // 准备测试数据
        FeedbackPlatformDto dto = new FeedbackPlatformDto();
        dto.setUserId(12345L);
        dto.setTitle("测试反馈标题");
        dto.setContent("这是一个测试反馈内容，用于验证反馈提交功能是否正常工作。");
        dto.setPhone("13800138000");
        dto.setIpAddress("*************");
        dto.setIpProvinceName("广东省");
        dto.setIpCityName("深圳市");
        dto.setCategory(1); // 建议
        dto.setSource(1); // 小程序

        // 添加测试图片
        List<FileInfo> images = new ArrayList<>();
        images.add(new FileInfo("test-bucket", "test-image1.jpg", ""));
        images.add(new FileInfo("test-bucket", "test-image2.jpg", ""));
        dto.setImages(images);

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Long id = feedbackPlatformService.submitFeedback(dto);
        stopWatch.stop();

        // 验证结果
        assert id != null && id > 0 : "反馈ID应该大于0";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("提交成功，反馈ID：{}", id);
        log.info("测试数据：{}", JSON.toJSONString(dto, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：提交反馈建议");
    }

    /**
     * 测试发布反馈评论
     */
    @Test
    public void testPublishComment() {
        log.info("开始测试：发布反馈评论");

        // 准备测试数据
        FeedbackCommentDto dto = new FeedbackCommentDto();
        dto.setFeedbackId(1L); // 假设存在ID为1的反馈
        dto.setUserId(12345L);
        dto.setContent("这是一个测试评论内容，用于验证评论发布功能。");
        dto.setIpAddress("*************");
        dto.setIpProvinceName("广东省");
        dto.setIpCityName("深圳市");

        // 添加测试图片
        List<FileInfo> images = new ArrayList<>();
        images.add(new FileInfo("test-bucket", "comment-image1.jpg", ""));
        dto.setImages(images);

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Long commentId = feedbackPlatformService.publishComment(dto);
        stopWatch.stop();

        // 验证结果
        assert commentId != null && commentId > 0 : "评论ID应该大于0";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("发布成功，评论ID：{}", commentId);
        log.info("测试数据：{}", JSON.toJSONString(dto, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：发布反馈评论");
    }

    /**
     * 测试发布回复评论
     */
    @Test
    public void testPublishReplyComment() {
        log.info("开始测试：发布回复评论");

        // 准备测试数据
        FeedbackCommentDto dto = new FeedbackCommentDto();
        dto.setFeedbackId(1L); // 假设存在ID为1的反馈
        dto.setUserId(12346L);
        dto.setContent("这是一个回复评论的测试内容。");
        dto.setIpAddress("*************");
        dto.setIpProvinceName("广东省");
        dto.setIpCityName("广州市");
        dto.setParentId(1L); // 假设存在ID为1的父评论
        dto.setReplyToUserId("12345");
        dto.setReplyToUserType(3); // 普通用户

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Long commentId = feedbackPlatformService.publishComment(dto);
        stopWatch.stop();

        // 验证结果
        assert commentId != null && commentId > 0 : "回复评论ID应该大于0";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("回复发布成功，评论ID：{}", commentId);
        log.info("测试数据：{}", JSON.toJSONString(dto, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：发布回复评论");
    }

    /**
     * 测试查询反馈列表 - 热门排序
     */
    @Test
    public void testGetFeedbackListByHot() {
        log.info("开始测试：查询反馈列表 - 热门排序");

        // 准备测试数据
        FeedbackPlatformListReq req = new FeedbackPlatformListReq();
        req.setPageNum(1);
        req.setPageSize(10);
        req.setSortType(1); // 热门排序
        req.setUserId(12345L);

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        FeedbackPlatformListRes result = feedbackPlatformService.getFeedbackList(req);
        stopWatch.stop();

        // 验证结果
        assert result != null : "查询结果不能为空";
        assert result.getFeedbackList() != null : "反馈列表不能为空";
        assert result.getTotal() >= 0 : "总记录数应该大于等于0";
        assert result.getCurrentPage().equals(req.getPageNum()) : "当前页码应该与请求一致";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("查询结果总数：{}", result.getTotal());
        log.info("当前页数据量：{}", result.getFeedbackList().size());
        log.info("测试数据：{}", JSON.toJSONString(req, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：查询反馈列表 - 热门排序");
    }

    /**
     * 测试查询反馈列表 - 最新排序
     */
    @Test
    public void testGetFeedbackListByLatest() {
        log.info("开始测试：查询反馈列表 - 最新排序");

        // 准备测试数据
        FeedbackPlatformListReq req = new FeedbackPlatformListReq();
        req.setPageNum(1);
        req.setPageSize(20);
        req.setSortType(2); // 最新排序
        req.setUserId(12345L);

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        FeedbackPlatformListRes result = feedbackPlatformService.getFeedbackList(req);
        stopWatch.stop();

        // 验证结果
        assert result != null : "查询结果不能为空";
        assert result.getFeedbackList() != null : "反馈列表不能为空";
        assert result.getPageSize().equals(req.getPageSize()) : "页面大小应该与请求一致";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("查询结果总数：{}", result.getTotal());
        log.info("总页数：{}", result.getTotalPages());
        log.info("是否有下一页：{}", result.getHasNext());
        log.info("测试数据：{}", JSON.toJSONString(req, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：查询反馈列表 - 最新排序");
    }

    /**
     * 测试查询反馈留言列表
     */
    @Test
    public void testGetFeedbackCommentList() {
        log.info("开始测试：查询反馈留言列表");

        // 准备测试数据
        FeedbackCommentListReq req = new FeedbackCommentListReq();
        req.setFeedbackId(1L); // 假设存在ID为1的反馈
        req.setPageNum(1);
        req.setPageSize(8);
        req.setUserId(12345L);

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        FeedbackCommentListRes result = feedbackPlatformService.getFeedbackCommentList(req);
        stopWatch.stop();

        // 验证结果
        assert result != null : "查询结果不能为空";
        assert result.getCommentList() != null : "评论列表不能为空";
        assert result.getTotal() >= 0 : "总记录数应该大于等于0";
        assert result.getCurrentPage().equals(req.getPageNum()) : "当前页码应该与请求一致";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("查询结果总数：{}", result.getTotal());
        log.info("当前页评论数量：{}", result.getCommentList().size());
        log.info("是否有上一页：{}", result.getHasPrevious());
        log.info("测试数据：{}", JSON.toJSONString(req, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：查询反馈留言列表");
    }

    /**
     * 测试查询反馈留言列表 - 分页测试
     */
    @Test
    public void testGetFeedbackCommentListPagination() {
        log.info("开始测试：查询反馈留言列表 - 分页测试");

        // 准备测试数据 - 第一页（首次展开3条）
        FeedbackCommentListReq req1 = new FeedbackCommentListReq();
        req1.setFeedbackId(1L);
        req1.setPageNum(1);
        req1.setPageSize(3); // 首次展开3条
        req1.setUserId(12345L);

        // 执行第一页测试
        StopWatch stopWatch1 = new StopWatch();
        stopWatch1.start();
        FeedbackCommentListRes result1 = feedbackPlatformService.getFeedbackCommentList(req1);
        stopWatch1.stop();

        // 验证第一页结果
        assert result1 != null : "第一页查询结果不能为空";
        assert result1.getCommentList() != null : "第一页评论列表不能为空";

        log.info("第一页耗时：{} second", stopWatch1.getTotalTimeSeconds());
        log.info("第一页结果总数：{}", result1.getTotal());
        log.info("第一页评论数量：{}", result1.getCommentList().size());

        // 准备测试数据 - 第二页（后续每次8条）
        FeedbackCommentListReq req2 = new FeedbackCommentListReq();
        req2.setFeedbackId(1L);
        req2.setPageNum(2);
        req2.setPageSize(8); // 后续每次8条
        req2.setUserId(12345L);

        // 执行第二页测试
        StopWatch stopWatch2 = new StopWatch();
        stopWatch2.start();
        FeedbackCommentListRes result2 = feedbackPlatformService.getFeedbackCommentList(req2);
        stopWatch2.stop();

        // 验证第二页结果
        assert result2 != null : "第二页查询结果不能为空";
        assert result2.getCommentList() != null : "第二页评论列表不能为空";

        log.info("第二页耗时：{} second", stopWatch2.getTotalTimeSeconds());
        log.info("第二页结果总数：{}", result2.getTotal());
        log.info("第二页评论数量：{}", result2.getCommentList().size());

        log.info("测试通过：查询反馈留言列表 - 分页测试");
    }

    /**
     * 测试参数校验 - 提交反馈建议空参数
     */
    @Test(expected = Exception.class)
    public void testSubmitFeedbackWithNullDto() {
        log.info("开始测试：提交反馈建议 - 空参数校验");

        // 执行测试 - 传入null参数
        feedbackPlatformService.submitFeedback(null);

        log.info("测试通过：提交反馈建议 - 空参数校验");
    }

    /**
     * 测试参数校验 - 提交反馈建议缺少用户ID
     */
    @Test(expected = Exception.class)
    public void testSubmitFeedbackWithoutUserId() {
        log.info("开始测试：提交反馈建议 - 缺少用户ID校验");

        // 准备测试数据 - 不设置用户ID
        FeedbackPlatformDto dto = new FeedbackPlatformDto();
        dto.setContent("测试内容");

        // 执行测试
        feedbackPlatformService.submitFeedback(dto);

        log.info("测试通过：提交反馈建议 - 缺少用户ID校验");
    }

    /**
     * 测试参数校验 - 提交反馈建议缺少内容
     */
    @Test(expected = Exception.class)
    public void testSubmitFeedbackWithoutContent() {
        log.info("开始测试：提交反馈建议 - 缺少内容校验");

        // 准备测试数据 - 不设置内容
        FeedbackPlatformDto dto = new FeedbackPlatformDto();
        dto.setUserId(12345L);

        // 执行测试
        feedbackPlatformService.submitFeedback(dto);

        log.info("测试通过：提交反馈建议 - 缺少内容校验");
    }

    /**
     * 测试参数校验 - 发布评论空参数
     */
    @Test(expected = Exception.class)
    public void testPublishCommentWithNullDto() {
        log.info("开始测试：发布评论 - 空参数校验");

        // 执行测试 - 传入null参数
        feedbackPlatformService.publishComment(null);

        log.info("测试通过：发布评论 - 空参数校验");
    }

    /**
     * 测试参数校验 - 发布评论缺少反馈ID
     */
    @Test(expected = Exception.class)
    public void testPublishCommentWithoutFeedbackId() {
        log.info("开始测试：发布评论 - 缺少反馈ID校验");

        // 准备测试数据 - 不设置反馈ID
        FeedbackCommentDto dto = new FeedbackCommentDto();
        dto.setUserId(12345L);
        dto.setContent("测试评论内容");

        // 执行测试
        feedbackPlatformService.publishComment(dto);

        log.info("测试通过：发布评论 - 缺少反馈ID校验");
    }

    /**
     * 测试参数校验 - 查询不存在的反馈留言列表
     */
    @Test(expected = Exception.class)
    public void testGetFeedbackCommentListWithInvalidFeedbackId() {
        log.info("开始测试：查询反馈留言列表 - 无效反馈ID校验");

        // 准备测试数据 - 使用不存在的反馈ID
        FeedbackCommentListReq req = new FeedbackCommentListReq();
        req.setFeedbackId(999999L); // 假设这个ID不存在
        req.setPageNum(1);
        req.setPageSize(8);
        req.setUserId(12345L);

        // 执行测试
        feedbackPlatformService.getFeedbackCommentList(req);

        log.info("测试通过：查询反馈留言列表 - 无效反馈ID校验");
    }

    /**
     * 测试标题空格处理
     */
    @Test
    public void testTitleSpaceProcessing() {
        log.info("开始测试：标题空格处理");

        // 准备测试数据 - 包含多个连续空格的标题
        FeedbackPlatformDto dto = new FeedbackPlatformDto();
        dto.setUserId(12345L);
        dto.setTitle("  测试   标题   包含   多个   空格  ");
        dto.setContent("测试内容");
        dto.setIpAddress("*************");
        dto.setIpProvinceName("广东省");
        dto.setIpCityName("深圳市");

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Long id = feedbackPlatformService.submitFeedback(dto);
        stopWatch.stop();

        // 验证结果
        assert id != null && id > 0 : "反馈ID应该大于0";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("提交成功，反馈ID：{}", id);
        log.info("处理后的标题：{}", dto.getTitle());

        log.info("测试通过：标题空格处理");
    }

    /**
     * 测试时间格式化显示
     */
    @Test
    public void testTimeFormatDisplay() {
        log.info("开始测试：时间格式化显示");

        // 准备测试数据
        FeedbackPlatformListReq req = new FeedbackPlatformListReq();
        req.setPageNum(1);
        req.setPageSize(5);
        req.setSortType(2); // 最新排序，便于查看时间
        req.setUserId(12345L);

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        FeedbackPlatformListRes result = feedbackPlatformService.getFeedbackList(req);
        stopWatch.stop();

        // 验证结果
        assert result != null : "查询结果不能为空";
        assert result.getFeedbackList() != null : "反馈列表不能为空";

        // 验证时间格式化
        if (!result.getFeedbackList().isEmpty()) {
            result.getFeedbackList().forEach(vo -> {
                if (vo.getCreateTimeShow() != null) {
                    // 验证时间格式是否符合 yyyy-MM-dd HH:mm:ss
                    String timePattern = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
                    assert vo.getCreateTimeShow().matches(timePattern) :
                        "时间格式应该为 yyyy-MM-dd HH:mm:ss，实际格式：" + vo.getCreateTimeShow();

                    log.info("反馈ID: {}, 格式化时间: {}", vo.getId(), vo.getCreateTimeShow());
                }
            });
        }

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("查询结果总数：{}", result.getTotal());
        log.info("时间格式验证通过");

        log.info("测试通过：时间格式化显示");
    }

    /**
     * 测试评论时间格式化显示
     */
    @Test
    public void testCommentTimeFormatDisplay() {
        log.info("开始测试：评论时间格式化显示");

        // 准备测试数据
        FeedbackCommentListReq req = new FeedbackCommentListReq();
        req.setFeedbackId(1L); // 假设存在ID为1的反馈
        req.setPageNum(1);
        req.setPageSize(5);
        req.setUserId(12345L);

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        FeedbackCommentListRes result = feedbackPlatformService.getFeedbackCommentList(req);
        stopWatch.stop();

        // 验证结果
        assert result != null : "查询结果不能为空";
        assert result.getCommentList() != null : "评论列表不能为空";

        // 验证时间格式化
        if (!result.getCommentList().isEmpty()) {
            result.getCommentList().forEach(vo -> {
                if (vo.getCreateTimeShow() != null) {
                    // 验证时间格式是否符合 yyyy-MM-dd HH:mm:ss
                    String timePattern = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}";
                    assert vo.getCreateTimeShow().matches(timePattern) :
                        "时间格式应该为 yyyy-MM-dd HH:mm:ss，实际格式：" + vo.getCreateTimeShow();

                    log.info("评论ID: {}, 格式化时间: {}", vo.getId(), vo.getCreateTimeShow());
                }
            });
        }

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("查询结果总数：{}", result.getTotal());
        log.info("评论时间格式验证通过");

        log.info("测试通过：评论时间格式化显示");
    }
}
