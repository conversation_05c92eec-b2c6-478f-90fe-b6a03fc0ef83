package com.somytrip.provider.community;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.somytrip.api.service.community.UserPointService;
import com.somytrip.entity.dto.community.user.UserPointRewardDto;
import com.somytrip.entity.vo.user.UserPointVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * @projectName: Test-feedback_platform
 * @package: com.somytrip.provider.community
 * @className: TestUserPointService
 * @author: lijunqi
 * @description: 用户积分服务测试类
 * @date: 2025-01-04 18:00:00
 * @version: 1.0
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class TestUserPointService {

    @Resource
    private UserPointService userPointService;

    /**
     * 【修改标注】测试新用户积分奖励（使用INSERT ON DUPLICATE KEY UPDATE自动创建积分账户）
     */
    @Test
    public void testRewardPointsForNewUser() {
        log.info("开始测试：新用户积分奖励（使用INSERT ON DUPLICATE KEY UPDATE自动创建积分账户）");

        // 准备测试数据 - 使用一个不存在的用户ID
        String newUserId = "test_new_user_" + System.currentTimeMillis();

        // 【修改标注】不再需要检查用户积分账户是否存在，直接进行奖励操作
        log.info("准备为新用户 {} 进行积分奖励", newUserId);

        // 准备奖励参数
        UserPointRewardDto rewardDto = new UserPointRewardDto();
        rewardDto.setUserId(newUserId);
        rewardDto.setRewardPoints(50);
        rewardDto.setChangeType("feedback");
        rewardDto.setBusinessType("FEEDBACK_SUBMIT");
        rewardDto.setRelatedId("test_feedback_123");
        rewardDto.setDescription("新用户首次反馈奖励测试");
        rewardDto.setSourcePlatform(1);
        rewardDto.setRemark("自动化测试");

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 【修改标注】直接执行积分奖励，系统会自动处理账户创建
        Boolean result = userPointService.rewardPoints(rewardDto);

        stopWatch.stop();

        // 验证结果
        assert result != null && result : "积分奖励应该成功";

        // 【修改标注】验证用户积分账户已通过INSERT ON DUPLICATE KEY UPDATE自动创建

        // 查询用户积分信息验证
        UserPointVo userPointVo = userPointService.getUserPointInfo(newUserId);
        assert userPointVo != null : "用户积分信息不能为空";
        assert userPointVo.getPointBalance().equals(50) : "积分余额应该为50";
        assert userPointVo.getTotalEarned().equals(50) : "累计获得积分应该为50";
        assert userPointVo.getTotalConsumed().equals(0) : "累计消费积分应该为0";
        assert userPointVo.getPointLevel().equals(1) : "积分等级应该为1";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("积分奖励成功，用户ID: {}", newUserId);
        log.info("用户积分信息：{}", JSON.toJSONString(userPointVo, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：新用户积分奖励（使用INSERT ON DUPLICATE KEY UPDATE自动创建积分账户）");
    }

    /**
     * 【修改标注】测试现有用户积分奖励（使用INSERT ON DUPLICATE KEY UPDATE更新现有账户）
     */
    @Test
    public void testRewardPointsForExistingUser() {
        log.info("开始测试：现有用户积分奖励（使用INSERT ON DUPLICATE KEY UPDATE更新现有账户）");

        // 准备测试数据 - 先通过第一次奖励创建账户
        String existingUserId = "test_existing_user_" + System.currentTimeMillis();

        // 【修改标注】先进行一次积分奖励来创建账户
        UserPointRewardDto firstRewardDto = new UserPointRewardDto();
        firstRewardDto.setUserId(existingUserId);
        firstRewardDto.setRewardPoints(10);
        firstRewardDto.setChangeType("feedback");
        firstRewardDto.setBusinessType("FEEDBACK_SUBMIT");
        firstRewardDto.setRelatedId("test_feedback_init");
        firstRewardDto.setDescription("初始化账户");
        firstRewardDto.setSourcePlatform(1);

        Boolean initResult = userPointService.rewardPoints(firstRewardDto);
        assert initResult != null && initResult : "初始化积分账户应该成功";
        log.info("为测试用户 {} 初始化积分账户成功", existingUserId);

        // 准备奖励参数
        UserPointRewardDto rewardDto = new UserPointRewardDto();
        rewardDto.setUserId(existingUserId);
        rewardDto.setRewardPoints(30);
        rewardDto.setChangeType("feedback");
        rewardDto.setBusinessType("FEEDBACK_SUBMIT");
        rewardDto.setRelatedId("test_feedback_456");
        rewardDto.setDescription("现有用户反馈奖励测试");
        rewardDto.setSourcePlatform(2);

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Boolean result = userPointService.rewardPoints(rewardDto);
        stopWatch.stop();

        // 验证结果
        assert result != null && result : "积分奖励应该成功";

        // 查询用户积分信息验证
        UserPointVo userPointVo = userPointService.getUserPointInfo(existingUserId);
        assert userPointVo != null : "用户积分信息不能为空";
        assert userPointVo.getPointBalance().equals(40) : "积分余额应该为40（10+30）";
        assert userPointVo.getTotalEarned().equals(40) : "累计获得积分应该为40（10+30）";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("积分奖励成功，用户ID: {}", existingUserId);
        log.info("用户积分信息：{}", JSON.toJSONString(userPointVo, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：现有用户积分奖励");
    }

    /**
     * 测试反馈提交积分奖励
     */
    @Test
    public void testHandleFeedbackSubmitReward() {
        log.info("开始测试：反馈提交积分奖励");

        // 准备测试数据
        String userId = "test_feedback_user_" + System.currentTimeMillis();
        String feedbackId = "feedback_" + System.currentTimeMillis();

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Boolean result = userPointService.handleFeedbackSubmitReward(userId, feedbackId);
        stopWatch.stop();

        // 验证结果
        assert result != null && result : "反馈提交积分奖励应该成功";

        // 查询用户积分信息验证
        UserPointVo userPointVo = userPointService.getUserPointInfo(userId);
        assert userPointVo != null : "用户积分信息不能为空";
        assert userPointVo.getPointBalance() >= 10 : "积分余额应该至少为10（反馈奖励）";
        assert userPointVo.getTodayFeedbackRewarded() : "今日应该已获得反馈奖励";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("反馈提交积分奖励成功，用户ID: {}, 反馈ID: {}", userId, feedbackId);
        log.info("用户积分信息：{}", JSON.toJSONString(userPointVo, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：反馈提交积分奖励");
    }

    /**
     * 测试重复反馈提交奖励（同一天不应该重复奖励）
     */
    @Test
    public void testDuplicateFeedbackSubmitReward() {
        log.info("开始测试：重复反馈提交奖励");

        // 准备测试数据
        String userId = "test_duplicate_user_" + System.currentTimeMillis();
        String feedbackId1 = "feedback_1_" + System.currentTimeMillis();
        String feedbackId2 = "feedback_2_" + System.currentTimeMillis();

        // 第一次提交反馈
        Boolean firstResult = userPointService.handleFeedbackSubmitReward(userId, feedbackId1);
        assert firstResult != null && firstResult : "第一次反馈提交积分奖励应该成功";

        // 第二次提交反馈（同一天）
        Boolean secondResult = userPointService.handleFeedbackSubmitReward(userId, feedbackId2);
        assert secondResult != null && !secondResult : "第二次反馈提交积分奖励应该失败（同一天不重复奖励）";

        // 验证用户积分信息
        UserPointVo userPointVo = userPointService.getUserPointInfo(userId);
        assert userPointVo.getPointBalance().equals(10) : "积分余额应该只有10（只奖励一次）";
        assert userPointVo.getTodayFeedbackRewarded() : "今日应该已获得反馈奖励";

        log.info("用户积分信息：{}", JSON.toJSONString(userPointVo, JSONWriter.Feature.WriteMapNullValue));
        log.info("测试通过：重复反馈提交奖励");
    }
}
