package com.somytrip.provider.community;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.somytrip.api.service.community.UserPointService;
import com.somytrip.entity.dto.community.user.UserPointRewardDto;
import com.somytrip.entity.vo.user.UserPointVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * @projectName: Test-feedback_platform
 * @package: com.somytrip.provider.community
 * @className: TestUserPointService
 * @author: lijunqi
 * @description: 用户积分服务测试类
 * @date: 2025-01-04 18:00:00
 * @version: 1.0
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class TestUserPointService {

    @Resource
    private UserPointService userPointService;

    /**
     * 测试处理反馈提交积分奖励
     */
    @Test
    public void testHandleFeedbackSubmitReward() {
        log.info("开始测试：处理反馈提交积分奖励");

        // 准备测试数据
        String userId = "test_feedback_user_" + System.currentTimeMillis();
        String feedbackId = "feedback_" + System.currentTimeMillis();

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Boolean result = userPointService.handleFeedbackSubmitReward(userId, feedbackId);
        stopWatch.stop();

        // 验证结果
        assert result != null && result : "反馈提交积分奖励应该成功";

        // 查询用户积分信息验证
        UserPointVo userPointVo = userPointService.getUserPointInfo(userId);
        assert userPointVo != null : "用户积分信息不能为空";
        assert userPointVo.getPointBalance() >= 10 : "积分余额应该至少为10（反馈奖励）";
        assert userPointVo.getTodayFeedbackRewarded() : "今日应该已获得反馈奖励";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("反馈提交积分奖励成功，用户ID: {}, 反馈ID: {}", userId, feedbackId);
        log.info("用户积分信息：{}", JSON.toJSONString(userPointVo, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：处理反馈提交积分奖励");
    }

    /**
     * 测试查询用户积分信息 - 新用户
     */
    @Test
    public void testGetUserPointInfoForNewUser() {
        log.info("开始测试：查询用户积分信息 - 新用户");

        // 准备测试数据 - 使用一个不存在的用户ID
        String newUserId = "test_new_user_info_" + System.currentTimeMillis();

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        UserPointVo result = userPointService.getUserPointInfo(newUserId);
        stopWatch.stop();

        // 验证结果
        assert result != null : "查询结果不能为空";
        assert result.getUserId().equals(newUserId) : "用户ID应该一致";
        assert result.getPointBalance().equals(0) : "新用户积分余额应该为0";
        assert result.getTotalEarned().equals(0) : "新用户累计获得积分应该为0";
        assert result.getTotalConsumed().equals(0) : "新用户累计消费积分应该为0";
        assert result.getPointLevel().equals(1) : "新用户积分等级应该为1";
        assert result.getTodayFeedbackRewarded() != null : "今日反馈奖励状态不能为空";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("查询结果：{}", JSON.toJSONString(result, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：查询用户积分信息 - 新用户");
    }

    /**
     * 测试查询用户积分信息 - 现有用户
     */
    @Test
    public void testGetUserPointInfoForExistingUser() {
        log.info("开始测试：查询用户积分信息 - 现有用户");

        // 准备测试数据 - 先创建一个有积分的用户
        String existingUserId = "test_existing_user_info_" + System.currentTimeMillis();

        // 先给用户奖励一些积分
        UserPointRewardDto rewardDto = new UserPointRewardDto();
        rewardDto.setUserId(existingUserId);
        rewardDto.setRewardPoints(100);
        rewardDto.setChangeType("feedback");
        rewardDto.setBusinessType("FEEDBACK_SUBMIT");
        rewardDto.setRelatedId("test_feedback_setup");
        rewardDto.setDescription("测试数据准备");
        rewardDto.setSourcePlatform(1);

        Boolean rewardResult = userPointService.handleFeedbackSubmitReward(existingUserId, "test_feedback_setup");
        log.info("测试数据准备完成，奖励结果: {}", rewardResult);

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        UserPointVo result = userPointService.getUserPointInfo(existingUserId);
        stopWatch.stop();

        // 验证结果
        assert result != null : "查询结果不能为空";
        assert result.getUserId().equals(existingUserId) : "用户ID应该一致";
        assert result.getPointBalance() > 0 : "现有用户积分余额应该大于0";
        assert result.getTotalEarned() > 0 : "现有用户累计获得积分应该大于0";
        assert result.getTotalConsumed().equals(0) : "累计消费积分应该为0";
        assert result.getPointLevel() >= 1 : "积分等级应该大于等于1";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("查询结果：{}", JSON.toJSONString(result, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：查询用户积分信息 - 现有用户");
    }

    /**
     * 测试检查用户今日是否已获得反馈奖励 - 未获得
     */
    @Test
    public void testCheckTodayFeedbackRewardedForNewUser() {
        log.info("开始测试：检查用户今日是否已获得反馈奖励 - 未获得");

        // 准备测试数据 - 使用一个新用户ID
        String newUserId = "test_check_reward_new_" + System.currentTimeMillis();

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Boolean result = userPointService.checkTodayFeedbackRewarded(newUserId);
        stopWatch.stop();

        // 验证结果
        assert result != null : "查询结果不能为空";
        assert !result : "新用户今日应该未获得反馈奖励";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("查询结果：{}", result);

        log.info("测试通过：检查用户今日是否已获得反馈奖励 - 未获得");
    }

    /**
     * 测试检查用户今日是否已获得反馈奖励 - 已获得
     */
    @Test
    public void testCheckTodayFeedbackRewardedForRewardedUser() {
        log.info("开始测试：检查用户今日是否已获得反馈奖励 - 已获得");

        // 准备测试数据 - 先让用户获得反馈奖励
        String rewardedUserId = "test_check_reward_rewarded_" + System.currentTimeMillis();
        String feedbackId = "feedback_check_" + System.currentTimeMillis();

        // 先执行反馈奖励
        Boolean rewardResult = userPointService.handleFeedbackSubmitReward(rewardedUserId, feedbackId);
        assert rewardResult != null && rewardResult : "反馈奖励应该成功";
        log.info("用户 {} 已获得反馈奖励", rewardedUserId);

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Boolean result = userPointService.checkTodayFeedbackRewarded(rewardedUserId);
        stopWatch.stop();

        // 验证结果
        assert result != null : "查询结果不能为空";
        assert result : "已奖励用户今日应该已获得反馈奖励";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("查询结果：{}", result);

        log.info("测试通过：检查用户今日是否已获得反馈奖励 - 已获得");
    }

    /**
     * 测试通用积分奖励方法 - 新用户
     */
    @Test
    public void testRewardPointsForNewUser() {
        log.info("开始测试：通用积分奖励方法 - 新用户");

        // 准备测试数据
        String newUserId = "test_reward_new_" + System.currentTimeMillis();

        UserPointRewardDto rewardDto = new UserPointRewardDto();
        rewardDto.setUserId(newUserId);
        rewardDto.setRewardPoints(50);
        rewardDto.setChangeType("feedback");
        rewardDto.setBusinessType("FEEDBACK_SUBMIT");
        rewardDto.setRelatedId("test_feedback_123");
        rewardDto.setDescription("新用户积分奖励测试");
        rewardDto.setSourcePlatform(1);
        rewardDto.setRemark("自动化测试");

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Boolean result = userPointService.rewardPoints(rewardDto);
        stopWatch.stop();

        // 验证结果
        assert result != null && result : "积分奖励应该成功";

        // 查询用户积分信息验证
        UserPointVo userPointVo = userPointService.getUserPointInfo(newUserId);
        assert userPointVo != null : "用户积分信息不能为空";
        assert userPointVo.getPointBalance().equals(50) : "积分余额应该为50";
        assert userPointVo.getTotalEarned().equals(50) : "累计获得积分应该为50";
        assert userPointVo.getTotalConsumed().equals(0) : "累计消费积分应该为0";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("积分奖励成功，用户ID: {}", newUserId);
        log.info("测试数据：{}", JSON.toJSONString(rewardDto, JSONWriter.Feature.WriteMapNullValue));
        log.info("用户积分信息：{}", JSON.toJSONString(userPointVo, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：通用积分奖励方法 - 新用户");
    }

    /**
     * 测试通用积分奖励方法 - 现有用户
     */
    @Test
    public void testRewardPointsForExistingUser() {
        log.info("开始测试：通用积分奖励方法 - 现有用户");

        // 准备测试数据 - 先创建一个有积分的用户
        String existingUserId = "test_reward_existing_" + System.currentTimeMillis();

        // 第一次奖励
        UserPointRewardDto firstRewardDto = new UserPointRewardDto();
        firstRewardDto.setUserId(existingUserId);
        firstRewardDto.setRewardPoints(30);
        firstRewardDto.setChangeType("feedback");
        firstRewardDto.setBusinessType("FEEDBACK_SUBMIT");
        firstRewardDto.setRelatedId("test_feedback_first");
        firstRewardDto.setDescription("第一次积分奖励");
        firstRewardDto.setSourcePlatform(1);

        Boolean firstResult = userPointService.rewardPoints(firstRewardDto);
        assert firstResult != null && firstResult : "第一次积分奖励应该成功";

        // 第二次奖励
        UserPointRewardDto secondRewardDto = new UserPointRewardDto();
        secondRewardDto.setUserId(existingUserId);
        secondRewardDto.setRewardPoints(20);
        secondRewardDto.setChangeType("feedback");
        secondRewardDto.setBusinessType("FEEDBACK_SUBMIT");
        secondRewardDto.setRelatedId("test_feedback_second");
        secondRewardDto.setDescription("第二次积分奖励");
        secondRewardDto.setSourcePlatform(2);

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Boolean result = userPointService.rewardPoints(secondRewardDto);
        stopWatch.stop();

        // 验证结果
        assert result != null && result : "第二次积分奖励应该成功";

        // 查询用户积分信息验证
        UserPointVo userPointVo = userPointService.getUserPointInfo(existingUserId);
        assert userPointVo != null : "用户积分信息不能为空";
        assert userPointVo.getPointBalance().equals(50) : "积分余额应该为50（30+20）";
        assert userPointVo.getTotalEarned().equals(50) : "累计获得积分应该为50（30+20）";
        assert userPointVo.getTotalConsumed().equals(0) : "累计消费积分应该为0";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("积分奖励成功，用户ID: {}", existingUserId);
        log.info("测试数据：{}", JSON.toJSONString(secondRewardDto, JSONWriter.Feature.WriteMapNullValue));
        log.info("用户积分信息：{}", JSON.toJSONString(userPointVo, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：通用积分奖励方法 - 现有用户");
    }

    /**
     * 测试重复反馈提交奖励（同一天不应该重复奖励）
     */
    @Test
    public void testDuplicateFeedbackSubmitReward() {
        log.info("开始测试：重复反馈提交奖励");

        // 准备测试数据
        String userId = "test_duplicate_user_" + System.currentTimeMillis();
        String feedbackId1 = "feedback_1_" + System.currentTimeMillis();
        String feedbackId2 = "feedback_2_" + System.currentTimeMillis();

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        // 第一次提交反馈
        Boolean firstResult = userPointService.handleFeedbackSubmitReward(userId, feedbackId1);
        assert firstResult != null && firstResult : "第一次反馈提交积分奖励应该成功";

        // 第二次提交反馈（同一天）
        Boolean secondResult = userPointService.handleFeedbackSubmitReward(userId, feedbackId2);
        assert secondResult != null && !secondResult : "第二次反馈提交积分奖励应该失败（同一天不重复奖励）";

        stopWatch.stop();

        // 验证用户积分信息
        UserPointVo userPointVo = userPointService.getUserPointInfo(userId);
        assert userPointVo.getPointBalance() >= 10 : "积分余额应该至少为10（只奖励一次）";
        assert userPointVo.getTodayFeedbackRewarded() : "今日应该已获得反馈奖励";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("第一次奖励结果：{}", firstResult);
        log.info("第二次奖励结果：{}", secondResult);
        log.info("用户积分信息：{}", JSON.toJSONString(userPointVo, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：重复反馈提交奖励");
    }

    /**
     * 测试参数校验 - 积分奖励空参数
     */
    @Test(expected = Exception.class)
    public void testRewardPointsWithNullDto() {
        log.info("开始测试：积分奖励 - 空参数校验");

        // 执行测试 - 传入null参数
        userPointService.rewardPoints(null);

        log.info("测试通过：积分奖励 - 空参数校验");
    }

    /**
     * 测试参数校验 - 积分奖励缺少用户ID
     */
    @Test(expected = Exception.class)
    public void testRewardPointsWithoutUserId() {
        log.info("开始测试：积分奖励 - 缺少用户ID校验");

        // 准备测试数据 - 不设置用户ID
        UserPointRewardDto dto = new UserPointRewardDto();
        dto.setRewardPoints(50);
        dto.setChangeType("feedback");
        dto.setBusinessType("FEEDBACK_SUBMIT");

        // 执行测试
        userPointService.rewardPoints(dto);

        log.info("测试通过：积分奖励 - 缺少用户ID校验");
    }

    /**
     * 测试参数校验 - 积分奖励缺少积分数量
     */
    @Test(expected = Exception.class)
    public void testRewardPointsWithoutRewardPoints() {
        log.info("开始测试：积分奖励 - 缺少积分数量校验");

        // 准备测试数据 - 不设置积分数量
        UserPointRewardDto dto = new UserPointRewardDto();
        dto.setUserId("test_user_123");
        dto.setChangeType("feedback");
        dto.setBusinessType("FEEDBACK_SUBMIT");

        // 执行测试
        userPointService.rewardPoints(dto);

        log.info("测试通过：积分奖励 - 缺少积分数量校验");
    }

    /**
     * 测试参数校验 - 反馈奖励空用户ID
     */
    @Test(expected = Exception.class)
    public void testHandleFeedbackSubmitRewardWithNullUserId() {
        log.info("开始测试：反馈奖励 - 空用户ID校验");

        // 执行测试 - 传入null用户ID
        userPointService.handleFeedbackSubmitReward(null, "feedback_123");

        log.info("测试通过：反馈奖励 - 空用户ID校验");
    }

    /**
     * 测试参数校验 - 反馈奖励空反馈ID
     */
    @Test(expected = Exception.class)
    public void testHandleFeedbackSubmitRewardWithNullFeedbackId() {
        log.info("开始测试：反馈奖励 - 空反馈ID校验");

        // 执行测试 - 传入null反馈ID
        userPointService.handleFeedbackSubmitReward("test_user_123", null);

        log.info("测试通过：反馈奖励 - 空反馈ID校验");
    }

    /**
     * 测试获取积分奖励配置信息
     */
    @Test
    public void testGetPointRewardConfig() {
        log.info("开始测试：获取积分奖励配置信息");

        // 执行测试
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Map<String, Object> config = userPointService.getPointRewardConfig();
        stopWatch.stop();

        // 验证结果
        assert config != null : "配置信息不能为空";
        assert config.containsKey("feedbackSubmitRewardPoints") : "应该包含反馈提交奖励积分配置";
        assert config.containsKey("feedbackAdoptRewardRange") : "应该包含反馈被采纳奖励区间配置";
        assert config.containsKey("lastUpdateTime") : "应该包含最后更新时间";

        // 验证具体配置值
        Integer feedbackRewardPoints = (Integer) config.get("feedbackSubmitRewardPoints");
        assert feedbackRewardPoints != null && feedbackRewardPoints > 0 : "反馈奖励积分应该大于0";

        @SuppressWarnings("unchecked")
        Map<String, Integer> adoptRewardRange = (Map<String, Integer>) config.get("feedbackAdoptRewardRange");
        assert adoptRewardRange != null : "反馈被采纳奖励区间不能为空";
        assert adoptRewardRange.containsKey("minPoints") : "应该包含最小积分";
        assert adoptRewardRange.containsKey("maxPoints") : "应该包含最大积分";

        // 打印结果
        log.info("耗时：{} second", stopWatch.getTotalTimeSeconds());
        log.info("积分奖励配置：{}", JSON.toJSONString(config, JSONWriter.Feature.WriteMapNullValue));

        log.info("测试通过：获取积分奖励配置信息");
    }
}
