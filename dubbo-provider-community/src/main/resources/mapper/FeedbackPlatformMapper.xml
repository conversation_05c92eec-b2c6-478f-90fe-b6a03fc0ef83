<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.somytrip.provider.community.mapper.FeedbackPlatformMapper">

    <!-- 反馈列表查询结果映射 -->
    <resultMap id="FeedbackPlatformListVoMap" type="com.somytrip.entity.vo.community.FeedbackPlatformListVo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="user_secret_id" property="userSecretId"/>
        <result column="user_avatar" property="userAvatar"/>
        <result column="user_nickname" property="userNickname"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="images" property="images"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result column="create_time" property="createTime"/>
        <result column="create_time_show" property="createTimeShow"/>
        <result column="ip_location_show" property="ipLocationShow"/>
        <result column="support_count" property="supportCount"/>
        <result column="comment_count" property="commentCount"/>
        <result column="is_adopted" property="isAdopted"/>
        <result column="category" property="category"/>
        <result column="category_show" property="categoryShow"/>
        <result column="is_supported" property="isSupported"/>
    </resultMap>

    <!-- 查询反馈的预览留言 -->
    <select id="selectPreviewComment" resultType="com.somytrip.entity.vo.community.FeedbackCommentVo">
        SELECT
            fc.id,
            fc.feedback_id as feedbackId,
            fc.user_id as userId,
            '' as userSecretId,
            COALESCE(cu.head_pic, '') as userAvatar,
            COALESCE(cu.nickname, '匿名用户') as userNickname,
            fc.content,
            fc.images,
            fc.create_time as createTime,
            '' as createTimeShow,
            CONCAT(
                    COALESCE(fc.ip_province_name, ''),
                    CASE
                        WHEN fc.ip_province_name IS NOT NULL AND fc.ip_city_name IS NOT NULL
                            THEN '-'
                        ELSE ''
                        END,
                    COALESCE(fc.ip_city_name, '')
            ) as ipLocationShow,
            fc.user_type as userType,
            CASE
                WHEN fc.user_type = 1 THEN '官方'
                WHEN fc.user_type = 2 THEN '作者'
                WHEN fc.user_type = 3 THEN '留言'
                ELSE '留言'
                END as userTypeShow,
            fc.parent_id as parentId,
            fc.reply_to_user_id as replyToUserId,
            reply_user.nickname as replyToUserNickname,
            fc.reply_to_user_type as replyToUserType,
            CASE
                WHEN fc.reply_to_user_type = 1 THEN '官方'
                WHEN fc.reply_to_user_type = 2 THEN '作者'
                WHEN fc.reply_to_user_type = 3 THEN '留言'
                ELSE '留言'
                END as replyToUserTypeShow
        FROM feedback_comment fc
                 LEFT JOIN community_user cu ON fc.user_id = cu.user_id
                 LEFT JOIN community_user reply_user ON fc.reply_to_user_id = reply_user.user_id
        WHERE fc.feedback_id = #{feedbackId}
          AND fc.del_flag = 0
        ORDER BY fc.create_time ASC
            LIMIT 1
    </select>

    <!-- 分页查询 -->
    <select id="selectFeedbackList" resultMap="FeedbackPlatformListVoMap">
        SELECT
        fp.id,
        fp.user_id,
        '' as user_secret_id,
        COALESCE(cu.head_pic, '') as user_avatar,
        COALESCE(cu.nickname, '匿名用户') as user_nickname,
        fp.title,
        fp.content,
        fp.images,
        fp.create_time,
        '' as create_time_show,
        CONCAT(
        COALESCE(fp.ip_province_name, ''),
        CASE
        WHEN fp.ip_province_name IS NOT NULL AND fp.ip_city_name IS NOT NULL
        THEN '-'
        ELSE ''
        END,
        COALESCE(fp.ip_city_name, '')
        ) as ip_location_show,
        fp.support_count,
        COALESCE(comment_stats.comment_count, 0) as comment_count,
        fp.is_adopted,
        fp.category,
        CASE
        WHEN fp.category = 1 THEN '建议'
        WHEN fp.category = 2 THEN 'Bug'
        ELSE '其他'
        END as category_show,
        CASE
        WHEN support_check.user_id IS NOT NULL THEN 1
        ELSE 0
        END as is_supported
        FROM feedback_platform fp
        LEFT JOIN community_user cu ON fp.user_id = cu.user_id
        LEFT JOIN (
        SELECT
        feedback_id,
        COUNT(*) as comment_count
        FROM feedback_comment
        WHERE del_flag = 0
        GROUP BY feedback_id
        ) comment_stats ON fp.id = comment_stats.feedback_id
        LEFT JOIN (
        SELECT DISTINCT business_id, user_id
        FROM likes
        WHERE business_type = 4
        AND flag = 1
        AND del_flag = 0
        AND user_id = #{userId}
        ) support_check ON fp.id = support_check.business_id
        WHERE fp.del_flag = 0
        <choose>
            <when test="sortType == 1">
                <!-- 热门排序：按支持数降序，相同时按创建时间降序 -->
                ORDER BY fp.support_count DESC, fp.create_time DESC
            </when>
            <when test="sortType == 2">
                <!-- 最新排序：按创建时间降序 -->
                ORDER BY fp.create_time DESC
            </when>
            <otherwise>
                <!-- 默认热门排序 -->
                ORDER BY fp.support_count DESC, fp.create_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>