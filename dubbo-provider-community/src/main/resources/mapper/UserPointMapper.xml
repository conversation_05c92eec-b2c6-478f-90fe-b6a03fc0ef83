<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.somytrip.provider.community.mapper.UserPointMapper">

    <!-- 【修改标注】更新用户积分余额 - 改为存在则更新，不存在则创建 -->
    <insert id="updatePointBalance">
        INSERT INTO user_point (
            user_id,
            point_balance,
            total_earned,
            total_consumed,
            point_level,
            create_time,
            update_time
        ) VALUES (
            #{userId},
            #{changeAmount},
            CASE WHEN #{changeAmount} > 0 THEN #{changeAmount} ELSE 0 END,
            CASE WHEN #{changeAmount} < 0 THEN ABS(#{changeAmount}) ELSE 0 END,
            1,
            NOW(),
            NOW()
        ) ON DUPLICATE KEY UPDATE
            point_balance = point_balance + #{changeAmount},
            total_earned = CASE WHEN #{changeAmount} > 0 THEN total_earned + #{changeAmount} ELSE total_earned END,
            total_consumed = CASE WHEN #{changeAmount} < 0 THEN total_consumed + ABS(#{changeAmount}) ELSE total_consumed END,
            update_time = NOW()
    </insert>

    <!-- 更新用户累计获得积分 -->
    <update id="updateTotalEarned">
        UPDATE user_point
        SET total_earned = total_earned + #{earnedAmount},
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>

    <!-- 更新用户累计消费积分 -->
    <update id="updateTotalConsumed">
        UPDATE user_point
        SET total_consumed = total_consumed + #{consumedAmount},
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>

    <!-- 检查用户积分账户是否存在 -->
    <select id="checkUserPointExists" resultType="int">
        SELECT COUNT(1)
        FROM user_point
        WHERE user_id = #{userId}
    </select>

    <!-- 创建用户积分账户 -->
    <insert id="createUserPointAccount">
        INSERT INTO user_point (
            user_id,
            point_balance,
            total_earned,
            total_consumed,
            point_level,
            create_time,
            update_time
        ) VALUES (
            #{userId},
            0,
            0,
            0,
            1,
            NOW(),
            NOW()
        )
    </insert>

</mapper>