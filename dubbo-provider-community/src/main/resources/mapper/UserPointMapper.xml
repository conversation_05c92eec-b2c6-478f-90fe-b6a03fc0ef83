<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.somytrip.provider.community.mapper.UserPointMapper">

    <!-- 更新用户积分余额（原子操作） -->
    <update id="updatePointBalance">
        UPDATE user_point
        SET point_balance = point_balance + #{changeAmount},
            update_time = NOW()
        WHERE user_id = #{userId}
          AND point_balance + #{changeAmount} >= 0
    </update>

    <!-- 更新用户累计获得积分 -->
    <update id="updateTotalEarned">
        UPDATE user_point
        SET total_earned = total_earned + #{earnedAmount},
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>

    <!-- 更新用户累计消费积分 -->
    <update id="updateTotalConsumed">
        UPDATE user_point
        SET total_consumed = total_consumed + #{consumedAmount},
            update_time = NOW()
        WHERE user_id = #{userId}
    </update>

    <!-- 检查用户积分账户是否存在 -->
    <select id="checkUserPointExists" resultType="int">
        SELECT COUNT(1)
        FROM user_point
        WHERE user_id = #{userId}
    </select>

</mapper>