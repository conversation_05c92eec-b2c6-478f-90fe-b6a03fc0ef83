<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.somytrip.provider.community.mapper.FeedbackCommentMapper">

    <!-- 留言列表查询结果映射 -->
    <resultMap id="FeedbackCommentVoMap" type="com.somytrip.entity.vo.community.FeedbackCommentVo">
        <id column="id" property="id"/>
        <result column="feedback_id" property="feedbackId"/>
        <result column="user_id" property="userId"/>
        <result column="user_secret_id" property="userSecretId"/>
        <result column="user_avatar" property="userAvatar"/>
        <result column="user_nickname" property="userNickname"/>
        <result column="content" property="content"/>
        <result column="images" property="images"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler"/>
        <result column="create_time" property="createTime"/>
        <result column="create_time_show" property="createTimeShow"/>
        <result column="ip_location_show" property="ipLocationShow"/>
        <result column="user_type" property="userType"/>
        <result column="user_type_show" property="userTypeShow"/>
        <result column="parent_id" property="parentId"/>
        <result column="reply_to_user_id" property="replyToUserId"/>
        <result column="reply_to_user_nickname" property="replyToUserNickname"/>
        <result column="reply_to_user_type" property="replyToUserType"/>
        <result column="reply_to_user_type_show" property="replyToUserTypeShow"/>
    </resultMap>

    <!-- 分页查询反馈留言列表 -->
    <select id="selectCommentList" resultMap="FeedbackCommentVoMap">
        SELECT fc.id,
               fc.feedback_id,
               fc.user_id,
               ''                                as user_secret_id,
               COALESCE(cu.head_pic, '')         as user_avatar,
               COALESCE(cu.nickname, '匿名用户') as user_nickname,
               fc.content,
               fc.images,
               fc.create_time,
               ''                                as create_time_show,
               CONCAT(
                       COALESCE(fc.ip_province_name, ''),
                       CASE
                           WHEN fc.ip_province_name IS NOT NULL AND fc.ip_city_name IS NOT NULL
                               THEN '-'
                           ELSE ''
                           END,
                       COALESCE(fc.ip_city_name, '')
               )                                 as ip_location_show,
               fc.user_type,
               CASE
                   WHEN fc.user_type = 1 THEN '官方'
                   WHEN fc.user_type = 2 THEN '作者'
                   WHEN fc.user_type = 3 THEN '留言'
                   ELSE '留言'
                   END                           as user_type_show,
               fc.parent_id,
               fc.reply_to_user_id,
               reply_user.nickname               as reply_to_user_nickname,
               fc.reply_to_user_type,
               CASE
                   WHEN fc.reply_to_user_type = 1 THEN '官方'
                   WHEN fc.reply_to_user_type = 2 THEN '作者'
                   WHEN fc.reply_to_user_type = 3 THEN '留言'
                   ELSE '留言'
                   END                           as reply_to_user_type_show
        FROM feedback_comment fc
                 LEFT JOIN community_user cu ON fc.user_id = cu.user_id
                 LEFT JOIN community_user reply_user ON fc.reply_to_user_id = reply_user.user_id
        WHERE fc.feedback_id = #{feedbackId}
          AND fc.del_flag = 0
        ORDER BY fc.create_time ASC
    </select>

</mapper>