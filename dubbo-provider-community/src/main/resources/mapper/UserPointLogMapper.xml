<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.somytrip.provider.community.mapper.UserPointLogMapper">

    <!-- 积分流水查询结果映射 -->
    <resultMap id="UserPointLogVoMap" type="com.somytrip.entity.vo.user.UserPointLogVo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="change_amount" property="changeAmount"/>
        <result column="balance_after" property="balanceAfter"/>
        <result column="change_type" property="changeType"/>
        <result column="change_type_name" property="changeTypeName"/>
        <result column="business_type" property="businessType"/>
        <result column="business_type_name" property="businessTypeName"/>
        <result column="related_id" property="relatedId"/>
        <result column="description" property="description"/>
        <result column="source_platform" property="sourcePlatform"/>
        <result column="source_platform_name" property="sourcePlatformName"/>
        <result column="create_time" property="createTime"/>
        <result column="create_time_show" property="createTimeShow"/>
        <result column="remark" property="remark"/>
        <result column="change_flag" property="changeFlag"/>
    </resultMap>

    <!-- 分页查询用户积分流水 -->
    <select id="selectUserPointLogPage" resultMap="UserPointLogVoMap">
        SELECT
        upl.id,
        upl.user_id,
        upl.change_amount,
        upl.balance_after,
        upl.change_type,
        CASE
        WHEN upl.change_type = 'feedback' THEN '反馈相关'
        ELSE upl.change_type
        END as change_type_name,
        upl.business_type,
        CASE
        WHEN upl.business_type = 'FEEDBACK_SUBMIT' THEN '反馈提交'
        WHEN upl.business_type = 'FEEDBACK_ADOPT' THEN '反馈被采纳'
        WHEN upl.business_type = 'POINT_CONSUME' THEN '积分消费'
        ELSE upl.business_type
        END as business_type_name,
        upl.related_id,
        upl.description,
        upl.source_platform,
        CASE
        WHEN upl.source_platform = 1 THEN '小程序'
        WHEN upl.source_platform = 2 THEN 'APP'
        ELSE '未知'
        END as source_platform_name,
        upl.create_time,
        '' as create_time_show,
        upl.remark,
        CASE
        WHEN upl.change_amount &gt; 0 THEN 1
        WHEN upl.change_amount &lt; 0 THEN -1
        ELSE 0
        END as change_flag
        FROM user_point_log upl
        WHERE upl.user_id = #{userId}
        <if test="changeType != null and changeType != ''">
            AND upl.change_type = #{changeType}
        </if>
        <if test="businessType != null and businessType != ''">
            AND upl.business_type = #{businessType}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE(upl.create_time) &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE(upl.create_time) &lt;= #{endDate}
        </if>
        ORDER BY upl.create_time DESC
    </select>

    <!-- 检查用户今日是否已获得指定业务类型的奖励 -->
    <select id="checkTodayRewardExists" resultType="int">
        SELECT COUNT(1)
        FROM user_point_log
        WHERE user_id = #{userId}
          AND business_type = #{businessType}
          AND change_amount &gt; 0
          AND DATE(create_time) = #{today}
    </select>

    <!-- 查询用户今日积分变动总额 -->
    <select id="getTodayPointChange" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(change_amount), 0)
        FROM user_point_log
        WHERE user_id = #{userId}
          AND DATE(create_time) = #{today}
    </select>

</mapper>