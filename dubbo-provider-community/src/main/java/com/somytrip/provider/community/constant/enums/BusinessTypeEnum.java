package com.somytrip.provider.community.constant.enums;

import lombok.Getter;

/**
 * 业务类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum BusinessTypeEnum {
    /**
     * 游记
     **/
    TRAVEL_NOTE(1, "游记"),
    /**
     * 评论
     **/
    COMMENT(2, "评论");

    private final int code;
    private final String description;

    BusinessTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取对应的业务类型枚举。
     *
     * @param code 业务类型的代码
     * @return 对应的业务类型枚举，如果未找到则返回null
     */
    public static BusinessTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BusinessTypeEnum type : BusinessTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        // 或者抛出异常，取决于你的需求
        return null;
    }

    /**
     * 检查给定的代码是否是有效的业务类型代码。
     *
     * @param code 要检查的代码
     * @return 如果代码有效则返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        return fromCode(code) != null;
    }
}
