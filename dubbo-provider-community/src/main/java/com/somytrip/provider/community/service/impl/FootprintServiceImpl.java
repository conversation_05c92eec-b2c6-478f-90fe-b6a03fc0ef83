package com.somytrip.provider.community.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.TencentCosService;
import com.somytrip.api.service.community.FootprintService;
import com.somytrip.api.service.community.FootprintSettingService;
import com.somytrip.api.service.tourism.GlobalCityService;
import com.somytrip.entity.community.FootprintEntity;
import com.somytrip.entity.dto.city.GlobalCityEntity;
import com.somytrip.entity.dto.community.footprint.FootprintCityDto;
import com.somytrip.entity.dto.community.footprint.FootprintQueryCitiesParam;
import com.somytrip.entity.dto.hotel.PaginationDto;
import com.somytrip.entity.resp.community.FootprintCityListResp;
import com.somytrip.entity.resp.community.FootprintCountryListResp;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.vo.community.FootprintCityDateVo;
import com.somytrip.entity.vo.community.FootprintCityVo;
import com.somytrip.provider.community.mapper.FootprintMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.community.service.impl
 * @className: FootprintServiceImpl
 * @author: shadow
 * @description: 足迹Service实现类
 * @date: 2024/12/31 10:34
 * @version: 1.0
 */
@Slf4j
@Service
@DubboService
public class FootprintServiceImpl extends ServiceImpl<FootprintMapper, FootprintEntity> implements FootprintService {

    @Resource
    private FootprintSettingService footprintSettingService;
    @DubboReference
    private GlobalCityService globalCityService;
    @DubboReference(check = false)
    private TencentCosService tencentCosService;

    /**
     * 记录足迹
     *
     * @param cityCode   城市code
     * @param recordTime 记录时间
     * @param uid        用户ID
     * @return com.somytrip.entity.response.ResponseResult<java.lang.Void>
     * <AUTHOR>
     * @date 2024/12/31 10:41
     */
    @Override
    public ResponseResult<Void> recordFootprint(String cityCode, LocalDateTime recordTime, String uid) {

        log.info("Start Record FootPoint, Uid: {}, CityCode: {}, RecordTime: {}", uid, cityCode, recordTime);

        // 查询城市
        GlobalCityEntity cityEntity = globalCityService.getEntityByCityCode(cityCode);
        if (cityEntity == null) {
            // 城市不存在
            return ResponseResult.fail(201, "api.city.not-found");
        }

        // 判断足迹功能是否开启
        if (!footprintSettingService.isEnable(uid)) {
            return ResponseResult.fail(202, "api.community.footprint.disable");
        }

        // 判断是否已打卡
        LambdaQueryWrapper<FootprintEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(FootprintEntity::getUid, uid)
                .eq(FootprintEntity::getCityId, cityEntity.getId())
                .eq(FootprintEntity::getDel, false);
        if (baseMapper.exists(qw)) {
            // 当前城市已打卡
            log.warn("The city has been Recorded");
            return ResponseResult.fail(201, "api.community.footprint.recorded");
        }

        // 封装实体类
        FootprintEntity footprintEntity = new FootprintEntity();
        footprintEntity.setUid(uid);
        footprintEntity.setCityId(cityEntity.getId());
        if (recordTime == null) {
            recordTime = LocalDateTime.now();
        }
        footprintEntity.setCreateTime(recordTime);
        footprintEntity.setUpdateTime(recordTime);
        // 插入数据
        baseMapper.insert(footprintEntity);

        return ResponseResult.ok(null);
    }

    /**
     * 查询国家记录
     *
     * @param uid        用户ID
     * @param pagination 分页参数
     * @return com.somytrip.entity.response.ResponseResult<com.somytrip.entity.resp.community.FootprintCountryListResp>
     * <AUTHOR>
     * @date 2024/12/31 16:44
     */
    @Override
    public ResponseResult<FootprintCountryListResp> queryCounties(String uid, PaginationDto pagination) {

        // 分页查询国家记录
        Page<FootprintCountryListResp.FootprintCountryVo> page
                = new Page<>(pagination.getPageNum(), pagination.getPageSize());
        page = baseMapper.queryCountries(page, uid);
        List<FootprintCountryListResp.FootprintCountryVo> records = page.getRecords();

        // 查询历时天数
        Integer durationDays = getDurationDays(uid);

        // 封装结果
        FootprintCountryListResp result = new FootprintCountryListResp();
        result.setTotal((int) page.getTotal());
        result.setDurationDays(durationDays);
        result.setCountries(records);
        result.setLast(page.getCurrent() == page.getPages());

        return ResponseResult.ok(result);
    }

    /**
     * 查询城市记录
     *
     * @param param 参数
     * @return com.somytrip.entity.response.ResponseResult<com.somytrip.entity.resp.community.FootprintCityListResp>
     * <AUTHOR>
     * @date 2025/1/2 11:29
     */
    @Override
    public ResponseResult<FootprintCityListResp> queryCities(FootprintQueryCitiesParam param) {

        log.info("param: {}", JSON.toJSONString(param));

        // 分页查询城市记录
        List<FootprintCityDto> records = baseMapper.queryCities(param);
        if (CollUtil.isEmpty(records)) {
            return ResponseResult.ok(null);
        }

        // 判断是否有下一页
        boolean hasNextPage = records.size() > param.getPageSize();
        if (hasNextPage) {
            records.remove(records.size() - 1);
        }

        // 全部数据按照日期分组
        Map<LocalDate, List<FootprintCityDto>> groupByDate = records.stream()
                .collect(Collectors.groupingBy(FootprintCityDto::getRecordTime));

        // 获取书签时间
        LocalDateTime bookmarkTime = LocalDateTimeUtil.parse(param.getBookmarkTime(), "yyyy-MM-dd HH:mm:ss");
        // 提取书签列表
        List<FootprintCityVo> bookmarkList = Optional.ofNullable(bookmarkTime)
                .map(dateTime -> groupByDate.remove(bookmarkTime.toLocalDate()))
                .orElse(Collections.emptyList())
                .stream().map(this::convertCityDto2Vo)
                .toList();

        // 转换Map并排序
        List<FootprintCityDateVo> dates = groupByDate.entrySet().stream()
                .map(entry -> new FootprintCityDateVo(
                        LocalDateTimeUtil.format(entry.getKey(), "yyyy年MM月"),
                        // dto列表转换为vo列表
                        convertCityDtoList2VoList(entry.getValue())
                ))
                .sorted(Comparator.comparing(FootprintCityDateVo::getDate).reversed())
                .toList();

        // 查询总数和历时天数
        Long total = getCityTotal(param);
        Integer durationDays = getDurationDays(param.getUid());

        // 封装结果
        FootprintCityListResp result = new FootprintCityListResp();
        result.setBookmarkList(bookmarkList);
        result.setDates(dates);
        // 获取最后一个城市 标记书签
        FootprintCityDto lastCity = CollUtil.getLast(records);
        if (lastCity != null) {
            result.setBookmarkTime(LocalDateTimeUtil.format(lastCity.getBookmarkTime(), "yyyy-MM-dd HH:mm:ss"));
            result.setBookmark(lastCity.getRecordId());
        }
        result.setTotal(Math.toIntExact(total));
        result.setDurationDays(durationDays);
        result.setLast(!hasNextPage);

        return ResponseResult.ok(result);
    }

    /**
     * 清空足迹记录
     *
     * @param uid 用户ID
     * @return com.somytrip.entity.response.ResponseResult<java.lang.Boolean>
     * <AUTHOR>
     * @date 2025/1/3 17:51
     */
    @Override
    public ResponseResult<Boolean> clear(String uid) {

        log.info("Clear Footprint, Uid: {}", uid);

        LambdaUpdateWrapper<FootprintEntity> uw = new LambdaUpdateWrapper<>();
        uw.eq(FootprintEntity::getUid, uid).set(FootprintEntity::getDel, true);

        try {
            baseMapper.update(uw);
        } catch (Exception e) {
            log.error("Clear Footprint Error");
            log.error(e.getMessage(), e);
            return ResponseResult.ok(false);
        }

        return ResponseResult.ok(true);
    }

    /**
     * 获取历时天数
     *
     * @param uid 用户ID
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2025/1/3 9:39
     */
    private Integer getDurationDays(String uid) {
        if (StrUtil.isBlank(uid)) {
            log.warn("Get DurationDays, Uid is Blank");
            return null;
        }
        return baseMapper.queryDurationDays(uid);
    }

    /**
     * 获取城市总数
     *
     * @param param 查询参数
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2025/1/3 9:43
     */
    private Long getCityTotal(FootprintQueryCitiesParam param) {
        return baseMapper.queryCityTotal(param);
    }

    /**
     * 将城市记录dto列表转换为城市记录vo列表
     *
     * @param cityDtoList 城市记录dto列表
     * @return java.util.List<com.somytrip.entity.vo.community.FootprintCityVo>
     * <AUTHOR>
     * @date 2025/1/3 15:13
     */
    private List<FootprintCityVo> convertCityDtoList2VoList(List<FootprintCityDto> cityDtoList) {

        if (CollUtil.isEmpty(cityDtoList)) {
            return new ArrayList<>();
        }
        List<FootprintCityVo> voList = new ArrayList<>();
        for (FootprintCityDto dto : cityDtoList) {
            voList.add(convertCityDto2Vo(dto));
        }
        return voList;
    }

    /**
     * 将城市记录dto转换为城市记录vo
     * 同时获取图片url
     *
     * @param dto 城市记录dto
     * @return com.somytrip.entity.vo.community.FootprintCityVo
     * <AUTHOR>
     * @date 2025/1/3 16:58
     */
    private FootprintCityVo convertCityDto2Vo(FootprintCityDto dto) {

        FootprintCityVo vo = new FootprintCityVo(dto);
        String url = tencentCosService.getUrl("scenic", dto.getLogoPic());
        vo.setCover(url);
        return vo;
    }
}
