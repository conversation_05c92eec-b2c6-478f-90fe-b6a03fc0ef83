package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.somytrip.entity.community.UserPointLog;
import com.somytrip.entity.vo.user.UserPointLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.provider.community.mapper
 * @className: UserPointLogMapper
 * @author: lijunqi
 * @description:
 * @date: 2025/8/5 15:27
 * @version: 1.0
 */
@Mapper
public interface UserPointLogMapper extends BaseMapper<UserPointLog> {

    /**
     * 分页查询用户积分流水
     *
     * @param page         分页参数
     * @param userId       用户ID
     * @param changeType   变动类型
     * @param businessType 业务类型
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @return 积分流水分页结果
     */
    Page<UserPointLogVo> selectUserPointLogPage(Page<UserPointLogVo> page,
                                                @Param("userId") String userId,
                                                @Param("changeType") String changeType,
                                                @Param("businessType") String businessType,
                                                @Param("startDate") String startDate,
                                                @Param("endDate") String endDate
    );

    /**
     * 检查用户今日是否已获得指定业务类型的奖励
     *
     * @param userId       用户ID
     * @param businessType 业务类型
     * @param today        今日日期（yyyy-MM-dd格式）
     * @return 记录数量
     */
    int checkTodayRewardExists(@Param("userId") String userId,
                               @Param("businessType") String businessType,
                               @Param("today") String today);

    /**
     * 查询用户今日积分变动总额
     *
     * @param userId 用户ID
     * @param today  今日日期（yyyy-MM-dd格式）
     * @return 今日积分变动总额
     */
    Integer getTodayPointChange(@Param("userId") String userId, @Param("today") String today);
}
