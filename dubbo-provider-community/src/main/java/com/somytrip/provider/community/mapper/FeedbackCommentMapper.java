package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.somytrip.entity.community.FeedbackComment;
import com.somytrip.entity.vo.community.FeedbackCommentVo;
import jakarta.validation.constraints.NotNull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.provider.community.mapper
 * @className: FeedbackCommentMapper
 * @author: lijun<PERSON>
 * @description:
 * @date: 2025/8/4 16:41
 * @version: 1.0
 */
@Mapper
public interface FeedbackCommentMapper extends BaseMapper<FeedbackComment> {

    Page<FeedbackCommentVo> selectCommentList(@Param("page") Page<FeedbackCommentVo> page,
                                              @NotNull(message = "反馈ID不能为空") Long feedbackId,
                                              Long userId);

}
