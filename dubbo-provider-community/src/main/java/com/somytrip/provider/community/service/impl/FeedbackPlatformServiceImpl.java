package com.somytrip.provider.community.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.community.FeedbackPlatformService;
import com.somytrip.entity.FileInfo;
import com.somytrip.entity.community.FeedbackComment;
import com.somytrip.entity.community.FeedbackPlatform;
import com.somytrip.entity.dto.community.feedback.FeedbackCommentDto;
import com.somytrip.entity.dto.community.feedback.FeedbackCommentListReq;
import com.somytrip.entity.dto.community.feedback.FeedbackPlatformDto;
import com.somytrip.entity.dto.community.feedback.FeedbackPlatformListReq;
import com.somytrip.entity.vo.community.FeedbackCommentListRes;
import com.somytrip.entity.vo.community.FeedbackCommentVo;
import com.somytrip.entity.vo.community.FeedbackPlatformListRes;
import com.somytrip.entity.vo.community.FeedbackPlatformListVo;
import com.somytrip.exception.BusinessException;
import com.somytrip.provider.community.mapper.FeedbackCommentMapper;
import com.somytrip.provider.community.mapper.FeedbackPlatformMapper;
import com.somytrip.provider.community.service.common.TmsClientService;
import com.somytrip.provider.community.service.common.UserCommonService;
import com.somytrip.provider.community.util.TimeDifferenceFormatterUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.provider.community.service.impl
 * @className: FeedbackPlatformService
 * @author: lijunqi
 * @description:
 * @date: 2025/8/4 15:02
 * @version: 1.0
 */
@Slf4j
@DubboService
public class FeedbackPlatformServiceImpl extends ServiceImpl<FeedbackPlatformMapper, FeedbackPlatform>
        implements FeedbackPlatformService {

    /**
     * 时间格式化器
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private FeedbackPlatformMapper feedbackPlatformMapper;

    @Resource
    private FeedbackCommentMapper feedbackCommentMapper;

    @Resource
    private TmsClientService tmsClientService;

    @Resource
    private UserCommonService userCommonService;


    /**
     * 提交反馈建议
     *
     * @param dto 提交参数
     * @return 反馈信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitFeedback(FeedbackPlatformDto dto) {
        log.info("用户提交反馈建议，参数: {}", JSON.toJSONString(dto));

        // 参数校验
        validateSubmitParams(dto);

        // 创建反馈实体
        FeedbackPlatform feedback = buildFeedbackEntity(dto);

        // 保存到数据库
        boolean saveResult = this.save(feedback);
        if (!saveResult) {
            throw new BusinessException("提交反馈失败，请稍后重试");
        }

        log.info("用户 {} 提交反馈成功，反馈ID: {}", dto.getUserId(), feedback.getId());
        return feedback.getId();
    }

    /**
     * 发布反馈评论
     *
     * @param dto 评论参数
     * @return 评论ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long publishComment(FeedbackCommentDto dto) {
        log.info("用户发布反馈评论，参数: {}", JSON.toJSONString(dto));

        // 参数校验
        validateCommentParams(dto);

        // 验证反馈是否存在
        FeedbackPlatform feedback = feedbackPlatformMapper.selectById(dto.getFeedbackId());
        if (feedback == null || feedback.getDelFlag() == 1) {
            throw new BusinessException("反馈不存在或已删除");
        }

        // 内容安全检测
        if (!tmsClientService.validateContentSecurity(dto.getContent())) {
            throw new BusinessException("输入内容包含非法字符，请重新输入");
        }

        // 构建评论实体
        FeedbackComment comment = buildCommentEntity(dto, feedback);

        // 保存到数据库
        int insertResult = feedbackCommentMapper.insert(comment);
        if (insertResult != 1) {
            log.error("保存反馈评论失败，用户ID: {}", dto.getUserId());
            throw new BusinessException("发布评论失败，请稍后重试");
        }

        log.info("用户 {} 发布反馈评论成功，评论ID: {}", dto.getUserId(), comment.getId());
        return comment.getId();
    }

    /**
     * 查询反馈列表
     *
     * @param req 查询请求参数
     * @return 反馈列表响应结果
     */
    @Override
    public FeedbackPlatformListRes getFeedbackList(FeedbackPlatformListReq req) {
        log.info("查询反馈列表，参数: {}", JSON.toJSONString(req));

        try {
            // 创建分页对象
            Page<FeedbackPlatformListVo> page = new Page<>(req.getPageNum(), req.getPageSize());

            // 执行分页查询
            Page<FeedbackPlatformListVo> resultPage = feedbackPlatformMapper.selectFeedbackList(
                    page, req.getSortType(), req.getUserId());

            // 处理查询结果
            List<FeedbackPlatformListVo> records = resultPage.getRecords();
            LocalDateTime currentTime = LocalDateTime.now();

            if (CollectionUtil.isNotEmpty(records)) {
                records.forEach(vo -> {
                    // 处理用户加密ID
                    if (vo.getUserId() != null) {
                        vo.setUserSecretId(userCommonService.userIdEncrypt(vo.getUserId()));
                    }

                    // 处理用户头像URL
                    String userAvatar = vo.getUserAvatar();
                    if (StrUtil.isNotBlank(userAvatar)) {
                        String[] fileNames = userAvatar.split(",");
                        if (fileNames.length >= 2) {
                            vo.setUserAvatar(userCommonService.getImageUrl(fileNames[0], fileNames[1]));
                        }
                    }

                    // 处理时间显示
                    vo.setCreateTimeShow(formatDateTime(vo.getCreateTime()));

                    // 处理图片URL
                    List<FileInfo> images = vo.getImages();
                    if (CollectionUtil.isNotEmpty(images)) {
                        images.forEach(image -> {
                            if (image != null && StrUtil.isNotBlank(image.getName()) && StrUtil.isNotBlank(image.getBucket())) {
                                String imageUrl = userCommonService.getImageUrl(image.getBucket(), image.getName());
                                image.setUrl(imageUrl);
                            }
                        });
                    }

                    // 查询并处理预览留言
                    FeedbackCommentVo previewComment = feedbackPlatformMapper.selectPreviewComment(vo.getId(), req.getUserId());
                    if (previewComment != null) {
                        processCommentVo(previewComment, currentTime);
                        vo.setPreviewComment(previewComment);
                    }
                });
            }

            // 构建响应结果
            FeedbackPlatformListRes response = new FeedbackPlatformListRes();
            response.setFeedbackList(records);
            response.setTotal(resultPage.getTotal());
            response.setTotalPages(resultPage.getPages());
            response.setCurrentPage(req.getPageNum());
            response.setPageSize(req.getPageSize());
            response.setHasNext(resultPage.hasNext());
            response.setHasPrevious(resultPage.hasPrevious());

            log.info("查询反馈列表成功，总记录数: {}, 当前页: {}", resultPage.getTotal(), req.getPageNum());
            return response;

        } catch (Exception e) {
            log.error("查询反馈列表失败，参数: {}", JSON.toJSONString(req), e);
            throw new BusinessException("查询反馈列表失败，请稍后重试");
        }
    }


    /**
     * 查询反馈留言列表
     *
     * @param req 查询请求参数
     * @return 留言列表响应结果
     */
    @Override
    public FeedbackCommentListRes getFeedbackCommentList(FeedbackCommentListReq req) {
        log.info("查询反馈留言列表，参数: {}", JSON.toJSONString(req));

        try {
            // 验证反馈是否存在
            FeedbackPlatform feedback = this.getById(req.getFeedbackId());
            if (feedback == null || feedback.getDelFlag() == 1) {
                throw new BusinessException("反馈不存在或已删除");
            }

            // 创建分页对象
            Page<FeedbackCommentVo> page = new Page<>(req.getPageNum(), req.getPageSize());

            // 执行分页查询
            Page<FeedbackCommentVo> resultPage = feedbackCommentMapper.selectCommentList(
                    page, req.getFeedbackId(), req.getUserId());

            // 处理查询结果
            List<FeedbackCommentVo> records = resultPage.getRecords();
            LocalDateTime currentTime = LocalDateTime.now();

            if (CollectionUtil.isNotEmpty(records)) {
                records.forEach(vo -> processCommentVo(vo, currentTime));
            }

            // 构建响应结果
            FeedbackCommentListRes response = new FeedbackCommentListRes();
            response.setCommentList(records);
            response.setTotal(resultPage.getTotal());
            response.setTotalPages(resultPage.getPages());
            response.setCurrentPage(req.getPageNum());
            response.setPageSize(req.getPageSize());
            response.setHasNext(resultPage.hasNext());
            response.setHasPrevious(resultPage.hasPrevious());

            log.info("查询反馈留言列表成功，反馈ID: {}, 总记录数: {}, 当前页: {}",
                    req.getFeedbackId(), resultPage.getTotal(), req.getPageNum());
            return response;

        } catch (Exception e) {
            log.error("查询反馈留言列表失败，参数: {}", JSON.toJSONString(req), e);
            throw new BusinessException("查询反馈留言列表失败，请稍后重试");
        }
    }

    /**
     * 处理留言视图对象
     *
     * @param vo          留言视图对象
     * @param currentTime 当前时间
     */
    private void processCommentVo(FeedbackCommentVo vo, LocalDateTime currentTime) {
        // 处理用户加密ID
        if (vo.getUserId() != null) {
            vo.setUserSecretId(userCommonService.userIdEncrypt(vo.getUserId()));
        }

        // 处理用户头像URL
        String userAvatar = vo.getUserAvatar();
        if (StrUtil.isNotBlank(userAvatar)) {
            String[] fileNames = userAvatar.split(",");
            if (fileNames.length >= 2) {
                vo.setUserAvatar(userCommonService.getImageUrl(fileNames[0], fileNames[1]));
            }
        }

        // 处理时间显示
        vo.setCreateTimeShow(formatDateTime(vo.getCreateTime()));

        // 处理图片URL
        List<FileInfo> images = vo.getImages();
        if (CollectionUtil.isNotEmpty(images)) {
            images.forEach(image -> {
                if (image != null && StrUtil.isNotBlank(image.getName()) && StrUtil.isNotBlank(image.getBucket())) {
                    String imageUrl = userCommonService.getImageUrl(image.getBucket(), image.getName());
                    image.setUrl(imageUrl);
                }
            });
        }
    }


    /**
     * 反馈建议参数校验
     *
     * @param dto 提交反馈建议参数
     */
    private void validateSubmitParams(FeedbackPlatformDto dto) {
        if (dto == null) {
            throw new BusinessException("反馈建议参数不能为空");
        }

        if (dto.getUserId() == null) {
            throw new BusinessException("用户未登录");
        }

        if (StrUtil.isBlank(dto.getContent())) {
            throw new BusinessException("反馈内容不能为空");
        }

        String title = dto.getTitle();
        if (StrUtil.isNotBlank(title)) {
            // 合并多个连续空格为单个空格，并去除首尾空格
            String processedTitle = title.replaceAll("\\s+", " ").trim();
            dto.setTitle(processedTitle);
            log.info("标题格式处理完成，原标题: [{}], 处理后: [{}]", title, processedTitle);
        }
    }


    /**
     * 构建反馈实体
     *
     * @param dto 提交参数
     * @return 反馈实体
     */
    private FeedbackPlatform buildFeedbackEntity(FeedbackPlatformDto dto) {
        FeedbackPlatform feedback = new FeedbackPlatform();

        // 用户信息
        feedback.setUserId(dto.getUserId());
        feedback.setTitle(dto.getTitle());
        feedback.setContent(dto.getContent());
        feedback.setImages(dto.getImages());
        feedback.setPhone(dto.getPhone());

        // IP地理位置信息
        feedback.setIpAddress(dto.getIpAddress());
        feedback.setIpCityName(dto.getIpCityName());
        feedback.setIpProvinceName(dto.getIpProvinceName());

        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        feedback.setCreateTime(now);
        feedback.setUpdateTime(now);

        // 业务字段
        feedback.setSupportCount(0);
        // 默认待处理
        feedback.setStatus(1);
        feedback.setCategory(dto.getCategory() != null ? dto.getCategory() : 1);
        feedback.setSource(dto.getSource() != null ? dto.getSource() : 1);
        // 正常状态
        feedback.setDelFlag(0);
        // 未采纳
        feedback.setIsAdopted(0);

        return feedback;
    }


    /**
     * 评论参数校验
     *
     * @param dto 评论参数
     */
    private void validateCommentParams(FeedbackCommentDto dto) {
        if (dto == null) {
            throw new BusinessException("评论参数不能为空");
        }

        if (dto.getFeedbackId() == null) {
            throw new BusinessException("反馈ID不能为空");
        }

        if (dto.getUserId() == null) {
            throw new BusinessException("用户未登录");
        }

        if (StrUtil.isBlank(dto.getContent())) {
            throw new BusinessException("评论内容不能为空");
        }

        String content = dto.getContent();
        if (StrUtil.isNotBlank(content)) {
            // 合并多个连续空格为单个空格，并去除首尾空格
            String processedContent = content.replaceAll("\\s+", " ").trim();

            // 检查处理后的内容是否为空
            if (StrUtil.isBlank(processedContent)) {
                throw new BusinessException("评论内容不能仅包含空格");
            }

            dto.setContent(processedContent);
            log.debug("评论内容格式处理完成，原内容: [{}], 处理后: [{}]", content, processedContent);
        }
    }

    /**
     * 构建评论实体
     *
     * @param dto      评论参数
     * @param feedback 反馈实体
     * @return 评论实体
     */
    private FeedbackComment buildCommentEntity(FeedbackCommentDto dto, FeedbackPlatform feedback) {
        FeedbackComment comment = new FeedbackComment();

        // 基本信息
        comment.setFeedbackId(dto.getFeedbackId());
        comment.setUserId(dto.getUserId());
        comment.setContent(dto.getContent());
        comment.setImages(dto.getImages());

        // IP地理位置信息
        comment.setIpAddress(dto.getIpAddress());
        comment.setIpCityName(dto.getIpCityName());
        comment.setIpProvinceName(dto.getIpProvinceName());

        // 时间信息
        LocalDateTime now = LocalDateTime.now();
        comment.setCreateTime(now);
        comment.setUpdateTime(now);

        // 回复信息
        comment.setParentId(dto.getParentId());
        comment.setReplyToUserId(dto.getReplyToUserId());
        comment.setReplyToUserType(dto.getReplyToUserType());

        // 用户类型判断
        if (dto.getUserId().equals(feedback.getUserId())) {
            // 作者
            comment.setUserType(2);
        } else {
            // 普通用户
            comment.setUserType(3);
        }
        // 默认值
        comment.setDelFlag(0);
        return comment;
    }

    /**
     * 格式化时间为字符串
     *
     * @param dateTime 时间对象
     * @return 格式化后的时间字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_TIME_FORMATTER) : null;
    }
}
