package com.somytrip.provider.community.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.community.UserPointService;
import com.somytrip.entity.community.UserPoint;
import com.somytrip.entity.community.UserPointLog;
import com.somytrip.entity.dto.community.user.UserPointRewardDto;
import com.somytrip.entity.enums.user.PointBusinessTypeEnum;
import com.somytrip.entity.vo.user.UserPointVo;
import com.somytrip.exception.BusinessException;
import com.somytrip.provider.community.mapper.UserPointLogMapper;
import com.somytrip.provider.community.mapper.UserPointMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.provider.community.service.impl
 * @className: UserPointServiceImpl
 * @author: lijunqi
 * @description:
 * @date: 2025/8/5 15:15
 * @version: 1.0
 */
@Slf4j
@Service
@DubboService
public class UserPointServiceImpl extends ServiceImpl<UserPointMapper, UserPoint> implements UserPointService {

    /**
     * 反馈奖励积分配置（可后续改为配置中心获取）
     */
    private static final Integer FEEDBACK_REWARD_POINTS = 10;

    /**
     * 反馈被采纳奖励积分最小值配置（可后续改为配置中心获取）
     */
    private static final Integer FEEDBACK_ADOPT_REWARD_MIN_POINTS = 100;

    /**
     * 反馈被采纳奖励积分最大值配置（可后续改为配置中心获取）
     */
    private static final Integer FEEDBACK_ADOPT_REWARD_MAX_POINTS = 500;

    @Resource
    private UserPointMapper userPointMapper;
    @Resource
    private UserPointLogMapper userPointLogMapper;

    /**
     * 处理反馈提交积分奖励
     * 当日首次发表反馈获得积分奖励
     *
     * @param userId     用户ID
     * @param feedbackId 反馈ID
     * @return 是否获得奖励
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleFeedbackSubmitReward(String userId, String feedbackId) {
        log.info("处理反馈提交积分奖励，用户ID: {}, 反馈ID: {}", userId, feedbackId);

        try {
            // 检查用户今日是否已获得反馈奖励
            if (checkTodayFeedbackRewarded(userId)) {
                log.info("用户今日已获得反馈奖励，用户ID: {}", userId);
                return false;
            }

            // 构建奖励参数
            UserPointRewardDto rewardDto = new UserPointRewardDto();
            rewardDto.setUserId(userId);
            rewardDto.setRewardPoints(FEEDBACK_REWARD_POINTS);
            rewardDto.setChangeType("feedback");
            rewardDto.setBusinessType(PointBusinessTypeEnum.FEEDBACK_SUBMIT.getCode());
            rewardDto.setRelatedId(feedbackId);
            rewardDto.setDescription("当日首次发表反馈奖励");
            rewardDto.setSourcePlatform(1);

            // 执行奖励
            Boolean result = rewardPoints(rewardDto);

            log.info("反馈提交积分奖励处理完成，用户ID: {}, 反馈ID: {}, 结果: {}", userId, feedbackId, result);
            return result;

        } catch (Exception e) {
            log.error("处理反馈提交积分奖励失败，用户ID: {}, 反馈ID: {}", userId, feedbackId, e);
            throw new BusinessException("积分奖励处理失败");
        }
    }

    /**
     * 查询用户积分信息
     *
     * @param userId 用户ID
     * @return 用户积分信息
     */
    @Override
    public UserPointVo getUserPointInfo(String userId) {
        log.info("查询用户积分信息，用户ID: {}", userId);

        try {
            // 确保用户积分账户存在
            if (!ensureUserPointAccount(userId)) {
                throw new BusinessException("用户积分账户初始化失败");
            }

            // 查询用户积分信息
            UserPoint userPoint = userPointMapper.selectById(userId);
            if (userPoint == null) {
                throw new BusinessException("用户积分信息不存在");
            }

            // 转换为VO
            UserPointVo vo = new UserPointVo();
            BeanUtils.copyProperties(userPoint, vo);

            // 检查今日是否已获得反馈奖励
            vo.setTodayFeedbackRewarded(checkTodayFeedbackRewarded(userId));

            log.info("查询用户积分信息成功，用户ID: {}, 积分余额: {}", userId, vo.getPointBalance());
            return vo;

        } catch (Exception e) {
            log.error("查询用户积分信息失败，用户ID: {}", userId, e);
            throw new BusinessException("查询用户积分信息失败");
        }
    }

    /**
     * 检查用户今日是否已获得反馈奖励
     *
     * @param userId 用户ID
     * @return 是否已获得奖励
     */
    @Override
    public Boolean checkTodayFeedbackRewarded(String userId) {
        try {
            String today = LocalDate.now().toString();
            int count = userPointLogMapper.checkTodayRewardExists(
                    userId, PointBusinessTypeEnum.FEEDBACK_SUBMIT.getCode(), today);
            return count > 0;
        } catch (Exception e) {
            log.error("检查用户今日反馈奖励状态失败，用户ID: {}", userId, e);
            return false;
        }
    }

    /**
     * 通用积分奖励方法
     *
     * @param rewardDto 奖励参数
     * @return 是否奖励成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean rewardPoints(UserPointRewardDto rewardDto) {
        log.info("执行积分奖励，参数: {}", JSON.toJSONString(rewardDto));

        try {
            String userId = rewardDto.getUserId();
            Integer rewardPoints = rewardDto.getRewardPoints();

            // 确保用户积分账户存在
            if (!ensureUserPointAccount(userId)) {
                throw new BusinessException("用户积分账户初始化失败");
            }

            // 更新用户积分余额
            int updateResult = userPointMapper.updatePointBalance(userId, rewardPoints);
            if (updateResult <= 0) {
                throw new BusinessException("积分余额更新失败");
            }

            // 更新累计获得积分
            userPointMapper.updateTotalEarned(userId, rewardPoints);

            // 查询更新后的积分余额
            UserPoint userPoint = userPointMapper.selectById(userId);
            if (userPoint == null) {
                throw new BusinessException("用户积分信息查询失败");
            }

            // 记录积分变动流水
            UserPointLog pointLog = new UserPointLog();
            pointLog.setUserId(userId);
            pointLog.setChangeAmount(rewardPoints);
            pointLog.setBalanceAfter(userPoint.getPointBalance());
            pointLog.setChangeType(rewardDto.getChangeType());
            pointLog.setBusinessType(rewardDto.getBusinessType());
            pointLog.setRelatedId(rewardDto.getRelatedId());
            pointLog.setDescription(rewardDto.getDescription());
            pointLog.setSourcePlatform(rewardDto.getSourcePlatform());
            pointLog.setRemark(rewardDto.getRemark());

            boolean logResult = userPointLogMapper.insert(pointLog) > 0;
            if (!logResult) {
                throw new BusinessException("积分流水记录失败");
            }

            log.info("积分奖励执行成功，用户ID: {}, 奖励积分: {}, 余额: {}",
                    userId, rewardPoints, userPoint.getPointBalance());
            return true;

        } catch (Exception e) {
            log.error("执行积分奖励失败，参数: {}", JSON.toJSONString(rewardDto), e);
            throw new BusinessException("积分奖励执行失败: " + e.getMessage());
        }
    }


    /**
     * 获取积分奖励配置信息
     *
     * @return 积分奖励配置
     */
    @Override
    public Map<String, Object> getPointRewardConfig() {
        Map<String, Object> config = new HashMap<>();

        // 反馈提交奖励配置
        config.put("feedbackSubmitRewardPoints", FEEDBACK_REWARD_POINTS);

        // 反馈被采纳奖励区间配置
        Map<String, Integer> adoptRewardRange = new HashMap<>();
        adoptRewardRange.put("minPoints", FEEDBACK_ADOPT_REWARD_MIN_POINTS);
        adoptRewardRange.put("maxPoints", FEEDBACK_ADOPT_REWARD_MAX_POINTS);
        config.put("feedbackAdoptRewardRange", adoptRewardRange);

        // 配置说明
        config.put("lastUpdateTime", LocalDateTime.now().toString());

        return config;
    }

    /**
     * 确保用户积分账户存在
     *
     * @param userId 用户ID
     * @return 是否存在或创建成功
     */
    private Boolean ensureUserPointAccount(String userId) {
        // 【修改标注】检查用户积分账户是否存在
        if (userPointMapper.checkUserPointExists(userId) > 0) {
            return true;
        }

        // 【修改标注】如果不存在，则创建新的积分账户
        try {
            int createResult = userPointMapper.createUserPointAccount(userId);
            if (createResult > 0) {
                log.info("为用户创建积分账户成功，用户ID: {}", userId);
                return true;
            } else {
                log.error("为用户创建积分账户失败，用户ID: {}", userId);
                return false;
            }
        } catch (Exception e) {
            log.error("创建用户积分账户异常，用户ID: {}", userId, e);
            return false;
        }
    }
}
