package com.somytrip.provider.community.util;

import cn.hutool.core.date.DateUtil;

import java.util.Date;

/**
 * 通过出生年月获取所属星座
 */
public class ConstellationUtil {

    // 定义每个星座的边界日子（从摩羯座开始）
    private static final int[] constellationEdgeDay = {20, 19, 21, 20, 21, 22, 23, 23, 23, 24, 23, 22};
    private static final String[] constellationName = {
            "摩羯座", "水瓶座", "双鱼座", "白羊座", "金牛座", "双子座",
            "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座"
    };

    /**
     * 根据生日获取星座.
     *
     * @param birthday 出生日期，格式为 yyyy-MM-dd 的字符串.
     * @return 返回对应的星座名称.
     */
    public static String getConstellation(String birthday) {
        Date date = DateUtil.parse(birthday);
        return getConstellation(date);
    }

    /**
     * 根据日期对象获取星座.
     *
     * @param date 出生日期的 Date 对象.
     * @return 返回对应的星座名称.
     */
    private static String getConstellation(Date date) {
        int month = DateUtil.month(date) + 1; // 获取月份并转换为从1开始计数
        int day = DateUtil.dayOfMonth(date);  // 获取月份中的日子

        // 计算星座
        if (day >= constellationEdgeDay[month - 1]) {
            return constellationName[month - 1];
        } else {
            return constellationName[(month - 2 + 12) % 12]; // 处理1月的情况
        }
    }
}
