package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.community.UserPoint;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.provider.community.mapper
 * @className: UserPointMapper
 * @author: lijunqi
 * @description:
 * @date: 2025/8/5 15:26
 * @version: 1.0
 */
@Mapper
public interface UserPointMapper extends BaseMapper<UserPoint> {

    /**
     * 更新用户积分余额（原子操作）
     *
     * @param userId       用户ID
     * @param changeAmount 变动积分（正数增加，负数减少）
     * @return 影响行数
     */
    int updatePointBalance(@Param("userId") String userId, @Param("changeAmount") Integer changeAmount);

    /**
     * 更新用户累计获得积分
     *
     * @param userId      用户ID
     * @param earnedAmount 获得积分数量
     * @return 影响行数
     */
    int updateTotalEarned(@Param("userId") String userId, @Param("earnedAmount") Integer earnedAmount);

    /**
     * 更新用户累计消费积分
     *
     * @param userId         用户ID
     * @param consumedAmount 消费积分数量
     * @return 影响行数
     */
    int updateTotalConsumed(@Param("userId") String userId, @Param("consumedAmount") Integer consumedAmount);

    /**
     * 检查用户积分账户是否存在
     *
     * @param userId 用户ID
     * @return 存在数量
     */
    int checkUserPointExists(@Param("userId") String userId);

    /**
     * 创建用户积分账户
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int createUserPointAccount(@Param("userId") String userId);
}