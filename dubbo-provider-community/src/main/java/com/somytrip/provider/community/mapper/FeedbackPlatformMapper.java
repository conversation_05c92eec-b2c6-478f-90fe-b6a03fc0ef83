package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.somytrip.entity.community.FeedbackPlatform;
import com.somytrip.entity.vo.community.FeedbackCommentVo;
import com.somytrip.entity.vo.community.FeedbackPlatformListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.provider.community.mapper
 * @className: FeedbackPlatformMapper
 * @author: lijunqi
 * @description:
 * @date: 2025/8/4 15:03
 * @version: 1.0
 */
@Mapper
public interface FeedbackPlatformMapper extends BaseMapper<FeedbackPlatform> {

    /**
     * 分页查询反馈列表
     *
     * @param page     分页参数
     * @param sortType 排序类型：1-热门排序，2-最新排序
     * @param userId   当前用户ID（用于判断是否已支持）
     * @return 反馈列表
     */
    Page<FeedbackPlatformListVo> selectFeedbackList(Page<FeedbackPlatformListVo> page,
                                                    @Param("sortType") Integer sortType,
                                                    @Param("userId") Long userId);


    FeedbackCommentVo selectPreviewComment(Long id, Long userId);

}
