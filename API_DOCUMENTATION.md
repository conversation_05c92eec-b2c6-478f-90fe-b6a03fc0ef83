# 反馈平台接口文档

## 1. 提建议接口

### 1.1 接口信息
- **接口路径**: `POST /api/feedback/submit`
- **接口描述**: 用户提交反馈建议
- **需要登录**: 是

### 1.2 请求参数

#### 请求头
```
Content-Type: application/json
Authorization: Bearer {token}
```

#### 请求体
```json
{
  "title": "建议标题（选填，最多30字符）",
  "content": "建议内容（必填，最多300字符）",
  "images": ["图片URL1", "图片URL2"],
  "phone": "联系方式（选填，86手机号格式）",
  "category": 1,
  "source": 1
}
```

#### 参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| title | String | 否 | 建议标题，最多30字符，支持空格自动合并 |
| content | String | 是 | 建议内容，最多300字符 |
| images | Array | 否 | 图片URL列表，最多9张，每张图片不超过2MB |
| phone | String | 否 | 联系方式，86手机号格式 |
| category | Integer | 否 | 分类：1-建议、2-bug，默认1 |
| source | Integer | 否 | 来源：1-小程序、2-app，默认1 |

### 1.3 响应结果

#### 成功响应
```json
{
  "code": 200,
  "success": true,
  "message": "反馈提交成功",
  "data": {
    "feedbackId": 1234567890123
  }
}
```

#### 失败响应
```json
{
  "code": 400,
  "success": false,
  "message": "建议内容不能为空",
  "data": null
}
```

### 1.4 错误码说明
| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误 |
| 401 | 用户未登录 |
| 500 | 系统异常 |

## 2. 点赞反馈接口

### 2.1 接口信息
- **接口路径**: `POST /api/feedback/support/{feedbackId}`
- **接口描述**: 对反馈进行点赞支持
- **需要登录**: 否（可选）

### 2.2 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| feedbackId | Long | 是 | 反馈ID |

### 2.3 响应结果

#### 成功响应
```json
{
  "code": 200,
  "success": true,
  "message": "点赞成功",
  "data": null
}
```

#### 失败响应
```json
{
  "code": 400,
  "success": false,
  "message": "点赞失败，反馈不存在或已删除",
  "data": null
}
```

## 3. 业务规则

### 3.1 标题处理规则
- 输入框中仅允许存在一个空格
- 用户输入连续多个空格时，自动合并为单个空格
- 当输入框内已存在一个空格时，阻止新空格输入
- 去除首尾空格

### 3.2 图片上传规则
- 最多支持9张图片
- 每张图片大小不超过2MB
- 支持常见图片格式（JPG、PNG、GIF等）

### 3.3 手机号验证规则
- 支持86区号的手机号格式
- 正则表达式：`^1[3-9]\\d{9}$`

### 3.4 IP地址记录
- 自动获取用户真实IP地址
- 支持代理服务器环境下的IP获取
- 自动解析IP地址获取省份和城市信息
- 支持内网IP和本地IP的识别
- 记录详细的地理位置信息用于统计分析

## 4. 数据库设计

### 4.1 反馈主表 (feedback_platform)
```sql
CREATE TABLE `feedback_platform` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，id自增',
    `user_id` VARCHAR(32) NOT NULL COMMENT '发布者ID',
    `title` VARCHAR(90) COMMENT '标题，最多30字符',
    `content` VARCHAR(500) NOT NULL COMMENT '内容，最多300字符',
    `images` JSON COMMENT '图片URL列表，最多9张',
    `phone` VARCHAR(20) COMMENT '联系方式，86手机号格式',
    `ipaddress` VARCHAR(45) NOT NULL COMMENT '发布者IP地址',
    `IpProvinceName` VARCHAR(50) COMMENT 'IP归属省份名称',
    `IpCityName` VARCHAR(50) COMMENT 'IP归属城市名称',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `support_count` INT NOT NULL DEFAULT 0 COMMENT '支持/点赞数',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-待处理 2-处理中 3-已解决 4-已关闭',
    `category` TINYINT NOT NULL DEFAULT 1 COMMENT '分类：1-建议、2-bug',
    `source` TINYINT NOT NULL DEFAULT 1 COMMENT '来源：1-小程序、2-app',
    `del_flag` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记：0-正常 1-已删除',
    `is_adopted` TINYINT NOT NULL DEFAULT 0 COMMENT '是否已采纳：0-未采纳 1-已采纳',

    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_create_time` (`create_time`),
    INDEX `idx_del_flag` (`del_flag`),
    INDEX `idx_support_count` (`support_count`),
    INDEX `idx_province_city` (`IpProvinceName`, `IpCityName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户反馈主表(`你说我改`)';
```

### 4.2 反馈评论表 (feedback_comment)
```sql
CREATE TABLE `feedback_comment` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键，id自增',
    `feedback_id` BIGINT NOT NULL COMMENT '所属反馈ID(`feedback_platform`.id)',
    `user_id` VARCHAR(32) NOT NULL COMMENT '发布者ID',
    `user_type` TINYINT NOT NULL COMMENT '用户类型：1-官方；2-作者；3-用户',
    `content` VARCHAR(500) NOT NULL COMMENT '内容，最多300字符',
    `images` JSON COMMENT '图片URL列表',
    `ipaddress` VARCHAR(45) NOT NULL COMMENT '发布者IP地址',
    `IpProvinceName` VARCHAR(50) COMMENT 'IP归属省份名称',
    `IpCityName` VARCHAR(50) COMMENT 'IP归属城市名称',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `parent_id` BIGINT COMMENT '父级评论ID',
    `reply_to_user_id` VARCHAR(32) COMMENT '被回复用户ID',
    `reply_to_user_type` TINYINT COMMENT '被回复用户类型：1-官方；2-作者；3-用户',
    `del_flag` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记：0-正常 1-已删除',

    INDEX `idx_feedback_id` (`feedback_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_parent_id` (`parent_id`),
    INDEX `idx_del_flag` (`del_flag`),
    INDEX `idx_province_city` (`IpProvinceName`, `IpCityName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='反馈评论表';
```

## 5. 技术实现

### 5.1 技术栈
- **后端框架**: Spring Boot + Dubbo
- **数据库**: MySQL 8.0
- **ORM框架**: MyBatis-Plus
- **参数校验**: Jakarta Validation
- **JSON处理**: Fastjson2

### 5.2 核心类说明
- `FeedbackPlatform`: 反馈主表实体类
- `FeedbackComment`: 反馈评论表实体类
- `SubmitFeedbackDto`: 提交反馈请求DTO
- `SubmitFeedbackVo`: 提交反馈响应VO
- `FeedbackPlatformService`: 反馈平台服务接口
- `FeedbackController`: 反馈控制器

### 5.3 枚举类
- `FeedbackStatusEnum`: 反馈状态枚举
- `FeedbackCategoryEnum`: 反馈分类枚举
- `FeedbackSourceEnum`: 反馈来源枚举
- `FeedbackUserTypeEnum`: 用户类型枚举
