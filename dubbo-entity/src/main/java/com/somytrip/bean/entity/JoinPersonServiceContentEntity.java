package com.somytrip.bean.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName: JoinPersonServiceContent
 * @Description: 个人加盟B端服务内容 实体类
 * @Author: shadow
 * @Date: 2023/11/3 17:32
 */
@Data
@TableName("join_person_service_contents")
public class JoinPersonServiceContentEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 服务类型列表(可使用的服务类型)
     */
    private String serviceTypes;

    /**
     * 内容名称
     */
    private String contentName;

    /**
     * 内容属性
     */
    private String contentProperties;

    /**
     * 单位
     */
    private Integer unit;

    /**
     * 是否必选
     */
    private boolean isRequired;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public boolean isRequired() {
        return isRequired;
    }

    public void setRequired(boolean required) {
        isRequired = required;
    }
}
