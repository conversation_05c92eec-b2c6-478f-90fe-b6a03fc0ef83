package com.somytrip.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class AccountBookRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    private String uid;
    private Integer accountBookId;
    private String type;
    private String payout;
    private Date createTime;

    public AccountBookRecord(Integer id, String uid, Integer accountBookId, String type, String payout, Date createTime) {
        this.id = id;
        this.uid = uid;
        this.accountBookId = accountBookId;
        this.type = type;
        this.payout = payout;
        this.createTime = createTime;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public void setAccountBookId(Integer accountBookId) {
        this.accountBookId = accountBookId;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setPayout(String payout) {
        this.payout = payout;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
