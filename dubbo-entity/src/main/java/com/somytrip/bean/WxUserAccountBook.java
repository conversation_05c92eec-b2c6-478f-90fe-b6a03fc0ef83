package com.somytrip.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
//@TableName("wx_user_account_book")
//public class WxUserAccountBook {
public class WxUserAccountBook {

    @TableId(value = "id", type = IdType.AUTO)
    private String id;
    private String uid;
    private String name;
    private String budget;
    @TableField(exist = false)
    private List<AccountBookRecord> records;
    private Date createTime;
    private Date updateTime;

    public WxUserAccountBook() {
    }

    public WxUserAccountBook(String uid, String name, String budget, Date createTime, Date updateTime) {
        this.uid = uid;
        this.name = name;
        this.budget = budget;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    public WxUserAccountBook(String id, String uid, String name, String budget, Date createTime, Date updateTime) {
        this.id = id;
        this.uid = uid;
        this.name = name;
        this.budget = budget;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }
}
