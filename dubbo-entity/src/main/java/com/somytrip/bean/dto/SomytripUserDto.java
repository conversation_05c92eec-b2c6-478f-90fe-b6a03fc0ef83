package com.somytrip.bean.dto;

import com.somytrip.entity.dto.user.SomytripUsers;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * @ClassName: SomytripUserDto
 * @Description:
 * @Author: shadow
 * @Date: 2023/9/20 16:17
 */
@Data
@Valid
public class SomytripUserDto {
    private Long id;
    private String uniqueId;
    private SomytripUsers.UserRole role = SomytripUsers.UserRole.guest;
    private String authImage;
    @NotBlank(message = "昵称不能为空")
    @Size(min = 1, max = 20)
    private String nickname;
    private String lang;
    private int gender;
    private String country;
    private String province;
    private String city;
    private int age;
    private int constellation;
    private String constellationName;
    private String personalizedSignature;
    private String personalityLabel;
    private String displayFiled;
    private String isVisible;
}
