package com.somytrip.bean.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 高亮显示dto
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class HighlightDTO {

    /**
     * 需要高亮显示文本文案，如：深圳世界之窗
     */
    private String showTxt;

    private String showTxtEn;

    /**
     * 跳转类型，1-跳转景点，2-跳转攻略
     */
    private Integer forwardType;

    /**
     * 业务id，如：景点id等
     */
    private String businessId;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 游玩时间
     */
    private BigDecimal stayTime;
}
