package com.somytrip.bean.vo;

import com.alibaba.fastjson2.JSONArray;
import com.somytrip.bean.entity.JoinPersonServiceOrderEntity;
import com.somytrip.bean.enums.JoinServiceOrderStatusEnums;
import com.somytrip.utils.SqlAesUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: JoinPersonServiceOrderDetailVo
 * @Description: 个人加盟服务 订单详情vo
 * @Author: shadow
 * @Date: 2023/11/17 11:26
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class JoinPersonServiceOrderDetailVo {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单序列号
     */
    private String orderSn;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 服务序列号
     */
    private String serviceSn;

    /**
     * 服务内容
     */
    private JSONArray serviceContent;

    /**
     * 服务图片
     */
    private JSONArray serviceImages;

    /**
     * 总时长值
     */
    private Integer totalDurationValue;

    /**
     * 总时长
     */
    private String totalDuration;

    /**
     * 总金额值
     */
    private BigDecimal totalAmountValue;

    /**
     * 总金额
     */
    private String totalAmount;

    /**
     * 订单总金额 单位: 分
     */
    private Integer payAmount;

    /**
     * 服务区域与时间
     */
    private List<JoinPersonServiceOrderEntity.ServiceAreaAndTime> serviceAreaAndTime;

    /**
     * 订单状态(1: 完成, 2: 待付款, 3: 待接单, 4: 待服务, 5: 待完成, 6: 待评价, 7: 已取消, 8: 已删除)
     */
    private Integer orderStatus;

    /**
     * 订单状态名称
     */
    private String orderStatusName;

    /**
     * 订单下单时间
     */
    private Date createTime;

    /**
     * 订单完成时间
     */
    private Date completionTime;

    /**
     * 订单支付时间
     */
    private Date paymentTime;

    /**
     * 订单接单时间
     */
    private Date receivingTime;

    /**
     * 开始服务时间
     */
    private Date startServiceTime;

    /**
     * 订单评论时间
     */
    private Date evaluationTime;

    /**
     * 订单取消时间
     */
    private Date cancelTime;

    /**
     * 消费者用户ID
     */
    private Long consumerUid;

    /**
     * 消费者用户呢称
     */
    private String consumerNickname;

    /**
     * 消费者手机号
     */
//    @Sensitive(strategy = SensitiveStrategy.PHONE_MASK)
    private String consumerMobile;

    /**
     * 消费者年龄
     */
    private Integer consumerAge;

    /**
     * 消费者性别
     */
    private Integer consumerGender;

    /**
     * 消费者头像
     */
    private String consumerAvatar;

    /**
     * entity -> detailDto
     *
     * @param orderEntity 订单实体类
     */
    public JoinPersonServiceOrderDetailVo(JoinPersonServiceOrderEntity orderEntity) {

        this.orderStatus = orderEntity.getOrderStatus();
        this.orderStatusName = JoinServiceOrderStatusEnums.get(orderEntity.getOrderStatus());
        this.orderSn = orderEntity.getOrderSn();
        this.serviceName = orderEntity.getServiceName();
        this.serviceSn = orderEntity.getServiceSn();
        this.serviceAreaAndTime = orderEntity.getServiceAreaAndTime();
        this.serviceContent = JSONArray.parseArray(orderEntity.getServiceContent());
        this.serviceImages = JSONArray.parseArray(orderEntity.getServiceImages());
        this.totalDuration = orderEntity.getTotalDuration();
        this.totalDurationValue = orderEntity.getTotalDurationValue();
        this.totalAmount = orderEntity.getTotalAmount();
        this.totalAmountValue = orderEntity.getTotalAmountValue();
        this.payAmount = orderEntity.getPayAmount();
        this.createTime = orderEntity.getCreateTime();
        this.consumerUid = orderEntity.getConsumerUid();
        this.consumerNickname = orderEntity.getConsumerNickname();
        this.consumerMobile = SqlAesUtil.decrypt(orderEntity.getConsumerMobile());
        this.consumerGender = orderEntity.getConsumerGender();
        this.consumerAge = orderEntity.getConsumerAge();
        this.consumerAvatar = orderEntity.getConsumerAvatar();
        this.createTime = orderEntity.getCreateTime();
        this.completionTime = orderEntity.getCompletionTime();
        this.paymentTime = orderEntity.getPaymentTime();
        this.receivingTime = orderEntity.getReceivingTime();
        this.startServiceTime = orderEntity.getStartServiceTime();
        this.evaluationTime = orderEntity.getEvaluationTime();
        this.cancelTime = orderEntity.getCancelTime();
    }
}
