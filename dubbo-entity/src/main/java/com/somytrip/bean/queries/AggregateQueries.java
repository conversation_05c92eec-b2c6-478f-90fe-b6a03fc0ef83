package com.somytrip.bean.queries;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.dto.hotel.PaginationDto;
import lombok.Data;

/**
 * @ClassName: AggregateQueries
 * @Description: 封装查询累
 * @Author: shadow
 * @Date: 2023/10/17 0:43
 */
@Data
public class AggregateQueries<T, R> {

    // 直接性查询条件对象
    private T equalsQueries;
    // 分页信息对象, 为空时默认第1页, 页容量: 10
    private PaginationDto pagination;
    // 模糊查询条件对象
    private R fuzzyQueries;
    // 排序字段，为空时不排序
    private String sortField;
    // 排序方式, 为空时默认升序    0: 升序, 1: 降序
    private Integer sortType;

    /**
     * @return boolean
     * @description 是否存在equals查询条件对象
     * <AUTHOR>
     * @date 2023/10/18 23:19
     */
    public boolean hasEqualsQueries() {
        return equalsQueries != null;
    }

    /**
     * @return boolean true: 存在, false: 不存在
     * @description 是否存在分页信息对象
     * <AUTHOR>
     * @date 2023/10/18 23:24
     */
    public boolean hasPagination() {
        return pagination != null;
    }

    /**
     * @return boolean true: 存在, false: 不存在
     * @description 是否存在模糊查询条件对象
     * <AUTHOR>
     * @date 2023/10/18 23:25
     */
    public boolean hasFuzzyQueries() {
        return fuzzyQueries != null;
    }

    /**
     * @return boolean true: 存在, false: 不存在
     * @description 是否存在排序字段
     * <AUTHOR>
     * @date 2023/10/18 23:25
     */
    public boolean hasSortField() {
        return StrUtil.isNotBlank(sortField);
    }

    /**
     * @return boolean true: 存在, false: 不存在
     * @description 是否存在排序方式
     * <AUTHOR>
     * @date 2023/10/18 23:25
     */
    public boolean hasSortType() {
        return sortType != null;
    }
}
