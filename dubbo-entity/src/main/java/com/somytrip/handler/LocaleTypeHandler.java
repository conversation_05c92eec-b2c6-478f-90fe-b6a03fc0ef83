package com.somytrip.handler;

import org.apache.commons.lang3.LocaleUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Locale;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.handler
 * @className: LocaleTypeHandler
 * @author: shadow
 * @description:
 * @date: 2024/9/19 11:36
 * @version: 1.0
 */
@MappedTypes(Locale.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class LocaleTypeHandler extends BaseTypeHandler<Locale> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Locale parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, parameter.toString());
    }

    @Override
    public Locale getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String languageTag = rs.getString(columnName);
        return languageTag != null ? LocaleUtils.toLocale(languageTag) : null;
    }

    @Override
    public Locale getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String languageTag = rs.getString(columnIndex);
        return languageTag != null ? LocaleUtils.toLocale(languageTag) : null;
    }

    @Override
    public Locale getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String languageTag = cs.getString(columnIndex);
        return languageTag != null ? LocaleUtils.toLocale(languageTag) : null;
    }
}
