package com.somytrip.entity.map.google.q;

import com.somytrip.entity.map.google.c.Circle;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-12-19 10:30
 */
@EqualsAndHashCode(callSuper = false)
@Data
@Valid
@NoArgsConstructor
@AllArgsConstructor
public class AutoCompleteReq extends Request {
    @NotBlank
    private String input;
    private Circle circle;
}
