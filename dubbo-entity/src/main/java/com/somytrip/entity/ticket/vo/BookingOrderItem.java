package com.somytrip.entity.ticket.vo;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 多个子单
 * @author: pigeon
 * @created: 2024-11-15 14:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Valid
public class BookingOrderItem {
    @NotNull
    private Integer goodId;
    @NotNull
    private List<Integer> tripUserInfoIds;
    @NotNull
    private LocalDate tripDate;
    /**
     * 场次票下单必须传入
     */
    private String orderInfo;
}
