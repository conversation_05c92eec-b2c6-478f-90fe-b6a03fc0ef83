package com.somytrip.entity.ticket.tc.order;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderFeedBackBody implements TcHead {
    /**
     * 同程订单流水号
     */
    @JSONField(name = "OrderSerialId")
    private String orderSerialId;
    /**
     * 反馈类型
     */
    @JSONField(name = "FeedBackType")
    private String feedBackType;
    /**
     * 退款申诉原因Id
     */
    @JSONField(name = "FeedBackId")
    private String feedBackId;
    /**
     * 游客手机号
     */
    @JSONField(name = "MobileNumber")
    private String mobileNumber;
    /**
     * 反馈标题
     */
    @JSONField(name = "FeedBackTitle")
    private String feedBackTitle;
    /**
     * 反馈内容
     */
    @JSONField(name = "FeedBackContent")
    private String feedBackContent;
    /**
     * 反馈备注
     */
    @JSONField(name = "FeedBackRemark")
    private String feedBackRemark;

    @Override
    public String md5Args() {
        return StrUtil.join("", orderSerialId);
    }
}
