package com.somytrip.entity.ticket.tc.order;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 10:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryFeedBackItem {
    /**
     * 订单流水号
     */
    @JSONField(name = "OrderSerialId")
    private String orderSerialId;
    /**
     * 反馈类型
     */
    @JSONField(name = "FeedBackType")
    private String feedBackType;
    /**
     * 处理状态
     * 0: 未处理,1: 处理中,2: 处理完成,3: 已取消,4: 已驳回
     */
    @JSONField(name = "DealStatus")
    private String dealStatus;
    /**
     * 客户姓名
     */
    @JSONField(name = "CustomerName")
    private String customerName;
    /**
     * 反馈标题
     */
    @JSONField(name = "FeedBackTitle")
    private String feedBackTitle;
    /**
     * 客户移动电话号码
     */
    @JSONField(name = "MobileNumber")
    private String mobileNumber;
    /**
     * 创建时间
     */
    @JSONField(name = "CreateTime")
    private String createTime;
    /**
     * 备注
     */
    @JSONField(name = "FeedBackRemark")
    private String feedBackRemark;
    /**
     * 处理完成时间
     */
    @JSONField(name = "DealedTime")
    private String completeTime;
    /**
     * 反馈内容
     */
    @JSONField(name = "FeedBackContent")
    private String feedBackContent;
    /**
     * 处理结果
     */
    @JSONField(name = "DealResultCode")
    private String dealResultCode;
    /**
     * 旅游日期
     */
    @JSONField(name = "TravelDate")
    private String travelDate;
    /**
     * 反馈操作日志
     */
    @JSONField(name = "OperationList")
    private List<QueryFeedBackLog> operationList;
}
