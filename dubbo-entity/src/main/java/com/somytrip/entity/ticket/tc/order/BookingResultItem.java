package com.somytrip.entity.ticket.tc.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 9:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BookingResultItem {
    private String serialId;
    /**
     * 验证第三方流水号已存在时
     * somytrip订单号
     */
    private String thirdSerialId;
    /**
     * 验证第三方流水号已存在时
     * 同程订单号
     */
    private String tcSerialId;
}
