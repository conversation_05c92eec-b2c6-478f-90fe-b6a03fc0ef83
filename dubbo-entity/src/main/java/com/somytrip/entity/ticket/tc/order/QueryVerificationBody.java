package com.somytrip.entity.ticket.tc.order;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 订单核销查询拉取
 * @author: pigeon
 * @created: 2024-11-12 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryVerificationBody implements TcHead {
    /**
     * 起始增量Id
     */
    private Integer maxIncrementId;
    /**
     * 查询数量
     */
    private Integer queryNumber;

    @Override
    public String md5Args() {
        return StrUtil.join("", maxIncrementId, queryNumber);
    }
}
