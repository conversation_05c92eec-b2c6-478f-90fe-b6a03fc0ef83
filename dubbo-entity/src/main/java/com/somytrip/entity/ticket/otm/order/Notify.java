package com.somytrip.entity.ticket.otm.order;

import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.otm.voucher.VoucherDetail;
import lombok.Data;

import java.util.List;

/**
 * @Description: 通知
 * @author: pigeon
 * @created: 2024-08-01 14:47
 */
public class Notify {
    /**
     * 创建订单通知Body
     */
    @Data
    public static class CreateBody {
        /**
         * 订单号
         */
        @JSONField(name = "order_no")
        private String orderNo;
        /**
         * OTA 订单号
         */
        @JSONField(name = "out_order_no")
        private String outOrderNo;
        /**
         * 订单状态( 待确认 PRE_ORDER，已下单 ORDERED，全部退款
         * REFUND，部分退款 PART_REFUND )
         */
        @JSONField(name = "order_status")
        private String orderStatus;
        /**
         * 下单时间（格式 yyyy-MM-dd HH:mm:ss ）
         */
        @JSONField(name = "order_time")
        private String orderTime;
        /**
         * 凭证信息
         */
        @JSONField(name = "vouchers")
        private List<VoucherDetail.Rsp> vouchers;
    }

    /**
     * 退订通知Body
     */
    @Data
    public static class RefundBody {
        /**
         * 订单号
         */
        @JSONField(name = "order_no")
        private String orderNo;
        /**
         * OTA 订单号
         */
        @JSONField(name = "out_order_no")
        private String outOrderNo;
        /**
         * 商品对应凭证 ID
         */
        @JSONField(name = "voucher_id")
        private String voucherId;
        /**
         * 商品凭证码
         */
        @JSONField(name = "voucher_no")
        private String voucherNo;
        /**
         * 申请退订份数
         */
        @JSONField(name = "apply_copies")
        private String applyCopies;
        /**
         * 申请退订单号
         */
        @JSONField(name = "apply_no")
        private String applyNo;
        /**
         * 退订处理单号
         */
        @JSONField(name = "refund_id")
        private String refundId;
        /**
         * 退订来源(微信 WEICHAT,网站 WEB,手机站点 WAP,淘宝
         * TAOBAO,移动端 MOBILE_PLATFORM,集团平台
         * GROUP_PLATFORM,景区平台 SCENIC_PLATFORM,分销商平台
         * CHANNEL_PLATFORM,线下票窗 SCENIC,景区码平台CODE_PLATFORM,OTA)
         */
        @JSONField(name = "refund_source")
        private String refundSource;
        /**
         * 退款金额
         */
        @JSONField(name = "refund_amount")
        private String refundAmount;
        /**
         * 退款手续费
         */
        @JSONField(name = "refund_fee")
        private String refundFee;
        /**
         * 审核结果(审核通过 APPROVE, 审核拒绝 REJECT)
         */
        @JSONField(name = "result")
        private String result;
        /**
         * 退订原因
         */
        @JSONField(name = "remarks")
        private String remarks;
        /**
         * 退订审核意见
         */
        @JSONField(name = "audit_suggestion")
        private String auditSuggestion;
        /**
         * 退订审核时间
         */
        @JSONField(name = "audit_time")
        private String auditTime;
        /**
         * 退订申请时间
         */
        @JSONField(name = "apply_time")
        private String applyTime;
        /**
         * 退订处理完成时间
         */
        @JSONField(name = "refund_time")
        private String refundTime;
    }

    /**
     * 核销通知body
     */
    @Data
    public static class VoucherBody {
        /**
         * 核销流水号（用于标识每一笔核销记录）
         */
        @JSONField(name = "consume_record_no")
        private String consumeRecordNo;
        /**
         * 订单号
         */
        @JSONField(name = "order_no")
        private String orderNo;
        /**
         * OTA 订单号
         */
        @JSONField(name = "out_order_no")
        private String outOrderNo;
        /**
         * 商品对应凭证 ID
         */
        @JSONField(name = "voucher_id")
        private String voucherId;
        /**
         * 商品凭证码
         */
        @JSONField(name = "voucher_no")
        private String voucherNo;
        /**
         * 商品类型码
         */
        @JSONField(name = "goods_type_code")
        private String goodsTypeCode;
        /**
         * 商品累计核销份数
         */
        @JSONField(name = "consumed_copies")
        private Integer consumedCopies;
        /**
         * 商品核销份数
         */
        @JSONField(name = "consume_copies")
        private Integer consumeCopies;
        /**
         * 核销景区编号
         */
        @JSONField(name = "consume_scenic_no")
        private String consumeScenicNo;
        /**
         * 核销景区名称
         */
        @JSONField(name = "consume_scenic_name")
        private String consumeScenicName;
        /**
         * 商品核销时间
         */
        @JSONField(name = "consume_time")
        private String consumeTime;
        /**
         * 若 goods_type_code 为套餐商品
         * PACKAGE_GOODS 时,有此子商品核销信息
         */
        @JSONField(name = "sub_voucher_consume")
        private SubVoucherConsume subVoucherConsume;
    }

    @Data
    public static class SubVoucherConsume {
        @JSONField(name = "voucher_id")
        private String voucherId;
        @JSONField(name = "voucher_no")
        private String voucherNo;
        @JSONField(name = "goods_type_code")
        private String goodsTypeCode;
        @JSONField(name = "consumed_copies")
        private Integer consumedCopies;
        @JSONField(name = "consume_copies")
        private Integer consumeCopies;
    }
}
