package com.somytrip.entity.ticket.otm.voucher;

import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.otm.order.CreateOrder;
import com.somytrip.entity.ticket.otm.scenic.ProductTicketDetail;
import lombok.Data;

import java.util.List;

/**
 * @Description: 凭证详情
 * @author: pigeon
 * @created: 2024-07-19 16:29
 */
public class VoucherDetail {
    @Data
    public static class Req {
        @JSONField(name = "voucher_id")
        private String voucherId;

        public Req(String voucherId) {
            this.voucherId = voucherId;
        }
    }

    @Data
    public static class Rsp {
        @JSONField(name = "voucher_id")
        private String voucherId;
        @JSONField(name = "voucher_no")
        private String voucherNo;
        @JSONField(name = "order_no")
        private String orderNo;
        @JSONField(name = "out_order_no")
        private String outOrderNo;
        @JSONField(name = "out_order_item_id")
        private String outOrderItemId;
        @JSONField(name = "goods_no")
        private String goodsNo;
        @JSONField(name = "goods_name")
        private String goodsName;
        @JSONField(name = "goods_shortname")
        private String goodsShortname;
        @JSONField(name = "goods_type_code")
        private String goodsTypeCode;
        @JSONField(name = "goods_type_name")
        private String goodsTypeName;
        @JSONField(name = "sale_scenic_no")
        private String saleScenicNo;
        @JSONField(name = "sale_scenic_name")
        private String saleScenicName;
        @JSONField(name = "package_voucher_id")
        private String packageVoucherId;
        @JSONField(name = "package_goods_no")
        private String packageGoodsNo;
        @JSONField(name = "all_copies")
        private Integer allCopies;
        @JSONField(name = "used_copies")
        private Integer usedCopies;
        @JSONField(name = "invalid_copies")
        private Integer invalidCopies;
        @JSONField(name = "freeze_copies")
        private Integer freezeCopies;
        @JSONField(name = "voucher_image_url")
        private String voucherImageUrl;
        @JSONField(name = "valid_start_time")
        private String validStartTime;
        @JSONField(name = "valid_end_date")
        private String validEndDate;
        @JSONField(name = "day_start_in_time")
        private Long dayStartInTime;
        @JSONField(name = "day_end_in_time")
        private Long dayEndInTime;
        @JSONField(name = "appoint_trip_date")
        private String appointTripDate;
        private String status;
        @JSONField(name = "allow_change")
        private String allowChange;
        @JSONField(name = "allow_refund")
        private String allowRefund;
        @JSONField(name = "order_time")
        private String orderTime;
        private CreateOrder.Buyer buyer;
        @JSONField(name = "travel_user")
        private List<CreateOrder.TravelUser> travelUser;
        @JSONField(name = "made_voucher_mode")
        private String madeVoucherMode;
        @JSONField(name = "sub_vouchers")
        private List<String> subVouchers;
        @JSONField(name = "gift_items")
        private List<ProductTicketDetail.GiftItem> giftItems;
    }

    @Data
    public static class SubVoucher {
        @JSONField(name = "voucher_id")
        private String voucherId;
        @JSONField(name = "voucher_no")
        private String voucherNo;
        @JSONField(name = "goods_type_code")
        private String goodsTypeCode;
        @JSONField(name = "all_copies")
        private Integer allCopies;
        @JSONField(name = "used_copies")
        private Integer usedCopies;
        @JSONField(name = "invalid_copies")
        private Integer invalidCopies;
        @JSONField(name = "freeze_copies")
        private Integer freezeCopies;
        @JSONField(name = "voucher_image_url")
        private String voucherImageUrl;
    }
}
