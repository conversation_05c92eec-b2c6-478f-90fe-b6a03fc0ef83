package com.somytrip.entity.ticket.tc.order;

import com.somytrip.entity.ticket.tc.TcResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 订单核销查询拉取
 * @author: pigeon
 * @created: 2024-11-12 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class QueryVerificationResult extends TcResponse {
    /**
     * 最大增量Id
     */
    private Integer maxIncrementId;
    /**
     * 订单列表
     */
    private List<QueryOrderItem> orderList;
}
