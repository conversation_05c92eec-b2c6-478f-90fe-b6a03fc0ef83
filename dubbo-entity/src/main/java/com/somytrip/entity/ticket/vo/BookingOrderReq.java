package com.somytrip.entity.ticket.vo;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 创建订单请求参数
 * @author: pigeon
 * @created: 2024-11-15 14:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Valid
public class BookingOrderReq {
    /**
     * 用户ID
     */
    @NotBlank
    private String uid;
    /**
     * 下单实参
     */
    @NotNull
    private List<BookingOrderItem> orders;

    /**
     * 会话ID，用于跟踪用户会话
     */
    private String sessionId;
}
