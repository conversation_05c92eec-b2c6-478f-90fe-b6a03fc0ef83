package com.somytrip.entity.ticket.otm.order;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * @Description: 订单退订
 * @author: pigeon
 * @created: 2024-07-19 15:54
 */
public class RefundOrder {
    @Data
    public static class Req {
        /**
         * 退订申请单号
         */
        @JSONField(name = "apply_no")
        private String applyNo;
        /**
         * OTA 订单号
         */
        @JSONField(name = "out_order_no")
        private String outOrderNo;
        /**
         * 销售景区编码
         */
        @JSONField(name = "sale_scenic_no")
        private String saleScenicNo;
        /**
         * 申请退订的媒介类型（OTM_VOUCHER_ID:OTM 凭证
         * 的 ID, OTM_VOUCHER_NO:OTM 凭证码）
         */
        @JSONField(name = "refund_media_type")
        private String refundMediaType;
        /**
         * 对应退订媒介类型值
         */
        @JSONField(name = "refund_media_value")
        private String refundMediaValue;
        /**
         * 申请退订数量
         */
        @JSONField(name = "refund_quantity")
        private Integer refundQuantity;
        /**
         * 退订原因
         */
        private String remarks;
    }
}
