package com.somytrip.entity.ticket.tc.increment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 17:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SceneryInfo {
    /**
     * 景区ID
     */
    private Integer sceneryId;
    /**
     * 返回对应的增量类型
     * 1：表示上线增量
     * 2：表示已下线增量
     * 3：表示信息修改的增量
     */
    private Integer incrementType;
}
