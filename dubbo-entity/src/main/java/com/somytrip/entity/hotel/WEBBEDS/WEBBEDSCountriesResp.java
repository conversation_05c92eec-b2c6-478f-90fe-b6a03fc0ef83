package com.somytrip.entity.hotel.WEBBEDS;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: WEBBEDSCountriesResp
 * @Description: WEBBEDS国家列表响应
 * @Author: shadow
 * @Date: 2024/3/18 19:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WEBBEDSCountriesResp {

    /**
     * 国家列表
     */
    private List<Country> countries;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Country {

        /**
         * 国家ID
         */
        private Integer id;

        /**
         * 国家名称
         */
        private String name;
    }
}
