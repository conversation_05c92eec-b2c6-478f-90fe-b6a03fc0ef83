package com.somytrip.entity.hotel.SZJL;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.hotel.SZJL
 * @className: SZJLQueryRatePlanReq
 * @author: shadow
 * @description: 深圳捷旅查询报价Req
 * @date: 2024/10/13 17:41
 * @version: 1.0
 */
@Data
public class SZJLQueryRatePlanReq {

    /**
     * 酒店编号
     * 必填
     */
    private Integer hotelId;

    /**
     * 入住日期
     * 必填
     * 格式：yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate checkInDate;

    /**
     * 离店日期
     * 必填
     * 格式：yyyy-MM-dd
     * 最大入离日期不能超过90天
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate checkOutDate;

    /**
     * 房间信息
     * 非必填
     * 常用设置为1个房间2个成人
     */
    private List<RoomGroup> roomGroups;

    /**
     * 是否跳过预订规则校验
     * 非必填
     * 默认true跳过预订规则校验，false不跳过预订规则校验,客户拿到预订规则需要自行解析。
     */
    private Boolean isSkipCheckCondition;

    @Data
    public static class RoomGroup {

        /**
         * 成人数
         * 默认2
         */
        private Integer adults = 2;

        /**
         * 儿童数
         */
        private Integer children;

        /**
         * 儿童年龄
         * 儿童年龄（多个时用逗号分割）。年龄的个数需要和儿童数保持一致
         */
        private String childAges;
    }
}
