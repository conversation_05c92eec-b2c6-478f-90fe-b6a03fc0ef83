package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ELongHotelDataValidateReq
 * @Description: 同程艺龙酒店数据验证(下单前校验)请求参数
 * @Author: shadow
 * @Date: 2024/2/22 17:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelDataValidateReq {

    /**
     * 入住日期
     * 使用yyyy-MM-dd格式，例如:2022-12-09
     */
    @NotNull
    @JsonProperty("ArrivalDate")
    private LocalDate arrivalDate;

    /**
     * 离店日期
     * 使用yyyy-MM-dd格式，例如:2022-12-09
     */
    @NotNull
    @JsonProperty("DepartureDate")
    private LocalDate departureDate;

    /**
     * 最早到店时间
     */
    @NotNull
    @JsonProperty("EarliestArrivalTime")
    private LocalDateTime earliestArrivalTime;

    /**
     * 最晚到店时间
     */
    @NotNull
    @JsonProperty("LatestArrivalTime")
    private LocalDateTime latestArrivalTime;

    /**
     * 钟点房入住开始时间
     * 当试单的产品为钟点房时，需要入参该字段，格式为yyyy-MM-dd HH:mm:ss，且应符合钟点房的开始结束时间；
     * 传入则进行验证，不传不进行验证
     */
    @Nullable
    @JsonProperty("HourRoomStartTime")
    private LocalDateTime hourRoomStartTime;

    /**
     * 钟点房入住结束时间
     * 当试单的产品为钟点房时，需要入参该字段，格式为yyyy-MM-dd HH:mm:ss，且应符合钟点房的开始结束时间；
     * 传入则进行验证，不传不进行验证
     */
    @Nullable
    @JsonProperty("HourRoomEndTime")
    private LocalDateTime hourRoomEndTime;

    /**
     * 酒店编号
     */
    @NotNull
    @JsonProperty("HotelId")
    private String hotelId;

    /**
     * 展示房型编号
     * 允许为空，当传入时会校验房型编号绑定关系
     */
    @Nullable
    @JsonProperty("RoomId")
    private String roomId;

    /**
     * 销售房型编号
     */
    @NotNull
    @JsonProperty("RoomTypeID")
    private String roomTypeId;

    /**
     * 产品编号
     */
    @NotNull
    @JsonProperty("RatePlanId")
    private Integer ratePlanId;

    /**
     * 总价
     * 货币类型为原币种
     */
    @NotNull
    @JsonProperty("TotalPrice")
    private BigDecimal totalPrice;

    /**
     * 房间数量
     * 客人想要预订的房间数量
     */
    @NotNull
    @JsonProperty("NumberOfRooms")
    private Integer numberOfRooms;

    /**
     * 马甲Id
     * 从hotel.detail接口获取
     * 酒店马甲Id不再有30分钟限制
     * 搜索模式所有产品必传
     */
    @Nullable
    @JsonProperty("LittleMajiaId")
    private String littleMajiaId;

    /**
     * 商品唯一标示
     * 从hotel.detail接口获取
     * 搜索模式所有产品必传
     */
    @Nullable
    @JsonProperty("GoodsUniqId")
    private String goodsUniqId;

    /**
     * 儿童年龄
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("ChildAges")
    private List<Integer> childAges;

    /**
     * 成人数
     * 国际特有字段
     */
    @NotNull
    @JsonProperty("NumberOfAdults")
    private Integer numberOfAdults;

    /**
     * 酒店code
     * 国际特有字段
     */
    @NotNull
    @JsonProperty("HotelCode")
    private String hotelCode;

    /**
     * 供应商id
     * 国际特有字段
     */
    @NotNull
    @JsonProperty("SupplierId")
    private String supplierId;

    /**
     * 二级供应商id
     * 国际特有字段
     */
    @NotNull
    @JsonProperty("SubSupplierId")
    private String subSupplierId;

    /**
     * 商品库shopperid
     * 国际特有字段
     */
    @NotNull
    @JsonProperty("ShopperProductId")
    private String shopperProductId;

    /**
     * 币种
     * 国际特有字段
     */
    @NotNull
    @JsonProperty("CurrencyCode")
    private String currencyCode;

    /**
     * 每日价
     * 每日价透传：用于每日金额校验，避免出现订单部分退艺龙与合作方退款金额不一致现象发生 。
     * DayPriceList节点里每个DayPrice里的Price之和 * NumberOfRooms = TotalPrice
     */
    @Nullable
    @JsonProperty("DayPriceList")
    private List<DayPrice> dayPriceList;

    /**
     * DayPrice节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DayPrice {

        /**
         * 每日价
         * 每日价格
         */
        @NotNull
        @JsonProperty("Price")
        private BigDecimal price;

        /**
         * 日期
         * 价格对应的日期
         */
        @NotNull
        @JsonProperty("Date")
        private LocalDate date;

        /**
         * 税后价
         * 国际必传、国内不允许传。对应于NightRate里MinRate，同时Price为NightRate里Rate
         */
        @Nullable
        @JsonProperty("MinRate")
        private BigDecimal minRate;
    }
}
