package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 酒店入住政策表(HotelCheckInPolicy)实体类
 *
 * <AUTHOR>
 * @since 2024-02-02 10:26:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_check_in_policy")
public class HotelCheckInPolicyEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -78656323947434346L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 酒店主键ID
     */
    private Long hotelPid;
    /**
     * 入住方式
     * frontdesk:请到前台领取钥匙/门卡
     * reception:住宿方会有专人等侯迎接
     * password:住宿方会提供住宿的进门密码
     * keybox:住宿方会将钥匙存放于隐蔽处，并会在你入住前提供详细说明
     * keyhide:住宿方会将钥匙存在保管箱内，并会在你入住前提供详细说明
     * instruction:住宿方会在你入住前提供详细说明
     * other:其他
     */
    private String checkinWay;
    /**
     * 入住地址
     */
    private String checkinAddress;
    /**
     * 入住地址英文
     */
    private String checkinAddressEn;
    /**
     * 入住方式描述
     */
    private String checkinNote;
    /**
     * 入住方式描述英文
     */
    private String checkinNoteEn;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelCheckInPolicyEntity(ELongStaticHotelInfoResp.Detail.CheckinPolicy elongCheckinPolicy, String hotelId) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;
        this.checkinWay = elongCheckinPolicy.getCheckinWay();
        this.checkinAddress = elongCheckinPolicy.getCheckinAddress();
        this.checkinAddressEn = elongCheckinPolicy.getCheckinAddressEn();
        this.checkinNote = elongCheckinPolicy.getCheckinNote();
        this.checkinNoteEn = elongCheckinPolicy.getCheckinNoteEn();
    }
}

