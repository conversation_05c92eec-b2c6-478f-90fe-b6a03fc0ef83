package com.somytrip.entity.hotel.NCNB;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName: NCNBBookingCancelResp
 * @Description: 捷旅 取消订单相应体
 * @Author: shadow
 * @Date: 2023/12/1 10:28
 */
@Data
public class NCNBBookingCancelResp {

    /**
     * 订单ID
     */
    @JsonProperty("OrderId")
    private String orderId;

    /**
     * 订单状态
     */
    @JsonProperty("OrderStatus")
    private OrderStatus orderStatus;

    @Data
    public static class OrderStatus {

        /**
         * 订单状态ID 【2：已锁定，7：取消中，8：已取消，9：待确认，10：已确认，27：可入住，28：已入住，29：已离店，30：罚金取消】
         */
        @JsonProperty("OrderStatusId")
        private String orderStatusId;

        /**
         * 订单状态名称
         */
        @JsonProperty("OrderStatusName")
        private String orderStatusName;

        /**
         * 订单状态描述
         */
        @JsonProperty("OrderStatusDes")
        private String orderStatusDes;
    }
}
