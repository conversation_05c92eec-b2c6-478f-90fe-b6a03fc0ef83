package com.somytrip.entity.hotel.NCNB;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @ClassName: NCNBCityMappingEntity
 * @Description: 捷旅城市映射表 实体类
 * @Author: shadow
 * @Date: 2023/10/31 15:27
 */
@Data
@TableName("ncnb_city_mapping")
public class NCNBCityMappingEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private String cityCode;

    private Long globalCityId;

    private String globalCityName;

    @TableField("ncnb_country_id")
    private String NCNBCountryId;

    @TableField("ncnb_province_id")
    private String NCNBProvinceId;

    @TableField("ncnb_city_id")
    private String NCNBCityId;

    @TableField("ncnb_city_name")
    private String NCNBCityName;
}
