package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * @ClassName: ELongHotelOrderRelatedResp
 * @Description: 同程艺龙酒店关联订单响应
 * @Author: shadow
 * @Date: 2024/2/24 15:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderRelatedResp {

    /**
     * 订单关系
     * 不存在指定关系的不返回到结果中
     */
    @Nullable
    @JsonProperty("Relations")
    private List<Relation> relations;

    /**
     * Relation节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Relation {

        /**
         * 父订单
         */
        @NotNull
        @JsonProperty("ParentId")
        private String parentId;

        /**
         * 子订单
         */
        @NotNull
        @JsonProperty("ChildId")
        private String childId;
    }
}
