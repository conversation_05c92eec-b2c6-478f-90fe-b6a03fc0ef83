package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * @ClassName: ELongHotelDataInventoryResp
 * @Description: 同程艺龙静态酒店库存响应
 * @Author: shadow
 * @Date: 2024/2/20 10:04
 */
@Data
public class ELongHotelDataInventoryResp {

    /**
     * 库存集合
     */
    @Nullable
    @JsonProperty("Inventories")
    private List<Inventory> inventories;

    /**
     * Inventory节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Inventory {

        /**
         * 酒店ID
         */
        @NotNull
        @JsonProperty("HotelID")
        private String hotelId;

        /**
         * 房型ID
         */
        @NotNull
        @JsonProperty("RoomTypeId")
        private String roomTypeId;

        /**
         * 酒店编码
         */
        @NotNull
        @JsonProperty("HotelCode")
        private String hotelCode;

        /**
         * 库存时间
         * 表示的是某天的库存
         */
        @NotNull
        @JsonProperty("Date")
        private String date;

        /**
         * 库存状态
         * False-不可用 True-可用
         */
        @NotNull
        @JsonProperty("Status")
        private Boolean status;

        /**
         * 库存数量
         * 剩余的可知库存数量
         */
        @NotNull
        @JsonProperty("Amount")
        private Integer amount;

        /**
         * 超售状态
         * 0---可超售，1—不可超售。可超售的时候即使Amount等于0也是可以继续销售的。
         */
        @NotNull
        @JsonProperty("OverBooking")
        private Integer overBooking;

        /**
         * 可用开始日期
         * 库存可用开始日期
         */
        @NotNull
        @JsonProperty("StartDate")
        private String startDate;

        /**
         * 可用结束日期
         * 库存可用结束日期
         */
        @NotNull
        @JsonProperty("EndDate")
        private String endDate;

        /**
         * 可用开始时间
         * 预订当天库存，须校验库存可用开始时间
         */
        @NotNull
        @JsonProperty("StartTime")
        private String startTime;

        /**
         * 可用结束时间
         * 预订当天库存，须校验库存可用结束时间; 若为23:59:59则为无限制;(
         */
        @NotNull
        @JsonProperty("EndTime")
        private String endTime;

        /**
         * 库存是否支持即时确认
         */
        @Nullable
        @JsonProperty("IsInstantConfirm")
        private Boolean isInstantConfirm;

        /**
         * 即时确认可用开始时间
         */
        @Nullable
        @JsonProperty("IC_BeginTime")
        private String ic_beginTime;

        /**
         * 即时确认可用结束时间
         */
        @Nullable
        @JsonProperty("IC_EndTime")
        private String ic_EndTime;
    }
}
