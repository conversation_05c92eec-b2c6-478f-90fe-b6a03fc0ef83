package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName: ELongHotelOrderUpdateResp
 * @Description: 同程艺龙酒店修改订单响应
 * @Author: shadow
 * @Date: 2024/2/24 15:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderUpdateResp {

    /**
     * 订单编号
     */
    @NotNull
    @JsonProperty("OrderId")
    private Long orderId;

    /**
     * 最晚取消时间
     */
    @NotNull
    @JsonProperty("CancelTime")
    private LocalDateTime cancelTime;

    /**
     * 担保金额
     */
    @Nullable
    @JsonProperty("GuaranteeAmount")
    private BigDecimal guaranteeAmount;

    /**
     * 担保金额的货币
     * RMB,HKD,MOP,TWD
     */
    @Nullable
    @JsonProperty("GuaranteeCurrencyCode")
    private String guaranteeCurrencyCode;

    /**
     * 订单总价
     * 更新订单后总价可能改变
     */
    @NotNull
    @JsonProperty("TotalPrice")
    private BigDecimal totalPrice;

    /**
     * 总价的货币
     * RMB,HKD,MOP,TWD
     */
    @NotNull
    @JsonProperty("CurrencyCode")
    private String currencyCode;

    /**
     * 是否是即时确认
     * 采用这个属性，就不需要再请求hotel.order.instant接口了。
     * 即时确认只说明这个库存确认方式，最终能否确认给客人还需要考虑订单是担保订单(担保订单需要担保成功后才能确认)
     */
    @Nullable
    @JsonProperty("IsInstantConfirm")
    private Boolean isInstantConfirm;

    /**
     * 支付最后期限
     * 如果担保订单，提交的信用卡因某种原因支付失败，系统可以保留一段时间继续支付。如果这个时间点还没有成功支付系统将自动取消订单。
     * 继续支付请使用 hotel.order.pay接口
     */
    @Nullable
    @JsonProperty("PaymentDeadlineTime")
    private LocalDateTime paymentDeadlineTime;

    /**
     * 支付错误信息
     */
    @Nullable
    @JsonProperty("PaymentMessage")
    private String paymentMessage;
}
