package com.somytrip.entity.hotel;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import com.somytrip.entity.hotel.NCNB.NCNBRoomSearchResp;
import com.somytrip.entity.hotel.SZJL.SZJLQueryHotelDetailResp;
import com.somytrip.entity.hotel.WEBBEDS.WEBBEDSRoomTypesResp;
import com.somytrip.entity.vo.hotel.HotelImageVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 酒店房型表(HotelRooms)实体类
 *
 * <AUTHOR>
 * @since 2024-02-02 15:52:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_rooms", autoResultMap = true)
public class HotelRoomEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -82020553247434487L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 酒店主键ID
     */
    private Long hotelPid;
    /**
     * 房型ID
     */
    private String roomId;
    /**
     * 房型中文名称
     */
    private String roomName;
    /**
     * 房型英文名称
     */
    private String roomNameEn;
    /**
     * 房间类型名称
     */
    private String roomTypeName;
    /**
     * 房型面积
     */
    private String area;
    /**
     * 楼层
     */
    private String floor;
    /**
     * 窗户类型(0: 无, 1: 有, 2: 部分有)
     */
    private Integer windowType;
    /**
     * 上网情况，0表示无宽带，1表示有宽带, 2表示有WIFI
     */
    private Integer broadnetAccess;
    /**
     * 上网费用，0表示免费，1表示收费，2表示未知
     */
    private Integer broadnetFee;
    /**
     * 是否允许加床(0: 不可, 1: 可, 2: 免费加床, 3: 收费加床, 4: 未知)
     */
    private Integer addBed;
    /**
     * 可加床数量
     */
    private Integer addBedNum;
    /**
     * 吸烟类型(0: 不允许, 1: 允许, 2: 部分允许)
     */
    private Integer smokeType;
    /**
     * 床型ID
     */
    private String bedTypeId;
    /**
     * 床型中文名称
     */
    private String bedType;
    /**
     * 床型英文名称
     */
    private String bedTypeEn;
    /**
     * 中文描述
     */
    private String description;
    /**
     * 英文描述
     */
    private String descriptionEn;
    /**
     * 中文备注
     */
    private String comments;
    /**
     * 英文备注
     */
    private String commentsEn;
    /**
     * 房间最大入住人数，如没有提供请根据房间名称判断：单人间或有单字的为1人，三人间的为3人，其他的默认2人；7表示6人以上
     */
    private Integer capacity;
    /**
     * 房间数量
     */
    private Integer amount;
    /**
     * 房间设施ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> facilities;
    /**
     * 房间设施V2ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> facilityV2;
    /**
     * 床型信息
     */
    private String roomBed;
    /**
     * 户型类型，民宿特有，bed:床位，true:独立单间，false:整套
     */
    private String sharing;
    /**
     * 户型，几室几厅几卫
     */
    private String roomTypeSummary;
    /**
     * 房间图片
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> images;
    /**
     * 房间图片列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<HotelImageVo> roomImages;
    /**
     * 国际化字典
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Map<String, String>> localeDict;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelRoomEntity(ELongStaticHotelInfoResp.Room elongRoom, String hotelid) {
        this.hotelId = hotelid;
        this.hotelOrigin = HotelOrigin.ELong;
        this.roomId = elongRoom.getRoomId();
        this.roomName = elongRoom.getRoomName();
        this.roomNameEn = elongRoom.getRoomNameEn();
        this.area = elongRoom.getArea();
        this.floor = elongRoom.getFloor();
        this.broadnetAccess = elongRoom.getBroadnetAccess();
        this.broadnetFee = elongRoom.getBroadnetFee();
        this.bedType = elongRoom.getBedType();
        this.bedTypeEn = elongRoom.getBedTypeEn();
        this.description = elongRoom.getDescription();
        this.descriptionEn = elongRoom.getDescriptionEn();
        this.comments = elongRoom.getComments();
        this.commentsEn = elongRoom.getCommentsEn();
        this.capacity = elongRoom.getCapacity();
        this.amount = elongRoom.getAmount();

        if (elongRoom.getFacilities() != null) {
            List<String> facilityIds = new ArrayList<>();
            for (ELongStaticHotelInfoResp.Facility facility : elongRoom.getFacilities()) {
                facilityIds.add(facility.getFacilityId());
            }
            this.facilities = facilityIds;
        }

        if (elongRoom.getFacilityV2() != null) {
            List<String> facilityV2Ids = new ArrayList<>();
            for (ELongStaticHotelInfoResp.FacilityType facilityType : elongRoom.getFacilityV2()) {
                if (facilityType.getFacilityInfoList() != null) {
                    for (ELongStaticHotelInfoResp.FacilityType.FacilityInfo facilityInfo : facilityType.getFacilityInfoList()) {
                        facilityV2Ids.add(String.valueOf(facilityInfo.getFacilityId()));
                    }
                }
            }
            this.facilityV2 = facilityV2Ids;
        }

        // FIXME: 床型数据暂时用JSON
        this.roomBed = JSONObject.toJSONString(elongRoom.getRoomBed());
        this.sharing = elongRoom.getSharing();
        this.roomTypeSummary = elongRoom.getRoomTypeSummary();
    }

    public HotelRoomEntity(NCNBRoomSearchResp.HotelRoomInfo.RoomInfo ncnbRoomInfo, String hotelId) {
        this.hotelOrigin = HotelOrigin.NCNB;
        this.hotelId = hotelId;
        this.roomId = ncnbRoomInfo.getRoomId();
        this.roomName = ncnbRoomInfo.getRoomName();
        this.roomTypeName = ncnbRoomInfo.getRoomType();
        this.bedTypeId = ncnbRoomInfo.getBedTypeId();
        this.bedType = ncnbRoomInfo.getBedType();
        this.area = ncnbRoomInfo.getAcreage();
        this.floor = ncnbRoomInfo.getFloor();
        if (StringUtils.isNotBlank(ncnbRoomInfo.getRoomAcount())) {
            this.amount = Integer.valueOf(ncnbRoomInfo.getRoomAcount());
        }
        if (StringUtils.isNotBlank(ncnbRoomInfo.getMaxAdult())) {
            this.capacity = Integer.valueOf(ncnbRoomInfo.getMaxAdult());
        }
        if (StringUtils.isNotBlank(ncnbRoomInfo.getHasWindow())) {
            this.windowType = Integer.valueOf(ncnbRoomInfo.getHasWindow());
        }
        if (StringUtils.isNotBlank(ncnbRoomInfo.getAllowAddBed())) {
            this.addBed = Integer.valueOf(ncnbRoomInfo.getAllowAddBed());
        }
        if (StringUtils.isNotBlank(ncnbRoomInfo.getAllowAddBedNum())) {
            this.addBedNum = Integer.valueOf(ncnbRoomInfo.getAllowAddBedNum());
        }
        if (StringUtils.isNotBlank(ncnbRoomInfo.getAllowSmoke())) {
            this.smokeType = Integer.valueOf(ncnbRoomInfo.getAllowSmoke());
        }
        if (StringUtils.isNotBlank(ncnbRoomInfo.getHasNet())) {
            this.broadnetAccess = Integer.valueOf(ncnbRoomInfo.getHasNet());
        }
        if (StringUtils.isNotBlank(ncnbRoomInfo.getIsNetFee())) {
            this.broadnetFee = Integer.valueOf(ncnbRoomInfo.getIsNetFee());
        }
        this.description = ncnbRoomInfo.getIntro();
        if (ncnbRoomInfo.getImages() != null) {
            this.roomImages = ncnbRoomInfo.getImages().stream().map(HotelImageVo::new).toList();
        }
    }

    public HotelRoomEntity(WEBBEDSRoomTypesResp.RoomType webbedsRoomType, String hotelId) {

        this.hotelOrigin = HotelOrigin.WEBBEDS;
        this.hotelId = hotelId;
        this.roomId = String.valueOf(webbedsRoomType.getId());
        this.roomName = webbedsRoomType.getName();
        this.floor = webbedsRoomType.getFloor();
        this.area = webbedsRoomType.getArea();
        this.amount = webbedsRoomType.getRoomQuantity();
        if (webbedsRoomType.getExtraBed() != null) {
            int extraBed = webbedsRoomType.getExtraBed();
            if (extraBed > 0) {
                extraBed = extraBed == 4 ? 2 : extraBed + 1;
            }
            this.addBed = extraBed;
        }
        if (webbedsRoomType.getWindowType() != null) {
            int windowType = webbedsRoomType.getWindowType();
            this.windowType = windowType == 56 ? 0 : windowType == 195 ? 2 : 1;
        }
        this.images = webbedsRoomType.getImages();
    }

    public HotelRoomEntity(SZJLQueryHotelDetailResp.RoomType roomType, String hotelId) {
        this.hotelOrigin = HotelOrigin.SZJL;
        this.hotelId = hotelId;
        this.roomId = String.valueOf(roomType.getRoomTypeId());
        this.roomName = roomType.getRoomTypeCn();
        this.roomNameEn = roomType.getRoomTypeEn();
//        roomType.getBasisRoomId();
//        roomType.getBasisRoomCn();
        this.capacity = roomType.getMaximize();
        this.bedType = roomType.getBedName();
//        roomType.getBedType();
        this.floor = roomType.getFloorDistribute();
        if (StringUtils.isNotBlank(roomType.getFacilities())) {
            this.facilityV2 = new ArrayList<>(Arrays.asList(roomType.getFacilities().split(",")));
        }
        this.addBed = 0;
        if (Objects.equals("0", roomType.getExtraBedState())) {
            this.addBed = 1;
        }
        this.addBedNum = roomType.getBedCount();
    }
}

