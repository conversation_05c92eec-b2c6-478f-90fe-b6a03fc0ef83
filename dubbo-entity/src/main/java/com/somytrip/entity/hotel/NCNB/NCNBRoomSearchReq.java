package com.somytrip.entity.hotel.NCNB;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName: NCNBRoomSearchReq
 * @Description: 捷旅 房型静态信息查询(RoomSearch) 请求体
 * @Author: shadow
 * @Date: 2023/10/14 14:32
 */
@Data
public class NCNBRoomSearchReq {

    /**
     * 市ID
     */
    @JsonProperty("CityId")
    private String cityId = "";
    /**
     * 酒店ID
     */
    @JsonProperty("HotelId")
    private String hotelId = "";
    /**
     * CN酒店ID列表
     */
    @JsonProperty("HotelIds")
    private String[] hotelIds = new String[]{};
    /**
     * Lang语言 ,对应语言二字码 【GB：中文，EN：英文】
     */
    @JsonProperty("Lang")
    private String lang = "GB";
    /**
     * 国家ID
     */
    @JsonProperty("CountryId")
    private String countryId = "";
    /**
     * 省ID
     */
    @JsonProperty("ProvinceId")
    private String provinceId = "";
    /**
     * CN 房间 ID
     */
    @JsonProperty("RoomId")
    private String roomId;
}
