package com.somytrip.entity.hotel.NCNB;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName: NCNBPreBookingCheckReq
 * @Description: 捷旅 可订检查(PreBookingCheck) 请求体
 * @Author: shadow
 * @Date: 2023/10/14 14:20
 */
@Data
public class NCNBPreBookingCheckReq {

    /**
     * 酒店 ID
     */
    @JsonProperty("HotelId")
    private String hotelId = "";
    /**
     * 房型 ID
     */
    @JsonProperty("RoomId")
    private String roomId = "";
    /**
     * 价格计划 ID
     */
    @JsonProperty("RateplanId")
    private String rateplanId = "";
    /**
     * 入住日期
     */
    @JsonProperty("CheckIn")
    private String checkIn = "";
    /**
     * 离店日期
     */
    @JsonProperty("CheckOut")
    private String checkOut = "";
    /**
     * 国籍(适用于海外酒店)
     */
    @JsonProperty("Nationality")
    private String nationality = "";
    /**
     * 预订间数, 取值范围 1~8
     */
    @JsonProperty("RoomCount")
    private String roomCount = "";
    /**
     * 币种，如果为空，则取CNY
     */
    @JsonProperty("Currency")
    private String currency = "CNY";
    /**
     * 订单金额
     */
    @JsonProperty("OrderAmount")
    private String orderAmount = "";
}
