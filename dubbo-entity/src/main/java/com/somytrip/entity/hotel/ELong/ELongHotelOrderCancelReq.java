package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;

/**
 * @ClassName: ELongHotelOrderCancelReq
 * @Description: 同程艺龙酒店取消订单请求参数
 * @Author: shadow
 * @Date: 2024/2/24 15:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderCancelReq {

    /**
     * 订单编号
     */
    @NotNull
    @JsonProperty("OrderId")
    private Long orderId;

    /**
     * 取消类型
     * 示例:
     * 对酒店相关条件不满意
     * 航班推迟
     * 价格过高，客人不接受
     * 通过其它途径预订
     * 行程变更
     * 已换酒店
     * 重单
     * 其它
     */
    @NotNull
    @JsonProperty("CancelCode")
    private String cancelCode;

    /**
     * 具体原因
     */
    @Nullable
    @JsonProperty("Reason")
    private String reason;

    /**
     * 取消罚金
     * 0，默认值，不做取消罚金校验，能取消就取消，有罚金也取消，以艺龙接口计算的罚金为准
     * -1，若取消需要收取罚金就不取消，无罚金则取消
     * 大于0，校验取消罚金，需要与艺龙的罚金相等才取消，否则拒绝取消
     */
    @Nullable
    @JsonProperty("PenaltyAmount")
    private BigDecimal penaltyAmount;
}
