package com.somytrip.entity.hotel.SZJL;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.hotel.SZJL
 * @className: SZJLReqBody
 * @author: shadow
 * @description: 深圳捷旅请求体
 * @date: 2024/10/13 14:57
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SZJLReqBody<T> {

    private Head head;

    private T data;

    @Data
    public static class Head {

        /**
         * 客户编号
         */
        private String appKey;

        /**
         * 时间戳(13位)
         */
        private String timestamp;

        /**
         * 数字签名
         */
        private String sign;

        /**
         * 版本
         */
        private String version;

        /**
         * 房间数量
         */
        private Integer roomNum;
    }
}
