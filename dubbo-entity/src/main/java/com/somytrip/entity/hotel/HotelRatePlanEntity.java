package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.*;
import com.somytrip.entity.hotel.ELong.offline.ELongHotelDataRpResp;
import com.somytrip.utils.LocalDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * (HotelRatePlan)实体类
 *
 * <AUTHOR>
 * @since 2024-02-19 14:27:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_rate_plan", autoResultMap = true)
public class HotelRatePlanEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -82651474265130579L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 产品编号
     */
    private Integer ratePlanId;
    /**
     * 产品名称
     */
    private String ratePlanName;
    /**
     * 客宾类型
     */
    private CustomerType customerType;
    /**
     * 使用人群
     */
    private String guestType;
    /**
     * 适用人群中其他的内容
     */
    private String guestTypeExtendCh;
    /**
     * 酒店编码
     */
    private String hotelCode;
    /**
     * 付款类型
     */
    private PaymentType paymentType;
    /**
     * 关联的房型编码，多个房型编码时以英文逗号分隔
     */
    private String roomTypeIds;
    /**
     * 产品特性类型
     */
    private String productTypes;
    /**
     * 是否需要提供身份证号
     */
    private Boolean needIdNo;
    /**
     * 身份信息验证类型
     */
    private Integer identification;
    /**
     * 分销渠道
     */
    private String sellChannels;
    /**
     * 是否是今日特价 (尾房)
     */
    private Boolean isLimitTimeSale;
    /**
     * 尾房每天预定开始时间
     */
    private LocalTime startTime;
    /**
     * 尾房每天预定结束时间
     */
    private LocalTime endTime;
    /**
     * 预定最少数量
     */
    private Integer minAmount;
    /**
     * 最多入住天数
     */
    private Integer maxDays;
    /**
     * 最多预定间数
     */
    private Integer maxCheckinRooms;
    /**
     * 最少提前预定小时数
     */
    private Integer minAdvHours;
    /**
     * 最多提前预定小时数
     */
    private Integer maxAdvHours;
    /**
     * 担保规则
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<GuaranteeRuleEntity> guaranteeRules;
    /**
     * 新担保规则
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<GuaranteeRuleExtendEntity> guaranteeRuleExtends;
    /**
     * 预付规则
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<PrepayRuleEntity> prepayRules;
    /**
     * 新预付规则
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<PrepayRuleExtendEntity> prepayRuleExtends;
    /**
     * 增值服务
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ValueAddEntity> valueAdds;
    /**
     * 餐食
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<MealEntity> meals;
    /**
     * 促销规则
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<DrrRuleEntity> drrRules;
    /**
     * 优惠券信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private CouponEntity coupon;
    /**
     * 产品提供服务的时间
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ServiceTimePolicyInfoEntity serviceTimePolicyInfo;
    /**
     * 产品可以展示销售的渠道
     */
    private String bookingChannels;
    /**
     * 是否为限价产品
     */
    private Boolean isPriceLimitProduct;
    /**
     * 限价类型
     */
    private Integer priceLimitedType;
    /**
     * 可售会员等级
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> customerLevel;
    /**
     * 预付产品发票模式
     */
    private String invoiceMode;
    /**
     * 酒店签约类型
     */
    private Integer cooperationType;
    /**
     * 可住开始时间
     */
    private String earliestToLiveTime;
    /**
     * 可住结束时间
     */
    private String latestToLiveTime;
    /**
     * 可住时长
     */
    private String stayTime;
    /**
     * 现实抢购规则
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<TimeRushRuleEntity> timeRushRuleList;
    /**
     * 可入住人数
     */
    private String xStayPeopleNum;
    /**
     * 可入住性别
     */
    private String xStaySex;
    /**
     * 床型
     */
    private String xBedType;
    /**
     * 楼层
     */
    private String xFloor;
    /**
     * 朝向
     */
    private String xOrientation;
    /**
     * 自定义说明
     */
    private String xUserDefined;
    /**
     * 是否支持专票
     */
    private Boolean supportSpecialInvoice;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelRatePlanEntity(ELongHotelDataRpResp.Hotel.RatePlan elongHotelRatePlan, String HotelId) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = HotelId;
        this.ratePlanId = elongHotelRatePlan.getRatePlanId();
        this.ratePlanName = elongHotelRatePlan.getRatePlanName();
        this.customerType = elongHotelRatePlan.getCustomerType();
        this.guestType = elongHotelRatePlan.getGuestType();
        this.guestTypeExtendCh = elongHotelRatePlan.getGuestTypeExtendCh();
        this.hotelCode = elongHotelRatePlan.getHotelCode();
        this.paymentType = elongHotelRatePlan.getPaymentType();
        this.roomTypeIds = elongHotelRatePlan.getRoomTypeIds();
        this.productTypes = elongHotelRatePlan.getProductTypes();
        this.needIdNo = elongHotelRatePlan.getNeedIdNo();
        this.identification = elongHotelRatePlan.getIdentification();
        this.sellChannels = elongHotelRatePlan.getSellChannels();
        this.isLimitTimeSale = elongHotelRatePlan.getIsLimitTimeSale();
        if (elongHotelRatePlan.getStartTime() != null) {
            this.startTime = LocalTime.parse(elongHotelRatePlan.getStartTime());
        }
        if (elongHotelRatePlan.getEndTime() != null) {
            this.endTime = LocalTime.parse(elongHotelRatePlan.getEndTime());
        }
        this.minAmount = elongHotelRatePlan.getMinAmount();
        this.maxDays = elongHotelRatePlan.getMaxDays();
        this.maxCheckinRooms = elongHotelRatePlan.getMaxCheckinRooms();
        this.minAdvHours = elongHotelRatePlan.getMinAdvHours();
        this.maxAdvHours = elongHotelRatePlan.getMaxAdvHours();
        if (elongHotelRatePlan.getGuaranteeRules() != null) {
            List<GuaranteeRuleEntity> guaranteeRuleEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.RatePlan.GuaranteeRule guaranteeRule
                    : elongHotelRatePlan.getGuaranteeRules()) {
                guaranteeRuleEntityList.add(new GuaranteeRuleEntity(guaranteeRule));
            }
            this.guaranteeRules = guaranteeRuleEntityList;
        }
        if (elongHotelRatePlan.getGuaranteeRuleExtends() != null) {
            List<GuaranteeRuleExtendEntity> guaranteeRuleExtendEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.RatePlan.GuaranteeRuleExtend guaranteeRuleExtend
                    : elongHotelRatePlan.getGuaranteeRuleExtends()) {
                guaranteeRuleExtendEntityList.add(new GuaranteeRuleExtendEntity(guaranteeRuleExtend));
            }
            this.guaranteeRuleExtends = guaranteeRuleExtendEntityList;
        }
        if (elongHotelRatePlan.getPrepayRules() != null) {
            List<PrepayRuleEntity> prepayRuleEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.RatePlan.PrepayRule prepayRule : elongHotelRatePlan.getPrepayRules()) {
                prepayRuleEntityList.add(new PrepayRuleEntity(prepayRule));
            }
            this.prepayRules = prepayRuleEntityList;
        }
        if (elongHotelRatePlan.getPrepayRuleExtends() != null) {
            List<PrepayRuleExtendEntity> prepayRuleExtendEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.RatePlan.PrepayRuleExtend prepayRuleExtend
                    : elongHotelRatePlan.getPrepayRuleExtends()) {
                prepayRuleExtendEntityList.add(new PrepayRuleExtendEntity(prepayRuleExtend));
            }
            this.prepayRuleExtends = prepayRuleExtendEntityList;
        }
        if (elongHotelRatePlan.getValueAdds() != null) {
            List<ValueAddEntity> valueAddEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.RatePlan.ValueAdd valueAdd : elongHotelRatePlan.getValueAdds()) {
                valueAddEntityList.add(new ValueAddEntity(valueAdd));
            }
            this.valueAdds = valueAddEntityList;
        }
        if (elongHotelRatePlan.getMeals() != null) {
            List<MealEntity> mealEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.RatePlan.Meal meal : elongHotelRatePlan.getMeals()) {
                mealEntityList.add(new MealEntity(meal));
            }
            this.meals = mealEntityList;
        }
        if (elongHotelRatePlan.getDrrRules() != null) {
            List<DrrRuleEntity> drrRuleEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.RatePlan.DrrRule drrRule : elongHotelRatePlan.getDrrRules()) {
                drrRuleEntityList.add(new DrrRuleEntity(drrRule));
            }
            this.drrRules = drrRuleEntityList;
        }
        if (elongHotelRatePlan.getCoupon() != null) {
            this.coupon = new CouponEntity(elongHotelRatePlan.getCoupon());
        }
        if (elongHotelRatePlan.getServiceTimePolicyInfo() != null) {
            this.serviceTimePolicyInfo = new ServiceTimePolicyInfoEntity(elongHotelRatePlan.getServiceTimePolicyInfo());
        }
        this.bookingChannels = elongHotelRatePlan.getBookingChannels();
        this.isPriceLimitProduct = elongHotelRatePlan.getIsPriceLimitProduct();
        this.priceLimitedType = elongHotelRatePlan.getPriceLimitedType();
        this.customerLevel = elongHotelRatePlan.getCustomerLevel();
        this.invoiceMode = elongHotelRatePlan.getInvoiceMode();
        this.cooperationType = elongHotelRatePlan.getCooperationType();
        this.earliestToLiveTime = elongHotelRatePlan.getEarliestToLiveTime();
        this.latestToLiveTime = elongHotelRatePlan.getLatestToLiveTime();
        this.stayTime = elongHotelRatePlan.getStayTime();
        if (elongHotelRatePlan.getTimeRushRuleList() != null) {
            List<TimeRushRuleEntity> timeRushRuleEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.RatePlan.TimeRushRule rushRule : elongHotelRatePlan.getTimeRushRuleList()) {
                timeRushRuleEntityList.add(new TimeRushRuleEntity(rushRule));
            }
            this.timeRushRuleList = timeRushRuleEntityList;
        }
        this.xStayPeopleNum = elongHotelRatePlan.getXStayPeopleNum();
        this.xStaySex = elongHotelRatePlan.getXStaySex();
        this.xBedType = elongHotelRatePlan.getXBedType();
        this.xFloor = elongHotelRatePlan.getXFloor();
        this.xOrientation = elongHotelRatePlan.getXOrientation();
        this.xUserDefined = elongHotelRatePlan.getXUserDefined();
        this.supportSpecialInvoice = elongHotelRatePlan.getSupportSpecialInvoice();
    }

    /**
     * GuaranteeRule节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GuaranteeRuleEntity {

        /**
         * 描述
         */
        private String description;

        /**
         * 日期类型
         */
        private DateType dateType;

        /**
         * 开始日期
         * 举例：DateType为CheckInDay：表示当前订单的入住日期落在StartDate和EndDate之间，
         * 并且入住日期符合周设置时才需要判断其它条件是否担保，否则不需要担保
         */
        private String startDate;

        /**
         * 结束日期
         * 举例：DateType为CheckInDay：表示当前订单的入住日期落在StartDate和EndDate之间，
         * 并且入住日期符合周设置时才需要判断其它条件是否担保，否则不需要担保
         */
        private String endDate;

        /**
         * 周有效天数， 一般为周一到周日都有效， 判断日期符合日期段同时也要满足周设置的有效
         * 周一对应为1，周二对应为2， 依次类推;逗号分隔
         * 为空时表示无周末设置
         * DateType为StayDay：表示当前订单的客人只要有住在店里面的日期（ArrivalDate,DepartureDate）落在StartDate和EndDate之间，
         * 并且入住日期符合周设置时才需要判断其它条件是否担保，否则不需要担保
         */
        private String weekSet;

        /**
         * 是否到店时间担保
         * False:为不校验到店时间
         * True:为需要校验到店时间
         * 此字段与之后的IsAmountGuarantee字段用法比较特殊，请仔细阅读注意事项中关于这两个字段的说明。
         */
        private Boolean isTimeGuarantee;

        /**
         * 到店担保开始时间
         * 用于IsTimeGuarantee==true进行检查。
         */
        private String startTime;

        /**
         * 到店担保结束时间
         * 当EndTime小于StartTime的时候，默认从StartTime到次日6点都需要担保。
         */
        private String endTime;

        /**
         * 到店担保的结束时间是否为第二天
         * 0为当天，1为次日
         */
        private Boolean isTomorrow;

        /**
         * 是否房量担保
         * False:为不校验房量条件
         * True:为校验房量条件
         */
        private Boolean isAmountGuarantee;

        /**
         * 担保的房间数,预定几间房以上要担保
         * 用于IsAmountGuarantee==true进行检查
         */
        private Integer amount;

        /**
         * 担保类型
         * FirstNightCost为首晚房费担保
         * FullNightCost为全额房费担保
         */
        private String guaranteeType;

        /**
         * 变更规则
         * 担保规则取消变更规则：
         * NoChange、不允许变更取消
         * NeedSomeDay、允许变更/取消,需在XX日YY时之前通知
         * NeedCheckinTime、允许变更/取消,需在最早到店时间之前几小时通知
         * NeedCheckin24hour、允许变更/取消,需在到店日期的24点之前几小时通知
         */
        private GuaranteeChangeRule changeRule;

        /**
         * 日期参数
         * ChangeRule=NeedSomeDay时，对应规则2描述中 “允许变更/取消,需在XX日YY时之前通知” 中的XX日，YY时
         */
        private String day;

        /**
         * 时间参数
         * ChangeRule=NeedSomeDay时，对应规则2描述中 “允许变更/取消,需在XX日YY时之前通知” 中的XX日，YY时
         */
        private String time;

        /**
         * 小时参数
         * ChangeRule=NeedCheckinTime时，对应规则3描述中 “ 允许变更/取消,需在最早到店时间之前几小时通知” 中的几小时
         * ChangeRule=NeedCheckin24hour时，对应规则4描述中“ 允许变更/取消,需在到店日期的24点之前几小时通知” 中的几小时
         */
        private Integer hour;

        public GuaranteeRuleEntity(ELongHotelDataRpResp.Hotel.RatePlan.GuaranteeRule elongGuaranteeRule) {
            this.description = elongGuaranteeRule.getDescription();
            this.dateType = elongGuaranteeRule.getDateType();
            this.startDate = elongGuaranteeRule.getStartDate();
            this.endDate = elongGuaranteeRule.getEndDate();
            this.weekSet = elongGuaranteeRule.getWeekSet();
            this.isTimeGuarantee = elongGuaranteeRule.getIsTimeGuarantee();
            this.startTime = elongGuaranteeRule.getStartTime();
            this.endTime = elongGuaranteeRule.getEndTime();
            this.isTomorrow = elongGuaranteeRule.getIsTomorrow();
            this.isAmountGuarantee = elongGuaranteeRule.getIsAmountGuarantee();
            this.amount = elongGuaranteeRule.getAmount();
            this.guaranteeType = elongGuaranteeRule.getGuaranteeType();
            this.changeRule = elongGuaranteeRule.getChangeRule();
            this.day = elongGuaranteeRule.getDay();
            this.time = elongGuaranteeRule.getTime();
            this.hour = elongGuaranteeRule.getHour();
        }
    }

    /**
     * PrepayRule节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrepayRuleEntity {

        /**
         * 描述
         */
        private String description;

        /**
         * 日期类型
         * CheckInDay：入住日期（该字段后期下线，可以不用判断）
         */
        private String dateType;

        /**
         * 开始日期
         * 使用离线数据模式需要判断
         */
        private String startDate;

        /**
         * 结束日期
         * 使用离线数据模式需要判断
         */
        private String endDate;

        /**
         * 周有效设置
         * 使用离线数据模式需要判断
         */
        private String weekSet;

        /**
         * 变更规则
         */
        private PrepayChangeRule changeRule;

        /**
         * 第一阶段提前的几小时
         * 用于PrepayNeedSomeDay
         */
        private Integer hour;

        /**
         * 第二阶段提前的几小时
         * 用于PrepayNeedSomeDay
         */
        private Integer hour2;

        /**
         * 具体取消时间日期部分
         * 用于PrepayNeedOneTime
         */
        private String dateNum;

        /**
         * 具体取消时间小时部分
         * 用于PrepayNeedOneTime
         */
        private String time;

        /**
         * 在变更时间点前是否扣费
         * 用于 PrepayNeedSomeDay的Hour前扣款类型（一般不收罚金）。DeductFeesBefore为1表示扣款，0表示不扣款。
         */
        private Integer deductFeesBefore;

        /**
         * 时间点前扣费的金额或比例
         * 用于 PrepayNeedSomeDay的Hour前扣款类型（一般不收罚金）。DeductFeesBefore为1表示扣款，0表示不扣款。
         */
        private BigDecimal deductNumBefore;

        /**
         * 时间点后扣款类型
         */
        private PrepayCashType cashScaleFirstAfter;

        /**
         * 在变更时间点后是否扣费
         * 用于 PrepayNeedSomeDay的Hour到Hour2之间的扣款类型。DeductFeesAfter为1表示扣款，0表示不扣款。
         * 如果CashScaleFirstAfter为FristNight，则返回-1，没有意义
         */
        private Integer deductFeesAfter;

        /**
         * 时间点后扣费的金额或比例
         * 用于 PrepayNeedSomeDay的Hour到Hour2之间的扣款类型。DeductFeesAfter为1表示扣款，0表示不扣款。
         * 如果CashScaleFirstAfter为FristNight，则返回-1，没有意义
         */
        private BigDecimal deductNumAfter;

        /**
         * 时间点前扣款类型
         */
        private PrepayCashType cashScaleFirstBefore;

        public PrepayRuleEntity(ELongHotelDataRpResp.Hotel.RatePlan.PrepayRule elongPrepayRule) {
            this.description = elongPrepayRule.getDescription();
            this.dateType = elongPrepayRule.getDateType();
            this.startDate = LocalDateTimeUtil.convertISOStr2LocalDate(elongPrepayRule.getStartDate()).toString();
            this.endDate = LocalDateTimeUtil.convertISOStr2LocalDate(elongPrepayRule.getEndDate()).toString();
            this.weekSet = elongPrepayRule.getWeekSet();
            this.changeRule = elongPrepayRule.getChangeRule();
            this.hour = elongPrepayRule.getHour();
            this.hour2 = elongPrepayRule.getHour2();
            this.dateNum = LocalDateTimeUtil.convertISOStr2LocalDate(elongPrepayRule.getDateNum()).toString();
            this.time = elongPrepayRule.getTime();
            this.deductFeesBefore = elongPrepayRule.getDeductFeesBefore();
            this.deductNumBefore = elongPrepayRule.getDeductNumBefore();
            this.cashScaleFirstAfter = elongPrepayRule.getCashScaleFirstAfter();
            this.deductFeesAfter = elongPrepayRule.getDeductFeesAfter();
            this.deductNumAfter = elongPrepayRule.getDeductNumAfter();
            this.cashScaleFirstBefore = elongPrepayRule.getCashScaleFirstBefore();
        }
    }

    /**
     * PrepayRuleExtend节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrepayRuleExtendEntity {

        /**
         * 开始时间
         * 按入住日匹配，入住日在开始时间和结束时间之间，且符合周有效规则，即为命中此规则
         */
        private String startDate;

        /**
         * 结束时间
         */
        private String endDate;

        /**
         * 周有效设置
         */
        private String weekSet;

        /**
         * 取消费用类型
         * 0:跟随取消费用 1:订单全额（目前只有0）
         */
        private Integer noShowPenalty;

        /**
         * 取消规则列表
         * 解析示例: https://open.elong.com/doc/info/cn-api-meta-hotel_data_rp#RuleExtend%E8%A7%A3%E6%9E%90
         */
        private List<PenaltyWindowTypeEntity> penaltyRuleList;

        /**
         * 规则类型
         * 1：长期规则；2：特殊规则，优先看特殊规则
         */
        private Integer subId;

        public PrepayRuleExtendEntity(ELongHotelDataRpResp.Hotel.RatePlan.PrepayRuleExtend elongPrepayRuleExtend) {
            this.startDate = elongPrepayRuleExtend.getStartDate();
            this.endDate = elongPrepayRuleExtend.getEndDate();
            this.weekSet = elongPrepayRuleExtend.getWeekSet();
            this.noShowPenalty = elongPrepayRuleExtend.getNoShowPenalty();
            List<PenaltyWindowTypeEntity> penaltyWindowTypeEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.RatePlan.PenaltyWindowType penaltyWindowType
                    : elongPrepayRuleExtend.getPenaltyRuleList()) {
                penaltyWindowTypeEntityList.add(new PenaltyWindowTypeEntity(penaltyWindowType));
            }
            this.penaltyRuleList = penaltyWindowTypeEntityList;
            this.subId = elongPrepayRuleExtend.getSubId();
        }
    }

    /**
     * GuaranteeRuleExtend节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GuaranteeRuleExtendEntity {

        /**
         * 开始时间
         * 按入住日匹配，入住日在开始时间和结束时间之间，且符合周有效规则，即为命中此规则
         */
        private String startDate;

        /**
         * 结束时间
         */
        private String endDate;

        /**
         * 周有效设置
         */
        private String weekSet;

        /**
         * 担保类型
         * 0:需担保 1:无需担保 2:超时担保
         */
        private Integer guaranteeType;

        /**
         * 取消费用类型
         * 0:跟随取消费用 1:订单全额 计算担保金额用，0时取PenaltyRuleList计算出的罚金列表中罚金的最大值
         */
        private Integer noShowPenalty;

        /**
         * 超时担保时间
         * 单位分钟，相对入住日24点的小时偏移量, 范围[0,840]
         */
        private Integer grtLatesCheckTime;

        /**
         * 取消规则列表
         * 解析示例: https://open.elong.com/doc/info/cn-api-meta-hotel_data_rp#RuleExtend%E8%A7%A3%E6%9E%90
         */
        private List<PenaltyWindowTypeEntity> penaltyRuleList;

        /**
         * 规则类型
         * 1：长期规则；2：特殊规则，优先看特殊规则
         */
        private Integer subId;

        public GuaranteeRuleExtendEntity(ELongHotelDataRpResp.Hotel.RatePlan.GuaranteeRuleExtend elongGuaranteeRuleExtend) {
            this.startDate = elongGuaranteeRuleExtend.getStartDate();
            this.endDate = elongGuaranteeRuleExtend.getEndDate();
            this.weekSet = elongGuaranteeRuleExtend.getWeekSet();
            this.guaranteeType = elongGuaranteeRuleExtend.getGuaranteeType();
            this.noShowPenalty = elongGuaranteeRuleExtend.getNoShowPenalty();
            this.grtLatesCheckTime = elongGuaranteeRuleExtend.getGrtLatesCheckTime();
            if (elongGuaranteeRuleExtend.getPenaltyRuleList() != null) {
                List<PenaltyWindowTypeEntity> penaltyWindowTypeEntityList = new ArrayList<>();
                for (ELongHotelDataRpResp.Hotel.RatePlan.PenaltyWindowType penaltyWindowType
                        : elongGuaranteeRuleExtend.getPenaltyRuleList()) {
                    penaltyWindowTypeEntityList.add(new PenaltyWindowTypeEntity(penaltyWindowType));
                }
                this.penaltyRuleList = penaltyWindowTypeEntityList;
            }
            this.subId = elongGuaranteeRuleExtend.getSubId();
        }
    }

    /**
     * PenaltyWindowType节点
     */
    @Data
    public static class PenaltyWindowTypeEntity {

        /**
         * 扣款类型
         * 0:百分比 1:晚数  2:首晚百分比
         */
        private Integer penaltyType;

        /**
         * 罚金
         * PenaltyType 为 0,2时，此值为两位小数。
         * PenaltyType  为1是，此值使用时会取整，按整数处理。
         */
        private BigDecimal penaltyValue;

        /**
         * 规则时间分割起始点
         * 单位分钟，第一个点为1439280
         */
        private String deadline;

        public PenaltyWindowTypeEntity(ELongHotelDataRpResp.Hotel.RatePlan.PenaltyWindowType elongPenaltyWindowType) {
            this.penaltyType = elongPenaltyWindowType.getPenaltyType();
            this.penaltyValue = elongPenaltyWindowType.getPenaltyValue();
            this.deadline = elongPenaltyWindowType.getDeadline();
        }
    }

    /**
     * ValueAdd节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValueAddEntity {

        /**
         * 业务代码
         * 01-早餐
         * 02-午餐
         * 03-晚餐
         * 04-宽带上网
         * 05-服务费
         * 06-政府税
         * 99-特殊早餐，有效日期内生效，优先级高于01早餐
         * 当99特殊早餐和01早餐同时存在时，需要根据特殊早餐的有效日期判断哪种早餐生效，
         * 即在特殊早餐有效日期内99特殊早餐生效，有效日期外01早餐生效。
         */
        private String typeCode;

        /**
         * 描述
         * 附加服务描述，代理不想解析的话，可以直接显示该描述
         */
        private String description;

        /**
         * 是否包含在房费中
         * false-不包含 true-包含，例如业务代码为早餐时，false即为不含早，true为含早
         */
        private Boolean isInclude;

        /**
         * 包含的份数
         */
        private Integer amount;

        /**
         * 货币代码
         */
        // TODO: 枚举
        private String currencyCode;

        /**
         * 单价默认选项
         */
        private PriceOption priceOption;

        /**
         * 单价
         * 视PriceOption表示金额或比例，比例值保存的百分数，不是最终的小数，例如 20%，则该字段保存为20
         */
        private BigDecimal price;

        /**
         * 是否单加
         * 目前只有早餐服务该字段有意义
         */
        private Boolean isExtAdd;

        /**
         * 单加单价默认选项
         */
        private PriceOption extOption;

        /**
         * 单加单价
         * 视 extOption 不同表示金额或比例值, 比例值保存的百分数，不是最终的小数 例如 20%， 则该字段保存为20
         */
        private BigDecimal extPrice;

        /**
         * 开始日期
         * 特殊早餐有效日期
         */
        private String startDate;

        /**
         * 结束日期
         * 特殊早餐有效日期
         */
        private String endDate;

        /**
         * 周有效设置
         * 特殊早餐有效日期
         */
        private String weekSet;

        public ValueAddEntity(ELongHotelDataRpResp.Hotel.RatePlan.ValueAdd elongValueAdd) {
            this.typeCode = elongValueAdd.getTypeCode();
            this.description = elongValueAdd.getDescription();
            this.isInclude = elongValueAdd.getIsInclude();
            this.amount = elongValueAdd.getAmount();
            this.currencyCode = elongValueAdd.getCurrencyCode();
            this.priceOption = elongValueAdd.getPriceOption();
            this.price = elongValueAdd.getPrice();
            this.isExtAdd = elongValueAdd.getIsExtAdd();
            this.extOption = elongValueAdd.getExtOption();
            this.extPrice = elongValueAdd.getExtPrice();
            this.startDate = elongValueAdd.getStartDate();
            this.endDate = elongValueAdd.getEndDate();
            this.weekSet = elongValueAdd.getWeekSet();
        }
    }

    /**
     * Meal节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MealEntity {

        /**
         * 餐食类型
         * 01-默认餐食
         * 02-带有效期范围的餐食
         * 当02和01同时存在时，入住日在02餐食有效日期内则02餐食生效，入住日在02范围内解析是无餐食也不看01默认餐食了；否则有效日期外01默认餐食生效。
         * 02餐食可能有多条，01默认餐食最多1条。
         * （注：此节点为餐食的原始规则节点，hotel.detail#Meals为移位后的到天餐食结果表格。以下2种情况需要移位：
         * 1、入住日期内全部为固定餐食，
         * 2、入住日期内全部为半固定餐食且固定餐食类型一样时，固定餐食早餐、午餐需向后移一天展示；动态餐食分别与入住日期对应，不需要后移一天）
         */
        private String type;

        /**
         * 是否包含在房费中
         * false-不包含 true-包含，false即为不含餐食，true为含餐食
         */
        private Boolean isInclude;

        /**
         * 早餐份数
         */
        private Integer numberOfBreakfast;

        /**
         * 午餐份数
         */
        private Integer numberOfLunch;

        /**
         * 晚餐份数
         */
        private Integer numberOfDinner;

        /**
         * 餐食种类数量
         * 表示早餐份数、午餐份数、晚餐份数这三个字段大于0的字段数量之和；最小为0，最大为3
         */
        private Integer numberOfTypeMeal;

        /**
         * 可选餐食种类数量
         * 当NumberOfTypeMeal>NumberOfOptionalMeal时，表示动态餐食，有以下几种情况：
         * 1、早午晚餐，三选一、三选二
         * 2、早午餐，二选一
         * 3、午晚餐，二选一
         * 4、早晚餐，二选一
         * 当NumberOfTypeMeal=NumberOfOptionalMeal时，表示固定餐食，有以下几种情况：
         * 1、早+午+晚
         * 2、早+午
         * 3、早+晚
         * 4、午+晚
         * 5、只有早/午/晚
         * NumberOfTypeMeal为0或NumberOfOptionalMeal为0，均为无餐食
         */
        private Integer numberOfOptionalMeal;

        /**
         * 可选餐食类型
         * 表示可选餐食的类型，多个类型以“,”分割，只有当餐食为固定+动态餐食时才会有值
         * 比如餐食为早餐两份+午餐2份或晚餐2份(到店2选1)，该字段有值，为"Lunch,Dinner"
         */
        private String optionalMeals;

        /**
         * 描述
         * 餐食描述
         */
        private String description;

        /**
         * 早餐描述
         */
        private String describeOfBreakfast;

        /**
         * 午餐描述
         */
        private String describeOfLunch;

        /**
         * 晚餐描述
         */
        private String describeOfDinner;

        /**
         * 开始日期
         * 02餐食的有效日期；01默认餐食时此字段均为空
         */
        private String startDate;

        /**
         * 结束日期
         * 02餐食的有效日期；01默认餐食时此字段均为空
         */
        private String endDate;

        /**
         * 周有效设置
         * 02餐食的有效日期；01默认餐食时此字段均为空
         */
        private String weekSet;

        public MealEntity(ELongHotelDataRpResp.Hotel.RatePlan.Meal elongMeal) {
            this.type = elongMeal.getType();
            this.isInclude = elongMeal.getIsInclude();
            this.numberOfBreakfast = elongMeal.getNumberOfBreakfast();
            this.numberOfLunch = elongMeal.getNumberOfLunch();
            this.numberOfDinner = elongMeal.getNumberOfDinner();
            this.numberOfTypeMeal = elongMeal.getNumberOfTypeMeal();
            this.numberOfOptionalMeal = elongMeal.getNumberOfOptionalMeal();
            this.optionalMeals = elongMeal.getOptionalMeals();
            this.description = elongMeal.getDescription();
            this.describeOfBreakfast = elongMeal.getDescribeOfBreakfast();
            this.describeOfLunch = elongMeal.getDescribeOfLunch();
            this.describeOfDinner = elongMeal.getDescribeOfDinner();
            this.startDate = elongMeal.getStartDate();
            this.endDate = elongMeal.getEndDate();
            this.weekSet = elongMeal.getWeekSet();
        }
    }

    /**
     * DrrRule节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DrrRuleEntity {

        /**
         * 关联的房型
         * 表示这个DrrRule关联的销售房型
         */
        private String roomTypeIds;

        /**
         * 产品促销规则类型代码
         */
        private DRRTypeCode typeCode;

        /**
         * 描述
         */
        private String description;

        /**
         * 日期类型
         */
        private DateType dateType;

        /**
         * 促销生效开始日期
         */
        private String startDate;

        /**
         * 促销生效结束日期
         */
        private String EndDate;

        /**
         * 提前几天
         */
        private Integer dayNum;

        /**
         * 连住几天
         */
        private Integer checkInNum;

        /**
         * 每连住几晚
         */
        private Integer everyCheckInNum;

        /**
         * 最后几天
         */
        private Integer lastDayNum;

        /**
         * 第几晚及以后优惠
         */
        private Integer whichDayNum;

        /**
         * 按金额或按比例来优惠
         * Cash-金额 Scale-比例
         */
        private CashScale cashScale;

        /**
         * 按金额或比例优惠的数值
         * 当CashScale为Percent时，该值保存的为百分数，例如30%
         */
        private BigDecimal deductNum;

        /**
         * 星期有效设置
         * 日期符合Weekset中的周设置，才享受 feetype所对应的价格
         * 仅DRRStayWeekDay和DRRCheckInWeekDay的时候使用
         */
        private String weekSet;

        /**
         * 关联的房型
         */
        private FeeType feeType;

        public DrrRuleEntity(ELongHotelDataRpResp.Hotel.RatePlan.DrrRule elongDrrRule) {
            this.roomTypeIds = elongDrrRule.getRoomTypeIds();
            this.typeCode = elongDrrRule.getTypeCode();
            this.description = elongDrrRule.getDescription();
            this.dateType = elongDrrRule.getDateType();
            this.startDate = elongDrrRule.getStartDate();
            this.EndDate = elongDrrRule.getEndDate();
            this.dayNum = elongDrrRule.getDayNum();
            this.checkInNum = elongDrrRule.getCheckInNum();
            this.everyCheckInNum = elongDrrRule.getEveryCheckInNum();
            this.lastDayNum = elongDrrRule.getLastDayNum();
            this.whichDayNum = elongDrrRule.getWhichDayNum();
            this.cashScale = elongDrrRule.getCashScale();
            this.deductNum = elongDrrRule.getDeductNum();
            this.weekSet = elongDrrRule.getWeekSet();
            this.feeType = elongDrrRule.getFeeType();
        }
    }

    /**
     * Coupon节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CouponEntity {

        /**
         * 有效开始日期
         */
        private String effectiveDateFrom;

        /**
         * 有效结束日期
         */
        private String effectiveDateTo;

        /**
         * 最高上限值
         * 根据用户优惠券的情况实行抵扣，抵扣不能超过这个数额
         */
        private BigDecimal couponRedemptionUpperLimit;

        /**
         * 类型
         * 1 - 返现
         * 9 - 预付立减
         */
        private Integer promotionType;

        /**
         * 关联的房型
         */
        private String roomTypeId;

        /**
         * 关联的产品
         */
        private Integer ratePlanId;

        public CouponEntity(ELongHotelDataRpResp.Hotel.RatePlan.Coupon elongCoupon) {
            this.effectiveDateFrom = elongCoupon.getEffectiveDateFrom();
            this.effectiveDateTo = elongCoupon.getEffectiveDateTo();
            this.couponRedemptionUpperLimit = elongCoupon.getCouponRedemptionUpperLimit();
            this.promotionType = elongCoupon.getPromotionType();
            this.roomTypeId = elongCoupon.getRoomTypeId();
            this.ratePlanId = elongCoupon.getRatePlanId();
        }
    }

    /**
     * ServiceTimePolicyInfo节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceTimePolicyInfoEntity {

        /**
         * 服务开始日期
         * 酒店或供应商能处理订单的时间，无数据代表全天服务
         * 预订当天入住且预订时间不在服务时间范围内的产品不可展示销售给客人，避免不必要的投诉。
         */
        private String startDate;

        /**
         * 服务结束日期
         * 酒店或供应商能处理订单的时间，无数据代表全天服务
         * 预订当天入住且预订时间不在服务时间范围内的产品不可展示销售给客人，避免不必要的投诉。
         */
        private String endDate;

        /**
         * 服务周有效
         * 周日:0,周一:1,周二:2,周三:3,周四:4,周五:5,周六:6
         * 在服务开始结束日期范围内，周无效代表不可服务
         */
        private List<Integer> weekEffective;

        /**
         * 服务开始时间
         * 可服务的时间段，如果end_time小于start_time，则end_time表示的是次日的时间
         */
        private String startTime;

        /**
         * 服务结束时间
         * 可服务的时间段，如果end_time小于start_time，则end_time表示的是次日的时间
         */
        private String endTime;

        public ServiceTimePolicyInfoEntity(ELongHotelDataRpResp.Hotel.RatePlan.ServiceTimePolicyInfo elongServiceTimePolicyInfo) {
            this.startDate = elongServiceTimePolicyInfo.getStartDate();
            this.endDate = elongServiceTimePolicyInfo.getEndDate();
            this.weekEffective = elongServiceTimePolicyInfo.getWeekEffective();
            this.startTime = elongServiceTimePolicyInfo.getStartTime();
            ;
            this.endTime = elongServiceTimePolicyInfo.getEndTime();
            ;
        }
    }

    /**
     * TimeRushRule节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRushRuleEntity {

        /**
         * 规则生效开始日期
         * 限时抢产品规则，只对产品类型为限时抢的产品生效。
         * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
         */
        private String startDate;

        /**
         * 规则生效开始时间
         * 限时抢产品规则，只对产品类型为限时抢的产品生效。
         * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
         */
        private String bookingStartTime;

        /**
         * 规则生效结束日期
         * 限时抢产品规则，只对产品类型为限时抢的产品生效。
         * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
         */
        private String endDate;

        /**
         * 规则生效结束时间
         * 限时抢产品规则，只对产品类型为限时抢的产品生效。
         * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
         */
        private String bookingEndTime;

        /**
         * 限时抢开始时间
         * 限时抢产品规则，只对产品类型为限时抢的产品生效。
         * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
         */
        private String startTime;

        /**
         * 限时抢结束时间
         * 限时抢产品规则，只对产品类型为限时抢的产品生效。
         * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
         */
        private String endTime;

        public TimeRushRuleEntity(ELongHotelDataRpResp.Hotel.RatePlan.TimeRushRule elongTimeRushRule) {
            this.startDate = elongTimeRushRule.getStartDate();
            this.bookingStartTime = elongTimeRushRule.getBookingStartTime();
            this.endDate = elongTimeRushRule.getEndDate();
            this.bookingEndTime = elongTimeRushRule.getBookingEndTime();
            this.startTime = elongTimeRushRule.getStartTime();
            this.endTime = elongTimeRushRule.getEndTime();
        }
    }
}

