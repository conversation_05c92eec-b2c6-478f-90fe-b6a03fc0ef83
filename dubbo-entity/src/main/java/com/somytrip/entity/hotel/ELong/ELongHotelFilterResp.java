package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: ELongHotelFilterResp
 * @Description: 同程艺龙筛选项信息响应
 * @Author: shadow
 * @Date: 2024/3/1 10:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelFilterResp {

    /**
     * 筛选项列表
     */
    @NotNull
    @JsonProperty("hotelFilterInfos")
    private List<HotelFilterInfo> hotelFilterInfos;

    /**
     * 筛选项节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HotelFilterInfo {

        /**
         * 筛选项类型
         * 3-品牌
         * 4-行政区
         * 5-商圈
         * 6-POI（大学、风景区、汽车站、火车站等）
         * 1007-支付类型
         * 1008-星级
         * 1010-房间可容纳人数
         * 1011-设施
         * 1012-主题
         * 1016-酒店特色
         * 1020-评分
         * 1888-热门搜索
         * 1889-热门酒店
         */
        @NotNull
        @JsonProperty("typeId")
        private Integer typeId;

        /**
         * 类型排序位置
         * 表示此类型排在第几个展示，可以无视该字段 废弃
         */
        @Nullable
        @JsonProperty("appWeight")
        private Integer appWeight;

        /**
         * 筛选项中文名
         */
        @NotNull
        @JsonProperty("nameCn")
        private String nameCn;

        /**
         * 筛选项英文名称
         */
        @Nullable
        @JsonProperty("nameEn")
        private String nameEn;

        /**
         * 筛选项类型中文名称
         * 一般是空字符串，可以忽略
         */
        @Nullable
        @JsonProperty("typeNameCn")
        private String typeNameCn;

        /**
         * 筛选项类型英文名称
         * 一般是空字符串，可以忽略
         */
        @Nullable
        @JsonProperty("typeNameEn")
        private String typeNameEn;

        /**
         * 用户分布信息
         * 描述本筛选项有多少用户选择，只有(TypeId=5)商圈有值
         */
        @Nullable
        @JsonProperty("userDistribution")
        private String userDistribution;

        /**
         * 简介
         */
        @Nullable
        @JsonProperty("introduce")
        private String introduce;

        /**
         * 筛选项外部排序
         * 表示本条筛选项在筛选项中的位置，数值越大越靠前，可以忽略，一般要求分销商自行排序。
         */
        @Nullable
        @JsonProperty("hotFilterWeightOutSide")
        private Integer hotFilterWeightOutSide;

        /**
         * 筛选项内部排序
         */
        @Nullable
        @JsonProperty("hotFilterWeightInSide")
        private Integer hotFilterWeightInSide;

        /**
         * 是否含有子节点
         * 表示本筛选项是否还有子筛选项，例如选择了故宫，还可以向下级选择北海公园、天安门等
         */
        @NotNull
        @JsonProperty("hasSubNode")
        private Boolean hasSubNode;

        /**
         * 递归子节点
         * HasSubNode为true时有值，表示本筛选项包含的子筛选项列表，节点类型与本节点一样
         */
        @Nullable
        @JsonProperty("subHotelFilterInfos")
        private List<HotelFilterInfo> subHotelFilterInfos;

        /**
         * 选择模式
         * 表示本筛选项所在的列表中是否支持多选，一般不用关注本字段
         * true-支持多选
         * false-不支持多选
         */
        @Nullable
        @JsonProperty("selectMode")
        private Boolean selectMode;

        /**
         * 父类型中文名称
         */
        @Nullable
        @JsonProperty("parentTypeName")
        private String parentTypeName;

        /**
         * 本筛选项的唯一标识
         * 仅用于标识本筛选项
         */
        @NotNull
        @JsonProperty("id")
        private Integer id;

        /**
         * 筛选项V4ID
         * 当TypeId为4时有值，可用于hotel.list接口DistrictId入参
         */
        @Nullable
        @JsonProperty("idV4")
        private String idV4;

        /**
         * 城市ID
         * 只有一级筛选项列表中的节点才可能有值，表示筛选项所属城市ID，可用于hotel.list接口CityId参数
         */
        @Nullable
        @JsonProperty("idCityV4")
        private String idCityV4;

        /**
         * 筛选项中文附加信息
         * 一般为空
         */
        @Nullable
        @JsonProperty("nameExtCn")
        private String nameExtCn;

        /**
         * 筛选项英文附加信息
         * 一般为空
         */
        @Nullable
        @JsonProperty("nameExtEn")
        private String nameExtEn;

        /**
         * 流程ID
         * 当前无用字段
         */
        @Nullable
        @JsonProperty("uniqueID")
        private Integer uniqueId;

        /**
         * 坐标信息
         * TypeId为6时有效
         */
        @Nullable
        @JsonProperty("poiInfo")
        private HotelGeoInfo poiInfo;

        /**
         * 价格区间
         * 表示筛选项可检索到的酒店中的最小价格和最大价格，目前一般为空
         */
        @Nullable
        @JsonProperty("hotelPricePair")
        private HotelPriceInfo hotelPricePair;

        /**
         * 多边形
         * 表示多边形区域
         */
        @Nullable
        @JsonProperty("regionInfo")
        private HotelGeoInfo regionInfo;

        /**
         * 是否含有非热门子节点
         * 表示本筛选项是否包含非热门的子节点
         */
        @NotNull
        @JsonProperty("hasExceptHotSubNode")
        private Boolean hasExceptHotSubNode;

        /**
         * 非热门子节点递归列表
         * HasExceptHotSubNode为true时有值，节点类型与本节点类型相同
         */
        @NotNull
        @JsonProperty("subExceptHotHotelFilterInfos")
        private List<HotelFilterInfo> subExceptHotHotelFilterInfos;

        /**
         * HotelGeoInfo节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class HotelGeoInfo {

            /**
             * 纬度
             */
            @NotNull
            @JsonProperty("lat")
            private BigDecimal lat;

            /**
             * 经度
             */
            @NotNull
            @JsonProperty("lng")
            private BigDecimal lng;
        }

        /**
         * HotelPriceInfo节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class HotelPriceInfo {

            /**
             * 最低价
             */
            @NotNull
            @JsonProperty("min")
            private BigDecimal min;

            /**
             * 最高价
             */
            @NotNull
            @JsonProperty("max")
            private BigDecimal max;
        }
    }
}
