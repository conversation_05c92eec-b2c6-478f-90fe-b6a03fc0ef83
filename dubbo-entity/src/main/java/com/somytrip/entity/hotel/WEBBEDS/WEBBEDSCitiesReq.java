package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName: WEBBEDSCitiesReq
 * @Description: WEBBEDS城市列表请求参数
 * @Author: shadow
 * @Date: 2024/3/19 2:16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WEBBEDSCitiesReq extends WEBBEDSBaseReq {

    /**
     * 国家ID
     */
    @JsonProperty("country_id")
    private Integer countryId;

    /**
     * 是否之返回包含酒店的城市
     */
    @JsonProperty("contain_hotels_only")
    private boolean containHotelsOnly = false;
}
