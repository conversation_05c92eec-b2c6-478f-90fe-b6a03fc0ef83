package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @projectName: hotel-service
 * @package: com.somytrip.entity.hotel
 * @className: CityMappingNCNBEntity
 * @author: shadow
 * @description: 捷旅城市映射表
 * @date: 2024/3/26 16:33
 * @version: 1.0
 */
@Data
@TableName("city_mapping_ncnb")
public class CityMappingNCNBEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 561738783943468081L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 通用城市code
     */
    private String cityCode;

    /**
     * 城市ID
     */
    private String cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 省份ID
     */
    private String provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 国家ID
     */
    private String countryId;

    /**
     * 国家名称
     */
    private String countryName;

    @TableField("is_active")
    private Boolean isActive;
}
