package com.somytrip.entity.hotel.NCNB;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: NCNBRespBodyEntity
 * @Description:
 * @Author: shadow
 * @Date: 2023/10/13 15:41
 */
@Data
@NoArgsConstructor
public class NCNBRespBody {

    /**
     * 方法名
     */
    @JsonProperty("ActionName")
    private String ActionName;
    /**
     * MessageInfo Message和Code
     * - Code
     * - Description
     */
    @JsonProperty("MessageInfo")
    private JSONObject MessageInfo;
    /**
     * Data 数据
     */
    @JsonProperty("Data")
    private JSONObject Data;

    public NCNBRespBody(JSONObject jsonObject) {
        if (jsonObject != null) {
            this.ActionName = jsonObject.getString("ActionName");
            this.MessageInfo = jsonObject.getJSONObject("MessageInfo");
            this.Data = jsonObject.getJSONObject("Data");
        }
    }
}
