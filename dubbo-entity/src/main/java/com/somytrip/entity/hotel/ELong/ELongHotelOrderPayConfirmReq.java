package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;

/**
 * @ClassName: ELongHotelOrderPayConfirmReq
 * @Description: 同程艺龙酒店订单支付确认请求参数
 * @Author: shadow
 * @Date: 2024/3/7 9:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderPayConfirmReq {

    /**
     * 订单号
     */
    @NotNull
    @JsonProperty("OrderId")
    private Long orderId;

    /**
     * 短信验证码
     * 信用卡支付使用此属性
     */
    @NotNull
    @JsonProperty("SMSCode")
    private String smsCode;

    /**
     * 支付金额
     * 人民币价格
     */
    @NotNull
    @JsonProperty("Amount")
    private BigDecimal amount;
}
