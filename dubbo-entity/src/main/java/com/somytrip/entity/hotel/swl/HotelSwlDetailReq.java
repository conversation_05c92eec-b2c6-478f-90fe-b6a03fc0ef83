package com.somytrip.entity.hotel.swl;

import com.somytrip.entity.dto.hotel.HotelDateRangeV2;
import lombok.Data;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.hotel.swl
 * @className: HotelSwlDetailReq
 * @author: shadow
 * @description: 深文旅查询酒店详情Req
 * @date: 2024/12/13 9:54
 * @version: 1.0
 */
@Data
public class HotelSwlDetailReq {

    /**
     * 秘钥id
     */
    private String secretId;

    /**
     * 10位时间戳
     */
    private String timestamp;

    /**
     * 数字签名
     * md5(md5(secretKey+secretId)+timestamp)md5采用32位小写
     */
    private String sign;

    /**
     * 酒店ID
     */
    private String hotelId;

    /**
     * 日期区间
     */
    private HotelDateRangeV2 dateRange;

    /**
     * 成人数
     */
    private Integer numberOfAdults;

    /**
     * 房间数
     */
    private Integer numberOfRooms;

    /**
     * 语言
     */
    private String lang = "zh_CN";
}
