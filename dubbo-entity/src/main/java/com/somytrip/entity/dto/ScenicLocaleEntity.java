package com.somytrip.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.dto
 * @className: ScenicLocaleEntity
 * @author: shadow
 * @description: 景区多语言Entity
 * @date: 2024/11/14 17:05
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "scenic_locale")
public class ScenicLocaleEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1205738802506863119L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 景区ID
     */
    private Integer scenicId;

    /**
     * 景区名称
     */
    private String name;

    /**
     * 地址
     */
    private String address;

    /**
     * 介绍
     */
    private String brief;

    /**
     * 营业时间
     */
    private String businessHours;

    /**
     * 语言
     */
    private String lang;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
