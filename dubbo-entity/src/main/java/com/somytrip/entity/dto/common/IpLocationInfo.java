package com.somytrip.entity.dto.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.dto.common
 * @className: IpLocationInfo
 * @author: li<PERSON><PERSON>
 * @description: IP地址位置信息DTO
 * @date: 2025-01-04 16:00:00
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IpLocationInfo {

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 运营商
     */
    private String isp;
}
