package com.somytrip.entity.dto.community;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.dto.community
 * @className: CommunityUserInfoEditParam
 * @author: shadow
 * @description: 编辑用户信息参数
 * @date: 2024/12/5 15:45
 * @version: 1.0
 */
@Data
public class CommunityUserInfoEditParam {

    private Long userId;
    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    private String bucketName;

    /**
     * 搜旅号
     */
    private String smtNo;

    /**
     * 自我介绍
     */
    private String selfIntroduction;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 职业标签
     */
    private List<String> careers;


    /**
     * 年龄标签是否展示，true-展示，false-不展示
     */
    private Boolean ageDisplayFlag;
    /**
     * 地区标签是否展示，true-展示，false-不展示
     */
    private Boolean areaDisplayFlag;

    /**
     * 生日标签是否展示，true-展示，false-不展示
     */
    private Boolean birthdayDisplayFlag;

    /**
     * 职业标签是否展示，true-展示，false-不展示
     */
    private Boolean careersDisplayFlag;

    /**
     * 星座标签是否展示，true-展示，false-不展示
     */
    private Boolean constellationDisplayFlag;

    /**
     * 年龄信息是否展示标志，true-展示，false-不展示
     */
    private Boolean genderDisplayFlag;
}
