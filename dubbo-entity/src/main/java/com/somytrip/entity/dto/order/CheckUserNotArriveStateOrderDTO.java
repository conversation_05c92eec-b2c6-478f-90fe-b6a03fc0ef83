package com.somytrip.entity.dto.order;

import com.somytrip.entity.enums.order.OrderBusinessTypeEnum;
import com.somytrip.entity.enums.order.OrderStatusEnums;
import com.somytrip.entity.enums.order.OrderUserTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 查询指定用户未到达指定状态前的订单
 * @author: pigeon
 * @created: 2024-04-08 9:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CheckUserNotArriveStateOrderDTO {
    private OrderBusinessTypeEnum businessType;
    private OrderUserTypeEnum userType;
    private String userSign;
    private OrderStatusEnums orderStatus;
}
