package com.somytrip.entity.dto.community.feedback;

import com.somytrip.entity.FileInfo;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.dto.community.feedback
 * @className: FeedbackCommentDto
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/8/4 16:25
 * @version: 1.0
 */

@Data
public class FeedbackCommentDto {

    /**
     * 所属反馈ID
     */
    private Long feedbackId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 评论内容，最多300字符
     */
    @NotBlank(message = "评论内容不能为空")
    @Size(max = 300, message = "评论内容不能超过300字符")
    private String content;

    /**
     * 图片列表
     */
    private List<FileInfo> images;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * IP归属省份名称
     */
    private String ipProvinceName;

    /**
     * IP归属城市名称
     */
    private String ipCityName;

    /**
     * 父级评论ID（回复评论时使用）
     */
    private Long parentId;

    /**
     * 被回复用户ID
     */
    private String replyToUserId;

    /**
     * 被回复用户类型：1-官方；2-作者；3-用户
     */
    private Integer replyToUserType;
}