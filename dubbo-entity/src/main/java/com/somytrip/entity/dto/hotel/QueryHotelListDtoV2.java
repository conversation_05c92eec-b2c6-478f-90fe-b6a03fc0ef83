package com.somytrip.entity.dto.hotel;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.enums.hotel.HotelSortType;
import com.somytrip.entity.enums.itinerary.ActivitySortType;
import com.somytrip.entity.enums.itinerary.ItineraryBudgetLevel;
import com.somytrip.entity.itinerary.ItineraryNearbyActivitySearchParam;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @ClassName: queryHotelListDto
 * @Description: 查询酒店列表dto
 * @Author: shadow
 * @Date: 2023/11/17 14:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryHotelListDtoV2 {

    /**
     * 酒店来源(NCNB: 捷旅, ELong: 同程, smt: 搜旅)
     */
    private HotelOrigin hotelOrigin;

    /**
     * 城市ID
     */
    @NotNull(message = "{api.hotel.non-city-code}")
    private String cityCode;

    /**
     * 酒店ID
     */
    private String hotelId;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 距离(距离目标经纬度的距离，单位: 米)
     */
    private Integer distance;

    /**
     * 星级
     */
    private List<Integer> star;

    /**
     * 币种, 默认 CNY
     */
    private String currency;

    /**
     * 排序属性(RE：默认，SC：评分，DI：距离，PR：价格，ST：星级)
     * 废弃
     */
    @Deprecated
    private String sortCode;

    /**
     * 排序方式(0: ASC, 1: DESC)
     * 废弃
     */
    @Deprecated
    private Integer sortType;

    /**
     * 排序筛选code
     * 2.1新增
     */
    private String sort = HotelSortType.Default.getCode();

    /**
     * 搜索关键字
     */
    private String queryText;

    /**
     * 最低价
     */
    private Integer lowRate;

    /**
     * 最高价
     */
    private Integer highRate;

    /**
     * 预算等级
     */
    private ItineraryBudgetLevel budgetLevel;

    /**
     * 入住人数
     */
    private Integer checkinPersonAmount;

    /**
     * 入住时间区间
     */
    private HotelDateRangeV2 dateRange;

    /**
     * 筛选
     */
    private List<QueryHotelListFilter> filters;

    /**
     * 分页参数
     */
    @NotNull(message = "分页参数不能为空")
    private PaginationDto pagination = new PaginationDto();

    /**
     * 是否包含测试数据
     */
    private boolean includeTest = true;

    /**
     * 国际化语种
     */
    private Locale locale;

    /**
     * 最低价
     */
    private BigDecimal minPriceConfig;

    /**
     * 目标ID列表
     */
    private Map<HotelOrigin, List<String>> targetIdMap;

    /**
     * 配置最低价
     */
    private Integer configMinPrice;

    /**
     * 配置最高价
     */
    private Integer configMaxPrice;

    /**
     * ai字段-top值
     */
    private Integer topN;
    /**
     * 数据来源：1-ai来源
     */
    private Integer dataSource;
    /**
     * ai排序字段，1-从低到高，2-从高到低
     */
    private Integer aiSort;
    /**
     * ai城市编码列表
     */
    private List<String> aiCityCodes;

    public QueryHotelListDtoV2(HotelOrigin hotelOrigin, String hotelId) {
        this.hotelOrigin = hotelOrigin;
        this.hotelId = hotelId;
    }

    public QueryHotelListDtoV2(ItineraryNearbyActivitySearchParam searchParam) {
        this.cityCode = searchParam.getCityCode();
        this.lon = searchParam.getLon();
        this.lat = searchParam.getLat();
        this.distance = searchParam.getDistance();
        this.queryText = searchParam.getQueryText();
        this.budgetLevel = ItineraryBudgetLevel.fromValue(searchParam.getBudgetLevel());
        this.pagination = searchParam.getPagination();
        ActivitySortType sortType = ActivitySortType.fromValue(searchParam.getSort());
        if (ActivitySortType.DISTANCE_ASC.equals(sortType)) {
            this.sort = HotelSortType.DistanceAsc.getCode();
        } else if (ActivitySortType.SCORE_DESC.equals(sortType)) {
            this.sort = HotelSortType.RateDesc.getCode();
        }
        if (searchParam.getDatetime() != null) {
            HotelDateRangeV2 dateRange = new HotelDateRangeV2();
            LocalDateTime checkIn = searchParam.getDatetime();
            dateRange.setCheckIn(checkIn.toLocalDate());
            dateRange.setCheckOut(checkIn.plusDays(1).toLocalDate());
            this.dateRange = dateRange;
        }
        String activityIds = searchParam.getActivityIds();
        if (StrUtil.isNotBlank(activityIds)) {
            List<String> idsStr = new ArrayList<>(Arrays.asList(activityIds.split(",")));
            Map<HotelOrigin, List<String>> targetIdMap = new HashMap<>();
            for (String item : idsStr) {
                String[] split = item.split("\\|");
                HotelOrigin hotelOrigin = HotelOrigin.getOriginFromName(split[0]);
                String hotelId = split[1];
                List<String> curIds = targetIdMap.get(hotelOrigin);
                if (curIds == null) {
                    curIds = new ArrayList<>();
                }
                curIds.add(hotelId);
                targetIdMap.put(hotelOrigin, curIds);
            }
            this.targetIdMap = targetIdMap;
        }
    }

    /**
     * 是否有定位
     *
     * @return boolean
     * <AUTHOR>
     * @date 2025/4/17 15:16
     */
    public Boolean hasLocation() {
        return StrUtil.isAllNotBlank(lon, lat);
    }

    @Data
    public static class QueryHotelListFilter {

        /**
         * 筛选项类型code
         */
        private String typeCode;

        /**
         * 筛选项code
         */
        private String filterCode;
    }
}
