package com.somytrip.entity.dto.community.feedback;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.dto.community.feedback
 * @className: FeedbackCommentListReq
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/8/4 17:38
 * @version: 1.0
 */
@Data
public class FeedbackCommentListReq {

    /**
     * 反馈ID
     */
    @NotNull(message = "反馈ID不能为空")
    private Long feedbackId;

    /**
     * 页码，默认1
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     * 第一次点击展开查看3条留言，第二次开始每次点击展开查看8条
     */
    private Integer pageSize = 8;

    /**
     * 当前用户ID
     */
    private Long userId;
}
