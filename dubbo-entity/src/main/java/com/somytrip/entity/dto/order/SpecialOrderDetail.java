package com.somytrip.entity.dto.order;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.somytrip.bean.order.InsuranceOrderInfoDTO;
import com.somytrip.entity.enums.SensitiveStrategy;
import com.somytrip.entity.enums.order.OrderCustomStateEnum;
import com.somytrip.entity.enums.order.OrderStatusEnum;
import com.somytrip.entity.special.CreateSpecialOrder;
import com.somytrip.entity.special.SpecialProduct;
import com.somytrip.target.Sensitive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Description: 订单列表-特色游detail
 * @author: pigeon
 * @created: 2024-03-30 10:15
 */
@Data
public class SpecialOrderDetail {
    @Data
    public static class Item {
        private Integer price;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date paymentDeadline;
        /**
         * 人数
         */
        private Integer guestCount;
        /**
         * 商品详情
         */
        private JSONObject specialProduct;
    }

    @Data
    public static class Detail {
        /**
         * 倒计时
         */
        private long countDown;
        private String orderNo;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTime;
        private ProductDesc productDesc;
        private List<Guest> guests;
        private String orderStatus;
        private OrderCustomStateEnum orderCustomState;
        private InsuranceOrderInfoDTO insuranceOrderInfoDTO;

        public Detail(OrderMasterDTO orderMasterDTO, OrderBigFieldDTO orderBigFieldDTO, OrderCustomStateEnum orderCustomStateEnum) {
            this.countDown = 0;
            this.orderNo = orderMasterDTO.getOrderNo();
            this.orderStatus = OrderStatusEnum.values()[orderMasterDTO.getOrderStatus() - 1].name();
            this.orderCustomState = orderCustomStateEnum;
            this.createTime = orderMasterDTO.getCreateTime();
            JSONObject jsonObject = orderBigFieldDTO.getField1();
            if (jsonObject != null) {
                this.guests = jsonObject.getJSONArray("guestList").stream().map(s -> new Guest(JSONObject.from(s)
                        .toJavaObject(CreateSpecialOrder.Guest.class))).toList();
                this.productDesc = new ProductDesc(
                        jsonObject.getJSONObject("specialProduct").toJavaObject(SpecialProduct.Vo.class),
                        this.guests.size(),
                        jsonObject.getString("remark")
                );
            }
        }
    }

    @Data
    public static class ProductDesc {
        private String name;
        private String price;
        private String[] timeRange;
        private String remark;

        public ProductDesc(SpecialProduct.Vo vo, int guestNum, String remark) {
            this.name = vo.getName();
            this.price = NumberUtil.mul(vo.getPrice(), Integer.toString(guestNum)).toString();
            this.remark = remark;
            this.timeRange = new String[]{DateUtil.formatDate(vo.getStartTime()), DateUtil.formatDate(vo.getEndTime())};
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Guest {
        private String name;
        private String mobile;
        @Sensitive(strategy = SensitiveStrategy.ID_CARD_MASK)
        private String creditNo;
        private String creditType;

        public Guest(CreateSpecialOrder.Guest guest) {
            this.name = guest.getName();
            this.mobile = guest.getMobile();
            this.creditNo = DesensitizedUtil.idCardNum(guest.getCreditNo(), 1, 1);
            this.creditType = guest.getCreditType().name();
        }
    }
}
