package com.somytrip.entity.dto.community.feedback;

import com.somytrip.entity.FileInfo;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.dto.community.feedback
 * @className: FeedbackPlatformDto
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/8/4 14:56
 * @version: 1.0
 */
@Data
public class FeedbackPlatformDto {

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * Ip地址
     */
    private String ipAddress;

    /**
     * IP归属省份名称
     */
    private String ipProvinceName;

    /**
     * IP归属城市名称
     */
    private String ipCityName;

    /**
     * 标题，最多30字符
     */
    private String title;

    /**
     * 内容，最多300字符
     */
    @NotBlank
    private String content;

    /**
     * 图片列表，最多9张
     */
    private List<FileInfo> images;

    /**
     * 联系方式，86手机号格式
     */
    private String phone;

    /**
     * 分类：1-建议、2-bug
     */
    private Integer category;

    /**
     * 来源：1-小程序、2-app
     */
    private Integer source;
}
