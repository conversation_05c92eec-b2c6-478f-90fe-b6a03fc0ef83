package com.somytrip.entity.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;

@Data
@TableName("member_card_user")
public class MemberCardUser {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 用户表ID 外键
     */
    private Integer userId;
    /**
     * 卡片类型
     */
    private Integer cardType;
    /**
     * 卡片名称
     */
    private String cardName;
    /**
     * 数量
     */
    private Integer surplusCt;
    /**
     * vip开始时间
     */
    @TableField(jdbcType = JdbcType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd HH:MM:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * vip结束时间
     */
    @TableField(jdbcType = JdbcType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd HH:MM:ss", timezone = "GMT+8")
    private Date endTime;
    /**
     * 创建时间
     */
    @TableField(jdbcType = JdbcType.TIMESTAMP)
    @JsonFormat(pattern = "yyyy-MM-dd HH:MM:ss", timezone = "GMT+8")
    private Date createTime;

    public MemberCardUser(Integer userId,
                          Integer cardType,
                          Date startTime,
                          Date endTime) {
        this.userId = userId;
        this.cardType = cardType;
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public MemberCardUser(Integer userId,
                          Integer cardType,
                          String cardName,
                          Integer surplusCt,
                          Date startTime,
                          Date endTime,
                          Date createTime) {
        super();
        this.userId = userId;
        this.cardType = cardType;
        this.cardName = cardName;
        this.surplusCt = surplusCt;
        this.startTime = startTime;
        this.endTime = endTime;
        this.createTime = createTime;
    }

    public MemberCardUser() {
        super();
    }
}
