package com.somytrip.entity.dto.common;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.dto.user.SomytripUsers;
import com.somytrip.exception.BusinessException;
import com.somytrip.utils.RSAUtils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.regex.Pattern;

@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
@Valid
public class VerificationCodeDto {
    @NotBlank
    private String phone;
    @NotBlank
    private String timestamp;
    @NotBlank
    private String sign;
    @NotBlank
    private String deviceId;
    private String countryCode = "+86";
    private String type = "1";

    public SomytripUsers.Mobile mobile() {
        // 正则表达式
        String regex = "^(?:\\+?86)?1[3-9]\\d{9}$";
        // 创建 Pattern 对象
        Pattern pattern = Pattern.compile(regex);
        if (pattern.matcher(countryCode + phone).matches()) {
            return new SomytripUsers.Mobile(countryCode, phone);
        } else {
//            throw new RuntimeException("手机号不符合规范");
            throw new BusinessException("api.user.phone-wrong");
        }
    }

    public boolean check() {
        String signFields = "deviceId,phone,timestamp";
        StringBuilder stringBuilder = new StringBuilder();
        Arrays.stream(signFields.split(","))
                .sorted().forEach(field -> {
                    stringBuilder.append(field);
                    stringBuilder.append('=');
                    stringBuilder.append(getValue(field));
                    stringBuilder.append('&');
                });

        stringBuilder.setLength(stringBuilder.length() - 1);
        String decryptCont = RSAUtils.decrypt(sign);
        return StringUtils.equals(decryptCont, stringBuilder.toString());
    }

    public String getValue(String fieldName) {
        try {
            Field field = ReflectUtil.getField(this.getClass(), fieldName);
            return StrUtil.str(ReflectUtil.getFieldValue(this, field), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("class取值报错");
//            throw new RuntimeException("class取值报错");
            throw new BusinessException("api.common.system.error");
        }
    }
}
