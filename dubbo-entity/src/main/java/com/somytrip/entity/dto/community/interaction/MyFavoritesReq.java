package com.somytrip.entity.dto.community.interaction;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class MyFavoritesReq {

    /**
     * 第N页，默认：1
     */
    private Integer pageIndex = 1;
    /**
     * 分页大小，默认：10
     */
    private Integer pageSize = 10;
    /**
     * 用户加密id，为空默认查询当前登录者信息
     */
    private String userSecretId;

    private Long userId;
}
