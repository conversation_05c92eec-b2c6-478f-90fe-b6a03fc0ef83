package com.somytrip.entity.dto.user;

import com.baomidou.mybatisplus.annotation.*;
import com.somytrip.entity.enums.user.UserBlackTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description: 封禁用户名单
 * @author: pigeon
 * @created: 2025-05-09 9:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("user_black_record")
public class UserBlackRecordDTO {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private UserBlackTypeEnum type;
    private String value;
    private String remark;
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    @TableLogic(value = "1", delval = "0")
    private Boolean active;
}
