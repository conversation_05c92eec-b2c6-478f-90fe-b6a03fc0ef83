package com.somytrip.entity.dto.dynamic;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("wx_user_dynamic_report")
public class DynamicReport {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 用户ID 外键
     */
    private Integer uid;
    /**
     * 动态ID 外键
     */
    private Integer dynamicId;
    /**
     * 类型
     */
    private String type;
    /**
     * 内容
     */
    private String content;
    /**
     * 图片列表
     */
    private String pics;
    /**
     * 创建时间
     */
    private Date createTime;
}
