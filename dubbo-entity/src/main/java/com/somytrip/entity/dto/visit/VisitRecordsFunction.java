package com.somytrip.entity.dto.visit;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class VisitRecordsFunction {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 函数名称
     */
    private String functionName;
    /**
     * 数量
     */
    private Integer number;
    /**
     * 年
     */
    private String year;
    /**
     * 月
     */
    private String month;
    /**
     * 日
     */
    private String day;
    /**
     * 小时
     */
    private String hour;
    /**
     * 逻辑删除
     */
    private boolean isDelete;
    /**
     * 创建时间
     */
    private String createTime;
}
