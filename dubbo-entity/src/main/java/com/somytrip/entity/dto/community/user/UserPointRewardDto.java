package com.somytrip.entity.dto.community.user;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.dto.community.user
 * @className: UserPointRewardDto
 * @author: li<PERSON><PERSON>
 * @description: 用户积分奖励请求参数
 * @date: 2025/8/5 15:20
 * @version: 1.0
 */
@Data
public class UserPointRewardDto {

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 奖励积分数量
     */
    @NotNull(message = "奖励积分不能为空")
    private Integer rewardPoints;

    /**
     * 变动类型
     */
    @NotBlank(message = "变动类型不能为空")
    private String changeType;

    /**
     * 业务操作类型
     */
    @NotBlank(message = "业务操作类型不能为空")
    private String businessType;

    /**
     * 关联业务ID
     */
    private String relatedId;

    /**
     * 变动描述
     */
    private String description;

    /**
     * 来源平台：1-小程序 2-APP
     */
    private Integer sourcePlatform = 1;

    /**
     * 备注信息
     */
    private String remark;
}