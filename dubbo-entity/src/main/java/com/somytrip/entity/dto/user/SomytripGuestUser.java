package com.somytrip.entity.dto.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.SensitiveStrategy;
import com.somytrip.handler.SqlAesTypeHandler;
import com.somytrip.target.Sensitive;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 游客信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("somytrip_mapper_user")
public class SomytripGuestUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * APP类型，APPLET-WX、IOS、Android、H5、WEB
     */
    @TableField(value = "app_type")
    private String appType;

    /**
     * 设备id，微信小程序传unionId，原生传具体设备id
     */
    @TableField(value = "device_no")
    private String deviceNo;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 手机号
     */
    @Sensitive(strategy = SensitiveStrategy.PHONE_MASK)
    @TableField(value = "mobile", typeHandler = SqlAesTypeHandler.class)
    private String mobile;

    /**
     * 设备型号，如：nova7，iPhone 8 plus
     */
    @TableField(value = "device_type")
    private String deviceType;

    /**
     * 应用渠道类型（预留字段），如：huawei-华为应用市场，xiaomi-小米应用市场，honor-荣耀
     */
    @TableField(value = "channel_type")
    private String channelType;

    /**
     * 逻辑删除标识
     */
    @TableField(value = "is_del")
    private Boolean isDel;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    @TableField(value = "version_code")
    private String versionCode;
}
