package com.somytrip.entity.dto.feedback;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.dto.feedback
 * @className: SubmitFeedbackDto
 * @author: l<PERSON><PERSON><PERSON>
 * @description: 提交反馈建议请求DTO
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
@Data
public class SubmitFeedbackDto {

    /**
     * 建议标题（选填），限制30个字符
     */
    @Size(max = 30, message = "标题不能超过30个字符")
    private String title;

    /**
     * 建议内容（必填），限制300个字符
     */
    @NotBlank(message = "建议内容不能为空")
    @Size(max = 300, message = "建议内容不能超过300个字符")
    private String content;

    /**
     * 添加图片（选填），限制9张图片
     */
    @Size(max = 9, message = "图片数量不能超过9张")
    private List<String> images;

    /**
     * 联系方式（选填），86手机号格式
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 分类：1-建议、2-bug
     */
    private Integer category = 1;

    /**
     * 来源：1-小程序、2-app
     */
    private Integer source = 1;
}
