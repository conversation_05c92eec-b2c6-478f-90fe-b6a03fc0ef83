package com.somytrip.entity.dto.order.search;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description: 订单搜索实体
 * @author: pigeon
 * @created: 2025-05-30 14:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "order_search_record", autoResultMap = true)
public class OrderSearchRecordDTO<T> {
    @TableId(type = IdType.INPUT)
    private String orderNo;
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private T data;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
