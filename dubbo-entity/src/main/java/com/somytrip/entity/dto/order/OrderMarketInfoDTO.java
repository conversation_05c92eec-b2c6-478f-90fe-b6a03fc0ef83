package com.somytrip.entity.dto.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Description: 营销信息
 * @author: pigeon
 * @created: 2023-11-02 11:35
 */
@Data
@TableName("order_market_info")
public class OrderMarketInfoDTO {
    /**
     * 订单ID
     */
    @TableId(type = IdType.INPUT)
    private String orderNo;
    /**
     * 优惠编码
     */
    private String discountCode;
    /**
     * 优惠模式
     */
    private String discountMode;
    /**
     * 使用金额
     */
    private Integer usePrice;
    /**
     * 优惠类型
     */
    private String discountType;
    /**
     * 优惠类型
     */
    private String discountClassify;
    /**
     * 文案描述
     */
    private String remark;
    /**
     * 优惠金额
     */
    private Integer discountPrice;
}
