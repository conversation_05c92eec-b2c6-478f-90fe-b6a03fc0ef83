package com.somytrip.entity.dto.tourism;

import com.somytrip.entity.vo.flight.AirLineCodeVo;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-07 9:42
 */
@Data
public class TourismConfigList {

    private List<TravelCommonConfig> mustTypes;
    private List<TravelCommonConfig> tpTypes;
    private List<TravelCommonConfig> travelRhythm;
    private List<TravelCommonConfig> travelMates;
    private List<TravelPreferenceConfig> travelPreferences;
    private List<TravelCommonConfig> travelBudgets;
    private List<AirLineCodeVo.NameValue> staticUrlPrefix;
    private List<AirLineCodeVo.NameValue> flightReservation;
    private String insuranceEnabled;

    /**
     * 根据value获取旅行伴侣名称
     *
     * @param value value
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/4/14 9:31
     */
    public String getTravelMateNameFromValue(String value) {
        if (this.travelMates == null || this.travelMates.isEmpty()) {
            return null;
        }

        return this.travelMates.stream()
                .filter(travelMate -> Objects.equals(value, travelMate.getValue()))
                .findFirst()
                .map(TravelCommonConfig::getName)
                .orElse(null);
    }

    /**
     * 根据value获取旅行预算名称
     *
     * @param value value
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/4/14 9:34
     */
    public String getTravelBudgetNameFromValue(String value) {
        if (this.travelBudgets == null || this.travelBudgets.isEmpty()) {
            return null;
        }

        return this.travelBudgets.stream()
                .filter(travelBudget -> Objects.equals(value, travelBudget.getValue()))
                .findFirst()
                .map(TravelCommonConfig::getName)
                .orElse(null);
    }

    @Data
    public static class TravelCommonConfig {
        private String name;
        private String value;
        private String checkedIcon;
        private String uncheckedIcon;
    }

    @Data
    public static class TravelPreferenceConfig {
        private String name;
        private String value;
        private String icon;
        //        private Integer min = 1;
//        private Integer max = 3;
        private Integer defaultIndex = 1;
        private String[] displayArr = {"不感兴趣", "感兴趣", "必去"};
    }
}
