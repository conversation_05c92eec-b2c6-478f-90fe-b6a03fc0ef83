package com.somytrip.entity.dto.community;

import com.somytrip.entity.FileInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ReportInfoReq {

    private Long userId;
    /**
     * 私信举报，开始消息id
     */
    private Integer beginMsgId;
    /**
     * 被举报业务主键Id（游记举报，评论举报必填）
     */
    private String businessId;
    /**
     * 私信举报，结束消息id
     */
    private Integer endMsgId;
    /**
     * 举报图片地址
     */
    private List<FileInfo> images;
    /**
     * 举报描述
     */
    private String reportDescription;
    /**
     * 举报原因
     */
    private String reportReason;
    /**
     * 举报类型：1-私信举报，2-游记举报，3-评论举报
     */
    private Integer reportType;
    /**
     * 被举报用户加密Id（私信举报必填）
     */
    private String userSecretId;
}
