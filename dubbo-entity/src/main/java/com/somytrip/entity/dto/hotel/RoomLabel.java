package com.somytrip.entity.dto.hotel;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.somytrip.entity.enums.hotel.roomlabel.RoomLabelEnum;
import com.somytrip.entity.vo.hotel.RoomListVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName: RoomLabel
 * @Description: 房间标签
 * @Author: shadow
 * @Date: 2024/3/26 10:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoomLabel {

    /**
     * 标签code
     */
    private String code;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 是否支持筛选
     */
    private boolean filterable = false;

    public RoomLabel(RoomLabelEnum roomLabelEnum) {
        this.code = roomLabelEnum.getGeneralCode();
        this.name = roomLabelEnum.getName();
        this.filterable = roomLabelEnum.isFilterable();
    }

    public static List<RoomLabel> mergeAndDistinctFromRatePlanVoList(List<RoomListVo.RoomListRatePlanVo> ratePlanVoList) {
        return ratePlanVoList.stream()
                .map(RoomListVo.RoomListRatePlanVo::getRatePlanLabels)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .distinct()
                .sorted(Comparator.comparing(RoomLabel::getCode))
                .toList();
    }

    /**
     * 提取并合并可筛选的房间标签
     *
     * @param roomLabelList 待提取的房间标签列表
     * @return java.util.Item<com.somytrip.entity.dto.hotel.RoomLabel> 提取出的房间标签列表
     * <AUTHOR>
     * @date 2024/3/26 11:30
     */
    public static List<RoomLabel> extractAndMergeFilterableLabels(
            List<RoomLabel> generalRoomLabelList,
            List<RoomLabel> roomLabelList
    ) {
        if (ObjectUtils.isNull(generalRoomLabelList, roomLabelList)) {
            return new ArrayList<>();
        }

        List<RoomLabel> mergedList = new ArrayList<>(generalRoomLabelList);
        List<RoomLabel> filterableList = roomLabelList.stream()
                .filter(RoomLabel::isFilterable)
                .toList();
        mergedList.addAll(filterableList);

        return mergedList.stream()
                .distinct()
                .sorted(Comparator.comparing(RoomLabel::getCode))
                .toList();
    }
}
