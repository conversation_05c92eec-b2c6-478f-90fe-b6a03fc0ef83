package com.somytrip.entity.dto.community.feedback;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.dto.community.feedback
 * @className: FeedbackPlatformListReq
 * @author: l<PERSON><PERSON><PERSON>
 * @description:
 * @date: 2025/8/4 17:37
 * @version: 1.0
 */

@Data
public class FeedbackPlatformListReq {

    /**
     * 页码，默认1
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum = 1;

    /**
     * 每页大小，默认10，最大50
     */
    private Integer pageSize = 10;

    /**
     * 排序类型：1-热门排序，2-最新排序，默认1
     */
    private Integer sortType = 1;

    /**
     * 当前用户ID（用于判断是否已支持）
     */
    private Long userId;
}
