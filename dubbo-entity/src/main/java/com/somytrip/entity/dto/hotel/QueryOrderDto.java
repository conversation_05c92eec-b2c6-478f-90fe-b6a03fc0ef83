package com.somytrip.entity.dto.hotel;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @ClassName: QueryOrderDto
 * @Description: 查询订单dto
 * @Author: shadow
 * @Date: 2023/12/2 9:10
 */
@Data
public class QueryOrderDto {

    @NotNull(message = "api.hotel.order.non-uid")
    private Long uid;

    private String orderId;

    private String customerOrderId;

    private String orderStatus;

    private String dateType;

    private HotelDateRange dateRange;

    private PaginationDto pagination = new PaginationDto();
}
