package com.somytrip.entity.train;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity
 * @className: TrainStationEntity
 * @author: shadow
 * @description: 火车站Entity
 * @date: 2025/5/6 17:30
 * @version: 1.0
 */
@Data
@TableName("train_stations")
public class TrainStationEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -248599291361264379L;

    /**
     * 火车站ID(自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 火车站code
     */
    private String code;

    /**
     * 火车站名称
     */
    private String name;

    /**
     * 火车站拼音
     */
    private String pinyin;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 城市ID
     */
    private Integer cityId;
}
