package com.somytrip.entity.train;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.train
 * @className: LeftTicketDto
 * @author: shadow
 * @description: 余票Dto
 * @date: 2025/5/7 16:35
 * @version: 1.0
 */
@Data
public class LeftTicketDto {

    /**
     * 车次密文
     */
    private String secretStr;

    /**
     * train_no
     * 240000G10336
     */
    private String trainNo;

    /**
     * 车次号
     */
    private String stationTrainCode;

    /**
     * 始发站车站code
     */
    private String startStationTelecode;

    /**
     * 终点站车站code
     */
    private String endStationTelecode;

    /**
     * 上车站车站code
     */
    private String fromStationTelecode;

    /**
     * 下车站车站code
     */
    private String toStationTelecode;

    /**
     * 出发时间
     * 06:20
     */
    private LocalTime startTime;

    /**
     * 到达时间
     * 11:58
     */
    private LocalTime arriveTime;

    /**
     * 历时
     * 05:38
     */
    private String lishi;

    private String canWebBuy;

    private String ypInfo;

    /**
     * 出发日期
     * 20250610
     */
    private String startTrainDate;

    private String trainSeatFeature;

    private String locationCode;

    private String fromStationNo;

    private String toStationNo;

    private String isSupportCard;

    private String controlledTrainFlag;

    /**
     * 座位列表
     */
    private List<Seat> seats;

    /**
     * 起价
     */
    private BigDecimal startPrice;

    private String ypEx;

    /**
     * 座位类型字符串
     * JOIO
     */
    private String seatTypes;

    private String exchangeTrainFlag;

    private String houbuTrainFlag;

    private String houbuSeatLimit;

    /**
     * 座位价格字符串
     * J071100021O047400021I090100021O047403026
     */
    private String ypInfoNew;

    private String dwFlag;

    private String stopcheckTime;

    private String countryFlag;

    private String localArriveTime;

    private String localStartTime;

    /**
     * 卧铺价格字符串
     * J307110J108300J207580I309010I110190
     */
    private String bedLevelInfo;

    private String seatDiscountInfo;

    private String saleTime;

    private String fromStationName;

    private String toStationName;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Seat {

        /**
         * 座位类型枚举
         */
        private SeatType seatType;

        /**
         * 座位类型
         */
        private String seatName;

        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 数量
         */
        private String num;
    }
}
