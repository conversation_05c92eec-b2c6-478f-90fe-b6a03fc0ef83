package com.somytrip.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class PayOrder {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 用户表ID 外键
     */
    private String userId;
    /**
     * 订单号
     */
    private String outTradeNo;
    /**
     * 商品ID
     */
    private String shopId;
    /**
     * 商品数量
     */
    private Integer productCt;
    /**
     * 描述
     */
    private String description;
    /**
     * 价格 单位不确定
     */
    private Integer amount;
    /**
     * 交易的货币
     */
    private String currency;
    /**
     * 微信支付-小程序支付特有的字段，技术债
     */
    private String openid;
    /**
     * 状态 技术债 目前只有 创建订单、支付成功、退款成功 三个值
     */
    private String status;
    /**
     * 创建时间
     */
    private Date createTime;

    public PayOrder(String userId, String outTradeNo, String shopId, Integer productCt, String description, Integer amount, String currency,
                    String openid, String status, Date createTime) {
        super();
        this.userId = userId;
        this.outTradeNo = outTradeNo;
        this.shopId = shopId;
        this.productCt = productCt;
        this.description = description;
        this.amount = amount;
        this.currency = currency;
        this.openid = openid;
        this.status = status;
        this.createTime = createTime;
    }

    public PayOrder() {
        super();
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public void setProductCt(Integer productCt) {
        this.productCt = productCt;
    }
}
