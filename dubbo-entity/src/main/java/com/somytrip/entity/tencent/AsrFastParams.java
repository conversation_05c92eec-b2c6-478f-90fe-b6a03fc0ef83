package com.somytrip.entity.tencent;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * @Description: Asr极速版URL参数
 * @author: pigeon
 * @created: 2024-07-16 9:51
 */
@Data
public class AsrFastParams {

    /**
     * 用户在腾讯云注册账号 AppId 对应的 SecretId，可以进入 API 密钥管理页面获取。
     */
    @JSONField(name = "secretid")
    private String secretId;

    /**
     * 引擎模型类型。
     * 支持多种语言和场景，例如：8k_zh, 8k_en, 16k_zh等。
     */
    @JSONField(name = "engine_type")
    private String engineType;

    /**
     * 音频格式。支持 wav、pcm、ogg-opus、speex、silk、mp3、m4a、aac、amr。
     */
    @JSONField(name = "voice_format")
    private String voiceFormat;

    /**
     * 当前UNIX时间戳，如果与当前时间相差超过3分钟，会报签名失败错误。
     */
    private String timestamp;

    /**
     * 是否开启说话人分离（目前支持中文普通话引擎），默认为0。
     */
    @JSONField(name = "speaker_diarization")
    private Integer speakerDiarization;

    /**
     * 热词表 id。如不设置该参数，自动生效默认热词表；如设置了该参数，那么将生效对应的热词表。
     */
    @JSONField(name = "hotword_id")
    private String hotWordId;

    /**
     * 自学习模型 id。如设置了该参数，将生效对应的自学习模型。
     */
    @JSONField(name = "customization_id")
    private String customizationId;

    /**
     * 是否过滤脏词（目前支持中文普通话引擎），默认为0。
     */
    @JSONField(name = "filter_dirty")
    private Integer filterDirty;

    /**
     * 是否过滤语气词（目前支持中文普通话引擎），默认为0。
     */
    @JSONField(name = "filter_modal")
    private Integer filterModal;

    /**
     * 是否过滤标点符号（目前支持中文普通话引擎），默认为0。
     */
    @JSONField(name = "filter_punc")
    private Integer filterPunc;

    /**
     * 是否进行阿拉伯数字智能转换，默认为1。
     */
    @JSONField(name = "convert_num_mode")
    private Integer convertNumMode;

    /**
     * 是否显示词级别时间戳，默认为0。
     */
    @JSONField(name = "word_info")
    private Integer wordInfo;

    /**
     * 是否只识别首个声道，默认为1。
     */
    @JSONField(name = "first_channel_only")
    private Integer firstChannelOnly;

    /**
     * 单标点最多字数，取值范围：[6，40]。默认为0，不开启该功能。
     */
    @JSONField(name = "sentence_max_length")
    private Integer sentenceMaxLength;

    /**
     * 临时热词表：该参数用于提升识别准确率。
     */
    @JSONField(name = "hotword_list")
    private String hotWordList;

    /**
     * 支持 pcm 格式的8k音频在与引擎采样率不匹配的情况下升采样到16k后识别。
     */
    @JSONField(name = "input_sample_rate")
    private Integer inputSampleRate;

}
