package com.somytrip.entity.po.amap;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.train.TrainStationLocaleEntity;
import lombok.Data;

import java.util.Map;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.po.amap
 * @className: AMapTransitReq
 * @author: shadow
 * @description: 高德地图公交路径规划Req
 * @date: 2025/2/11 10:59
 * @version: 1.0
 */
@Data
public class AMapTransitReq {

    /**
     * 高德Key
     */
    private String key;

    /**
     * 起点经纬度
     * 经度在前，纬度在后，经度和纬度用","分割，经纬度小数点后不得超过6位。
     */
    private String origin;

    /**
     * 目的地经纬度
     * 经度在前，纬度在后，经度和纬度用","分割，经纬度小数点后不得超过6位。
     */
    private String destination;

    /**
     * 起点 POI ID
     * 1、起点 POI ID 与起点经纬度均填写时，服务使用起点 POI ID；
     * 2、该字段必须和目的地 POI ID 成组使用。
     */
    @JsonProperty("originpoi")
    private String originPoi;

    /**
     * 目的地 POI ID
     * 1、目的地 POI ID 与目的地经纬度均填写时，服务使用目的地  POI ID；
     * 2、该字段必须和起点 POI ID 成组使用。
     */
    @JsonProperty("destinationpoi")
    private String destinationPoi;

    /**
     * 起点所在行政区域编码
     * 仅支持 adcode，参考行政区域编码表
     */
    private String ad1;

    /**
     * 终点所在行政区域编码
     * 仅支持 adcode，参考行政区域编码表
     */
    private String ad2;

    /**
     * 起点所在城市
     * 仅支持 citycode，相同时代表同城，不同时代表跨城
     */
    private String city1;

    /**
     * 目的地所在城市
     * 仅支持 citycode，相同时代表同城，不同时代表跨城
     */
    private String city2;

    /**
     * 公共交通换乘策略
     * 可选值：
     * 0：推荐模式，综合权重，同高德APP默认
     * 1：最经济模式，票价最低
     * 2：最少换乘模式，换乘次数少
     * 3：最少步行模式，尽可能减少步行距离
     * 4：最舒适模式，尽可能乘坐空调车
     * 5：不乘地铁模式，不乘坐地铁路线
     * 6：地铁图模式，起终点都是地铁站
     * （地铁图模式下 originpoi 及 destinationpoi 为必填项）
     * 7：地铁优先模式，步行距离不超过4KM
     * 8：时间短模式，方案花费总时间最少
     */
    private String strategy;

    /**
     * 返回方案条数
     * 可传入1-10的阿拉伯数字，代表返回的不同条数。
     */
    @JsonProperty("AlternativeRoute")
    private Integer alternativeRoute;

    /**
     * 地铁出入口数量
     * 0：只返回一个地铁出入口
     * 1：返回全部地铁出入口
     */
    @JsonProperty("multiexport")
    private Integer multiExport;

    /**
     * 考虑夜班车
     * 可选值：
     * 0：不考虑夜班车
     * 1：考虑夜班车
     */
    @JsonProperty("nightflag")
    private Integer nightFlag;

    /**
     * 请求日期
     * 例如:2013-10-28
     */
    private String date;

    /**
     * 请求时间
     * 例如:9-54
     */
    private String time;

    /**
     * 返回结果控制
     * show_fields 用来筛选 response 结果中可选字段。show_fields 的使用需要遵循如下规则：
     * 1、具体可指定返回的字段类请见下方返回结果说明中的“show_fields”内字段类型；
     * 2、多个字段间采用“,”进行分割；
     * 3、show_fields 未设置时，只返回基础信息类内字段。
     */
    @JsonProperty("show_fields")
    private String showFields;

    /**
     * 数字签名
     */
    private String sig;

    /**
     * 返回结果格式类型
     * 可选值：JSON
     */
    private String output;

    /**
     * 回调函数
     * callback 值是用户定义的函数名称，此参数只在 output 参数设置为 JSON 时有效。
     */
    private String callback;

    /**
     * 车站名称多语言字典
     */
    @JsonIgnore
    private Map<String, TrainStationLocaleEntity> trainStationLocaleDict;
}
