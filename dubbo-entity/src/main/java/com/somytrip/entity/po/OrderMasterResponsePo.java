package com.somytrip.entity.po;

import com.somytrip.entity.dto.order.OrderMasterDTO;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2023-12-01 10:05
 */
@Data
public class OrderMasterResponsePo {
    private OrderMasterDTO parentOrder;
    private List<OrderMasterDTO> childOrderList;

    public OrderMasterResponsePo() {
    }

    public OrderMasterResponsePo(OrderMasterDTO parentOrder, List<OrderMasterDTO> childOrderList) {
        this.parentOrder = parentOrder;
        this.childOrderList = childOrderList;
    }
}
