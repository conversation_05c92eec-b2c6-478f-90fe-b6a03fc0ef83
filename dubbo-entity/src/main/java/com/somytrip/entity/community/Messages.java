package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 消息信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Data
@ToString
@TableName("messages")
public class Messages implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 发送用户id
     */
    private Long senderId;

    /**
     * 接受用户id
     */
    private Long receiverId;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 发送时间
     */
    private LocalDateTime sentTime;

    /**
     * 已读时间
     */
    private LocalDateTime readTime;

    /**
     * 读取标记，0-未读，1-已读
     */
    private Boolean isRead;

    /**
     * 消息类型：1-私信
     */
    private Integer type;

    /**
     * 会话id
     */
    private String sessionId;
}
