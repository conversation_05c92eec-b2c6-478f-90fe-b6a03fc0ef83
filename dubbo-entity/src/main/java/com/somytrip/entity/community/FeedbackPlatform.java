package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.somytrip.entity.FileInfo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.community
 * @className: FeedbackPlatform
 * @author: lijunqi
 * @description: 反馈平台表
 * @date: 2025/8/4
 * @version: 1.0
 */
@Data
@TableName("feedback_platform")
public class FeedbackPlatform implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键，id自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 发布者ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 标题，最多30字符
     */
    private String title;

    /**
     * 内容，最多300字符
     */
    private String content;

    /**
     * 图片URL列表，最多9张
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<FileInfo> images;

    /**
     * 联系方式，86手机号格式
     */
    private String phone;

    /**
     * 发布者IP地址
     */
    @TableField("ipaddress")
    private String ipAddress;

    /**
     * IP归属省份名称
     */
    @TableField("IpProvinceName")
    private String ipProvinceName;

    /**
     * IP归属城市名称
     */
    @TableField("IpCityName")
    private String ipCityName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 支持/点赞数
     */
    @TableField("support_count")
    private Integer supportCount;

    /**
     * 状态：1-待处理 2-处理中 3-已解决 4-已关闭
     */
    private Integer status;

    /**
     * 分类：1-建议、2-bug
     */
    private Integer category;

    /**
     * 来源：1-小程序、2-app
     */
    private Integer source;

    /**
     * 删除标记：0-正常 1-已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 是否已采纳：0-未采纳 1-已采纳
     */
    @TableField("is_adopted")
    private Integer isAdopted;

}

