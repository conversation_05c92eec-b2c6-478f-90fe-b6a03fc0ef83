package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.somytrip.entity.FileInfo;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.community
 * @className: FeedbackComment
 * @author: lijunqi
 * @description: 反馈评论表
 * @date: 2025/8/4 14:09
 * @version: 1.0
 */
@Data
@TableName("feedback_comment")
public class FeedbackComment implements Serializable {


    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键，id自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属反馈ID(feedback_platform.id)
     */
    @TableField("feedback_id")
    private Long feedbackId;

    /**
     * 发布者ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户类型：1-官方；2-作者；3-用户
     */
    @TableField("user_type")
    private Integer userType;

    /**
     * 内容，最多300字符
     */
    private String content;

    /**
     * 图片URL列表
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<FileInfo> images;

    /**
     * 发布者IP地址
     */
    @TableField("ipaddress")
    private String ipAddress;

    /**
     * IP归属省份名称
     */
    @TableField("IpProvinceName")
    private String ipProvinceName;

    /**
     * IP归属城市名称
     */
    @TableField("IpCityName")
    private String ipCityName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 父级评论ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 被回复用户ID
     */
    @TableField("reply_to_user_id")
    private String replyToUserId;

    /**
     * 被回复用户类型：1-官方；2-作者；3-用户
     */
    @TableField("reply_to_user_type")
    private Integer replyToUserType;

    /**
     * 删除标记：0-正常 1-已删除
     */
    @TableField("del_flag")
    private Integer delFlag;
}
