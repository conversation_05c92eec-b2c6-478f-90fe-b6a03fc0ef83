package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.somytrip.entity.FileInfo;
import lombok.Data;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.community
 * @className: FeedbackComment
 * @author: lijunqi
 * @description: 反馈评论表实体类
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
@Data
@TableName("feedback_comment")
public class FeedbackComment implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键，id自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 所属反馈ID(feedback_platform.id)
     */
    private Integer feedbackId;

    /**
     * 发布者ID
     */
    private String userId;

    /**
     * 用户类型：1-官方；2-作者；3-用户
     */
    private Integer userType;

    /**
     * 内容，最多300字符
     */
    private String content;

    /**
     * 图片URL列表
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<FileInfo> images;

    /**
     * 发布者IP地址：省名
     */
    private String ip;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 父级评论ID
     */
    private Integer parentId;

    /**
     * 被回复用户ID
     */
    private String replyToUserId;

    /**
     * 被回复用户类型：1-官方；2-作者；3-用户
     */
    private Integer replyToUserType;

    /**
     * 删除标记：0-正常 1-已删除
     */
    private Integer delFlag;
}
