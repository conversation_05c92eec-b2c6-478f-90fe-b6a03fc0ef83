package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.community
 * @className: UserPoint
 * @author: lijunqi
 * @description: 用户积分信息实体类
 * @date: 2025/8/5 15:09
 * @version: 1.0
 */
@Data
@TableName("somytrip_user.user_point")
public class UserPoint {

    /**
     * 用户ID，外键关联user表
     */
    @TableId(type = IdType.INPUT)
    private String userId;

    /**
     * 当前积分余额
     */
    private Integer pointBalance;

    /**
     * 累计获得积分
     */
    private Integer totalEarned;

    /**
     * 累计消费积分
     */
    private Integer totalConsumed;

    /**
     * 积分等级
     */
    private Integer pointLevel;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}