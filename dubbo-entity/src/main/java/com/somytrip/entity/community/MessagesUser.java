package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 消息信息用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TableName("messages_user")
public class MessagesUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 发送用户id
     */
    private Long senderId;

    /**
     * 接受用户id
     */
    private Long receiverId;

    /**
     * session会话id
     */
    private String sessionId;

    /**
     * 删除标记，0-未删除，1-已删除
     */
    private Boolean delFlag;

    /**
     * 消息类型：1-私信
     */
    private Integer type;

    /**
     * 未读消息数量
     */
    private Integer unReadNum;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最后一条消息内容
     */
    private String lastContent;

}
