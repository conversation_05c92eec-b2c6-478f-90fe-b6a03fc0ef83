package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.community
 * @className: UserPointLog
 * @author: lijunqi
 * @description:
 * @date: 2025/8/5 15:30
 * @version: 1.0
 */
@Data
@TableName("somytrip_user.user_point_log")
public class UserPointLog {

    /**
     * 流水ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 积分变动值（正数为获得，负数为消费）
     */
    private Integer changeAmount;

    /**
     * 变动后的积分余额
     */
    private Integer balanceAfter;

    /**
     * 变动类型：feedback-反馈
     */
    private String changeType;

    /**
     * 业务操作类型：FEEDBACK_SUBMIT:反馈提交、FEEDBACK_ADOPT:反馈被采纳
     */
    private String businessType;

    /**
     * 关联业务ID（如反馈ID、评论ID）
     */
    private String relatedId;

    /**
     * 变动描述
     */
    private String description;

    /**
     * 来源平台：1-小程序 2-APP
     */
    private Integer sourcePlatform;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 备注信息
     */
    private String remark;


}
