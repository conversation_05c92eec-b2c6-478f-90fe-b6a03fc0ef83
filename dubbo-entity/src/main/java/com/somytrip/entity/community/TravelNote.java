package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.somytrip.entity.FileInfo;
import lombok.Data;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 游记信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Data
@ToString
@TableName("travel_notes")
public class TravelNote implements Serializable {

    @Serial
    private static final long serialVersionUID = 1137948265397820710L;

    /**
     * 主键，自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 游记序列号
     */
    private String travelNoteSn;

    /**
     * 攻略唯一id
     */
    private String itineraryId;

    /**
     * 游记标题
     */
    private String title;

    /**
     * 景点数
     */
    private Integer totalSpots;

    /**
     * 行程天数
     */
    private Integer totalDays;

    /**
     * 封面图地址url
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private FileInfo coverImage;
//
//    /**
//     * 图片集合
//     */
//    @TableField(typeHandler = Fastjson2TypeHandler.class)
//    private List<FileInfo> imageJson;

    /**
     * 游记内容
     */
    private String content;

    /**
     * 创建人
     */
    private Long userId;

    /**
     * 游记状态，1-待发布（审核中），2-（发布）审核成功，3-待确认（需要人工确认），4-下架（审核失败）
     */
    private Integer notesStatus;

    /**
     * 下架原因
     */
    private String reason;

    /**
     * 是否删除
     */
    @TableField(value = "is_del")
    private Boolean del;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}
