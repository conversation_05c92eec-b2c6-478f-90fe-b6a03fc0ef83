package com.somytrip.entity.tourism;

import com.somytrip.target.AtLeastOneNotNull;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: TourismSaveSchemeDto
 * @Description: 保存(更新)方案Dto
 * @Author: shadow
 * @Date: 2024/1/3 15:58
 */
@Data
@AtLeastOneNotNull(fieldNames = {"title", "content"}, message = "标题和内容不能都为空")
public class TourismSaveSchemeDto {

    /**
     * 方案序列号
     */
    @NotNull(message = "schemeSn is required")
    private String schemeSn;

    /**
     * 方案标题
     */
    private String title;

    /**
     * 修改内容
     */
    private List<Content> content;

    @Data
    public static class Content {

        private Integer level;

        private Integer day;

        private Integer time;

        private List<String> value;

        private List<TourismSearchingBudgetDto> budget;
    }
}
