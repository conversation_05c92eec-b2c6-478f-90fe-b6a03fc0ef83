package com.somytrip.entity.tourism;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: TourismEmergencyContactVo
 * @Description: 攻略紧急联络vo
 * @Author: shadow
 * @Date: 2024/1/4 11:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TourismEmergencyContactVo {

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 报警电话
     */
    private String alarmTel;

    /**
     * 旅游局电话
     */
    private String tourismAdTel;

    /**
     * 中国大使馆电话
     */
    private String chinaEmbassyTel;

    public TourismEmergencyContactVo(TourismEmergencyContactEntity entity) {
        // 国家名称
        setCountryName(entity.getCountryCn());
        // 报警电话
        setAlarmTel(entity.getAlarmTel());
        // 旅游局电话
        setTourismAdTel(entity.getTourismAdTel());
        // 中国大使馆电话
        setChinaEmbassyTel(entity.getChinaEmbassyTel());
    }
}
