package com.somytrip.entity.tourism;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@TableName(value = "plan_route_day")
public class PlanRouteDay {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 计划表ID 外键
     */
    private int routeId;
    /**
     * 当天位于总天数的 举例：1-10 中的 1 或 其他
     */
    private int dayNum;
    /**
     * 早餐
     */
    private String eat1;
    /**
     * 早上攻略
     */
    private String morning;
    /**
     * 午餐
     */
    private String eat2;
    /**
     * 下午攻略
     */
    private String afternoon;
    /**
     * 晚餐
     */
    private String eat3;
    /**
     * 晚上攻略
     */
    private String evening;
    /**
     * 酒店名称
     */
    private String hotel;
    /**
     * 酒店对象
     */
    private String hotelObj;

    private LocalDateTime createTime;

    public PlanRouteDay(int routeId, int dayNum, String eat1, String morning, String eat2, String afternoon,
                        String eat3, String evening, String hotel) {
        this.routeId = routeId;
        this.dayNum = dayNum;
        this.morning = morning;
        this.afternoon = afternoon;
        this.evening = evening;
        this.hotel = hotel;
        this.eat1 = eat1;
        this.eat2 = eat2;
        this.eat3 = eat3;
    }

    public PlanRouteDay(int routeId, int dayNum, String eat1, String morning, String eat2, String afternoon,
                        String eat3, String evening, String hotel, String hotelJson) {
        this.routeId = routeId;
        this.dayNum = dayNum;
        this.morning = morning;
        this.afternoon = afternoon;
        this.evening = evening;
        this.hotel = hotel;
        this.eat1 = eat1;
        this.eat2 = eat2;
        this.eat3 = eat3;
        this.hotelObj = hotelJson;
    }
}
