package com.somytrip.entity.tourism;

import com.somytrip.entity.vo.ScenicVo;
import com.somytrip.entity.vo.TourismHotelVo;
import lombok.Data;

import java.util.List;

/**
 * @ClassName: QueryTourismDayHotelDto
 * @Description: 查询攻略每日酒店dto
 * @Author: shadow
 * @Date: 2024/1/2 14:50
 */
@Data
public class QueryTourismDayHotelDto {

    private Integer level;

    private String[] hotelIds;

    private List<String> originIdList;

    private String lon;

    private String lat;

    private Integer cityId;

    private String[] times;

    private TourismHotelVo lastHotel;

    private ScenicVo nextScenic;

    public QueryTourismDayHotelDto() {
    }

    public QueryTourismDayHotelDto(Integer level, String lon, String lat, Integer cityId, String[] times, TourismHotelVo lastHotel, ScenicVo nextScenic) {
        this.level = level;
        this.lon = lon;
        this.lat = lat;
        this.cityId = cityId;
        this.times = times;
        this.lastHotel = lastHotel;
        this.nextScenic = nextScenic;
    }
}
