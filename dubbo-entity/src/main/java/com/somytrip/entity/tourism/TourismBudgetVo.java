package com.somytrip.entity.tourism;

import com.somytrip.entity.enums.tourism.ActivityTypeNameEnums;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @ClassName: TourismBudgetVo
 * @Description: 方案预算vo
 * @Author: shadow
 * @Date: 2024/1/5 11:11
 */
@Data
public class TourismBudgetVo {

    /**
     * 类型(1: 景区, 2: 餐厅, 3: 酒店)
     */
    private Integer type;

    /**
     * 类型名称(景区, 餐厅, 酒店)
     */
    private String typeName;

    /**
     * 预算
     */
    private String budget;

    public TourismBudgetVo() {
    }

    public TourismBudgetVo(Integer type) {
        this.type = type;
        this.typeName = ActivityTypeNameEnums.get(type);
    }

    public TourismBudgetVo(TourismSearchingBudgetDto dto) {
        this.type = dto.getType();
        this.typeName = ActivityTypeNameEnums.get(this.type) + "预算";
        this.budget = halfAdjust2Str(dto.getBudget());
    }

    public TourismBudgetVo(Integer type, String typeName, String budget) {
        this.type = type;
        this.typeName = typeName;
        this.budget = budget;
    }

    private String halfAdjust2Str(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return "";
        }
        return bigDecimal.setScale(2, RoundingMode.HALF_UP).toString();
    }
}
