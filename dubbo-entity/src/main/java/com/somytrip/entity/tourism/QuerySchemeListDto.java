package com.somytrip.entity.tourism;

import com.somytrip.entity.dto.hotel.PaginationDto;
import lombok.Data;

/**
 * @ClassName: QuerySchemeListDto
 * @Description: 查询方案列表
 * @Author: shadow
 * @Date: 2024/1/5 15:07
 */
@Data
public class QuerySchemeListDto {

    /**
     * 城市ID
     */
    private String cityId;

    /**
     * 方案标题
     */
    private String title;

    /**
     * 几天内
     */
    private Integer dayLimit;

    /**
     * 分页参数
     */
    private PaginationDto pagination;
}
