package com.somytrip.entity.tourism;

import lombok.Data;

/**
 * @ClassName: TourismActivityVo
 * @Description: 攻略方案活动Vo
 * @Author: shadow
 * @Date: 2024/1/2 11:02
 */
@Data
public class TourismActivityVo {

    /**
     * 类型(1: 景区, 2: 餐厅, 3: 酒店)
     */
    private Integer type;

    /**
     * 类型名称(景区, 餐厅, 酒店)
     */
    private String typeName;

    /**
     * 来源
     */
    private String origin;

    /**
     * 活动ID(如 type=1 则为景区ID...)
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 与下一活动地点的距离
     */
    private String distance;

    /**
     * 封面图片
     */
    private String coverImage;

    /**
     * 介绍
     */
    private String introduction;

    /**
     * 预算
     */
    private String budget;

    /**
     * 评分
     */
    private String score;
}
