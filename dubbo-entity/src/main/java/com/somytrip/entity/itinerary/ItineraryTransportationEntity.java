package com.somytrip.entity.itinerary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryTransportationEntity
 * @author: shadow
 * @description: 攻略交通数据Entity
 * @date: 2024/5/15 10:06
 * @version: 1.0
 */
@Data
@TableName(value = "itinerary_transportations", autoResultMap = true)
public class ItineraryTransportationEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 攻略ID
     */
    private Long itineraryId;

    /**
     * 攻略序列号
     */
    private String itinerarySn;

    /**
     * 预算等级
     */
    private Integer budgetLevel;

    /**
     * 每日数据ID
     */
    private Long itineraryDayId;

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 交通方式(1: 飞机, 2: 火车)
     */
    private Integer transportationMode;

    /**
     * 交通数据内容
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<ItineraryTransportationVo> transportationContent;

    /**
     * 是否删除
     */
    @TableField(value = "is_del")
    private boolean del;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public void convert4SaveAs() {
        this.id = null;
        this.createTime = null;
        this.updateTime = null;
    }
}
