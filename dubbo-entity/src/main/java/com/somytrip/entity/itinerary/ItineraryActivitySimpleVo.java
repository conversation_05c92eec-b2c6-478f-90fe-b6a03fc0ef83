package com.somytrip.entity.itinerary;

import cn.hutool.core.util.StrUtil;
import com.somytrip.utils.BigDecimalUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryActivitySimpleVo
 * @author: shadow
 * @description: 攻略活动简略vo
 * @date: 2024/4/20 16:30
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryActivitySimpleVo {

    /**
     * 活动类型(1: 景区, 2: 餐饮, 3: 酒店)
     */
    private Integer type;

    /**
     * 活动数据来源
     * (目前只支持酒店)
     */
    private String activityOrigin;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 主题ID列表
     */
    private List<Integer> themes = new ArrayList<>();

    /**
     * 评分
     */
    private BigDecimal score;

    /**
     * 星级
     */
    private Integer star;

    /**
     * 短介绍
     */
    private String shortIntro;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 图片列表(最多3张)
     */
    private List<String> images;

    /**
     * 价格(如: 免费、￥251元起)
     */
    private String startPrice;

    /**
     * 距离
     */
    private String distance;

    /**
     * 距离值(公里)
     */
    private BigDecimal distanceValue;

    /**
     * 预计停留时间数值
     */
    private BigDecimal stayTimeValue = BigDecimal.valueOf(2);

    public ItineraryActivitySimpleVo(ItineraryActivityVo activityVo) {
        this.type = activityVo.getType();
        this.activityOrigin = activityVo.getActivityOrigin();
        this.activityId = activityVo.getActivityId();
        this.activityName = activityVo.getActivityName();
        this.themes = activityVo.getThemes();
        if (StrUtil.isNotBlank(activityVo.getScore())) {
            this.score = BigDecimalUtil.halfAdjust(new BigDecimal(activityVo.getScore()), 1);
        }
        this.star = activityVo.getStar();
        this.shortIntro = activityVo.getShortIntro();
        this.address = activityVo.getAddress();
        this.lon = activityVo.getLon();
        this.lat = activityVo.getLat();
        this.cityCode = activityVo.getCityCode();
        this.images = activityVo.getImages();
        this.startPrice = activityVo.getStartPrice();
        this.stayTimeValue = activityVo.getStayTimeValue();
    }

    public ItineraryActivitySimpleVo(ItineraryActivityDto activityDto) {
        this.type = activityDto.getType();
        this.activityOrigin = activityDto.getActivityOrigin();
        this.activityId = activityDto.getActivityId();
        this.activityName = activityDto.getActivityName();
        this.themes = activityDto.getThemes();
        if (StrUtil.isNotBlank(activityDto.getScore())) {
            this.score = BigDecimalUtil.halfAdjust(new BigDecimal(activityDto.getScore()), 1);
        }
        this.star = activityDto.getStar();
        this.shortIntro = activityDto.getShortIntro();
        this.address = activityDto.getAddress();
        this.lon = activityDto.getLon();
        this.lat = activityDto.getLat();
        this.cityCode = activityDto.getCityCode();
        this.images = activityDto.getImages();
        this.startPrice = activityDto.getStartPrice();
        this.stayTimeValue = activityDto.getStayTimeValue();
    }
}
