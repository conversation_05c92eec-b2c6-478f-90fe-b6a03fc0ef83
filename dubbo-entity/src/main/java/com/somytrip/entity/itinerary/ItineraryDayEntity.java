package com.somytrip.entity.itinerary;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.somytrip.entity.vo.CitySimpleVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryDayEntity
 * @author: shadow
 * @description: 攻略每日数据Entity
 * @date: 2024/4/13 15:59
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "itinerary_days", autoResultMap = true)
public class ItineraryDayEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 攻略ID
     */
    private Long itineraryId;

    /**
     * 预算等级
     */
    private Integer budgetLevel;

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 周几
     */
    @Deprecated
    private String week;

    /**
     * 第几天
     */
    private Integer dayNum;

    /**
     * 当天所在城市code
     * V2.2.2 新增
     */
    private String cityCode;

    /**
     * 城市信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private CitySimpleVo cityInfo;

    /**
     * 活动列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ActivityEntity> activities;

    /**
     * 是否删除
     */
    @TableField(value = "is_del")
    private Boolean del;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public void setActivities(List<ActivityEntity> activities) {
        this.activities = JSONArray.from(activities).toJavaList(ActivityEntity.class);
    }

    public void setMyActivities(List<ActivityEntity> activities) {
        this.activities = activities;
    }

    public void convert4Copy() {
        this.id = null;
        this.createTime = null;
        this.updateTime = null;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityEntity {

        /**
         * 活动类型(1: 景区, 2: 餐饮, 3: 酒店)
         */
        private Integer type;

        /**
         * 活动来源
         */
        private String activityOrigin;

        /**
         * 活动ID
         */
        private String activityId;

        /**
         * 活动名称
         */
        private String activityName;

        /**
         * 序号
         */
        private Integer number;

        /**
         * 停留开始时间(如: 09:00)
         */
        @JsonFormat(pattern = "HH:mm")
        @JsonSerialize(using = LocalTimeSerializer.class)
        @JsonDeserialize(using = LocalTimeDeserializer.class)
        private LocalTime stayTimeStart;

        /**
         * 停留结束时间(如: 18:00)
         */
        @JsonFormat(pattern = "HH:mm")
        @JsonSerialize(using = LocalTimeSerializer.class)
        @JsonDeserialize(using = LocalTimeDeserializer.class)
        private LocalTime stayTimeEnd;

        /**
         * 停留开始日期时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime stayDateTimeStart;

        /**
         * 停留结束日期时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime stayDateTimeEnd;

        /**
         * 停留时间段(如: 18:00 - 19:00、20:00)
         */
        private String stayTimePeriod;

        /**
         * 预计停留时间(几小时)
         */
        private String stayTime;

        /**
         * 预计停留时间数值
         */
        private BigDecimal stayTimeValue;

        /**
         * 推荐游玩时间
         */
        private BigDecimal recommendedPlaytime;

        /**
         * 到下一地点距离
         */
        private String distanceToNext;

        /**
         * 到下一地点距离值(米)
         */
        private BigDecimal distanceToNextValue;

        /**
         * 到下一地点花费时间
         */
        private String timeToNext;

        /**
         * 到下一地点花费小时数值
         */
        private BigDecimal hoursToNext;

        /**
         * 到下一地点的交通方式
         */
        private Integer transportationModeToNext;



        /* ↓↓↓↓↓ 针对当前餐饮 ↓↓↓↓↓ */


        /**
         * 主题ID列表
         */
        @TableField(typeHandler = JacksonTypeHandler.class)
        private List<Integer> themes;

        /**
         * 评分
         */
        private String score;

//        /**
//         * 短介绍
//         */
//        private String shortIntro;

        /**
         * 地址
         */
        private String address;

//        /**
//         * 商圈
//         */
//        private String businessZone;

        /**
         * 经度
         */
        private String lon;

        /**
         * 纬度
         */
        private String lat;

        /**
         * 城市code
         */
        private String cityCode;

        /**
         * 图片列表(最多3张)
         */
        @TableField(typeHandler = JacksonTypeHandler.class)
        private List<String> images;

        /**
         * 价格(如: 免费、￥251元起)
         */
        private String startPrice;

        public ActivityEntity(ItineraryActivityVo itineraryActivityVo) {
            this.type = itineraryActivityVo.getType();
            this.activityOrigin = itineraryActivityVo.getActivityOrigin();
            this.activityId = itineraryActivityVo.getActivityId();
            this.activityName = itineraryActivityVo.getActivityName();
            this.stayTimeStart = itineraryActivityVo.getStayTimeStart();
            this.stayTimeEnd = itineraryActivityVo.getStayTimeEnd();
            this.stayDateTimeStart = itineraryActivityVo.getStayDateTimeStart();
            this.stayDateTimeEnd = itineraryActivityVo.getStayDateTimeEnd();
            this.stayTimePeriod = itineraryActivityVo.getStayTimePeriod();
            this.stayTime = itineraryActivityVo.getStayTime();
            this.stayTimeValue = itineraryActivityVo.getStayTimeValue();
            this.distanceToNext = itineraryActivityVo.getDistanceToNext();
            this.distanceToNextValue = itineraryActivityVo.getDistanceToNextValue();
            this.timeToNext = itineraryActivityVo.getTimeToNext();
            this.hoursToNext = itineraryActivityVo.getHoursToNext();
            this.transportationModeToNext = itineraryActivityVo.getTransportationModeToNext();
            this.lon = itineraryActivityVo.getLon();
            this.lat = itineraryActivityVo.getLat();

            this.themes = itineraryActivityVo.getThemes();
            this.score = itineraryActivityVo.getScore();
            this.address = itineraryActivityVo.getAddress();
            this.cityCode = itineraryActivityVo.getCityCode();
            this.images = itineraryActivityVo.getImages();
            this.startPrice = itineraryActivityVo.getStartPrice();
        }

        public ActivityEntity(ItineraryActivityDto itineraryActivityDto) {
            this.type = itineraryActivityDto.getType();
            this.activityOrigin = itineraryActivityDto.getActivityOrigin();
            this.activityId = itineraryActivityDto.getActivityId();
            this.activityName = itineraryActivityDto.getActivityName();
//            this.number = itineraryActivityVo.getNumber();
            this.stayTimeStart = itineraryActivityDto.getStayTimeStart();
            this.stayTimeEnd = itineraryActivityDto.getStayTimeEnd();
            this.stayDateTimeStart = itineraryActivityDto.getStayDateTimeStart();
            this.stayDateTimeEnd = itineraryActivityDto.getStayDateTimeEnd();
            this.stayTimePeriod = itineraryActivityDto.getStayTimePeriod();
            this.stayTime = itineraryActivityDto.getStayTime();
            this.stayTimeValue = itineraryActivityDto.getStayTimeValue();
            this.distanceToNext = itineraryActivityDto.getDistanceToNext();
            this.distanceToNextValue = itineraryActivityDto.getDistanceToNextValue();
            this.timeToNext = itineraryActivityDto.getTimeToNext();
            this.hoursToNext = itineraryActivityDto.getHoursToNext();
            this.transportationModeToNext = itineraryActivityDto.getTransportationModeToNext();
            this.lon = itineraryActivityDto.getLon();
            this.lat = itineraryActivityDto.getLat();

            this.themes = itineraryActivityDto.getThemes();
            this.score = itineraryActivityDto.getScore();
            this.address = itineraryActivityDto.getAddress();
            this.cityCode = itineraryActivityDto.getCityCode();
            this.images = itineraryActivityDto.getImages();
            this.startPrice = itineraryActivityDto.getStartPrice();
        }

        public ActivityEntity(ActivityDetailParam activityDetailParam) {
            this.activityOrigin = activityDetailParam.getActivityOrigin();
            this.activityId = activityDetailParam.getActivityId();
            this.type = activityDetailParam.getActivityType();
            this.activityName = activityDetailParam.getActivityName();
            this.cityCode = activityDetailParam.getCityCode();
            this.address = activityDetailParam.getAddress();
            this.lon = activityDetailParam.getLon();
            this.lat = activityDetailParam.getLat();
            this.images = activityDetailParam.getImages();
            this.score = activityDetailParam.getScore();
            this.startPrice = activityDetailParam.getStartPrice();
        }

        public boolean hasLocation() {
            return StrUtil.isNotBlank(this.lon) && StrUtil.isNotBlank(this.lat);
        }

        public void setStayDateTimeStart(LocalDateTime stayDateTimeStart) {
            this.stayDateTimeStart = stayDateTimeStart;
            if (stayDateTimeStart != null) {
                this.stayTimeStart = stayDateTimeStart.toLocalTime();
            }
        }

        public void setStayDateTimeEnd(LocalDateTime stayDateTimeEnd) {
            this.stayDateTimeEnd = stayDateTimeEnd;
            if (stayDateTimeEnd != null) {
                this.stayTimeEnd = stayDateTimeEnd.toLocalTime();
            }
        }
    }
}
