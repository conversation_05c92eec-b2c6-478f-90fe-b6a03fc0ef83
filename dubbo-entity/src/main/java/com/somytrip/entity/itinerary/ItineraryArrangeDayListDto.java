package com.somytrip.entity.itinerary;

import com.somytrip.entity.dto.city.CityDto;
import com.somytrip.entity.enums.itinerary.ItineraryActivityType;
import com.somytrip.entity.enums.itinerary.ItineraryBudgetLevel;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryAssemblyDayActivityListDto
 * @author: shadow
 * @description: 攻略安排每日活动列表参数dto
 * @date: 2024/4/14 15:00
 * @version: 1.0
 */
@Data
public class ItineraryArrangeDayListDto {

    /**
     * 第几天
     */
    private Integer dayNum;

    /**
     * 天数
     */
    private Integer days;

    /**
     * 预算等级
     */
    private ItineraryBudgetLevel budgetLevel;

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 国际化
     */
    private Locale locale;

    /**
     * 预备数据
     */
    private ItineraryPreparedDataDto.PreparedDataDetail preparedData;

    /**
     * 交通数据
     */
    private List<ItineraryTransportationVo> tpData;

    /**
     * 预备数据
     */
    private ItineraryPreparedDataDto preparedDataDto;

    /**
     * 攻略查询参数
     */
    private ItinerarySearchParam itinerarySearchParam;

    /**
     * 已选择活动ID列表
     */
    private Set<String> selectedIds;

    /**
     * 已选择餐厅ID列表
     */
    private Set<String> selectedRestaurantIds;

    /**
     * 获取当天下一个景区参数dto
     */
    private ItineraryGetDayNextScenicDto getDayNextScenicDto;

    /**
     * 上一个酒店
     */
    private ItineraryActivityDto lastHotel;

    /**
     * 每日开始时间[预设]
     */
    private LocalTime dayStartTime;

    /**
     * 每日结束时间[预设]
     */
    private LocalTime dayEndTime;

    /**
     * 是否安排餐厅
     */
    private Boolean arrangeRestaurantFlag;

    /**
     * 上一天时间区间
     */
    private ItineraryDateTimeRange lastDayDateTimeRange;

    /**
     * 目的地城市映射map
     */
    private Map<Integer, CityDto> destinationCityIdMap;

    /**
     * 日期城市map
     * 各日期与城市的映射关系
     * key -> 日期
     * value -> 该日期所在城市
     */
    private Map<LocalDate, String> dateCityMap;

    /**
     * 必去活动
     */
    private Map<ItineraryActivityType, List<ItineraryActivityDto>> mustActivityMap;

    /**
     * 当前城市必去活动
     */
    private Map<ItineraryActivityType, List<ItineraryActivityDto>> curCityMustActivityMap = new HashMap<>();

    /**
     * 是否插入餐厅占位节点
     */
    private boolean restaurantFlag = true;

    public void clearLast() {
        this.lastHotel = null;
        if (this.getDayNextScenicDto != null) {
            this.getDayNextScenicDto.setLastActivity(null);
        }
    }

    public void clearLastDayDateTime() {
//        this.lastDayStartDateTime = null;
//        this.lastDayEndDateTime = null;
        this.lastDayDateTimeRange = null;
    }
}
