package com.somytrip.entity.itinerary;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryDateTimeRange
 * @author: shadow
 * @description: 攻略日期时间区间
 * @date: 2024/10/8 10:29
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryDateTimeRange implements Cloneable {

    private LocalDateTime start;
    private LocalDateTime end;

    public boolean enable() {
        return this.start != null && this.end != null;
    }

    @Override
    public ItineraryDateTimeRange clone() {
        try {
            return (ItineraryDateTimeRange) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
