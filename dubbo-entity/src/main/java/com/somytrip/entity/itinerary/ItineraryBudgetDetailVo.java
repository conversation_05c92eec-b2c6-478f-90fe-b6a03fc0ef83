package com.somytrip.entity.itinerary;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.somytrip.entity.enums.itinerary.ItineraryBudgetType;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryBudgetDetailVo
 * @author: shadow
 * @description: 攻略预算明细vo
 * @date: 2024/5/21 15:09
 * @version: 1.0
 */
@Data
public class ItineraryBudgetDetailVo {

    /**
     * 攻略总预算
     */
    private String totalBudget;

    /**
     * 类型列表
     */
    private List<BudgetDetailType> types;

    @Data
    public static class BudgetDetailType {

        /**
         * 类型
         * 1 - 景点
         * 2 - 餐厅
         * 3 - 酒店
         * 4 - 交通
         */
        private Integer type;

        /**
         * 类型名称
         */
        private String typeName;

        /**
         * 每日预算列表
         */
        private List<BudgetDetailDay> days;

//        /**
//         * 预算元素
//         */
//        private List<BudgetDetailItem> items;
    }

    @Data
    public static class BudgetDetailDay {

        /**
         * 第几天
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate date;

        /**
         * 当天总预算
         */
        private String dayTotalBudget;

        /**
         * 当天预算明细
         */
        private List<BudgetDetailItem> items;
    }

    @Data
    public static class BudgetDetailItem {

        /**
         * 活动类型
         */
        private Integer type;

        /**
         * 活动类型名称
         */
        private String typeName;

        /**
         * 活动名称
         */
        private String itemName;

        /**
         * 活动预算
         */
        private String budget;

        public BudgetDetailItem(ItineraryActivityVo activityVo, Map<String, String> budgetTypeNameLocaleDict) {
            this.type = activityVo.getType();
//            this.typeName = ItineraryActivityType.getNameFromValue(this.type);
            this.typeName = budgetTypeNameLocaleDict.get(ItineraryBudgetType.fromValue(this.type).name());
            this.itemName = activityVo.getActivityName();
            this.budget = activityVo.getStartPrice();
        }
    }
}
