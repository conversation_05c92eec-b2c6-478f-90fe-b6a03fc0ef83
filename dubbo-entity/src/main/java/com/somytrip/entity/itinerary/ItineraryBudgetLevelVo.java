package com.somytrip.entity.itinerary;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryBudgetLevelVo
 * @author: shadow
 * @description: 攻略预算等级vo
 * @date: 2024/6/18 15:18
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryBudgetLevelVo {

    /**
     * 预算等级
     */
    private Integer budgetLevel;

    /**
     * 预算等级名称
     */
    private String budgetLevelName;

    /**
     * 攻略每日列表
     */
    private List<ItineraryDayVo> days;

    /**
     * 交通数据
     */
    private List<ItineraryTransportationVo> transportations;

    /**
     * 预算
     */
    private BigDecimal budget;
}
