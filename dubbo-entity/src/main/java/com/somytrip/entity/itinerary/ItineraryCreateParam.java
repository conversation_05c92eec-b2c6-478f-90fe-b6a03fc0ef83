package com.somytrip.entity.itinerary;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryCreateParam
 * @author: shadow
 * @description: 创建攻略参数(非查询)
 * @date: 2024/7/23 14:31
 * @version: 1.0
 */
@Data
public class ItineraryCreateParam {

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 出发城市code
     */
    private String departureCityCode;

    /**
     * 目的地城市codes
     */
    private List<String> destinationCityCodes;

    /**
     * 去程时间
     */
    private LocalDateTime goTime;

    /**
     * 返程时间
     */
    private LocalDateTime backTime;

    /**
     * 天数
     * 优先使用goTime和backTime计算天数
     */
    private Integer days;

    /**
     * 每日安排列表
     */
    private List<DayItem> dayItems;

    private String lang;

    @Data
    public static class DayItem {

        /**
         * 第几天
         */
        private Integer dayNum;

        /**
         * 当天城市code
         */
        private String cityCode;

        /**
         * 活动列表
         */
        private List<ActivityItem> activities;

        @Data
        public static class ActivityItem {

            /**
             * 活动类型
             */
            private Integer activityType;

            /**
             * 活动ID
             */
            private String activityId;

            /**
             * 活动名称
             */
            private String activityName;

            /**
             * 经度
             */
            private String lon;

            /**
             * 纬度
             */
            private String lat;

            /**
             * 游玩时间
             */
            private BigDecimal stayTime = BigDecimal.valueOf(2);

        }
    }
}
