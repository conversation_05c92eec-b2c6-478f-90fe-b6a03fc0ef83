package com.somytrip.entity.enums.locale;

import java.util.Locale;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.enums.locale
 * @enumName: NCNBLocaleMapping
 * @author: shadow
 * @description: 捷旅国际化标识映射
 * @date: 2024/9/20 11:29
 * @version: 1.0
 */
public enum NCNBLocaleMapping {

    GB,
    EN;

    public static String fromLocale(Locale locale) {
        return locale.toString().startsWith("zh") ? GB.name() : EN.name();
    }
}
