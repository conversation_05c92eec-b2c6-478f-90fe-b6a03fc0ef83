package com.somytrip.entity.enums.locale;

import lombok.Getter;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.enums.locale
 * @enumName: LocaleType
 * @author: shadow
 * @description: 国际化字典类型枚举
 * @date: 2024/9/19 11:27
 * @version: 1.0
 */
@Getter
public enum LocaleDictType {

    /**
     * 表
     */
    TABLE,
    /**
     * 机票配置
     */
    FLIGHT_CONFIG,
    /**
     * 酒店筛选配置
     */
    HOTEL_FILTER_CONFIG,
    /**
     * 订单配置
     */
    ORDER_SETTING,
    /**
     * 攻略预算类型
     */
    ITINERARY_BUDGET_TYPE,
    /**
     * 攻略条件配置
     */
    ITINERARY_QUERY_SETTING,
    /**
     * 城市等级
     */
    CITY_LEVEL,
}
