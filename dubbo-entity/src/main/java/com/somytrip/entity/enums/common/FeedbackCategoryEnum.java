package com.somytrip.entity.enums.common;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.enums.common
 * @enumName: FeedbackCategoryEnum
 * @author: lijunqi
 * @description: 反馈分类枚举
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
@Getter
public enum FeedbackCategoryEnum {

    /**
     * 建议
     */
    SUGGESTION(1, "建议"),
    
    /**
     * Bug反馈
     */
    BUG(2, "Bug");

    /**
     * 分类值
     */
    private final Integer value;
    
    /**
     * 分类描述
     */
    private final String description;

    FeedbackCategoryEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 分类值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static FeedbackCategoryEnum fromValue(Integer value) {
        return Arrays.stream(FeedbackCategoryEnum.values())
                .filter(item -> Objects.equals(value, item.getValue()))
                .findFirst()
                .orElse(null);
    }
}
