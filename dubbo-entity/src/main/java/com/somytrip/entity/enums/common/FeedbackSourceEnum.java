package com.somytrip.entity.enums.common;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.enums.common
 * @enumName: FeedbackSourceEnum
 * @author: lijunqi
 * @description: 反馈来源枚举
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
@Getter
public enum FeedbackSourceEnum {

    /**
     * 小程序
     */
    MINI_PROGRAM(1, "小程序"),
    
    /**
     * APP
     */
    APP(2, "APP");

    /**
     * 来源值
     */
    private final Integer value;
    
    /**
     * 来源描述
     */
    private final String description;

    FeedbackSourceEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 来源值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static FeedbackSourceEnum fromValue(Integer value) {
        return Arrays.stream(FeedbackSourceEnum.values())
                .filter(item -> Objects.equals(value, item.getValue()))
                .findFirst()
                .orElse(null);
    }
}
