package com.somytrip.entity.enums.user;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.enums.user
 * @className: PointBusinessTypeEnum
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/8/5 15:34
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum PointBusinessTypeEnum {

    /**
     * 反馈提交
     */
    FEEDBACK_SUBMIT("FEEDBACK_SUBMIT", "反馈提交"),

    /**
     * 反馈被采纳
     */
    FEEDBACK_ADOPT("FEEDBACK_ADOPT", "反馈被采纳"),

    /**
     * 积分消费
     */
    POINT_CONSUME("POINT_CONSUME", "积分消费");

    /**
     * 业务类型代码
     */
    private final String code;

    /**
     * 业务类型名称
     */
    private final String name;


    /**
     * 根据代码获取枚举
     *
     * @param code 业务类型代码
     * @return 枚举对象
     */
    public static PointBusinessTypeEnum getByCode(String code) {
        for (PointBusinessTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}