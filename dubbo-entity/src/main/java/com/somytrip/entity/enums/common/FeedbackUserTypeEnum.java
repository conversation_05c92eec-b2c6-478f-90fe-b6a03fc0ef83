package com.somytrip.entity.enums.common;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.enums.common
 * @enumName: FeedbackUserTypeEnum
 * @author: lijunqi
 * @description: 反馈用户类型枚举
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
@Getter
public enum FeedbackUserTypeEnum {

    /**
     * 官方
     */
    OFFICIAL(1, "官方"),
    
    /**
     * 作者
     */
    AUTHOR(2, "作者"),
    
    /**
     * 用户
     */
    USER(3, "用户");

    /**
     * 用户类型值
     */
    private final Integer value;
    
    /**
     * 用户类型描述
     */
    private final String description;

    FeedbackUserTypeEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 用户类型值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static FeedbackUserTypeEnum fromValue(Integer value) {
        return Arrays.stream(FeedbackUserTypeEnum.values())
                .filter(item -> Objects.equals(value, item.getValue()))
                .findFirst()
                .orElse(null);
    }
}
