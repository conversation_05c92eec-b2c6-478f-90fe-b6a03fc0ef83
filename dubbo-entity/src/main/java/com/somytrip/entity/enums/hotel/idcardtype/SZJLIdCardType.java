package com.somytrip.entity.enums.hotel.idcardtype;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @ClassName: IdCardType
 * @Description: 证件类型
 * @Author: shadow
 * @Date: 2024/2/23 16:14
 */
@Getter
public enum SZJLIdCardType {

    // 身份证
    IDC(IdCardType.IdentityCard),
    // 护照
    PSP(IdCardType.Passport),
    // 台胞证
    TWC(IdCardType.TaiwanCompatriotCertificate),
    // 大陆居民往来台湾通行证,
    CTT(IdCardType.Other),
    // 港澳通行证
    CTH(IdCardType.Other),
    ;

    private final IdCardType generalIdCardType;

    SZJLIdCardType(IdCardType generalIdCardType) {
        this.generalIdCardType = generalIdCardType;
    }

    public static SZJLIdCardType getFromGeneralType(IdCardType generalIdCardType) {
        return Arrays.stream(SZJLIdCardType.values())
                .filter(item -> Objects.equals(generalIdCardType, item.generalIdCardType))
                .findFirst()
                .orElse(IDC);
    }
}
