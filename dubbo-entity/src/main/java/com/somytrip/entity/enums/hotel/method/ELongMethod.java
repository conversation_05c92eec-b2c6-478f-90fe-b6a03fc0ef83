package com.somytrip.entity.enums.hotel.method;

import lombok.Getter;

@Getter
public enum ELongMethod {
    // 城市列表
    HOTEL_STATIC_CITY("hotel.static.city"),
    // 酒店列表
    HOTEL_STATIC_LIST("hotel.static.list"),
    // 酒店详情
    HOTEL_STATIC_INFO("hotel.static.info"),
    // 酒店点评
    HOTEL_STATIC_GRADE("hotel.static.grade"),
    // 产品信息
    HOTEL_DATA_RP("hotel.data.rp"),
    // 库存信息
    HOTEL_DATA_INVENTORY("hotel.data.inventory"),
    // 价格信息
    HOTEL_DATA_RATE("hotel.data.rate"),
    // 预定数据
    HOTEL_DATA_BOOKING("hotel.data.booking"),
    // 分片增量状态
    HOTEL_INCR_SHARDING_STATE("hotel.incr.sharding.state"),
    // 分片增量编号
    HOTEL_INCR_SHARDING_ID("hotel.incr.sharding.id"),
    // 分片库存增量
    HOTEL_INCR_SHARDING_INV("hotel.incr.sharding.inv"),
    // 分片价格增量
    HOTEL_INCR_SHARDING_RATE("hotel.incr.sharding.rate"),
    // 订单增量
    HOTEL_INCR_ORDER("hotel.incr.order"),
    // 数据验证(下单前校验)
    HOTEL_DATA_VALIDATE("hotel.data.validate"),
    // 创建订单
    HOTEL_ORDER_CREATE("hotel.order.create"),
    // 订单列表
    HOTEL_ORDER_LIST("hotel.order.list"),
    // 订单详情
    HOTEL_ORDER_DETAIL("hotel.order.detail"),
    // 订单支付
    HOTEL_ORDER_PAY("hotel.order.pay"),
    // 订单支付确认
    HOTEL_ORDER_PAY_CONFIRM("hotel.order.pay.confirm"),
    // 取消订单
    HOTEL_ORDER_CANCEL("hotel.order.cancel"),
    // 修改订单
    HOTEL_ORDER_UPDATE("hotel.order.update"),
    // 订单催确认
    HOTEL_ORDER_PROMOTE("hotel.order.promote"),
    // 关联订单
    HOTEL_ORDER_RELATED("hotel.order.related"),


    // 目的地补全
    HOTEL_DESTINATION("hotel.destination"),
    // 筛选项信息
    HOTEL_FILTER("hotel.filter"),
    // 列表搜索
    HOTEL_LIST("hotel.list"),
    // 详情搜索
    HOTEL_DETAIL("hotel.detail"),
    // 最小价
    HOTEL_RATE_MIN("hotel.rate.min"),
    ;

    private final String name;

    ELongMethod(String name) {
        this.name = name;
    }
}
