package com.somytrip.entity.enums.yeepay;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2023-10-17 15:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
//@ApiModel(description = "下单参数")
public class PaymentParams {
    //@ApiModelProperty(value = "发起方商户编号", example = "10040012345")
    private String parentMerchantNo;

    //@ApiModelProperty(value = "商户编号", example = "10040054321")
    private String merchantNo;

    //@ApiModelProperty(value = "商户收款请求号", example = "orderId12345")
    private String orderId;

    //@ApiModelProperty(value = "订单金额", example = "0.01")
    private Double orderAmount;

    //@ApiModelProperty(value = "订单截止时间", example = "2021-01-01 12:34:56")
    private String expiredTime;

    //@ApiModelProperty(value = "支付结果通知地址", example = "https://notify.merchant.com/xxx")
    private String notifyUrl;

    //@ApiModelProperty(value = "页面回调地址", example = "https://notify.merchant.com/xxx")
    private String redirectUrl;

    //@ApiModelProperty(value = "自定义对账备注", example = "自定义对账备注")
    private String memo;

    //@ApiModelProperty(value = "商品名称", example = "旺仔牛奶")
    private String goodsName;

    //@ApiModelProperty(value = "分账订单标记", example = "REAL_TIME")
    private String fundProcessType;

    //@ApiModelProperty(value = "支付方式", example = "USER_SCAN")
    private PayWayEnum payWay = PayWayEnum.JS_PAY;

    //@ApiModelProperty(value = "渠道类型", example = "WECHAT")
    private ChannelEnum channel = ChannelEnum.WECHAT;

    //@ApiModelProperty(value = "场景", example = "OFFLINE")
    private SceneEnum scene;

    //@ApiModelProperty(value = "微信公众号ID/微信小程序ID/支付宝小程序ID", example = "appId12345")
    private String appId;

    //@ApiModelProperty(value = "用户ID", example = "userId12345")
    private String userId;

    //@ApiModelProperty(value = "用户IP", example = "***************")
    private String userIp;

    //@ApiModelProperty(value = "渠道指定支付信息")
    private String channelSpecifiedInfo;

    //@ApiModelProperty(value = "渠道优惠信息")
    private String channelPromotionInfo;

    //@ApiModelProperty(value = "限制付款人信息")
    private String identityInfo;

    //@ApiModelProperty(value = "是否限制贷记卡", example = "N")
    private char limitCredit = 'N';

    //@ApiModelProperty(value = "下单接口返回的token",example = "83BCDF29CFACB4411533080B67864EF8C907CCDC5E10A707C285FEA10CDB8221")
    private String token;

    //@ApiModelProperty(value = "易宝订单号", example = "1012202101070000001989946571")
    private String uniqueOrderNo;

    //@ApiModelProperty(value = "清算回调地址")
    private String csUrl;

    //@ApiModelProperty(value = "合作银行信息")
    private String accountLinkInfo;

    //@ApiModelProperty(value = "易宝营销信息")
    private String ypPromotionInfo;

    //@ApiModelProperty(value = "银行编码", example = "BOC")
    private String bankCode;

    //@ApiModelProperty(value = "自定义参数信息")
    private String businessInfo;

    //@ApiModelProperty(value = "用户授权码")
    private String userAuthCode;

    //@ApiModelProperty(value = "渠道活动信息")
    private String channelActivityInfo;

    //@ApiModelProperty(value = "终端号")
    private String terminalId;

    //@ApiModelProperty(value = "终端场景信息")
    private String terminalSceneInfo;

    //@ApiModelProperty(value = "记账簿编号")
    private String ypAccountBookNo;

    //@ApiModelProperty(value = "终端信息")
    private String terminalInfo;

    //@ApiModelProperty(value = "产品信息")
    private String productInfo;

    //@ApiModelProperty(value = "分账明细")
    private String divideDetail;

    //@ApiModelProperty(value = "分账通知地址")
    private String divideNotifyUrl;

    //@ApiModelProperty(value = "手续费补贴信息")
    private String feeSubsidyInfo;
}
