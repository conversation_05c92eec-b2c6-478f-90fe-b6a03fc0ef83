package com.somytrip.entity.enums.hotel;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: HotelSortType
 * @Description: 酒店排序类型
 * @Author: shadow
 * @Date: 2024/3/1 11:23
 */
@Getter
public enum HotelSortType {

    // 默认排序(智能排序)
    Default("11"),
    // 推荐星级降序
    StarRankDesc("12"),
    // 点评数降序
    ReviewCountDesc("13"),
    // 价格升序
    RateAsc("14"),
    // 价格降序
    RateDesc("15"),
    // 距离升序
    DistanceAsc("16"),
    ;

    private static final Map<String, HotelSortType> hotelSortTypeCodeMap = new HashMap<>();

    static {
        for (HotelSortType value : HotelSortType.values()) {
            hotelSortTypeCodeMap.put(value.getCode(), value);
        }
    }

    private final String code;

    HotelSortType(String code) {
        this.code = code;
    }

    /**
     * 根据code获取排序方式枚举
     *
     * @param code code
     * @return HotelSortType 酒店排序方式枚举
     */
    public static HotelSortType getSortTypeByCode(String code) {
        HotelSortType sortType = hotelSortTypeCodeMap.get(code);
        return sortType != null ? sortType : Default;
    }
}
