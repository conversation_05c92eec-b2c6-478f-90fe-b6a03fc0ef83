package com.somytrip.entity.enums.order;

/**
 * @Description:
 * @author: pigeon
 * @created: 2023-11-03 9:46
 */
public enum OrderCancelStatusEnum {
    NO_CANCEL(0, "没有取消"),
    YES_CANCEL(1, "已经取消"),
    ;
    private final int code;
    private final String dec;

    OrderCancelStatusEnum(int code, String dec) {
        this.code = code;
        this.dec = dec;
    }

    public String getDec() {
        return dec;
    }

    public int getCode() {
        return code;
    }
}
