package com.somytrip.entity.enums;

import com.somytrip.entity.EncryptData;
import lombok.Getter;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-23 16:35
 */

@Getter
public enum EncryptDataEnum {
    /**
     *
     */
    USER_TRIP_INFO(
            new EncryptData.Encrypt(
                    "ucti2325e9srkrqa",
                    "kvc7rczjgbt0hl8d",
                    EncryptData.SignType.SM4
            ),
            new EncryptData.Encrypt(
                    "vp2i0xxo0rm3g84s",
                    "dnjlf97duxv0ie4s",
                    EncryptData.SignType.SM4
            )),
    ;


    private final EncryptData.Encrypt front;
    private final EncryptData.Encrypt back;

    EncryptDataEnum(EncryptData.Encrypt front, EncryptData.Encrypt back) {
        this.front = front;
        this.back = back;
    }
}
