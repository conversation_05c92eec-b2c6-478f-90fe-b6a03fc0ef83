package com.somytrip.entity.enums.thirdparty.visa;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum VisaUriEnum {
    getShopGoodList("/openvisa/product_brief_query"), //商品列表查询
    getShopGoodDetail("/openvisa/product_query"), //商品详情查询
    createOrder("/openvisa/order_add"), //创建订单
    payOrder("/openvisa/order_pay"), //订单支付
    uploadUserData("/openvisa/applicant_material_add"),//提交申请人资料
    updateUserData("/openvisa/applicant_material_update"),//更新申请人资料
    getOrderDetail("/openvisa/order_query"), //查询订单详情
    orderDiffPrice("/openvisa/order_diff_price"), //订单补差价
    updateUserStartData("/openvisa/applicant_appointment_update"), //申请人赴馆时间更新
    getEmbassyByCountry("/openvisa/country_embassy_get"),//查询国家使馆信息
    getUserDataDetail("/openvisa/applicant_query"), //
    orderExpressAdd("/openvisa/order_express_add"),//订单快递邮寄资料信息更新
    getVisaConfig("/openvisa/sys_config_get"),//配置获取
    getBill("/openvisa/bill_get"),//账单获取接口
    getShopGoodPrice("/openvisa/product_price_query"), //商品价格查询
    applicantReverify("/openvisa/applicant_reverify"), //申请人发起重新审核
    orderRefund("/openvisa/order_refund"), //发起退款
    ;
    private final String value;

    VisaUriEnum(String path) {
        this.value = path;
    }
}