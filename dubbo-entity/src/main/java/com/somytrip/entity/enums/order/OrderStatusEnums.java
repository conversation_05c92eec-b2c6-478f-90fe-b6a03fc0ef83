package com.somytrip.entity.enums.order;

import lombok.Getter;

/**
 * @Description: 订单状态
 * @author: pigeon
 * @created: 2023-11-01 10:55
 */
@Getter
public enum OrderStatusEnums {
    /**
     * 创建订单
     */
    WAIT_PAYMENT(1, "待支付"),
    WAIT_DELIVER(2, "已支付"),
    WAIT_RECEIVE(3, "机票已出票，签证待邮寄"),
    FINISH(4, "完成"),
    CANCELED(5, "已取消"),
    REFUNDED(6, "已退款"),
    ;
    private final int code;
    private final String des;

    OrderStatusEnums(int code, String des) {
        this.code = code;
        this.des = des;
    }

}
