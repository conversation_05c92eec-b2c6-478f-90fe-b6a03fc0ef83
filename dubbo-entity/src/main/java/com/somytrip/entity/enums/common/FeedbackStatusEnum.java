package com.somytrip.entity.enums.common;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.enums.common
 * @enumName: FeedbackStatusEnum
 * @author: lijunqi
 * @description: 反馈状态枚举
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
@Getter
public enum FeedbackStatusEnum {

    /**
     * 待处理
     */
    PENDING(1, "待处理"),
    
    /**
     * 处理中
     */
    PROCESSING(2, "处理中"),
    
    /**
     * 已解决
     */
    RESOLVED(3, "已解决"),
    
    /**
     * 已关闭
     */
    CLOSED(4, "已关闭");

    /**
     * 状态值
     */
    private final Integer value;
    
    /**
     * 状态描述
     */
    private final String description;

    FeedbackStatusEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static FeedbackStatusEnum fromValue(Integer value) {
        return Arrays.stream(FeedbackStatusEnum.values())
                .filter(item -> Objects.equals(value, item.getValue()))
                .findFirst()
                .orElse(null);
    }
}
