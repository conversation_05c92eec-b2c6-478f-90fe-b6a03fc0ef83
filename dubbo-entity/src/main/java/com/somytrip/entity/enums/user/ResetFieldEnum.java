package com.somytrip.entity.enums.user;

import lombok.Getter;

/**
 * @Description: 重设用户参数枚举
 * @author: pigeon
 * @created: 2024-12-31 11:17
 */
public enum ResetFieldEnum {
    /**
     * 邮箱密码
     */
    EMAIL_PASSWORD("email-password"),
    /**
     * 邮箱本身
     */
    EMAIL_SIGN("email-sign"),
    /**
     * 手机号本身
     */
    PHONE_SIGN("phone-sign"),
    /**
     * 手机号-密码
     */
    PHONE_PASSWORD("phone-password"),
    /**
     * 邮箱-手机号公用密码
     */
    PASSWORD("password"),
    /**
     * 第一次注册设置密码
     */
    PASSWORD_FIRST("password-first"),
    ;
    @Getter
    private final String low;

    ResetFieldEnum(String low) {
        this.low = low;
    }
}
