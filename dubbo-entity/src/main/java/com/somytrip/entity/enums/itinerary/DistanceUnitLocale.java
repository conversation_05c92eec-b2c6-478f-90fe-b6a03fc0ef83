package com.somytrip.entity.enums.itinerary;

import lombok.Getter;

import java.util.Arrays;
import java.util.Locale;
import java.util.Objects;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.enums.itinerary
 * @enumName: HourUnit
 * @author: shadow
 * @description: 攻略活动距离单位国际化枚举
 * @date: 2024/8/17 15:08
 * @version: 1.0
 */
@Getter
public enum DistanceUnitLocale {

    // 米
    M_zh_CN("米"),
    M_zh_TW("米"),
    M_en_US("m"),

    // 公里
    KM_zh_CN("公里"),
    KM_zh_TW("公里"),
    KM_en_US("km"),
    ;

    private final String value;

    DistanceUnitLocale(String value) {
        this.value = value;
    }

    public static String fromLocale(Locale locale, Unit unit) {

        return Arrays.stream(DistanceUnitLocale.values())
                .filter(item -> item.name().startsWith(unit.name()))
                .filter(item -> Objects.equals(locale.toString(), item.name().replace(unit.name() + "_", "")))
                .map(DistanceUnitLocale::getValue)
                .findFirst()
                .orElse(null);
    }

    @Getter
    public enum Unit {

        M,
        KM
    }
}
