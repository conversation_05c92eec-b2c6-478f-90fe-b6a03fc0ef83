package com.somytrip.entity.pay.douyin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.pay.douyin
 * @className: DouyinPayQueryRefundResp
 * @author: shadow
 * @description: 抖音支付退款结果查询Resp
 * @date: 2024/7/27 11:22
 * @version: 1.0
 */
@Data
public class DouyinPayQueryRefundResp extends DouyinPayPublicResp {

    private RefundInfo refundInfo;

    @Data
    public static class RefundInfo {

        /**
         * 退款金额，单位为分
         */
        @JsonProperty("refund_amount")
        private BigDecimal refundAmount;

        /**
         * 退款状态枚举
         * SUCCESS: 成功
         * FAIL: 失败
         * PROCESSING: 处理中
         */
        @JsonProperty("refund_status")
        private String refundStatus;

        /**
         * 退款时间，Unix时间戳，10位，整数，秒级
         */
        @JsonProperty("refunded_at")
        private Integer refundedAt;

        /**
         * 退款账户枚举
         * TRUE: 分帐后退款，现金户出款
         * FALSE: 分帐前退款，在途户出款
         */
        @JsonProperty("is_all_settled")
        private Boolean isAllSettled;

        /**
         * 抖音退款单号
         */
        @JsonProperty("refund_no")
        private String refundNo;

        /**
         * 开发者自定义字段，回调原样回传
         */
        @JsonProperty("cp_extra")
        private String cpExtra;

        /**
         * 退款错误描述
         */
        private String msg;
    }
}
