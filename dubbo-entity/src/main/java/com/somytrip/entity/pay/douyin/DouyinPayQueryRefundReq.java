package com.somytrip.entity.pay.douyin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.pay.douyin
 * @className: DouyinPayQueryRefundReq
 * @author: shadow
 * @description: 抖音支付退款结果查询Req
 * @date: 2024/7/27 11:19
 * @version: 1.0
 */
@Data
public class DouyinPayQueryRefundReq {

    /**
     * 小程序APPID
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 商户退款单号
     */
    @JsonProperty("out_refund_no")
    private String outRefundNo;

    /**
     * 签名
     */
    private String sign;

    /**
     * 第三方平台服务商id，服务商模式接入必传，非服务商模式留空
     */
    @JsonProperty("thirdparty_id")
    private String thirdPartyId;
}
