package com.somytrip.entity.vo.community;

import com.somytrip.entity.FileInfo;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.vo.community
 * @className: FeedbackPlatformListVo
 * @author: li<PERSON><PERSON>
 * @description:
 * @date: 2025/8/4 17:42
 * @version: 1.0
 */
@Data
public class FeedbackPlatformListVo {
    /**
     * 反馈ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户加密ID
     */
    private String userSecretId;

    /**
     * 用户头像URL
     */
    private String userAvatar;

    /**
     * 用户昵称
     */
    private String userNickname;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 图片列表
     */
    private List<FileInfo> images;

    /**
     * 发表时间
     */
    private LocalDateTime createTime;

    /**
     * 发表时间显示文本
     */
    private String createTimeShow;

    /**
     * IP地址显示（省份-城市格式）
     */
    private String ipLocationShow;

    /**
     * 支持数量
     */
    private Integer supportCount;

    /**
     * 留言数量
     */
    private Integer commentCount;

    /**
     * 是否已采纳：0-未采纳 1-已采纳
     */
    private Integer isAdopted;

    /**
     * 分类：1-建议、2-bug
     */
    private Integer category;

    /**
     * 分类显示文本
     */
    private String categoryShow;

    /**
     * 当前用户是否已支持该反馈
     */
    private Boolean isSupported;

    /**
     * 预览留言（默认显示1条最早的留言）
     */
    private FeedbackCommentVo previewComment;
}
