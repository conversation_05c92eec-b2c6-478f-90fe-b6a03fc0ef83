package com.somytrip.entity.vo;

import com.somytrip.entity.tourism.TourismThemeEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: TourismThemeVo
 * @Description: 攻略风格偏好vo
 * @Author: shadow
 * @Date: 2023/12/8 10:54
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TourismThemeVo {

    /**
     * 主题ID
     */
    private Long id;

    /**
     * 主题名称
     */
    private String name;

    public TourismThemeVo(TourismThemeEntity entity) {
        if (entity != null) {
            this.id = entity.getId();
            this.name = entity.getName();
        }
    }
}
