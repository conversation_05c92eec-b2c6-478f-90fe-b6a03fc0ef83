package com.somytrip.entity.vo.hotel;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @projectName: hotel-service
 * @package: com.somytrip.entity.vo.hotel
 * @className: HotelFacilityTypeVo
 * @author: shadow
 * @description: 酒店设施类型vo
 * @date: 2024/4/3 10:55
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HotelFacilityTypeVo {

    /**
     * 设施类型ID
     */
    private Long facilityTypeId;

    /**
     * 设施类型名称
     */
    private String facilityTypeName;

    /**
     * 设施列表
     */
    private List<HotelFacilityVo> facilities;
}
