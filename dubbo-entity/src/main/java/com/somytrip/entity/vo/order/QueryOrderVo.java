package com.somytrip.entity.vo.order;

import com.somytrip.entity.dto.order.OrderBigFieldDTO;
import com.somytrip.entity.dto.order.OrderCostBreakDownDTO;
import com.somytrip.entity.dto.order.OrderFinanceDTO;
import com.somytrip.entity.dto.order.OrderMasterDTO;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2023-11-05 9:22
 */
@Data
public class QueryOrderVo {
    private OrderMasterDTO orderMaster;
    private OrderFinanceDTO orderFinance;
    private List<OrderCostBreakDownDTO> orderProduct;
    private OrderBigFieldDTO orderBigField;
}
