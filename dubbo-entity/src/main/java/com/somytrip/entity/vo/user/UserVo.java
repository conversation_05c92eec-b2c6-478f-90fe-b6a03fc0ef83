package com.somytrip.entity.vo.user;

import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.somytrip.entity.dto.user.SomytripUsers;
import com.somytrip.entity.enums.SensitiveStrategy;
import com.somytrip.target.Sensitive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserVo {
    /**
     * 用户ID
     */
    private Integer id;
    /**
     * 唯一ID
     */
    private String smtId;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 头像
     */
    private String coverImage;
    /**
     * 性别
     */
    private String gender;
    /**
     * 性别值
     */
    private Integer genderValue;
    /**
     * 国家
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date ctime;
    /**
     * 手机号 +（区号+手机号）
     */
    @Sensitive(strategy = SensitiveStrategy.PHONE_MASK)
    private String mobile;
    /**
     * 选择语言
     */
    private String language;
    /**
     * 星座
     */
    private String constellation;
    /**
     * 年龄 -1 未初始化 0-999
     */
    private Integer age;
    /**
     * 个性签名
     */
    private String signature;
    /**
     * 个性标签
     */
    private String tagStr;
    /**
     * 是否展示性别 非数据库表字段
     */
    private char viewGender;
    /**
     * 是否展示国家 非数据库表字段
     */
    private char viewCountry;
    /**
     * 是否展示城市 非数据库表字段
     */
    private char viewCity;
    /**
     * 是否展示星座 非数据库表字段
     */
    private char viewConstellation;
    /**
     * 是否展示年龄 非数据库表字段
     */
    private char viewAge;
    /**
     * 邮箱
     */
    private String email;

    public UserVo(SomytripUsers somytripUsers) {
        /**
         * 0: gender
         * 1: country
         * 2: province
         * 3: city
         * 4: age
         * 5: constellation
         * 6: personalizedSignature
         * 7: personalityLabel
         */
        this.id = somytripUsers.getId().intValue();
        this.smtId = somytripUsers.getUniqueId();
        this.country = somytripUsers.getCountry();
        this.province = somytripUsers.getProvince();
        this.city = somytripUsers.getCity();
        this.language = somytripUsers.getLang();
        this.signature = somytripUsers.getPersonalizedSignature();
        this.tagStr = somytripUsers.getPersonalityLabel();
        this.ctime = somytripUsers.getCreateTime();
        this.mobile = DesensitizedUtil.mobilePhone(somytripUsers.getMobile());
        this.coverImage = somytripUsers.getAuthImage();
        this.nickname = somytripUsers.getNickname();
        this.genderValue = somytripUsers.getGender();

        this.viewAge = somytripUsers.getDisplayFiled().charAt(4);
        this.viewGender = somytripUsers.getDisplayFiled().charAt(0);
        this.viewCity = somytripUsers.getDisplayFiled().charAt(3);
        this.viewCountry = somytripUsers.getDisplayFiled().charAt(1);
        this.viewConstellation = somytripUsers.getDisplayFiled().charAt(5);

        String mg = "未知";
        if (somytripUsers.getConstellation() != -1) {
            final String[] CONSTELLATION_ARRAY = {"摩羯座", "水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座"};
            this.constellation = CONSTELLATION_ARRAY[somytripUsers.getConstellation()];
        } else {
            this.constellation = mg;
        }
        if (somytripUsers.getGender() != -1) {
            this.gender = somytripUsers.getGender() == 1 ? "男" : "女";
        } else {
            this.gender = mg;
        }
        if (somytripUsers.getAge() != -1) {
            this.age = somytripUsers.getAge();
        }
    }

    public JSONObject createNickNameCover() {
        JSONObject jsonObject = JSONObject.parseObject("{\"cover\":null,\"nickname\":\"SMTUser\"}");
        jsonObject.put("nickname", this.getNickname());
        jsonObject.put("cover", this.getCoverImage());
        return jsonObject;
    }

    public String notPlusMobile() {
        return this.mobile.substring(1);
    }

    public void setUserVo(SomytripUsers somytripUsers) {
        /**
         * 0: gender
         * 1: country
         * 2: province
         * 3: city
         * 4: age
         * 5: constellation
         * 6: personalizedSignature
         * 7: personalityLabel
         */
        this.id = somytripUsers.getId().intValue();
        this.smtId = somytripUsers.getUniqueId();
        this.country = somytripUsers.getCountry();
        this.province = somytripUsers.getProvince();
        this.city = somytripUsers.getCity();
        this.language = somytripUsers.getLang();
        this.signature = somytripUsers.getPersonalizedSignature();
        this.tagStr = somytripUsers.getPersonalityLabel();
        this.ctime = somytripUsers.getCreateTime();
        this.mobile = DesensitizedUtil.mobilePhone(somytripUsers.getMobile());
        this.coverImage = somytripUsers.getAuthImage();
        this.nickname = somytripUsers.getNickname();
        this.genderValue = somytripUsers.getGender();

        this.viewAge = somytripUsers.getDisplayFiled().charAt(4);
        this.viewGender = somytripUsers.getDisplayFiled().charAt(0);
        this.viewCity = somytripUsers.getDisplayFiled().charAt(3);
        this.viewCountry = somytripUsers.getDisplayFiled().charAt(1);
        this.viewConstellation = somytripUsers.getDisplayFiled().charAt(5);

        String mg = "未知";
        if (somytripUsers.getConstellation() != -1) {
            final String[] CONSTELLATION_ARRAY = {"摩羯座", "水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座"};
            this.constellation = CONSTELLATION_ARRAY[somytripUsers.getConstellation()];
        } else {
            this.constellation = mg;
        }
        if (somytripUsers.getGender() != -1) {
            this.gender = somytripUsers.getGender() == 1 ? "男" : "女";
        } else {
            this.gender = mg;
        }
        if (somytripUsers.getAge() != -1) {
            this.age = somytripUsers.getAge();
        }
    }

    public void setUserVo(SomytripUsers somytripUsers, boolean desensitized) {
        if (desensitized) {
            this.setUserVo(somytripUsers);
            return;
        }
        /**
         * 0: gender
         * 1: country
         * 2: province
         * 3: city
         * 4: age
         * 5: constellation
         * 6: personalizedSignature
         * 7: personalityLabel
         */
        this.id = somytripUsers.getId().intValue();
        this.smtId = somytripUsers.getUniqueId();
        this.country = somytripUsers.getCountry();
        this.province = somytripUsers.getProvince();
        this.city = somytripUsers.getCity();
        this.language = somytripUsers.getLang();
        this.signature = somytripUsers.getPersonalizedSignature();
        this.tagStr = somytripUsers.getPersonalityLabel();
        this.ctime = somytripUsers.getCreateTime();
        this.mobile = somytripUsers.getMobile();
        this.coverImage = somytripUsers.getAuthImage();
        this.nickname = somytripUsers.getNickname();
        this.genderValue = somytripUsers.getGender();

        this.viewAge = somytripUsers.getDisplayFiled().charAt(4);
        this.viewGender = somytripUsers.getDisplayFiled().charAt(0);
        this.viewCity = somytripUsers.getDisplayFiled().charAt(3);
        this.viewCountry = somytripUsers.getDisplayFiled().charAt(1);
        this.viewConstellation = somytripUsers.getDisplayFiled().charAt(5);

        String mg = "未知";
        if (somytripUsers.getConstellation() != -1) {
            final String[] CONSTELLATION_ARRAY = {"摩羯座", "水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座"};
            this.constellation = CONSTELLATION_ARRAY[somytripUsers.getConstellation()];
        } else {
            this.constellation = mg;
        }
        if (somytripUsers.getGender() != -1) {
            this.gender = somytripUsers.getGender() == 1 ? "男" : "女";
        } else {
            this.gender = mg;
        }
        if (somytripUsers.getAge() != -1) {
            this.age = somytripUsers.getAge();
        }
    }
}

