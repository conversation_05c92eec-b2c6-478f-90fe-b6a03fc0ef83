package com.somytrip.entity.vo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.somytrip.entity.vo.common.CaptchaAppVo;
import com.somytrip.utils.RSAUtils;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Author: pigeon_fancier
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Valid
public class PhoneMessageVo {
    @NotBlank
    private String phone;
    @NotNull
    @Positive
    private Long timestamp;
    @NotBlank
    private String sign;
    @NotBlank
    private String deviceId;
    @NotBlank
    private String areaCode;
    private CaptchaAppVo aliCaptchaParams;

    public boolean signCheck() {
        PhoneMessageVo signVo = JSON.parseObject(RSAUtils.decrypt(this.getSign()), PhoneMessageVo.class);
        return StrUtil.equals(signVo.getPhone(), phone)
                && StrUtil.equals(signVo.getAreaCode(), areaCode)
                && StrUtil.equals(signVo.getDeviceId(), deviceId)
                && ObjectUtil.equals(signVo.getTimestamp(), timestamp);
    }

    public String toMobile() {
        return "+" + areaCode + phone;
    }
}
