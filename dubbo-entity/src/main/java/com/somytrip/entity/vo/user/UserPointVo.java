package com.somytrip.entity.vo.user;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.vo.user
 * @className: UserPointVo
 * @author: lijunqi
 * @description:
 * @date: 2025/8/5 15:22
 * @version: 1.0
 */
@Data
public class UserPointVo {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 当前积分余额
     */
    private Integer pointBalance;

    /**
     * 累计获得积分
     */
    private Integer totalEarned;

    /**
     * 累计消费积分
     */
    private Integer totalConsumed;

    /**
     * 积分等级
     */
    private Integer pointLevel;

    /**
     * 积分等级名称
     */
    private String pointLevelName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 今日是否已获得反馈奖励
     */
    private Boolean todayFeedbackRewarded;
}
