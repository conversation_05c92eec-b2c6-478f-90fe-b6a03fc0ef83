package com.somytrip.entity.vo.hotel.sms;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-09-23 17:04
 */
@Data
public class HotelTieredRefundRuleVo {
    // 入住前天数
    @Deprecated
    private int daysBeforeCheckIn;
    // 入住前时间
    @Deprecated
    private String timeBeforeCheckIn;
    // 手续费百分比
    @Deprecated
    private BigDecimal refundFeePercentage;


    //入住前N小时免费取消
    private Integer limitedTimeReTime;
    //限时取消收费类型：1-首晚房费，2-按百分比收取
    private Integer limitedTimeReSelect;
    //百分比
    private BigDecimal limitedTimeRePercentage;
}
