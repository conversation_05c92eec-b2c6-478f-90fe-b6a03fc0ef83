package com.somytrip.entity.vo.visa;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 创建订单返回
 * @author: pigeon
 * @created: 2023-10-28 9:22
 */
@Data
@TableName(value = "third_visa_order", autoResultMap = true)
public class CreateVisaOrderReturn {
    /**
     * 订单ID
     */
    @TableId(type = IdType.INPUT)
    @JSONField(name = "order_id")
    @JsonProperty("order_id")
    private Integer orderId;
    /**
     * 支付状态
     */
    @JSONField(name = "pay_status")
    @JsonProperty("pay_status")
    private Integer payStatus;
    /**
     * 订单状态
     */
    @JSONField(name = "order_status")
    @JsonProperty("order_status")
    private Integer orderStatus;
    /**
     * 申请人信息列表
     */
    @JSONField(name = "applicant_set")
    @JsonProperty("applicant_set")
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<ApplicantSet> applicantSet;
}
