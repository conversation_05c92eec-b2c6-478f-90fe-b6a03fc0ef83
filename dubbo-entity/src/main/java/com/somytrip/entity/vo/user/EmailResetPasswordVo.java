package com.somytrip.entity.vo.user;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 邮箱重设密码
 * @author: pigeon
 * @created: 2024-12-19 14:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Valid
public class EmailResetPasswordVo {
    /**
     * 密码登陆必须填写
     */
    @Size(max = 256)
    @NotBlank(message = "password is empty")
    private String password;
    /**
     * 邮箱登陆
     */
    @NotBlank
    @Email
    @Size(min = 3, max = 256, message = "Email length must be between 3 and 256 characters.")
    private String email;
    /**
     * 验证码登陆必须填写
     */
    @Size(max = 6, min = 6, message = "api.user.consumer.message.length-limit")
    private String verifyCode;
}
