package com.somytrip.entity.vo.community;

import lombok.Data;

import java.util.List;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.vo.community
 * @className: FeedbackCommentListRes
 * @author: lijunqi
 * @description:
 * @date: 2025/8/4 17:37
 * @version: 1.0
 */
@Data
public class FeedbackCommentListRes {

    /**
     * 留言列表数据
     */
    private List<FeedbackCommentVo> commentList;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 总页数
     */
    private Long totalPages;

    /**
     * 当前页码
     */
    private Integer currentPage;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
}