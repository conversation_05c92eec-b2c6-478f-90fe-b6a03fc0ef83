package com.somytrip.entity.vo.order;

import jakarta.validation.constraints.*;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * @Description:
 * @author: pigeon
 * @created: 2023-11-01 9:19
 */
@Data
@Validated
public class OrderBaseVo {
    /**
     * 商品ID
     */
    @Positive(message = "商品ID不能为空")
    private Integer spuId;
    /**
     * 商品价格 单位：分
     */
    @Positive(message = "商品价格不能为空")
    private Integer spuPrice;
    /**
     * 商品类型 1.实物 2.虚拟 3.服务
     */
    @Min(value = 0, message = "枚举类型0-2")
    @Max(value = 2, message = "枚举类型0-2")
    private Integer spuType;
    /**
     * 下单用户
     */
    @Positive(message = "用户ID不能为空")
    private Integer uid;
    /**
     * 品牌信息
     */
    @NotBlank(message = "品牌信息不能为空")
    private String brandInfo;
    /**
     * 结算价 单位：分
     */
    @NotNull(message = "结算价 不能为空")
    @Min(value = 0, message = "最小值为0")
    private Integer settlePrice;
    @NotBlank(message = "结算货币不能为空")
    private String currency;
    /**
     * 其他信息
     */
    private String other;

    /**
     * 非需要传入字段
     */
    private String orderNo;
}
