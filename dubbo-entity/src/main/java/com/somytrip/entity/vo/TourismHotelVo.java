package com.somytrip.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.tourism.DefaultCoverUrlEnums;
import com.somytrip.entity.vo.hotel.HotelVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: TourismHotelVo
 * @Description: 攻略酒店vo
 * @Author: shadow
 * @Date: 2023/12/6 10:38
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TourismHotelVo {

    /**
     * 酒店来源(NCNB: 捷旅, elong: 同程, smt: 搜旅酒店加盟)
     */
    private String hotelOrigin;

    /**
     * 酒店ID
     */
    private String id;

    /**
     * 酒店名称
     */
    private String title;

    /**
     * 酒店地址
     */
    private String address;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lng;

    /**
     * 城市名
     */
    private String city;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 国家名
     */
    private String country;

    /**
     * 封面图
     */
    private String cover;

    /**
     * 推荐价格
     */
    @JsonProperty("advice_price")
    private String advicePrice;

    /**
     * 评分
     */
    private String score;

    /**
     * 数据下标(业务中用)
     */
    private Integer index;

    /**
     * 类型字段, 固定为"hotel"(业务中用)
     */
    private String type = "hotel";

    /**
     * 时间(morning, afternoon, evening)(业务中用)
     */
    private String time;

    /**
     * 是否高亮(业务中用)
     */
    private boolean isHighlight = false;

    public TourismHotelVo(HotelVo hotelVo) {
        if (hotelVo != null) {
            this.hotelOrigin = hotelVo.getHotelOrigin();
            this.id = hotelVo.getId();
            this.title = hotelVo.getHotelName();
            this.address = hotelVo.getAddress();
            this.lat = hotelVo.getLat();
            this.lng = hotelVo.getLon();
            this.city = hotelVo.getCityName();
            this.country = hotelVo.getCountryName();
            if (hotelVo.getHotelImages() == null || hotelVo.getHotelImages().isEmpty()) {
                this.cover = DefaultCoverUrlEnums.hotel.getUrl();
            } else {
                this.cover = hotelVo.getHotelImages().get(0).getImageUrl();
            }
            this.advicePrice = hotelVo.getStartPrice();
            this.score = hotelVo.getScore();
        }
    }
}
