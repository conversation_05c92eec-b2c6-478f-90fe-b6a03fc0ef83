package com.somytrip.entity.vo.user;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.vo.user
 * @className: UserPointLogVo
 * @author: lijunqi
 * @description:
 * @date: 2025/8/5 15:29
 * @version: 1.0
 */
@Data
public class UserPointLogVo {

    /**
     * 流水ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 积分变动值（正数为获得，负数为消费）
     */
    private Integer changeAmount;

    /**
     * 变动后的积分余额
     */
    private Integer balanceAfter;

    /**
     * 变动类型
     */
    private String changeType;

    /**
     * 变动类型显示名称
     */
    private String changeTypeName;

    /**
     * 业务操作类型
     */
    private String businessType;

    /**
     * 业务操作类型显示名称
     */
    private String businessTypeName;

    /**
     * 关联业务ID
     */
    private String relatedId;

    /**
     * 变动描述
     */
    private String description;

    /**
     * 来源平台：1-小程序 2-APP
     */
    private Integer sourcePlatform;

    /**
     * 来源平台显示名称
     */
    private String sourcePlatformName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建时间显示文本
     */
    private String createTimeShow;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 变动类型标识：1-获得 -1-消费
     */
    private Integer changeFlag;
}