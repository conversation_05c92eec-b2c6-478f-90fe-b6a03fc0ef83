package com.somytrip.entity.vo.user;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Description: Oauth2谷歌渠道登陆VO
 * @author: pigeon
 * @created: 2024-12-19 14:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Valid
@EqualsAndHashCode(callSuper = false)
public class Oauth2GoogleVo extends Oauth2ChannelVo {
    /**
     * 邮箱
     */
    @NotBlank
    @Email
    @Size(max = 256)
    private String email;
    /**
     * 昵称
     */
    @NotBlank
    @Size(max = 32)
    private String nickname;
    /**
     * 渠道（web、app）下唯一标识
     */
    @NotBlank
    @Size(max = 128)
    private String openId;
    /**
     * 渠道唯一标识
     */
    @NotBlank
    @Size(max = 128)
    private String unionId;
    /**
     * 头像链接
     */
    private String avatar;
}
