package com.somytrip.entity.vo.flight;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-11 15:46
 */
@Data
public class FlightListVo {
    private List<FlightInfoVo> cheap;
    private List<FlightInfoVo> comfort;
    private List<FlightInfoVo> performance;

    public FlightListVo() {
        this.cheap = new ArrayList<>();
        this.comfort = new ArrayList<>();
        this.performance = new ArrayList<>();
    }

    public FlightListVo(List<FlightInfoVo> cheap, List<FlightInfoVo> comfort, List<FlightInfoVo> performance) {
        this.cheap = cheap;
        this.comfort = comfort;
        this.performance = performance;
    }
}
