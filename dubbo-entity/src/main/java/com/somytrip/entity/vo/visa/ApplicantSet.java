package com.somytrip.entity.vo.visa;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description:
 * @author: yefuxing
 * @created: 2023-10-28 9:34
 */
@Data
public class ApplicantSet {
    /**
     * 第三方申请人编号
     */
    @JsonProperty("applicant_no")
    @JSONField(name = "applicant_no")
    private String applicantNo;
    /**
     * 申请人姓名
     */
    @JsonProperty("applicant_name")
    @JSONField(name = "applicant_name")
    private String applicantName;
    /**
     * 申请人ID
     */
    @JsonProperty("applicant_id")
    @JSONField(name = "applicant_id")
    private Integer applicantId;
    /**
     * 申请人状态
     */
    @JsonProperty("applicant_status")
    @JSONField(name = "applicant_status")
    private Integer applicantStatus;
}
