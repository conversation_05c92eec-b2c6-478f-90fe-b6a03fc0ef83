package com.somytrip.entity.vo.community;

import com.somytrip.entity.FileInfo;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: feedback_platform
 * @package: com.somytrip.entity.vo.community
 * @className: FeedbackCommentVo
 * @author: lijunqi
 * @description:
 * @date: 2025/8/4 17:50
 * @version: 1.0
 */
@Data
public class FeedbackCommentVo {

    /**
     * 留言ID
     */
    private Long id;

    /**
     * 反馈ID
     */
    private Long feedbackId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户加密ID
     */
    private String userSecretId;

    /**
     * 用户头像URL
     */
    private String userAvatar;

    /**
     * 用户昵称
     */
    private String userNickname;

    /**
     * 留言内容
     */
    private String content;

    /**
     * 图片列表
     */
    private List<FileInfo> images;

    /**
     * 发表时间
     */
    private LocalDateTime createTime;

    /**
     * 发表时间显示文本
     */
    private String createTimeShow;

    /**
     * IP地址显示（省份-城市格式）
     */
    private String ipLocationShow;

    /**
     * 用户类型：1-官方 2-作者 3-普通用户
     */
    private Integer userType;

    /**
     * 用户类型显示文本
     */
    private String userTypeShow;

    /**
     * 父级留言ID（回复时使用）
     */
    private Long parentId;

    /**
     * 被回复的用户ID
     */
    private Long replyToUserId;

    /**
     * 被回复的用户昵称
     */
    private String replyToUserNickname;

    /**
     * 被回复的用户类型：1-官方 2-作者 3-普通用户
     */
    private Integer replyToUserType;

    /**
     * 被回复的用户类型显示文本
     */
    private String replyToUserTypeShow;
}
