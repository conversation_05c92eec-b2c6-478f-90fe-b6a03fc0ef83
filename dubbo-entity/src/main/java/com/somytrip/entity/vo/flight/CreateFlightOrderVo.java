package com.somytrip.entity.vo.flight;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.dto.user.UserTripInfoDto;
import com.somytrip.model.flight.enums.CreditTypeEnum;
import com.somytrip.model.flight.enums.GenderEnum;
import com.somytrip.model.flight.enums.PassengerTypeEnum;
import com.somytrip.model.flight.enums.ProviderSourceEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 创建订单封装
 * @author: pigeon
 * @created: 2024-03-14 15:14
 */
@Data
@Valid
@AllArgsConstructor
@NoArgsConstructor
public class CreateFlightOrderVo {
    private String userId;
    /**
     * 国内0 国外1
     */
    @NotNull
    private Integer sourceType;
    /**
     * 联系人信息
     */
    @NotNull
    @Valid
    private Contact contactInfo;
    /**
     * 乘客个人信息列表
     */
    @NotNull
    private List<PassengerInfo> passengers;
    /**
     * 精确询价接口request
     */
    @NotNull
    @Valid
    private QueryFlightAccurateRequest accurateRequest;
    /**
     * 商品来源
     */
    @NotNull
    private ProviderSourceEnum productSource;
    /**
     * 保险序列号
     */
    private String insuranceSequenceNo;
    /**
     * ai会话sessionId
     */
    private String sessionId;


    public CreateFlightOrderVo(CreateFlightOrderV2Vo vo, List<PassengerInfo> passengers) {
        this.sourceType = vo.getSourceType();
        this.contactInfo = vo.getContactInfo();
        this.passengers = passengers;
        this.accurateRequest = vo.getAccurateRequest();
        this.productSource = vo.getProductSource();
        this.insuranceSequenceNo = vo.getInsuranceSequenceNo();
        this.sessionId = vo.getSessionId();
    }

    @Data
    @Valid
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PassengerInfo {
        /**
         * 姓 叶 ye Smith
         */
        @NotNull
        private String lastName;
        /**
         * 名 公 gon Mary
         */
        @NotBlank
        private String firstName;
        @NotNull
        private GenderEnum gender;
        /**
         * 乘客类型
         */
        @NotNull
        private PassengerTypeEnum type;
        /**
         * 证件类型
         */
        @NotNull
        private CreditTypeEnum creditType;
        /**
         * 国籍
         */
        private String notion;
        /**
         * 国籍二字码
         */
        private String notionCode;
        /**
         * 证件发行国家
         */
        private String creditIssueNotion;
        /**
         * 证件号码
         */
        @NotBlank
        private String creditNo;
        /**
         * 证件有效期
         */
        private String creditValidDate;
        /**
         * 联系电话
         */
        @NotBlank
        private String linkPhone;
        /**
         * 出生日期
         */
        @NotBlank
        private String birthday;

        public PassengerInfo(UserTripInfoDto dto) {
            this.lastName = dto.getWord();
            this.firstName = dto.getSurname();
            if (StrUtil.isBlank(firstName) && StrUtil.isBlank(lastName)) {
                int length = StrUtil.length(dto.getName());
                this.firstName = StrUtil.sub(dto.getName(), 0, length / 2);
                this.lastName = StrUtil.sub(dto.getName(), length / 2, length);
            } else if (StrUtil.isBlank(firstName)) {
                this.firstName = StrUtil.removeAll(dto.getName(), lastName);
            } else if (StrUtil.isBlank(lastName)) {
                this.lastName = StrUtil.removeAll(dto.getName(), firstName);
            }
            this.gender = GenderEnum.valueOf(dto.getGender().name());
            this.type = PassengerTypeEnum.ADULT;
            this.creditType = CreditTypeEnum.valueOf(dto.getIdentity().name());
            this.notion = dto.getNationality();
            this.notionCode = dto.getNationality();
            this.creditIssueNotion = dto.getIssue();
            this.creditNo = dto.getIdentityNumber();
            this.creditValidDate = dto.getPeriod();
            this.linkPhone = dto.getIphone();
            this.birthday = dto.getDate();
        }
    }

    @Data
    @Valid
    public static class Contact {
        /**
         * 姓名
         */
        @NotBlank(message = "{api.flight.non-contactInfo-name}")
        private String name;
        /**
         * 手机号
         */
        @NotBlank(message = "{api.flight.non-contactInfo-mobile}")
        private String mobile;
        /**
         * 邮箱
         */
        private String email;

        public Contact() {
        }

        public Contact(String name, String mobile, String email) {
            this.name = name;
            this.mobile = mobile;
            this.email = email;
        }
    }

}
