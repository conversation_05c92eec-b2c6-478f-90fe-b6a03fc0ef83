package com.somytrip.entity.vo.hotel;

import com.somytrip.entity.hotel.HotelFacilityV2Entity;
import com.somytrip.entity.hotel.NCNB.NCNBBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: HotelFacilityVo
 * @Description: 酒店设施vo
 * @Author: shadow
 * @Date: 2024/2/28 16:02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HotelFacilityVo {

    /**
     * 设施ID
     */
    private String facilityId;

    /**
     * 设施名称
     */
    private String facilityName;

    /**
     * 设施值
     */
    private String facilityValue;

    public HotelFacilityVo(NCNBBaseEntity.FacilityInfos.Service ncnbFacility) {
        this.facilityName = ncnbFacility.getServiceName();
    }

    public HotelFacilityVo(HotelFacilityV2Entity entity) {
        this.facilityId = String.valueOf(entity.getFacilityId());
        this.facilityName = entity.getFacilityName();
    }
}
