package com.somytrip.entity.vo.feedback;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.vo.feedback
 * @className: SubmitFeedbackVo
 * @author: l<PERSON><PERSON><PERSON>
 * @description: 提交反馈建议响应VO
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubmitFeedbackVo {

    /**
     * 反馈ID
     */
    private Long feedbackId;
}
