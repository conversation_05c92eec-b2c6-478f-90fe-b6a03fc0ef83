package com.somytrip.utils;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.dto.common.IpLocationInfo;
import jakarta.servlet.http.HttpServletRequest;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.utils
 * @className: IpUtils
 * @author: lijunqi
 * @description: IP地址工具类
 * @date: 2025-01-04 15:30:00
 * @Version: 1.0
 */
public class IpUtils {

    /**
     * 获取客户端真实IP地址
     *
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public static String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }

        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            // 多次反向代理后会有多个IP值，第一个为真实IP
            int index = ip.indexOf(',');
            if (index != -1) {
                return ip.substring(0, index).trim();
            } else {
                return ip.trim();
            }
        }

        ip = request.getHeader("X-Real-IP");
        if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip.trim();
        }

        ip = request.getHeader("Proxy-Client-IP");
        if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip.trim();
        }

        ip = request.getHeader("WL-Proxy-Client-IP");
        if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip.trim();
        }

        ip = request.getHeader("HTTP_CLIENT_IP");
        if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip.trim();
        }

        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip.trim();
        }

        return request.getRemoteAddr();
    }

    /**
     * 判断IP是否为内网IP
     *
     * @param ip IP地址
     * @return 是否为内网IP
     */
    public static boolean isInternalIp(String ip) {
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            int firstPart = Integer.parseInt(parts[0]);
            int secondPart = Integer.parseInt(parts[1]);

            // 10.0.0.0 - **************
            if (firstPart == 10) {
                return true;
            }

            // ********** - **************
            if (firstPart == 172 && secondPart >= 16 && secondPart <= 31) {
                return true;
            }

            // *********** - ***************
            if (firstPart == 192 && secondPart == 168) {
                return true;
            }

            // ********* - *************** (localhost)
            if (firstPart == 127) {
                return true;
            }

        } catch (NumberFormatException e) {
            return false;
        }

        return false;
    }

    /**
     * 解析IP地址获取位置信息
     * 注意：这是一个示例方法，实际项目中需要集成第三方IP地址库或API
     *
     * @param ipAddress IP地址
     * @return IP位置信息
     */
    public static IpLocationInfo parseIpLocation(String ipAddress) {
        if (StrUtil.isBlank(ipAddress) || "unknown".equalsIgnoreCase(ipAddress)) {
            return new IpLocationInfo(ipAddress, "未知", "未知", "未知", "未知");
        }

        // 本地IP处理
        if (isInternalIp(ipAddress) || "127.0.0.1".equals(ipAddress) || "localhost".equals(ipAddress)) {
            return new IpLocationInfo(ipAddress, "本地", "本地", "中国", "本地网络");
        }

        // TODO: 集成第三方IP地址库或API服务
        // 这里可以集成如下服务：
        // 1. 离线IP数据库（如：ip2region、GeoLite2等）
        // 2. 在线API服务（如：百度地图API、高德地图API等）
        // 3. 自建IP地址数据库

        // 示例返回，实际使用时需要替换为真实的IP解析逻辑
        return parseIpLocationMock(ipAddress);
    }

    /**
     * 模拟IP地址解析（仅用于示例）
     * 实际项目中应该替换为真实的IP解析服务
     *
     * @param ipAddress IP地址
     * @return 模拟的位置信息
     */
    private static IpLocationInfo parseIpLocationMock(String ipAddress) {
        // 这里只是示例代码，实际使用时需要集成真实的IP地址库
        if (ipAddress.startsWith("192.168.") || ipAddress.startsWith("10.") ||
            ipAddress.startsWith("172.")) {
            return new IpLocationInfo(ipAddress, "本地", "本地", "中国", "内网");
        }

        // 默认返回未知位置
        return new IpLocationInfo(ipAddress, "未知", "未知", "未知", "未知");
    }
}
