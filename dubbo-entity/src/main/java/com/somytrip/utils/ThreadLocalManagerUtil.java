package com.somytrip.utils;

import com.somytrip.entity.enums.hotel.LanguageEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: ThreadLocalManagerUtil
 * @Description: ThreadLocal 工具类
 * @Author: shadow
 * @Date: 2023/11/6 2:20
 */
@Slf4j
public class ThreadLocalManagerUtil {

    /**
     * 存储请求头信息
     */
    private final static ThreadLocal<HeaderInfo> headerInfoThreadLocal = new ThreadLocal<>();

    public static void add(HeaderInfo headerInfo) {
        headerInfoThreadLocal.set(headerInfo);
    }

    public static String getToken() {
        return headerInfoThreadLocal.get().getToken();
    }

    public static String getLanguage() {
        return headerInfoThreadLocal.get() != null ? headerInfoThreadLocal.get().getLanguage() : LanguageEnum.ZH_CN.getName();
    }

    /**
     * 释放资源
     */
    public static void remove() {
        headerInfoThreadLocal.remove();
    }

    @Data
    public static class HeaderInfo {

        /**
         * 用户token信息
         */
        private String token;

        /**
         * 国际化语言包名称
         */
        private String language;
    }
}
