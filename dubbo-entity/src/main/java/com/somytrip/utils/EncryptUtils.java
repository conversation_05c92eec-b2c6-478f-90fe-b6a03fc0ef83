package com.somytrip.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SM4;
import com.somytrip.entity.EncryptData;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-23 16:40
 */
@Slf4j
public class EncryptUtils {
    public static String sm4Encrypt(EncryptData data) {
        if (StrUtil.isBlank(data.getContent())) {
            return data.getContent();
        }
        if (data.getEncryptInfo().getSignType() == EncryptData.SignType.SM4) {
            SM4 sm4 = SmUtil.sm4(data.getEncryptInfo().getKey().getBytes(StandardCharsets.UTF_8));
            sm4.setIv(data.getEncryptInfo().getIv().getBytes(StandardCharsets.UTF_8));
            return sm4.encryptHex(data.getContent());
        }
        throw new RuntimeException("sign type error");
    }

    public static String sm4Decrypt(EncryptData data) {
        if (StrUtil.isBlank(data.getContent())) {
            return data.getContent();
        }
        if (data.getEncryptInfo().getSignType() == EncryptData.SignType.SM4) {
            SM4 sm4 = SmUtil.sm4(data.getEncryptInfo().getKey().getBytes(StandardCharsets.UTF_8));
            sm4.setIv(data.getEncryptInfo().getIv().getBytes(StandardCharsets.UTF_8));
            return sm4.decryptStr(data.getContent());
        }
        throw new RuntimeException("sign type error");
    }

    public static void main(String[] args) {
        EncryptData encryptData = new EncryptData("你好", new EncryptData.Encrypt("rx8fmiditum3nd8x", "4vejacc8i63pluyi", EncryptData.SignType.SM4));
        String enS = sm4Encrypt(encryptData);
        log.info("{}", enS);
        encryptData.setContent(enS);
        String s = sm4Decrypt(encryptData);
        log.info("{}", s);
    }
}
