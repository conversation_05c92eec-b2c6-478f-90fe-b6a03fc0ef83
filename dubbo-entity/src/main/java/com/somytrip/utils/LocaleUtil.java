package com.somytrip.utils;

import java.text.MessageFormat;
import java.util.Locale;
import java.util.MissingResourceException;
import java.util.ResourceBundle;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.utils
 * @className: LocaleUtil
 * @author: shadow
 * @description: 多语言工具类
 * @date: 2025/5/29 15:53
 * @version: 1.0
 */
public class LocaleUtil {

    private static final String BASE_NAME = "messages";

    public static String getLocalizedString(String key, Object[] args, Locale locale) {

        ResourceBundle bundle = ResourceBundle.getBundle(BASE_NAME, locale);

        try {
            String pattern = bundle.getString(key);
            return MessageFormat.format(pattern, args);
        } catch (MissingResourceException e) {
            // 如果找不到对应的资源，返回默认的英文消息
            ResourceBundle defaultBundle = ResourceBundle.getBundle(BASE_NAME, Locale.CHINA);
            return MessageFormat.format(defaultBundle.getString(key), args);
        }
    }

    public static String getLocalizedString(String key, Locale locale) {

        ResourceBundle bundle = ResourceBundle.getBundle(BASE_NAME, locale);

        Object[] args = new Object[]{};
        try {
            String pattern = bundle.getString(key);
            return MessageFormat.format(pattern, args);
        } catch (MissingResourceException e) {
            // 如果找不到对应的资源，返回默认的英文消息
            ResourceBundle defaultBundle = ResourceBundle.getBundle(BASE_NAME, Locale.CHINA);
            return MessageFormat.format(defaultBundle.getString(key), args);
        }
    }
}
