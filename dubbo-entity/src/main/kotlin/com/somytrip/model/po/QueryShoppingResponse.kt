package com.somytrip.model.po

import cn.hutool.core.util.StrUtil
import com.somytrip.entity.vo.flight.AirLineCodeVo
import com.somytrip.model.flight.common.ProductInfo
import com.somytrip.model.flight.enums.PassengerTypeEnum
import com.somytrip.model.flight.tc.TCSegmentItem
import com.somytrip.util.FlightTCUtils
import java.io.Serializable

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-02-27 11:33
 */

data class QueryShoppingResponse(
    val prices: List<QueryShoppingPriceItem>,
    val segments: List<QueryShoppingSegmentItem>,
    val cabins: List<QueryShoppingCabinItem>?,
    val productInfo: ProductInfo,
    val durationTotal: String,
    val sourceType: Int,
) : Serializable


data class QueryShoppingCabinItem(
    /**
     * 舱等
     */
    val cabinClass: String,
    /**
     * 舱等 中文
     */
    val cabinClassDisplay: String,
    /**
     * 舱位
     */
    val cabinCode: String,
    /**
     * 剩余座位数
     */
    val seat: Int,
    /**
     * 航段序号
     */
    val segmentIndex: Int,
) : Serializable

data class QueryShoppingSegmentStopItem(
    val stopAirport: String = "",
    val stopTime: String = "0",
)

data class QueryShoppingSegmentItem(
    /**
     * 实际航司
     */
    val operationAirline: String,
    /**
     * 实际航班号
     */
    val operationFlightNo: String,
    /**
     * 航司 IATA 二字码，必须与 flightNumber 航司相同
     */
    val airline: String,
    /**
     * 往返 0 去程 1 返
     */
    val tripType: Int,
    /**
     * 共享航班标识 false 非共享 true共享
     */
    val flightShare: Boolean,
    /**
     * 航班号，如：CA123
     */
    val flightNo: String,
    /**
     * 机型
     */
    val aircraft: String,
    /**
     * 机型类型
     */
    val aircraftType: String,
    /**
     * 到达机场 IATA 三字码
     */
    val arriveAirport: String,
    /**
     * 抵达航站楼，使用简写，例如T1
     */
    val arriveAirportTerm: String,
    /**
     * 到达时间，格式：YYYY-MM-DD HH:MM
     */
    val arriveDateTime: String,
    /**
     * 到达机场 IATA 三字码
     */
    val departAirport: String,
    /**
     * 抵达航站楼，使用简写，例如T1
     */
    val departAirportTerm: String,
    /**
     * 到达时间，格式：YYYY-MM-DD HH:MM
     */
    val departDateTime: String,

    /**
     * 餐食信息
     * 未知 "" 有 "有餐食" 无 "无餐食"
     */
    val meal: String,
    /**
     * 航班里程数 单位：km
     */
    val mileage: String,
    /**
     * 行程序号从1开始
     */
    val segmentIndex: Int,
    /**
     * 经停机场iata码
     */
    val stops: List<QueryShoppingSegmentStopItem>?,
    /**
     * 1d2h3m
     */
    val stopTime: String,
    /**
     * 1d2h3m
     */
    val duration: String,
    /**
     * 1d2h3m
     */
    val durationTotal: String,
    val arriveAirportDisplay: String,
    val departAirportDisplay: String,
    val airlineDisplay: AirLineCodeVo?,
    val stopsDisplay: List<String>?,
    var transitTime: String,
) : Serializable, Comparable<QueryShoppingSegmentItem> {
    override fun compareTo(other: QueryShoppingSegmentItem): Int {
        return StrUtil.compare(this.departDateTime, other.departDateTime, true)
    }


    constructor(
        item: TCSegmentItem,
        arriveAirportDisplay: String,
        departAirportDisplay: String,
        airlineDisplay: AirLineCodeVo,
        airportFn: (x: String) -> String,
        transitTime: Array<String>
    ) : this(
        item.operatingAirline,
        item.operatingFlightNo,
        item.marketingAirline,
        item.segmentType - 1,
        item.flightShare,
        item.marketingFlightNo,
        item.aircraft,
        "${item.aircraftType}",
        item.arrAirport,
        item.arrAirportTerm,
        item.arrDateTime,
        item.depAirport,
        item.depAirportTerm,
        item.depDateTime,
        FlightTCUtils.convertMeal(item.meal),
        "${item.mileage}",
        item.segmentIndex,
        item.stops.map {
            QueryShoppingSegmentStopItem(it.stopAirport, FlightTCUtils.convertMin(it.stopTime.toInt()))
        },
        FlightTCUtils.convertMin(item.stopTime),
        FlightTCUtils.convertMin(item.duration),
        FlightTCUtils.convertMin(item.stopTime + item.duration),
        arriveAirportDisplay,
        departAirportDisplay,
        airlineDisplay,
        item.stops.map { airportFn(it.stopAirport) },
        ""
    ) {
        if (this.tripType != transitTime[3].toInt()) {
            transitTime[3] = this.tripType.toString()
            transitTime[0] = item.depDateTime;
        }
        transitTime[1] = item.depDateTime;
        val calculateMinutesDifference =
            FlightTCUtils.calculateMinutesDifference(transitTime[0], transitTime[1]).toInt()
        val durationTotalIntVal = transitTime[2].toInt() + calculateMinutesDifference + item.stopTime + item.duration
        transitTime[2] = durationTotalIntVal.toString()
        this.transitTime = FlightTCUtils.convertMin(calculateMinutesDifference)
        transitTime[0] = item.arrDateTime;
    }

    constructor(item: TCSegmentItem, transitTime: Array<String>) : this(
        item.operatingAirline,
        item.operatingFlightNo,
        item.marketingAirline,
        item.segmentType - 1,
        item.flightShare,
        item.marketingFlightNo,
        item.aircraft,
        "${item.aircraftType}",
        item.arrAirport,
        item.arrAirportTerm,
        item.arrDateTime,
        item.depAirport,
        item.depAirportTerm,
        item.depDateTime,
        FlightTCUtils.convertMeal(item.meal),
        "${item.mileage}",
        item.segmentIndex,
        item.stops.map {
            QueryShoppingSegmentStopItem(it.stopAirport, FlightTCUtils.convertMin(it.stopTime.toInt()))
        },
        FlightTCUtils.convertMin(item.stopTime),
        FlightTCUtils.convertMin(item.duration),
        FlightTCUtils.convertMin(item.stopTime + item.duration),
        "",
        "",
        null,
        listOf(),
        ""
    ) {
        if (this.tripType != transitTime[3].toInt()) {
            transitTime[3] = this.tripType.toString()
            transitTime[0] = item.depDateTime;
        }
        transitTime[1] = item.depDateTime;
        val calculateMinutesDifference =
            FlightTCUtils.calculateMinutesDifference(transitTime[0], transitTime[1]).toInt()
        transitTime[2] =
            (transitTime[2].toInt() + calculateMinutesDifference + item.stopTime + item.duration).toString()
        this.transitTime = FlightTCUtils.convertMin(calculateMinutesDifference)
        transitTime[0] = item.arrDateTime;
    }
}

data class QueryShoppingPriceItem(
    /**
     * 乘客类型
     */
    val passengerType: PassengerTypeEnum,
    /**
     * 燃油机建费用-非税收-总共
     */
    val taxTotal: String,
    /**
     * 售价
     */
    val price: Int,
    /**
     * 燃油机建费用-明细
     */
    val taxDetail: QueryShoppingPriceItemTaxDetail,
) : Serializable, Comparable<QueryShoppingPriceItem> {
    override fun compareTo(other: QueryShoppingPriceItem): Int {
        return this.price - other.price
    }
}

data class QueryShoppingPriceItemTaxDetail(
    /**
     * 燃油
     */
    val yq: String,
    /**
     * 机建
     */
    val tax: String,
) : Serializable