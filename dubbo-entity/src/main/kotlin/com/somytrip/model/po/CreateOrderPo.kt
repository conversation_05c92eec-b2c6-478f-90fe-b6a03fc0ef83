package com.somytrip.model.po

import com.somytrip.entity.vo.flight.QueryFlightAccurateResponse
import com.somytrip.model.flight.tc.TCOrderRequest

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-15 15:06
 */

data class TCOrderRequestPo(
    val request: TCOrderRequest = TCOrderRequest(),
    val ruleUnit: QueryFlightAccurateResponse.FlightUnitRuleItem = QueryFlightAccurateResponse.FlightUnitRuleItem(),
    val totalTax: String = "",
    val totalPrice: String = "",
)


data class TCOrderResponsePo(
    val orderSerialNo: String = "",
    val totalPrice: String = "",
    val products: List<TCOrderResponsePoProduct> = emptyList(),
    val accurate: TCOrderRequestPo = TCOrderRequestPo(),
)

data class TCOrderResponsePoProduct(
    val productType: String = "",
    val state: Boolean = false,
    val unitKey: String = "",
    val productDetail: String = "",
    val productTotalPrice: String = "",
)
