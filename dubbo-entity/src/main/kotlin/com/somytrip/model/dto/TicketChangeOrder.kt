package com.somytrip.model.dto

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import com.somytrip.model.flight.enums.ProviderSourceEnum
import java.time.LocalDateTime

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-01 15:38
 */
@TableName("ticket_change_order")
data class TicketChangeOrder(
    @TableId(type = IdType.AUTO)
    val id: Long = 0,
    val orderNo: String = "",
    /**
     * 外部订单-订单号
     */
    val outOrderNo: String = "",
    /**
     * 改签订单号
     */
    val changeNo: String = "",
    /**
     * 外部订单-改签订单号
     */
    val outChangeNo: String = "",
    /**
     * 供应商
     */
    val source: ProviderSourceEnum = ProviderSourceEnum.DEFAULT,
    val createTime: LocalDateTime = LocalDateTime.now(),
    val updateTime: LocalDateTime = LocalDateTime.now(),
)