package com.somytrip.model.dto

import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import com.somytrip.model.flight.enums.ZfOrderType
import java.time.LocalDateTime

/**
 * @Description: 智飞科技申请记录表
 * @author: pigeon
 * @created: 2025-01-15 10:51
 */
@TableName("zf_apply_record")
data class ZfApplyRecord(
    val type: ZfOrderType = ZfOrderType.ORIGIN,
    val orderNo: String = "",
    val outOriginId: String = "",
    val outNewId: String = "",
    val deleteFlag: Boolean = false,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    @TableId(type = IdType.AUTO)
    val id: Long = 0,
)