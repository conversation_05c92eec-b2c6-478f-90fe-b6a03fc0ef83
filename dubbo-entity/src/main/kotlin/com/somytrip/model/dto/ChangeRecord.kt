package com.somytrip.model.dto

import com.alibaba.fastjson2.JSONObject
import com.baomidou.mybatisplus.annotation.IdType
import com.baomidou.mybatisplus.annotation.TableField
import com.baomidou.mybatisplus.annotation.TableId
import com.baomidou.mybatisplus.annotation.TableName
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-01 15:38
 */
@TableName("change_record", autoResultMap = true)
data class ChangeRecord(
    @TableId(type = IdType.INPUT)
    val orderNo: String = "",
    val outOrderNo: String = "",
    @TableField(typeHandler = Fastjson2TypeHandler::class)
    val ext: JSONObject = JSONObject(),
)