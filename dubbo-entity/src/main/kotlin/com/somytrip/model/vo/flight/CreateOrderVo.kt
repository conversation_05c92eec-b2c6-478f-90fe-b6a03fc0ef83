package com.somytrip.model.vo.flight

import com.somytrip.model.flight.enums.CreditTypeEnum
import com.somytrip.model.flight.enums.PassengerTypeEnum
import com.somytrip.model.flight.enums.ProductOrderTypeEnum

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-17 10:23
 */

data class CreateOrderVo(
    /**
     * 0国内 1国外
     */
    val locationType: Int,
    /**
     * 联系人
     */
    val contact: CreateOrderContact,
    /**
     * 分销产品信息
     */
    val products: List<CreateOrderProduct>,
    /**
     * 订单总金额
     */
    val totalPrice: String
)

data class CreateOrderContact(
    /**
     * 联系人姓名
     */
    val name: String,
    /**
     * 联系人详细地址
     */
    val address: String?,
    /**
     * 邮政编码
     */
    val postalCode: String?,
    /**
     * email
     */
    val email: String?,
    /**
     * 手机号
     */
    val phone: String,
    /**
     * 手机号-区号，没有值默认+86
     */
    val phoneAreaCode: String?,
    /**
     * 联系人传真
     */
    val fax: String?
)

data class CreateOrderProduct(
    val price: String,
    val type: ProductOrderTypeEnum,
    val detail: CreateOrderProductDetail,
    val tcData: String,
)

data class CreateOrderProductDetail(
    val officeNo: String?,
    val passengers: List<CreateOrderProductPassengerItem>,
    val prices: List<CreateOrderPriceItem>
)

data class CreateOrderProductPassengerItem(
    /**
     * 姓 李
     */
    val lastName: String,
    /**
     * 名 四
     */
    val firstName: String,
    /**
     * 性别 0 1 2
     */
    val gender: Int,
    /**
     * 乘客类型
     */
    val type: PassengerTypeEnum,
    /**
     * 国籍
     */
    val nation: String?,
    /**
     * 国籍二字码
     */
    val nationCode: String,
    /**
     * 证件类型
     */
    val creditType: CreditTypeEnum,
    /**
     * 证件号
     */
    val creditNo: String,
    /**
     * 证件有效期
     */
    val creditValidate: String,
    /**
     * 证件发行国家
     */
    val cardIssueCountry: String,
    /**
     * 出生日期
     */
    val birthday: String,
    /**
     * 联系电话
     */
    val linkPhone: String?
)

data class CreateOrderPriceItem(
    /**
     * 乘客类型
     */
    val type: PassengerTypeEnum,
    /**
     * 税费
     */
    val tax: String,
    /**
     * 销售价
     */
    val price: String
)