package com.somytrip.model.flight.zf

import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString

/**
 * @Description: 通知Request Body
 * @author: pigeon
 * @created: 2025-01-12 14:18
 */
data class NotifyReq(
    /**
     * journey list
     */
    val journeys: List<OrderJourney> = emptyList(),
    val passengers: List<Any> = emptyList(),
    val priceDetail: List<Any> = emptyList(),
    /**
     * order ID
     */
    val id: String = "",
    /**
     * Allowed values:
     * origin
     * change
     * refund
     * void
     */
    val orderType: String = "",
    /**
     * orgin order id
     */
    val originOrderId: String = "",
    /**
     * parent order id
     */
    val parentOrderId: String = "",
    /**
     * supplier code
     */
    val source: String = "",
    /**
     * order status
     *
     * Allowed values:
     * reviewing - 审核中
     * to_be_paid - 待支付
     * reserved - 已留坐
     * proceeding - 出票中
     * issued - 已出票
     * chg_reviewing - 改签审核中
     * chg_to_be_paid - 改签待支付
     * chg_rejected - 改签待支付
     * chg_proceeding - 改签中
     * changed - 已改签
     * rfd_reviewing - 退票待审核
     * rfd_rejected - 退票已拒绝
     * rfd_to_be_confirm - 退票待确认
     * rfd_proceeding - 退票中
     * refunded_reimbursing - 已退票待退款
     * refunded_reimbursed - 已退票已退款
     * void_proceeding - 废票中
     * void_rejected - 废票被拒绝
     * voided_reimbursing - 已废票待退款
     * voided_reimbursed - 已废票已退款
     * failed - 订单失败
     * failed_reimbursing - 订单失败待退款
     * failed_reimbursed - 订单失败已退款
     * closed - 订单已关闭
     * canceled - 订单已取消
     * holding - 订单已挂起
     */
    val status: String = "",
    /**
     * reject reason
     */
    val rejectReason: String = "",
    /**
     * last payment time for origin and change order, last confirm time for refund order
     */
    val lastActionTime: String = "",
    /**
     * last payment time for origin and change order, last confirm time for refund order
     */
    val lastTicketingTime: String = "",
    /**
     * is void permitted
     */
    val isVoidPermitted: Boolean = false,
    /**
     * void deadline
     */
    val lastVoidTime: String = "",
    /**
     * payment id
     */
    val incomeId: Int = 0,
    /**
     * is order in holding
     */
    val isHolding: Boolean = false,
    /**
     * order created datetime
     */
    val createdAt: String = "",
    /**
     * order completed datetime
     */
    val completedAt: String = "",
    /**
     * order updated datetime
     */
    val updatedAt: String = "",
    val contactEmail: String = "",
    val contactName: String = "",
    val contactRegion: String = "",
    val contactPhone: String = "",
    val voidServiceFee: String = "",
    val extraInfo: ExtraInfo = ExtraInfo(),
) {
    fun orderPassengers(): List<OrderPassenger> {
        return this.passengers.map {
            it.toJSONString().into<OrderPassenger>()
        }
    }

    fun ticketPassengers(): List<TicketPassenger> {
        return this.passengers.map {
            it.toJSONString().into<TicketPassenger>()
        }
    }

    fun refundPrices(): List<RefundPrice> {
        return this.priceDetail.map {
            it.toJSONString().into<RefundPrice>()
        }
    }

    fun refundPricesNew(): List<RefundPrice> {

        return this.priceDetail.map {
            it.toJSONString().into<OrderPriceDetail>()
        }.map {
            it.priceList.map { p ->
                p.toJSONString().into<RefundPrice>()
            }
        }.flatten()
    }

    fun notifyPrices(): List<NotifyPrice> {
        return this.priceDetail.map {
            it.toJSONString().into<NotifyPrice>()
        }
    }
}
