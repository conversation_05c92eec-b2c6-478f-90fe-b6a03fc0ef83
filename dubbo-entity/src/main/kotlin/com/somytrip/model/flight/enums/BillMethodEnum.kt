package com.somytrip.model.flight.enums


/**
 * @Description: 出票枚举
 * @author: pigeon
 * @created: 2024-08-22 11:08
 */
enum class BillMethodEnum(
    val tcValue: Int
) {
    /**
     *
     */
    BSP(1),
    B2B(2),

    /**
     * 官网
     */
    OW(3),

    /**
     * 其他
     */
    OTHER(4),
    ;

    companion object {
        fun tcToEnum(value: Int): BillMethodEnum {
            return entries.find { it.tcValue == value } ?: OTHER
        }
    }
}