package com.somytrip.model.flight.vo

import com.somytrip.model.flight.tc.TCPayCheckProduct

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-08-27 16:22
 */
data class PayReq<out T>(
    val payInfo: T,
)


data class FR24PayInfo(
    val orderNo: String = "",
    val partnerOrderNo: String = "",
    val currency: String = "",
    val totalPrice: String = "",
    val paymentMethod: String = "0",
)

data class PayResponse<out T>(
    val payResult: T,
    val success: Boolean = false,
    val message: String = "",
)


data class TCPayInfo(
    val source: Int = 0,
    val orderSerialNo: String = "",
    val totalPrice: String = "",
    val orderNo: String = "",
    val products: List<TCPayCheckProduct> = emptyList(),
)