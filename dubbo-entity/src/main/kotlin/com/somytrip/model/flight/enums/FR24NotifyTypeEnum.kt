package com.somytrip.model.flight.enums


/**
 * @Description: FR24 通知类型枚举
 * @author: pigeon
 * @created: 2024-08-28 11:39
 */
enum class FR24NotifyTypeEnum(
    val dec: String,
    val code: String,
) {
    ORDER_STATUS("订单状态", "orderStatus"),
    TICKET_NO("票号或航司大编码推送", "ticketNo"),
    SCHEDULE_CHANGE(" 航班变化", "scheduleChange"),
    ANCILLARY_ORDER_STATUS("辅营订单状态", "ancillaryOrderStatus"),
    TICKET_NO_UPDATE("票号或大编码二次推送", "ticketNoUpdate"),
    REFUND("退票相关通知", "refund"),
    CHANGE("改期相关通知", "change"),
    VOID("废票相关通知", "void"),
    ;

    companion object {
        fun codeToEnum(code: String): FR24NotifyTypeEnum {
            return entries.find { it.code == code } ?: ORDER_STATUS
        }
    }
}