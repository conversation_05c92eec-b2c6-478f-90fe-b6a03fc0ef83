package com.somytrip.model.flight.zf

/**
 * @Description: 改签列表查询request
 * @author: pigeon
 * @created: 2025-01-12 17:24
 */
data class RefundReq(
    val orderId: String = "",
    val segmentIds: List<Int> = emptyList(),
    /**
     * passenger travel document number
     */
    val passengers: List<String> = emptyList(),
    /**
     * Enums
     * 0
     * 1
     * 2
     * 3
     * Description
     * voluntarily
     * aviation changes
     * illness or death
     * other
     */
    val reason: Int = 0,
    /**
     * file url
     */
    val fileList: List<String> = emptyList(),
    val reasonDetail: String = "",
    val contactEmail: String = "",
    val contactName: String = "",
    val contactRegion: String = "",
    val contactPhone: String = "",
)
