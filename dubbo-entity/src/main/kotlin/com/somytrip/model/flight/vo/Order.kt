package com.somytrip.model.flight.vo

import com.somytrip.model.flight.common.AccurateRsp
import com.somytrip.model.flight.common.ProductInfo
import com.somytrip.model.flight.enums.CreditTypeEnum
import com.somytrip.model.flight.enums.GenderEnum
import com.somytrip.model.flight.enums.PassengerTypeEnum

/**
 * @Description: 订单相关
 * @author: pigeon
 * @created: 2024-08-27 14:20
 */

data class Contact(
    val name: String = "",
    val mobile: String = "",
    val email: String = "",
)

data class PassengerInfo(
    val lastName: String = "",
    val firstName: String = "",
    val birthday: String = "",
    val linkPhone: String = "",
    val creditValidDate: String = "",
    val creditNo: String = "",
    val creditIssueNotion: String = "",
    val notionCode: String = "",
    val notion: String = "",
    val creditType: CreditTypeEnum = CreditTypeEnum.UNKNOWN,
    val type: PassengerTypeEnum = PassengerTypeEnum.ADULT,
    val gender: GenderEnum = GenderEnum.UNKNOWN,
    val email: String = "",
)

data class CreateOrderReq(
    val sourceType: Int = 0,
    var userId: String = "",
    val insuranceSequenceNo: String = "",
    val sessionId: String = "",
    val accurateRequest: AccurateReq = AccurateReq(),
    val contactInfo: Contact = Contact(),
    val passengers: List<Int> = emptyList(),
    val productInfo: ProductInfo = ProductInfo(),
)

data class CreateOrderRsp(
    val success: Boolean = true,
    val code: Int = 200,
    val message: String = "",
    val orderNo: String = "",
    val accurate: AccurateRsp = AccurateRsp()
)