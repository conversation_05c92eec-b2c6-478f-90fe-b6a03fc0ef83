package com.somytrip.model.flight.common

import com.somytrip.model.flight.enums.ProductTagEnum
import com.somytrip.model.flight.enums.ProviderSourceEnum

/**
 * @Description: 商品信息
 * @author: pigeon
 * @created: 2024-08-20 9:58
 */
data class ProductInfo(
    val productCode: String = "",
    val productTag: ProductTagEnum = ProductTagEnum.DEFAULT,
    val productTagDisplay: String = ProductTagEnum.DEFAULT.display,
    val productSource: ProviderSourceEnum = ProviderSourceEnum.DEFAULT,
    val orderInfo: String = "",
    val issueTicketDesc: String = "",
) {
    constructor(
        productCode: String,
        productTag: ProductTagEnum,
        productSource: ProviderSourceEnum,
        orderInfo: String,
        issueTicketDesc: String
    ) : this(
        productCode = productCode,
        productTag = productTag,
        productTagDisplay = productTag.display,
        productSource = productSource,
        orderInfo = orderInfo,
        issueTicketDesc = issueTicketDesc
    )

    constructor(
        productCode: String,
        productTag: ProductTagEnum,
        productSource: ProviderSourceEnum,
        orderInfo: String
    ) : this(
        productCode = productCode,
        productTag = productTag,
        productTagDisplay = productTag.display,
        productSource = productSource,
        orderInfo = orderInfo,
    )
}
