package com.somytrip.model.flight.enums

import com.somytrip.entity.enums.order.OrderCustomStateEnum

/**
 * @Description: 同程订单状态enum
 * @author: pigeon
 * @created: 2024-02-04 16:38
 */
enum class TCOrderStateEnum(
    val tcValue: Int,
    val display: String,
) {
    /**
     * 待支付
     */
    WAIT_PAY(0, "待支付"),
    ORDER_CANCEL(1, "下单取消"),
    PAY_ING(2, "支付中"),
    INTENDED_ING(3, "预定中"),
    INTENDED_SUCCESS(5, "已预订"),
    REFUND_ING(6, "退款中"),
    REFUND_SUCCESS(7, "已退款"),

    ;

    companion object {
        fun intToOrderCustomStateEnum(value: Int): OrderCustomStateEnum {
            return when (value) {
                INTENDED_ING.tcValue -> {
                    OrderCustomStateEnum.FLIGHT_WAIT_TICKET
                }

                INTENDED_SUCCESS.tcValue -> {
                    OrderCustomStateEnum.FLIGHT_TICKET_FINISH
                }

                WAIT_PAY.tcValue -> {
                    OrderCustomStateEnum.WAIT_PAY
                }

                else -> OrderCustomStateEnum.PAY_OVER_TIME
            }

        }
    }
}