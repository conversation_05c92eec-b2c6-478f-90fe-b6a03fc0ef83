package com.somytrip.model.flight.common

import com.somytrip.model.flight.enums.PassengerTypeEnum

/**
 * @Description: 价格信息
 * @author: pigeon
 * @created: 2024-08-20 10:13
 */
data class PriceItem(
    val passengerType: PassengerTypeEnum = PassengerTypeEnum.ADULT,
    val taxTotal: String = "",
    val price: String = "0",
    val taxDetail: TaxDetail = TaxDetail()
) {
//    constructor(info: TCPriceInfo, func: (x: Double) -> Int) : this(
//        price = func.invoke(info.priceB),
//        passengerType = PassengerTypeEnum.tcValueToEnums(info.passengerType),
//        taxTotal = info.tax,
//        taxDetail = TaxDetail(info.taxDetail)
//    )
}

