package com.somytrip.model.flight.enums

import com.somytrip.model.flight.tc.*
import kotlin.reflect.KClass

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-02-21 10:28
 */
enum class TCNotifyEnum(
    val value: KClass<out Any>,
    val serviceCode: String,
) {
    PAY_NOTIFY(TCPayNotify::class, TCRestApiEnum.PAY_NOTIFY.value),
    TICKET_RESULT_NOTIFY(TCTicketNotify::class, TCRestApiEnum.TICKET_NOTIFY.value),
    REFUND_RESULT_NOTIFY(TCRefundResultNotify::class, TCRestApiEnum.REFUND_NOTIFY.value),
    REFUND_FEE_NOTIFY(TCRefundFeeNotify::class, TCRestApiEnum.REFUND_FEE_NOTICE_INTERNATIONAL.value),
    ENDORSE_CONFIRM_NOTIFY(TCEndorseConfirmNotify::class, TCRestApiEnum.ENDORSE_CONFIRM.value),
    ENDORSE_RESULT_NOTIFY(TCEndorseResultNotify::class, TCRestApiEnum.ENDORSE_RESULT_NOTIFY.value),
    CHANGE_NOTIFY(TCFlightChangeNotify::class, TCRestApiEnum.CHANGE_NOTIFY.value), ;

    ;

    companion object {
        fun codeToEnum(code: String): TCNotifyEnum {
            return entries.find { it.serviceCode == code } ?: PAY_NOTIFY
        }
    }
}