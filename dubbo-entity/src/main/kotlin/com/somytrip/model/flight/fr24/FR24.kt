package com.somytrip.model.flight.fr24

import com.alibaba.fastjson2.annotation.JSONField
import com.somytrip.model.flight.common.CommonValFunc
import com.somytrip.model.flight.common.CommonValFunc.FR24_CODE_SUCCESS
import com.somytrip.model.flight.common.CommonValFunc.FR24_MESSAGE_SUCCESS
import com.somytrip.model.flight.enums.CreditTypeEnum
import com.somytrip.model.flight.enums.GenderEnum
import jakarta.validation.constraints.Size

/**
 * @Description: FlightRoutes24
 * @author: pigeon
 * @created: 2024-08-06 11:17
 */

data class FR24Response<out T>(
    val traceId: String = "",
    val code: String = FR24_CODE_SUCCESS,
    val message: String = FR24_MESSAGE_SUCCESS,
    val processingTime: Long = 0,
    val data: T,
) {
    fun notDataStr(): String {
        return """{"traceId": "$traceId", "code": "$code", "message": "$message", "processingTime": $processingTime}"""
    }

    fun success(): Boolean {
        return code == FR24_CODE_SUCCESS
    }
}

data class FR24NotifyResponse(
    val traceId: String = "",
    val code: String = FR24_CODE_SUCCESS,
    val message: String = FR24_MESSAGE_SUCCESS,
    val processingTime: Long = 0,
    val authentication: Authentication = Authentication()
)

data class ShoppingData(
    /**
     * 报价信息。包含了直飞和中转所有的报价组合
     */
    val offers: List<OfferItem> = emptyList(),
    /**
     * 行程信息。包含了所有航班组合
     */
    val segments: List<SegmentItem> = emptyList(),
    /**
     * 航段信息。包含了所有不重复的航班信息
     */
    val legs: List<LegItem> = emptyList(),
)

data class ShoppingRequest(
    /**
     * 用户认证信息
     */
    val authentication: Authentication,
    /**
     * 搜索航段信息。最多支持 6 段行程查询
     */
    @Size(max = 6)
    val searchLegs: List<SearchLegItem> = emptyList(),
    /**
     * 成人乘客人数，成人、儿童和婴儿人数总和不能
     * 超过 9
     */
    val adultNum: Int = 1,
    /**
     * 儿童乘客人数，一成人最多带两个儿童或婴儿。
     * 默认为 0
     */
    val childNum: Int = 0,
    /**
     * 婴儿乘客人数，一成人最多带两个儿童或婴儿。
     * 默认为 0
     */
    val infantNum: Int = 0,
    /**
     * 搜索偏好
     * 目前仅支持舱等偏好
     */
    val preferences: Preferences = Preferences(),
)

data class PricingRequest(
    val authentication: Authentication,
    val offerId: String = "",
    val adultNum: Int = 1,
    val childNum: Int = 0,
    val infantNum: Int = 0,
)

data class PricingData(
    /**
     * 报价信息。包含了直飞和中转所有的报价组合
     */
    val offer: List<OfferItem> = emptyList(),
    /**
     * 行程信息。包含了所有航班组合
     */
    val segments: List<SegmentItem> = emptyList(),
    /**
     * 航段信息。包含了所有不重复的航班信息
     */
    val legs: List<LegItem> = emptyList(),
)

data class BookingRequest(
    /**
     * [Authentication]
     */
    val authentication: Authentication,
    val offerId: String = "",
    val partnerOrderNo: String = "",
    /**
     * 乘客信息。使用 AES 对全部内容加密后的字符
     * 串，解密后的参数见 passengers 说明
     * 列表[PassengerItem]
     */
    val passengers: String = "",
    /**
     * 订单变动联系方式
     * [AgentContact]
     */
    val agentContact: AgentContact = AgentContact(""),
    /**
     * 售前辅营生单信息
     * [AncillaryItem]
     */
    val ancillary: List<AncillaryItem> = emptyList(),
) {
    constructor(body: BookingRequestBody) : this(
        authentication = body.authentication,
        offerId = body.offerId,
        partnerOrderNo = body.partnerOrderNo,
        "",
        agentContact = body.agentContact,
        ancillary = body.ancillary
    )
}

data class BookingRequestBody(
    /**
     * [Authentication]
     */
    val authentication: Authentication,
    val offerId: String = "",
    val partnerOrderNo: String = "",
    /**
     * 乘客信息。使用 AES 对全部内容加密后的字符
     * 串，解密后的参数见 passengers 说明
     * 列表[PassengerItem]
     */
    val passengers: List<PassengerItem> = emptyList(),
    /**
     * 订单变动联系方式
     * [AgentContact]
     */
    val agentContact: AgentContact = AgentContact(""),
    /**
     * 售前辅营生单信息
     * [AncillaryItem]
     */
    val ancillary: List<AncillaryItem> = emptyList(),
)

data class BookingData(
    /**
     * FR24 订单号
     */
    val orderNo: String = "",
    /**
     * 订单状态
     */
    val orderStatus: String = "",
    /**
     * 合作方订单号，somytrip订单号
     */
    val partnerOrderNo: String = "",
    /**
     * 订单币种
     */
    val currency: String = "",
    /**
     * 订单总价
     */
    val totalPrice: String = "",
    /**
     * 订单详情
     */
    val offer: OfferItem,
    /**
     * 行程信息
     */
    val legs: LegItem,
    /**
     * 航段信息
     */
    val segments: List<SegmentItem> = emptyList(),
    /**
     * 机票其他信息
     */
    val ticketInfo: List<TicketInfo> = emptyList(),
    /**
     * 最晚支付时间，YYYY-MM-DDThh:mm，
     * 2022-10-21T21:23
     */
    val payDeadline: String = "",
    /**
     * 废票截止时间，YYYY-MM-DDThh:mm，
     * GMT+8
     * 时间仅供参考
     */
    val voidDeadline: String = "",
    /**
     * 乘客信息
     * AES加密后字符串
     * [PassengerItem]
     */
    val passengers: String = "",
    /**
     * 联系人信息
     */
    val agentContact: AgentContact = AgentContact(""),
    /**
     * 辅营信息
     */
    val ancillary: AncillaryR = AncillaryR(),
)

data class TicketingRequest(
    val authentication: Authentication,
    /**
     * FR24订单号
     */
    val orderNo: String = "",
    /**
     * somytrip订单号
     */
    val partnerOrderNo: String = "",
    /**
     * 报价币种
     */
    val currency: String = "CNY",
    /**
     * 订单总价
     */
    val totalPrice: String = "",
    /**
     * 支付方式
     * 0 - 余额支付
     */
    val paymentMethod: String = "0",
)

data class TicketingData(
    /**
     * FR24订单号
     */
    val orderNo: String = "",
    /**
     * 订单状态
     */
    val orderStatus: String = "",
    /**
     * somytrip订单号
     */
    val partnerOrderNo: String = "",
    /**
     * 报价币种
     */
    val currency: String = "CNY",
    /**
     * 订单总价
     */
    val totalPrice: String = "",
)

data class OrderDetailRequest(
    val authentication: Authentication,
    /**
     * FR24订单号
     */
    val orderNo: String = "",
    /**
     * somytrip订单号
     */
    val partnerOrderNo: String = "",
)

data class OrderDetailData(
    /**
     * FR24 订单号
     */
    val orderNo: String = "",
    /**
     * 订单状态
     */
    val orderStatus: String = "",
    /**
     * 合作方订单号，somytrip订单号
     */
    val partnerOrderNo: String = "",
    /**
     * 订单币种
     */
    val currency: String = "",
    /**
     * 订单总价
     */
    val totalPrice: String = "",
    /**
     * 订单详情
     */
    val offer: OfferItem,
    /**
     * 行程信息
     */
    val legs: LegItem,
    /**
     * 航段信息
     */
    val segments: List<SegmentItem> = emptyList(),
    /**
     * 机票其他信息
     */
    val ticketInfo: List<TicketInfo> = emptyList(),
    /**
     * 最晚支付时间，YYYY-MM-DDThh:mm，
     * 2022-10-21T21:23
     */
    val payDeadline: String = "",
    /**
     * 废票截止时间，YYYY-MM-DDThh:mm，
     * GMT+8
     * 时间仅供参考
     */
    val voidDeadline: String = "",
    /**
     * 乘客信息
     * AES加密后字符串
     * [PassengerItem]
     */
    val passengers: String = "",
    /**
     * 联系人信息
     */
    val agentContact: AgentContact = AgentContact(""),
    /**
     * 辅营信息
     */
    val ancillary: AncillaryDetail = AncillaryDetail(),
)

data class OrderChangeInfoNotify(
    val authentication: Authentication?,
    /**
     * 追溯码
     */
    val traceId: String = "",
    /**
     * 变更类别
     * orderStatus - 订单状态
     * ticketNo - 票号或航司大编码推送
     * scheduleChange - 航班变化
     * ancillaryOrderStatus - 辅营订单状态
     * ticketNoUpdate - 票号或大编码二次推送
     * refund- 退票相关通知
     * change- 改期相关通知
     * void- 废票相关通知
     */
    val type: String = "",
    /**
     * FR24 订单号
     */
    val orderNo: String = "",
    /**
     * 合作方订单号
     */
    val partnerOrderNo: String = "",
    /**
     * 订单状态。当 type 为 orderStatus 时，该项
     * 为必填。
     * 11 - 生单成功
     * 12 - 出票中
     * 13 - 出票完成
     * 14 - 等待确认
     * 15 - 订单已取消
     * 67- 支付成功.
     * 改期
     * 47, 改期待审核
     * 48, 改期待付款
     * 49, 改期付款完成待处理
     * 50, 改期失败
     * 51, 改期驳回
     * 52, 改期待退款.
     * 53, 退款中
     * 54, 退款完成
     * 61, 改期完成
     * 退票
     * 72, 待核价
     * 73, 核价完成
     * 25, 退票待审核
     * 26, 暂不能退票
     * 27, 已审核正退票
     * 29, 已退票待退款
     * 30, 退款完成
     * 废票
     * 36, 废票待审核
     * 37, 废票驳回
     * 38, 废票完成
     */
    val orderStatus: Int = 0,
    /**
     * 通知详情
     */
    val info: NotifyInfo? = NotifyInfo(),
)

data class PreSaleAncillaryShoppingRequest(
    val authentication: Authentication,
    /**
     * 机票 offerId
     */
    val offerId: String = ""
)

data class PreSaleAncillaryShoppingData(
    val currency: String = "",
    /**
     * 航段可添加辅营报价信息
     */
    @JSONField(name = "auxes")
    val auxes: List<AuxItem> = emptyList(),
    /**
     * 航段信息
     */
    val segments: List<SegmentItem> = emptyList(),
)

data class PostSaleAncillaryRequest(
    val authentication: Authentication,
    /**
     * 机票订单号 fr24 订单号
     */
    val orderNo: String = "",
)

data class PostSaleAncillaryData(
    /**
     * 币种
     */
    val currency: String = "",
    /**
     * 航段可添加辅营报价信息
     */
    @JSONField(name = "auxes")
    val auxes: List<AuxItem> = emptyList(),
    /**
     * 航段信息
     */
    val segments: List<SegmentItem> = emptyList(),
)

data class AncillaryBookingRequest(
    val authentication: Authentication,
    /**
     * FR24 机票订单号。如果已在 FR24 生单，则
     * 填写该订单
     */
    val orderNo: String = "",
    /**
     * 合作方机票订单号
     */
    val partnerAuxOrderNo: String = "",
    /**
     * 预定详情
     */
    val ancillary: List<AncillaryItem> = emptyList(),
    /**
     * 乘客信息
     */
    val passengers: List<NotifyPassengerItem> = emptyList(),
)

data class AncillaryBookingData(
    /**
     * 辅营订单号
     */
    val auxOrderNo: String = "",
    /**
     * 订单状态
     */
    val auxOrderStatus: String = "",
    /**
     * 合作方辅营订单 id somytrip订单号
     */
    val partnerAuxOrderNo: String = "",
    /**
     * 机票订单号
     */
    val orderNo: String = "",
    /**
     * 订单创建时间
     */
    val createTime: String = "",
    /**
     * 报价币种
     */
    val currency: String = "CNY",
    /**
     * 辅营订单总价
     */
    val totalPrice: String = "",
    /**
     * 辅营信息
     */
    val ancillary: List<AncillaryR> = emptyList(),
    /**
     * 乘客信息
     */
    val passengers: List<NotifyPassengerItem> = emptyList(),
    /**
     * 航段信息
     */
    val segments: List<SegmentItem> = emptyList(),
    /**
     * 最晚支付时间。
     * YYYY-MM-DDThh:mm
     */
    val payDeadline: String = "",
)

data class AncillaryPurchaseRequest(
    val authentication: Authentication,
    /**
     * FR24 辅营订单号
     */
    val auxOrderNo: String = "",
    /**
     * 合作方辅营订单号 somytrip订单号
     */
    val partnerAuxOrderNo: String = "",
    /**
     * 机票订单号 fr24 订单号
     */
    val orderNo: String = "",
    /**
     * 币种
     */
    val currency: String = "",
    /**
     * 辅营订单总价
     */
    val totalPrice: String = "",
    /**
     * 支付方式
     */
    val paymentMethod: String = "",
)

data class AncillaryPurchaseData(
    /**
     * FR24 辅营订单号
     */
    val auxOrderNo: String = "",
    /**
     * 合作方辅营订单号 somytrip订单号
     */
    val partnerAuxOrderNo: String = "",
    /**
     * 机票订单号 fr24 订单号
     */
    val orderNo: String = "",
    /**
     * 币种
     */
    val currency: String = "",
    /**
     * 订单总价
     */
    val totalPrice: String = "",
)

data class AncillaryOrderDetailRequest(
    val authentication: Authentication,
    /**
     * FR24 辅营订单号
     */
    val auxOrderNo: String = "",
    /**
     * 合作方辅营订单号 somytrip订单号
     */
    val partnerAuxOrderNo: String = "",
    /**
     * 机票订单号 fr24 订单号
     */
    val orderNo: String = "",
    /**
     * 订单创建时间。YYYY-MM-DDTHH:mm
     */
    val createTime: String = "",
    /**
     * 订单币种
     */
    val currency: String = "",
    /**
     * 辅营订单总价
     */
    val totalPrice: String = "",
    /**
     * 辅营信息
     */
    val ancillary: List<AncillaryR> = emptyList(),
    /**
     * 乘客信息
     */
    val passengers: List<NotifyPassengerItem> = emptyList(),
    /**
     * 最晚支付时间。YYYY-MM-DDTHH:mm
     */
    val payDeadline: String = "",
)

data class AncillaryOrderDetailData(
    /**
     * FR24 辅营订单号
     */
    val auxOrderNo: String = "",
    /**
     * 订单状态
     */
    val orderStatus: String = "",
    /**
     * 合作方辅营订单号 somytrip订单号
     */
    val partnerAuxOrderNo: String = "",
)

data class ChangeReShopRequest(
    val authentication: Authentication,
    /**
     * FR24 订单号
     */
    val orderNo: String = "",
    /**
     * 原始航段信息
     */
    val originalSegments: List<OriginalSegmentItem> = emptyList(),
    /**
     * 新的改期航段信息
     */
    val newLegs: List<NewLegItem> = emptyList(),
    /**
     * 成人乘客人数
     */
    val adultNum: Int = 1,
    /**
     * 儿童乘客人数
     */
    val childNum: Int = 0,
    /**
     * 婴儿乘客人数
     */
    val infantNum: Int = 0,
    /**
     * 偏好
     */
    val preferences: Preferences = Preferences(),
)

data class ChangeReShopData(
    /**
     * 报价信息。每一行程可改航班组合
     */
    val offers: List<ChangeReShopOffer> = emptyList(),
    /**
     * 航段信息。包含了所有不重复的航班信息
     */
    val segments: List<SegmentItem> = emptyList(),
)

data class ReissueRequest(
    val authentication: Authentication,
    /**
     * FR24 出票订单号
     */
    val tktOrderNo: String = "",
    /**
     * 待改期的原始航班信息
     */
    val originalSegments: List<OriginalSegmentItem> = emptyList(),
    /**
     * 新改期航班
     */
    val newLegs: List<ReissueNewLeg> = emptyList(),
    /**
     * 乘客信息
     */
    val passengers: List<PassengerItem> = emptyList(),
    /**
     * 舱等，若不传则默认原舱等改期
     */
    val cabin: String = "",
    /**
     * 改期理由
     * 0- 自愿改期
     * 1- 航班变动
     * 2- 因病不能乘机
     * 3-其他
     */
    val changeReason: String = "",
    /**
     * 备注
     */
    val remarks: String = "",
    /**
     * 非自愿改期证明文件，取自 file upload 接口
     */
    val fileId: List<String> = emptyList(),
)

data class ReissueData(
    val changeOrderNo: String = "",
    val tktOrderNo: String = "",
    val changeOrderStatus: String = "",
)

data class RefundRequest(
    val authentication: Authentication,
    /**
     * FR24 出票订单号
     */
    val tktOrderNo: String = "",
    /**
     * 合作方（采购商）订单号
     */
    val changeOrderNo: String = "",
    /**
     * 乘客信息
     */
    val passengers: List<PassengerItem> = emptyList(),
    /**
     * 退票航段，若不传则代表该乘客整个订单行程
     * 退票
     */
    val segments: List<RefundSeg> = emptyList(),
    /**
     * 退票理由
     * 0- 自愿退票
     * 1- 航班变动
     * 2- 因病不能乘机
     * 3- 其他
     */
    val refundReason: String = "",
    /**
     * 退票理由备注
     */
    val remarks: String = "",
    /**
     * 证明文件，id 取自 file upload 接口
     */
    val fileId: String = "",
)

data class RefundData(
    /**
     * 退票订单号
     */
    val refundOrderNo: String = "",
    /**
     * 出票订单号
     */
    val tktOrderNo: String = "",
    /**
     * 退票订单状态
     */
    val refundOrderStatus: String = "",
)

data class RefundConfirmRequest(
    val authentication: Authentication,
    /**
     * 退票订单号。
     * 退票订单号和出票订单号二者必填其一
     */
    val refundOrderNo: String = "",
    /**
     * 出票订单号。
     * 退票订单号和出票订单号二者必填其一
     */
    val tktOrderNo: String = "",
    /**
     * 退票乘客信息
     * 当仅传了出票订单号时，必填
     */
    val passengers: List<String> = emptyList(),
    /**
     * 退票航段信息
     * 当仅传了出票订单号时，必填
     */
    val segments: List<String> = emptyList(),
    /**
     * 退票理由
     * 0- 自愿退票
     * 1- 航班变动
     * 2- 因病不能乘机
     * 3-其他
     */
    val refundReason: String = "",
    /**
     * 备注
     */
    val remarks: String = "",
    /**
     * 文件 id
     */
    val fileId: List<String> = emptyList(),
)

data class RefundConfirmData(
    /**
     * 退票订单号
     */
    val refundOrderNo: String = "",
    /**
     * 出票订单号
     */
    val tktOrderNo: String = "",
    /**
     * 退票订单状态
     */
    val refundOrderStatus: String = "",
)

data class VoidRequest(
    val authentication: Authentication,
    val tktOrderNo: String = "",
    val partnerOrderNo: String = "",
    val passengers: List<PassengerItem>,
)

data class VoidData(
    /**
     * 废票订单号
     */
    val voidOrderNo: String = "",
    /**
     * 出票订单号
     */
    val tktOrderNo: String = "",
    /**
     * 废票订单状态
     */
    val voidOrderStatus: String = "",
)

data class RefundSeg(
    /**
     * 航司
     */
    val carrier: String = "",
    /**
     * 航班号
     */
    val flightNo: String = "",
    /**
     * 起飞机场
     */
    val depAirport: String = "",
    /**
     * 起飞时间
     */
    val depTime: String = "",
    /**
     * 到达机场
     */
    val arrAirport: String = "",
    /**
     * 到达时间
     */
    val arrTime: String = "",
)

data class ReissueNewLeg(
    val carrier: String = "",
    val flightNo: String = "",
    val depAirport: String = "",
    val depTime: String = "",
    val arrAirport: String = "",
    val arrTime: String = "",
    @JSONField(name = "RBD")
    val rbd: String = "",
)

data class ChangeReShopOffer(
    val origin: String = "",
    val destination: String = "",
    val depDate: String = "",
    val legs: List<ChangeReShopOfferSeg> = emptyList(),
)

data class ChangeReShopOfferSeg(
    val segmentIds: List<String> = emptyList(),
)

data class NewLegItem(
    /**
     * 起飞机场，IATA 机场码
     */
    val origin: String = "",
    /**
     * 起飞机场，IATA 机场码
     */
    val destination: String = "",
    /**
     * 出发日期 YYYY-MM-DD
     */
    val depDate: String = "",
)

data class OriginalSegmentItem(
    /**
     * 航司二字码
     */
    val carrier: String = "",
    /**
     * 航班号
     */
    val flightNo: String = "",
    /**
     * 出发时间 YYYY-MM-DDTHH:mm
     */
    val depTime: String = "",
)

data class AuxItem(
    /**
     * 航段组合
     */
    val segmentId: List<String> = emptyList(),
    /**
     * 辅营 id
     */
    val auxId: String = "",
    /**
     * 基础单价
     */
    val baseFare: String = "",
    /**
     * 服务费。为 0 代表无服务费
     */
    val serviceFee: String = "",
    /**
     * 尺寸，单位为 CM。没有相关信息用 e 表示
     */
    val size: String = "",
    /**
     * 包含的辅营件数，单位 PC
     */
    val piece: Int = 0,
    /**
     * 报价总重量，单位 KG。若 piece 不为 1，表
     * 示 piece 件数的总重量
     */
    val weight: Int = 0,
    /**
     * 每个乘客可购买件数
     */
    val limitPerPax: Int = 0,
    /**
     * 该辅营可售件数
     */
    val availability: Int = 0,
)

data class NotifyInfo(
    /**
     * 机票信息。当 type 为 ticketNo 票号或航司
     * 大编码推送时，该项为必填
     */
    val ticketInfo: List<TicketInfo> = emptyList(),
    /**
     * 原始信息。当 type 为 scheduleChange 或者
     * ticketNoUpdate 时，该项为必填
     */
    val originInfo: List<OriginInfo> = emptyList(),
    /**
     * 变更后信息。当 type 为 scheduleChange 或
     * 者 ticketNoUpdate 时，该项为必填
     */
    val newInfo: List<String> = emptyList(),
    /**
     * 航段信息。当 type 为 ticketNo 票号或航司
     * 大编码推送时，该项为必填
     */
    val segments: List<NewInfo> = emptyList(),
    /**
     * 乘客信息。当 type 为 ticketNo 票号或航司
     * 大编码推送时，该项为必填
     */
    val passengers: List<NotifyPassengerItem> = emptyList(),
    /**
     * 售后费用信息
     */
    val feeInfo: List<FeeInfo> = emptyList(),
)

data class FeeInfo(
    /**
     * 币种
     */
    val currency: String = "",
    /**
     * 实际不可退回金额
     * ADTNUM*ADT(totalFare + totalTax +
     * serviceFee)+CHDNUM*
     * CHD(...)+INFNUM * INF(...)
     */
    val totalAmount: String = "",
    /**
     * 应退采购金额
     */
    val refundAmount: String = "",
    /**
     * 核价有效期
     */
    val expiryTime: String = "",
    /**
     * 退票金额信息
     * 当 type 为 refund 时，该项会返回
     */
    val refundFee: List<RefundFee> = emptyList(),
    /**
     * 改期金额信息
     * 当 type 为 change 时，该项会返回
     */
    val changeFee: List<RefundFee> = emptyList(),
    /**
     * 废票金额信息
     * 当 type 为 void 时，该项会返回
     */
    val voidFee: List<RefundFee> = emptyList(),
)

data class RefundFee(
    /**
     * ADT- 成人
     * CHD- 儿童
     * INF- 婴儿
     */
    val paxType: String = "ADT",
    /**
     * 总扣除金额。
     * 等于误机费+改期费
     */
    val totalFare: String = "",
    /**
     * 扣除金额明细
     */
    val deductFare: List<DeductFee> = emptyList(),
    /**
     * 总税费
     */
    val totalTax: String = "",
    /**
     * 税费明细
     * [TaxBreakdownItem]
     */
    val taxBreakdown: List<TaxBreakdownItem> = emptyList(),
    /**
     * 服务费
     */
    val serviceFee: String = "",
)

data class DeductFee(
    /**
     * 扣除费类别
     * noshow – 误机费
     * changeFee – 改期费
     */
    val fareType: String = "",
    /**
     * 扣除费用
     */
    val fareAmount: String = "",
)

data class NotifyPassengerItem(
    val paxId: String = "",
    val name: String = "",
    val birthday: String = "",
)

data class OriginInfo(
    /**
     * 发生变化的航段 id
     */
    val segmentId: List<String> = emptyList(),
    /**
     * 航段详情
     */
    val segments: List<String> = emptyList(),
)

data class NewInfo(
    val segmentIds: String = "",
    val segmentStatus: String = "",
    val segments: List<SegmentItem> = emptyList(),
)

data class AncillaryDetail(
    val paxId: Int = 0,
    val segmentId: List<String> = emptyList(),
    val auxId: String = "",
    val baseFare: String = "",
    val serviceFee: String = "",
    val size: String = "",
    val weight: Int = 0,
    val piece: Int = 0,
    val quantity: Int = 0,
)

data class AncillaryR(
    val baggage: List<Baggage> = emptyList(),
)

data class Baggage(
    val paxId: Int = 0,
    val segmentId: List<String> = emptyList(),
    val auxId: String = "",
    val baseFare: String = "",
    val serviceFee: String = "",
    val size: Int = 0,
    val piece: Int = 0,
    val quantity: Int = 0,
)

data class TicketInfo(
    /**
     * 航司 PNR
     * 当航段 PNR 不同时，航段 PNR 用逗号隔开
     */
    val airlinePnr: String = "",
    /**
     * 当来源为 GDS，才会返回
     */
    val gdsPnr: String? = "",
    /**
     * 乘客 id
     */
    val paxId: String = "",
    /**
     * 乘客姓名
     */
    val paxName: String = "",
    /**
     * 票号
     * 当航段票号不同时，票号会按照航段顺序依次用
     * 逗号隔开
     */
    val ticketNo: String = "",
    /**
     * 航段 id
     */
    val segmentIds: List<String> = emptyList(),
)

data class AncillaryItem(
    /**
     * 乘客 id，若购买行李，该项为必填项
     */
    val paxId: Int,
    val segmentId: List<String> = emptyList(),
    val auxId: String = "",
    val quantity: Int = 0,
)

data class AgentContact(
    /**
     * 姓名
     * 英文大写
     * LastName/FirstName MiddleName
     * 姓/名
     */
    val agentName: String = "",
    val agentEmail: String = "",
    val mobile: String = "",
    val areaCode: String = "",
)

data class PassengerItem(
    /**
     * 乘客序列号
     */
    val paxId: Int = 1,
    /**
     * 姓名
     * 英文大写 LastName/FirstName
     * 姓/名
     * 不可含有空格
     */
    val name: String = "",
    /**
     * 乘客类型
     * ADT-成人
     * CHD-儿童
     * INF-婴儿
     */
    val paxType: String = CommonValFunc.ADT,
    /**
     * 出生日期
     * YYYY-MM-dd
     */
    val birthday: String = "",
    /**
     * 性别
     * F-female 女性
     * M-male 男性
     */
    val gender: String = GenderEnum.MAN.fr24Value,
    /**
     * 证件号码
     */
    val cardNum: String = "",
    /**
     * 证件类型
     * ID - 本地身份证（仅支持预定 x 国境内段时，
     * 使用当国的身份证）
     * PP - 护照
     * GA - 港澳通行证
     * TW - 台湾通行证
     * TB - 台胞证
     * HX - 回乡证
     * HY -其他
     */
    val cardType: String = CreditTypeEnum.ID_CARD.fr24Value(),
    /**
     * 证件发行国家
     */
    val cardIssuedPlace: String = "",
    /**
     * 证件有效期。YYYY-MM-DD
     */
    val cardExpiryDate: String = "",
    /**
     * 乘客国籍。使用 IATA 国家标准二字代码
     */
    val nationality: String = "",
    /**
     * 乘客联系邮箱
     */
    val paxEmail: String = "",
    /**
     * 乘客联系电话
     */
    val paxMobile: String = "",
    /**
     * 国家电话区号
     */
    val areaCode: String = "+86",
    /**
     * 关联成人的 id。当购票乘客含有婴儿或儿童
     * 时，关联成人 id 必填
     */
    val accompaniedPaxId: String = "",
)

data class Authentication(
    val sign: String = "",
    val timestamp: String = "",
)

data class Preferences(
    /**
     * 舱等偏好
     * F-头等舱；
     * C-商务舱；
     * P-超级经济舱；
     * Y-经济舱
     * 若不设置，默认为 Y
     */
    val cabin: String = "Y"
)

data class SearchLegItem(
    /**
     * 起飞城市，使用 IATA 城市标准三字代码
     */
    val origin: String,
    /**
     * 到达城市，使用 IATA 城市标准三字代码
     */
    val destination: String,
    /**
     * 起飞日期 ，YYYY-MM-DD
     */
    val depDate: String,
)

data class SegmentItem(
    val segmentId: String,
    /**
     * 航段总时长，单位分钟min
     */
    val duration: Int,
    /**
     * 销售航司。使用IATA航司标准二字代码
     */
    val carrier: String,
    /**
     * 航班号，和carrier搭配使用
     * BA1234，carrier为BA，flightNo为1234
     */
    val flightNo: String,
    /**
     * 航班共享标识
     * true-代码共享
     * false-非代码共享
     */
    val codeShare: Boolean = false,
    /**
     * 实际承运航司。使用IATA航司标准二字代码
     */
    val operatingCarrier: String = "",
    /**
     * 实际承运航班号
     */
    val operatingFlightNo: String = "",
    /**
     * 机型。使用IATA机型标准三字代码
     */
    val aircraftCode: String = "",
    /**
     * 出发机场。使用IATA机场标准三字代码
     */
    val depAirport: String,
    /**
     * 出发航站楼
     */
    val depTerminal: String = "",
    /**
     * 出发时间。YYYY-MM-DDThh:mm，比如2022-10-21T21:23
     * 起飞机场所在地时间
     */
    val depTime: String,
    /**
     * 到达机场。使用IATA机场标准三字代码
     */
    val arrAirport: String,
    /**
     * 到达航站楼
     */
    val arrTerminal: String = "",
    /**
     * 到达时间。YYYY-MM-DDThh:mm 到达机场所在地时间
     */
    val arrTime: String,
    /**
     * 经停机场。使用IATA机场标准三字标准代码。若无经停则无数据内容
     */
    val stopAirport: List<String> = emptyList(),
    /**
     * 停留时长。若无经停则无数据内容
     */
    val stopDuration: List<Int> = emptyList(),
)

data class LegItem(
    /**
     * 行程 id。表示不同航班组合
     */
    val legId: String,
    /**
     * 航段 id。同一个单程的不同航段用^连接
     */
    val segmentIds: List<String>,
)

data class OfferItem(
    /**
     * 报价的唯一标识码。
     * 该 id 必须携带至校验接口
     */
    val offerId: String = "",
    /**
     * 行程信息标识
     */
    val legId: String = "",
    /**
     * 报价币种
     * CNY
     */
    val currency: String = "CNY",
    /**
     * 开票航司，IATA 航司标准二字代码
     */
    val platingCarrier: String = "",
    /**
     * 乘客单价。包含不同类型乘客的价格详情
     */
    val pricePerPax: List<PricePerPax> = emptyList(),
    /**
     * 舱等。元素按照航段顺序排列
     * F-头等舱
     * C-商务舱
     * P-超级经济舱
     * Y-经济舱
     */
    val cabin: List<String> = emptyList(),
    /**
     * 舱位。和 cabin 按顺序一一对应
     */
    @JSONField(name = "RBD")
    val rbd: List<String> = emptyList(),
    /**
     * fare basis code。和 cabin 按顺序一一对应
     */
    val fareBasis: List<String> = emptyList(),
    /**
     * 航段的可售座位数。
     * 9 表示剩余座位数>=9
     * [1, 5, 9]表示航段 1 可售座位数为 1，航段 2 可
     * 售座位数为 5，航段 3 可售座位数>=9
     */
    val availability: List<Int> = emptyList(),
    /**
     * 其他信息。
     * 包含免费行李额
     */
    val extraInfo: ExtraInfo = ExtraInfo(),
    /**
     * 条件限制标识
     * true-有限制
     * false-无限制
     */
    val eligibilityFlag: Boolean,
    /**
     * 条件限制详情。当eligibilityFlag为false时，表示没有乘客条件限制
     */
    val eligibilityDetail: EligibilityDetail = EligibilityDetail(),
    /**
     * 退改规则
     */
    val rules: RuleItem = RuleItem(),
    /**
     * 产品类型三字码
     */
    val productType: String = "",
    /**
     * 产品类型标签
     */
    val productTag: ProductTag = ProductTag(),
    /**
     * 报价来源
     * GDS-GDS
     * WEB-航司官网（包含APP）AGT-代理账号或航司API
     * NDC-航司NDC
     * OTH-未知
     */
    val productSource: String = "",
    /**
     * 总报价金额
     */
    val totalPrice: String = "",
    /**
     * 生单需要乘客提供的信息
     */
    val paxInfoRequired: PaxInfoRequired = PaxInfoRequired(),
)

data class PaxInfoRequired(
    val birthday: Boolean = true,
    val gender: Boolean = true,
    val cardNum: Boolean = true,
    val cardType: Boolean = true,
    val cardIssuedPlace: Boolean = true,
    val cardExpiryDate: Boolean = true,
    val nationality: Boolean = true,
    val paxEmail: Boolean = true,
    val paxMobile: Boolean = true,
    val areaCode: Boolean = true,
)

data class ProductTag(
    /**
     * 出票保证说明
     * 0-支付成功后保证出票
     * 1-支付成功后不保证出票
     * 2-支付成功后可拒单
     */
    val ticketPromise: Int = -1,
    /**
     * 拒单时间
     * 单位：分钟；
     * 当“出票保证说明”为“支付成功后可拒
     * 单”则必须返回拒单时间；
     * 当“出票保证说明”为“支付成功后保证出
     * 票”、“支付成功后不保证出票”，则拒单
     * 时间为空
     */
    val refuseDeadline: Int = -1,
    /**
     * 出票时间说明
     * 单位：分钟；
     * 正数代表预计支付后 xx 分钟内出票；
     * 负数代表最晚起飞前 xx 分钟内出票；
     * 0 代表立即出票
     */
    val ticketingTime: Int = 0,
    /**
     * 改期审核时长说明
     * 单位：分钟；
     * 不为空，则代表提交改期后 xx 分钟内完成改
     * 期审核；为空，则不确定改期审核时长。
     */
    val reschedulePendingTime: Int = -1,
    /**
     * 自愿退适用说明
     * 0, 以航司退款时间为准
     * 1, 提交退票后
     * 2, 状态变更完成后
     */
    val voluntaryServiceStandard: Int = -1,
    /**
     * 自愿退回款时长
     * 单位：分钟；
     * 当自愿退适用说明为“以航司退款时间为
     * 准”，则自愿退回款时长为空；当自愿退适
     * 用说明为“提交退票后”，则代表提交自愿
     * 退票后 xx 分钟内退款；当自愿退适用说明为
     * “状态变更后”，则代表状态变更后 xx 分钟
     * 内退款。
     */
    val voluntaryRefundTime: Int = -1,
    /**
     * 非自愿退适用说明
     * 0-以航司退款时间为准
     * 提交资料通过审核后
     */
    val involuntaryServiceStandard: Int = -1,
    /**
     * 非自愿退回款时长
     * 单位：分钟；当非自愿退适用说明为“以航
     * 司退款时间为准”，则非自愿退回款时长为
     * 空；当非自愿退适用说明为“提交资料通过
     * 审核后”，则代表提交非自愿退票后 xx 分钟
     * 内退款
     */
    val involuntaryRefundTime: Int = -1,
    /**
     * 变舱风险说明
     * true-存在变舱风险，以实际出票舱位为准
     * false-不存在变舱风险
     */
    @JSONField(name = "RBDChangedRisk")
    val rbdChangedRisk: Boolean = false,
    /**
     * 退票规定
     * 0-以航空公司规定为准
     * 1-以 FR24 实际审核为准
     * 2-任何情况下不可退票、不可退税
     * 3-此航司支持出票后 24 小时内免费退票，具
     * 体以航空公司规定为准
     * 4-此航司支持出票后 24 小时内免费退票，具
     * 体以 FR24 实际审核为准
     */
    val refundCondition: Int = 0,
    /**
     * 改期规定
     * 0-以航空公司规定为准
     * 1-以 FR24 实际审核为准
     * 2-任何情况下不可改期签转
     */
    val reissueCondition: Int = 0,
    /**
     * 废票规定
     * true-支持废票，但需在废票工作时间内提
     * 交，并以审核为准；
     * false-不可废票
     */
    val voidingCondition: Boolean = false,
)

data class RuleItem(
    val refund: List<RefundRule> = emptyList(),
    val change: List<ChangeRule> = emptyList(),
)

data class ChangeRule(
    /**
     * 乘客类别
     * ADT-成人
     * CHD-儿童
     * INF-婴儿
     */
    val paxType: String,
    /**
     * 客规适用的客票使用情况
     * 0-全程未使用
     * 1-部分已使用
     * 2-两种情况均适用
     */
    val couponStatus: Int,
    /**
     * 改期标识
     * notAllowed-不可改
     * withCondition-有条件改
     * withNoPenalty-免费改
     * airlinePolicyApplied-按航司客规
     * verified-需核实
     */
    val changePolicy: String,
    /**
     * 改期时间说明，以分钟为单位，正数表示起飞
     * 前，负数表示起飞后，与 changeFee 配合使用
     * 如填入[80,60]，表示起飞前 60 分钟到起飞前
     * 80 分钟的改期费
     * 如填入[-60,-1440]，表示起飞后 1h-起飞后
     * 24h 改期费
     * 如填入[60,null]，表示起飞前 60min 内的改期
     * 费
     * 如填入[null,60]，表示起飞前 60min 及之前的
     * 改期费
     * 如填入[0,null]，表示起飞后任何时候的改期费
     * 如填入[null,0]，表示起飞前任何时候的改期费
     */
    val applicableTime: List<Int?> = emptyList(),
    /**
     * 改期费
     * 当 changePolicy = withCondition，必须赋值
     * 当 changePolicy = notAllowed /
     * withNoPenalty，可不赋值
     */
    val changeFee: String = "",
)

data class RefundRule(
    /**
     * 乘客类别
     * ADT-成人
     * CHD-儿童
     * INF-婴儿
     */
    val paxType: String,
    /**
     * 客规适用的客票使用情况
     * 0-全程未使用
     * 1-部分已使用
     * 2-两种情况均适用
     */
    val couponStatus: Int,
    /**
     * 退票标识
     * notAllowed-不可退
     * withCondition-有条件退
     * withNoPenalty-免费退
     * taxRefundOnly-仅可退部分税
     * airlinePolicyApplied-按航司客规
     * verified-需核实
     */
    val refundPolicy: String,
    /**
     * 退票时间说明，以分钟为单位，正数表示起飞
     * 前，负数表示起飞后，与 refundFee 配合使用
     * 如填入[80,60]，表示起飞前 60 分钟到起飞前
     * 80 分钟的退票费，如填入[-60,-1440]，表示起
     * 飞后 1h-起飞后 24h 退票费
     * 如填入[60,null]，表示起飞前 60min 内的退票
     * 费
     * 如填入[null,60]，表示起飞前 60min 及之前的
     * 退票费
     * 如填入[0,null]，表示起飞后任何时候的退票费
     * 如填入[null,0]，表示起飞前任何时候的退票费
     */
    val applicableTime: List<Int?> = emptyList(),
    /**
     * 退票费
     * 1. 当 refundPolicy = withCondition ，必须有
     * 值
     * 2. 当 refundPolicy = notAllowed /
     * withNoPenalty，可不赋值
     * 3. 当 fullRefundAP 有值，refundFee = 0
     */
    val refundFee: String = "",
    /**
     * 24h 免费退票规定的距离起飞前天数。
     * 当退票规定
     * offers.productTag.refundCondition 为“此
     * 航司支持出票后 24 小时内免费退票，具体以
     * 航空公司规定为准”或“此航司支持出票后
     * 24 小时内免费退票，具体以 FR24 实际审核
     * 为准”，必须有值。
     * 如：7，表示该规定适用的情况为，距离起飞
     * 时间至少七天及以上，在出票后的 24 小时内
     * 提交退票，可免费退。
     * 假设传 0，代表起飞时间不限，在出票后的
     * 24 小时内提交退票，可免费退。
     */
    @JSONField(name = "fullRefundAP")
    val fullRefundAP: Int = 0,
)

data class EligibilityDetail(
    /**
     * 乘客类别限制。
     * NOR-普通成人
     * LAB-工人
     * SEA-海员
     * SNR-老人
     * STU-学生
     * YOU-青年
     */
    val paxType: String = "NOR",
    /**
     * 最小出行乘客数量
     */
    val minPaxNum: Int = 1,
    /**
     * 最小年龄
     */
    val minAge: Int = 0,
    /**
     * 最大出行乘客数量
     */
    val maxPaxNum: Int = 1,
    /**
     * 最大年龄
     */
    val maxAge: Int = 0,
    /**
     * 乘客允许国籍类型
     * 0-全部
     * 1-适用部分国籍
     * 2-不适用部分国籍
     */
    val nationalityType: Int = 0,
    /**
     * 国籍，使用 IATA 国家标准二字代码，和
     * nationalityType 搭配使用
     * 当 nationalityType 为 0 时，nationality 无内
     * 容，表示该报价适用所有乘客国籍
     * 当 nationalityType 为 1 时，nationality 表示
     * 该报价适用的乘客国籍
     * 当 nationalityType 为 2 时，nationality 表示
     * 该报价不适用的乘客国籍
     */
    val nationality: List<String> = emptyList(),
)

data class PricePerPax(
    /**
     * 乘客类型ADT-成人CHD-儿童INF-婴儿
     */
    val paxType: String,
    /**
     * 单人票面价。支持小数点后两位，下述金额进位规则一致
     */
    val baseFare: String,
    /**
     * 单人总税费。为0则代表税费为0
     */
    val totalTax: String,
    /**
     * 税费明细
     */
    val taxBreakdown: List<TaxBreakdownItem> = emptyList(),
    /**
     * 单人服务费。为0则代表服务费为0
     */
    val serviceFee: String = "",
)

data class ExtraInfo(
    /**
     * 免费行李额
     */
    val freeBaggageAllowance: List<FreeBaggageAllowance> = emptyList(),
)

data class FreeBaggageAllowance(
    /**
     * 行李额对应航段的id
     */
    val segmentId: String,
    /**
     * 手提行李件数，单位 PC。按成人-儿童-婴儿的
     * 格式返回。没有相关信息用 e 表示
     * 2-1-e 表示成人可带两件手提行李，儿童可带一
     * 件手提行李，婴儿可带行李无法确认
     * e-e-e 表示无法拿到乘客可携带手提行李
     */
    val cabinBagPc: String,
    /**
     * 手提行李大小限制，单位 CM。按成人-儿童-婴
     * 儿的格式返回。没有相关信息用 e 表示
     */
    val cabinBagSize: String = "",
    /**
     * 手提行李重量限制，单位 KG。按成人-儿童-婴
     * 儿的格式返回。没有相关信息用 e 表示
     */
    val cabinBagWeight: String = "",
    /**
     * 托运行李件数，单位 PC。按成人-儿童-婴儿的
     * 格式返回。没有相关信息用 e 表示
     */
    val checkedBagPc: String = "",
    /**
     * 托运行李大小限制，单位 CM。按成人-儿童-婴
     * 儿的格式返回。没有相关信息用 e 表示
     */
    val checkedBagSize: String = "",
    /**
     * 托运行李重量限制，单位 KG。按成人-儿童-婴
     * 儿的格式返回。没有相关信息用 e 表示
     */
    val checkedBagWeight: String = "",
)

data class TaxBreakdownItem(
    /**
     * 税费类别
     * FS-燃油费AT-机建费
     */
    val taxType: String = "",
    /**
     * 税费金额。为0代表无该类别税费
     */
    val taxAmount: String = "",
)