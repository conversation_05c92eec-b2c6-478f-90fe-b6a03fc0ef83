package com.somytrip.model.flight.enums


/**
 * @Description: 性别 enum
 * @author: pigeon
 * @created: 2024-03-15 14:48
 */
enum class GenderEnum(
    val tcValue: Int,
    val display: String,
    val fr24Value: String,
    private val zfValue: String,
) {
    FEMALE(2, "女", "F", "female"),
    MAN(1, "男", "M", "male"),
    OTHER(0, "未知", "", "male"),
    UNKNOWN(-1, "未知", "", "male"),
    ;

    fun zfValue() = zfValue

    companion object {
        fun tcValueToEnum(value: Int): GenderEnum {
            return entries.find { it.tcValue == value }
                ?: UNKNOWN
        }
    }
}