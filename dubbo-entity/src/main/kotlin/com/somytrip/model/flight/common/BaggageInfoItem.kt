package com.somytrip.model.flight.common

import com.somytrip.model.flight.enums.BaggageInfoEnum
import com.somytrip.model.flight.enums.PassengerTypeEnum
import com.somytrip.model.flight.tc.TCAccurateSearchItemBaggage

/**
 * @Description: 行李信息Item
 * @author: pigeon
 * @created: 2024-08-20 17:50
 */
data class BaggageInfoItem(
    val passengerType: PassengerTypeEnum = PassengerTypeEnum.ADULT,
    val type: BaggageInfoEnum = BaggageInfoEnum.HAND,
    val rules: List<BaggageRuleItem> = emptyList(),
) {
    constructor() : this(PassengerTypeEnum.ADULT)
    constructor(baggage: TCAccurateSearchItemBaggage, type: BaggageInfoEnum = BaggageInfoEnum.HAND) : this(
        passengerType = PassengerTypeEnum.tcValueToEnums(baggage.passengerType),
        type = type,
        rules = baggage.rules.map { BaggageRuleItem(it) }
    )
}
