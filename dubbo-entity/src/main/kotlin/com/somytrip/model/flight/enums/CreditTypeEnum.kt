package com.somytrip.model.flight.enums


/**
 * @Description: 证件 enum
 * @author: pigeon
 * @created: 2024-01-18 10:30
 */
enum class CreditTypeEnum(
    private val display: String,
    private val tcCreate: Int,
    private val tcSearch: Int,
    private val fr24Value: String,
    private val zfValue: String,
) {
    UNKNOWN("未知", 0, -1, "HY", ""),
    UNLIMITED("不限", -1, 0, "HY", ""),
    ID_CARD("身份证", 1, 1, "ID", "idcard"),
    PASS_PORT("护照", 2, 3, "PP", "passport"),
    JGZ("军官证", 3, 4, "HY", ""),
    REENTRY_PERMIT("回乡证", 4, 5, "HX", ""),
    HK_AM_TRAVEL("港澳通行证", 5, 6, "GA", "rphmt"),
    TAIWAN_FELLOW_CARD("台胞证", 6, 7, "HY", ""),
    TAIWAN_RESIDENT("台湾通行证", 7, 8, "TW", "tphm"),
    SEAMAN_BOOK("海员证", 8, 9, "HY", ""),
    OTHER("其他", 9, -1, "HY", ""),
    VISA("签证", 10, 2, "HY", ""),
    RESIDENCE_BOOKLET("户口薄", 11, 10, "HY", ""),
    FOREIGNER_RESIDENCE("外国人永久居留证", 13, 11, "HY", "fpidcard"),
    HK_AM_CARD("港澳居住证", 14, 12, "HY", "eep"),
    TAIWAN_CARD("台湾居民居住证", 15, 13, "HY", "tptw"),
    ;

    fun tcCreate() = tcCreate
    fun display() = display

    fun tcSearch() = tcSearch

    fun zfValue() = zfValue

    fun fr24Value() = fr24Value

    companion object {
        fun tcCreateToEnum(v: Int): CreditTypeEnum {
            return entries.find { it.tcCreate == v }
                ?: UNKNOWN
        }

        fun tcSearchToEnum(v: Int): CreditTypeEnum {
            return entries.find { it.tcSearch == v }
                ?: UNKNOWN
        }
    }
}
