package com.somytrip.model.flight.enums

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-08-31 15:20
 */
enum class RefundReasonEnum(
    val fr24Value: Int,
    val tcValue: Int,
) {
    /**
     * 自愿
     */
    VOLUNTARY(0, 0),

    /**
     * 非自愿
     */
    INVOLUNTARY(0, 0),

    /**
     * 航班变动
     */
    FLIGHT_CHANGE(1, 21),

    /**
     * 航班变动导致后段无法乘坐
     */

    FLIGHT_CHANGE_(-1, 22),

    /**
     * 因病不能乘机
     */
    ILLNESS_PREVENT(2, 24),

    /**
     * 其他
     */
    OTHER(3, 27),
}