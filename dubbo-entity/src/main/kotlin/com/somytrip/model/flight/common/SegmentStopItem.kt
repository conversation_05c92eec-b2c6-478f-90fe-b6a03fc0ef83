package com.somytrip.model.flight.common

import com.somytrip.model.po.QueryShoppingSegmentStopItem

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-08-20 10:12
 */
data class SegmentStopItem(
    val stopAirport: String = "",
    val stopTime: String = "",
    /**
     * 经停地起飞时间
     */
    val departureDateTime: String = "",
    /**
     * 经停地到达时间
     */
    val arrivalDateTime: String = "",
) {
    constructor(item: QueryShoppingSegmentStopItem) : this(
        stopAirport = item.stopAirport,
        stopTime = item.stopTime,
    )
}
