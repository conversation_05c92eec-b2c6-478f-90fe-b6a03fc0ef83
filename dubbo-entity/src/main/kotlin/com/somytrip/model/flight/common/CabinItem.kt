package com.somytrip.model.flight.common

import com.somytrip.model.flight.enums.CabinClassEnum

/**
 * @Description: 舱位信息
 * @author: pigeon
 * @created: 2024-08-20 10:04
 */
data class CabinItem(
    /**
     * 舱位等级
     */
    val cabinClass: CabinClassEnum = CabinClassEnum.ECONOMY_CLASS,
    /**
     * 舱位等级 display
     */
    val cabinClassDisplay: String = cabinClass.display,
    /**
     * 舱位编号
     */
    val cabinCode: String = "",
    /**
     * 剩余座位数量
     */
    val seat: Int = 0,
    /**
     * 对应segment位置
     */
    val segmentIndex: Int = 0,
) {
    constructor(cabinClass: CabinClassEnum, cabinCode: String, seat: Int, segmentIndex: Int) : this(
        cabinClass,
        cabinClass.display,
        cabinCode,
        seat,
        segmentIndex
    )
}
