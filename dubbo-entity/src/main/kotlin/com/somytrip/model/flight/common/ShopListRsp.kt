package com.somytrip.model.flight.common

/**
 * @Description: 模糊询价列表
 * @author: pigeon
 * @created: 2024-08-20 9:55
 */
data class ShopListRsp(
    val prices: List<PriceItem> = emptyList(),
    val segments: List<SegmentItem> = emptyList(),
    val cabins: List<CabinItem> = emptyList(),
    val productInfo: ProductInfo = ProductInfo(),
    val sourceType: Int = 0,
    val durationTotal: String = "",
)
