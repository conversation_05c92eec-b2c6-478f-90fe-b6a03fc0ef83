package com.somytrip.model.flight.vo

import com.somytrip.model.flight.common.ProductInfo
import com.somytrip.model.flight.common.SegmentItem
import com.somytrip.model.flight.enums.RefundReasonEnum

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-29 10:11
 */

data class ApplyChangeVo(
    val orderNo: String = "",
    val productInfo: ProductInfo = ProductInfo(),
    val type: RefundReasonEnum = RefundReasonEnum.VOLUNTARY,
    val tickets: List<String> = emptyList(),
    val changeSegments: List<SegmentItem> = emptyList(),
    val fileKeys: List<String> = emptyList(),
)
