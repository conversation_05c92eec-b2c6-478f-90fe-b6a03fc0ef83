package com.somytrip.model.flight.zf

import com.somytrip.model.flight.enums.CabinClassEnum
import com.somytrip.model.flight.vo.ShopListReq

/**
 * @Description: 模糊询价
 * @author: pigeon
 * @created: 2025-01-07 16:15
 */
data class ShopReq(
    /**
     * journeys, at most 5 journeys.
     */
    val journeys: List<Journey> = emptyList(),
    /**
     * number of passengers
     */
    val passengers: Passenger = Passenger(),
    /**
     * cabin class, default to economy
     *
     * Allowed values:
     * economy
     * premium_economy
     * first
     * business
     */
    val cabinClass: String = "",
    /**
     * defines the airline codes to be excluded.
     * Each airline code should be a 2-character IATA code
     * consisting of either two uppercase letters
     * or one uppercase letter and one digit.
     * You can search for airline codes on IATA.
     */
    val excludeAirlines: List<String> = emptyList(),
    /**
     * defines the airline codes to be included.
     */
    val includeAirlines: List<String> = emptyList(),
    /**
     * defines the maximum flight duration, in minutes.
     * default to unlimited
     */
    val maxDuration: Int = 0,
    /**
     * defines the maximum ticket price. Default to unlimited.
     */
    val maxPrice: String = "0",
    /**
     * defines the number of segments during one journey.
     * Available options:
     */
    val maxSegments: Int = 0,
    /**
     * defines whether checked bags is must
     */
    val mustHaveBag: Boolean = false,
) {
    constructor(req: ShopListReq) : this(
        req.segments.map { segment ->
            Journey(
                segment.departTime,
                segment.arriveCode,
                segment.departCode
            )
        },
        Passenger(
            req.adultNum(),
            req.childNum(),
            req.infantNum()
        ),
        if (req.screenCondition.cabin.isEmpty()) {
            CabinClassEnum.ECONOMY_CLASS.zfValue()
        } else {
            req.screenCondition.cabin.first().zfValue()
        },
        emptyList(),
        req.screenCondition.airline,
    )
}
