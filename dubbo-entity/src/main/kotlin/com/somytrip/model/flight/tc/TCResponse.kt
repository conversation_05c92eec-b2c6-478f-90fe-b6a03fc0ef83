package com.somytrip.model.flight.tc

import com.alibaba.fastjson2.annotation.JSONField
import com.somytrip.model.flight.common.AgeLimitItem
import com.somytrip.model.po.QueryShoppingSegmentStopItem
import java.io.Serializable

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-03 16:27
 */
/**
 * 同程-综合response
 */

data class FlightResponse(
    /**
     * 业务response
     */
    val businessResponse: String = "",
    /**
     * HH000000成功，其他失败
     */
    val code: String? = "",
    /**
     * 请求信息
     */
    val msg: String? = "",
    /**
     * 状态
     */
    val status: Int? = -1
) {
    constructor(code: String, msg: String, status: Int?) : this("", code, msg, status)
//    constructor(): this("", "", "", 0)
}

/**
 * 同程-模糊查询response
 */
data class TCShoppingBusinessResponse(
    /**
     * 产品类型
     */
    val productType: Int = 0,
    val status: Int = 0,
    /**
     * 是否成功
     */
    val success: Boolean = false,
    val traceId: String = "",
    val uuid: String = "",
    /**
     * 返回码对应描述
     */
    val message: String? = "",
    /**
     * 产品明细信息
     */
    val productList: List<TCShoppingProductItem> = emptyList(),
) : Serializable

data class TCShoppingProductItem(
    /**
     * 可保存必要信息，之后生单按原值回传。最大 50 个字符
     */
    val data: String = "",
    /**
     * 产品标签：1 普通产品（快速出票，标准退改签）；3 特惠产品（保障出行）；4、官网产品（官网直营，标准退改）；5 特殊产品（个性产品，保障出行）；10自营产品（快速出票，标准退改）；
     */
    val productTag: String = "",
    /**
     * 乘机人报价：md5Key：乘机人类型 value: 价格信息
     */
    val prices: Map<String, TCPriceInfo> = emptyMap(),
    /**
     * 去程航段按顺序，参考下面的 TCOrderDetailQuerySegment Element
     */
    val segments: List<TCSegmentItem> = emptyList(),
    /**
     * 报销凭证（0 行程单 1 发票 2 行程单+差额发票 3不提供）
     */
    val invoiceType: Int = 3,
    /**
     * 新增字段：开票方式（1 BSP 2 B2B 3 官网 4 其他）
     */
    val invoiceWay: Int,
    /**
     * 出票信息, 参考ticketInfo
     */
    val issueTicketInfo: TCShoppingIssueTicketInfo,
    /**
     * 服务时间，md5Key:ticket-出票，refundAndChange-退改;value格式：星期几;时间段1/时间段2&星期几;时间段，如：1;00:00-14:30/15:00-23:59&2;09:00-18:00
     */
    val serviceTime: TCShoppingProductServiceTime,
    /**
     * 行李信息，此节点为空
     */
    val baggageInfo: Map<String, Any>?,
    /**
     * 舱位信息
     */
    val cabins: List<TCShoppingCabin>,
    /**
     * 限制信息
     */
    val limitInfo: TCShoppingLimitInfo,
    /**
     * 价格类型
     */
    val priceType: Int,
    /**
     * 商品code
     */
    val productCode: Int,
    /**
     * 限制信息
     */
    val recommend: String,
    /**
     * 退改规则信息，此节点返回为空
     */
    val refundChangeInfo: Map<String, Any>?,
) : Serializable

data class TCShoppingProductServiceTime(
    val refundAndChange: String,
    val ticket: String
) : Serializable

data class TCPriceInfo(
    /**
     * 乘客类型 ADT/CHD/INF
     */
    val passengerType: String,
    /**
     * 票面价(不含税) : Serializable，人民币 将弃用
     */
    val netPrice: Int,
    /**
     * 销售价，人民币 将弃用
     */
    val price: Int,
    /**
     * 税费总金额 将弃用
     */
    val tax: String,
    /**
     * 票面价(不含税) : Serializable，人民币 类型为浮点型
     */
    @JSONField(name = "netPrice_b")
    val netPriceB: Int,
    /**
     * 折扣
     */
    val discount: Double,
    /**
     * 销售价，人民币 类型为浮点型
     */
    @JSONField(name = "price_b")
    val priceB: Double,
    /**
     * 税费总金额 类型为浮点型
     */
    @JSONField(name = "tax_b")
    val taxB: Double,
    /**
     * Key为税项（tax-机建费(整数) : Serializable，yq-燃油费（整数），tax_b(浮点型) : Serializable，yq_b(浮点型) : Serializable）
     */
    val taxDetail: TCTaxDetail,
    val airportFee: Int,
    @JSONField(name = "airportFee_b")
    val airportFeeB: Int,
    val standardPrice: Int,
    @JSONField(name = "standardPrice_b")
    val standardPriceB: Int,
) : Serializable

data class TCSegmentItem(
    /**
     * 机型 ，如738
     */
    val aircraft: String = "",
    /**
     * 机型类型
     */
    val aircraftType: Int = 0,
    /**
     * 到达机场 IATA 三字码
     */
    val arrAirport: String = "",
    /**
     * 抵达航站楼，使用简写，例如T1
     */
    val arrAirportTerm: String = "",
    /**
     * 到达城市三字码
     */
    val arrCity: String = "",
    /**
     * 到达城市
     */
    val arrCityName: String = "",
    /**
     * 到达时间，格式：YYYYMMDDHHMM
     */
    val arrDateTime: String = "",
    /**
     * 舱等，头等：F，商务：C，超经：S，经济：Y
     */
    val cabinClass: String = "",
    /**
     * 舱位
     */
    val cabinCode: String = "",
    /**
     * 出发机场三字码
     */
    val depAirport: String = "",
    /**
     * 出发航站楼，使用简写，例如T1
     */
    val depAirportTerm: String = "",
    /**
     * 出发城市三字码
     */
    val depCity: String = "",
    /**
     * 出发城市
     */
    val depCityName: String = "",
    /**
     * 起飞时间，格式：YYYYMMDDHHMM
     */
    val depDateTime: String = "",
    /**
     * 飞行时长（单位分钟）：120, 0表示未知
     */
    val duration: Int = 0,
    /**
     * 共享航班标识 false 非共享 true共享
     */
    val flightShare: Boolean = false,
    val mainSegment: Boolean = false,
    /**
     * 航司 IATA 二字码，必须与 flightNumber 航司相同
     */
    val marketingAirline: String = "",
    /**
     * 航班号，如：CA123
     */
    val marketingFlightNo: String = "",
    /**
     * 实际承运航司
     */
    val operatingAirline: String = "",
    /**
     * 实际承运航班号
     */
    val operatingFlightNo: String = "",
    /**
     * 行程序号，全局序号从1开始计数
     */
    val segmentIndex: Int = 0,
    val segmentKey: String = "",
    /**
     * 1：去程； 2: 返程
     */
    val segmentType: Int = 0,
    /**
     * 经停总时长
     */
    val stopTime: Int = 0,
    /**
     * 经停信息,只包含机场三字码，默认为null
     */
    val stops: List<QueryShoppingSegmentStopItem> = emptyList(),
    /**
     * 餐食信息 0:未知 1:有 2:无
     */
    val meal: Int = 0,
    /**
     * 航班里程数（单位km）：1980，0表示未知
     */
    val mileage: Int = 0,
) : Serializable

data class TCShoppingIssueTicketInfo(
    /**
     * 是否自动出票（0否 1是）
     */
    val autoTicket: Int,
    /**
     * GDS(国内默认1E) : Serializable;1E：TravelSky;1A：Amadeus;1B：Abacus;1S：Sabre;1P：WorldSpan;1G：Galileo;00：未知
     */
    val reservationType: String?,
    /**
     * 编码code
     */
    val changePnrCode: Int,
    /**
     * 开票航司
     */
    val issueTicketCarrier: String,
) : Serializable

data class TCShoppingCabin(
    /**
     * 舱等
     */
    val cabinClass: String = "",
    /**
     * 舱位
     */
    val cabinCode: String = "",
    /**
     * 剩余座位数
     */
    val cabinCount: Int = 0,
    /**
     * 航段序号
     */
    val segmentIndex: Int = 0,
) : Serializable

data class TCShoppingLimitInfo(
    /**
     * 年龄限制
     */
    val agePairs: List<TCShoppingAgePair>?,
    /**
     * 证件类型 0:不限 1:身份证 2:签证 3:护照 4:军官证 5:回乡证 6:港澳通行证 7:台胞证;8:台湾通行证 9:海员证 10:户口簿 11:外国人永久居留证 12:港澳居住证 13:台湾居民居住证
     */
    val credentialType: String?,
    /**
     * 团体资质:NML普通票 SGT小团票
     */
    val groupQualification: String,
    /**
     * 身份证开头
     */
    val idcardPrefix: String?,
    /**
     * 人数上限（小团票必须有值）
     */
    val maxPersonCount: Int?,
    /**
     * 人数下限（小团票必须有值）
     */
    val minPersonCount: Int?,
    /**
     * 允许售卖的国家二字码
     */
    val nationalityAllows: List<String>?,
    /**
     * 禁止售卖的国家二字码
     */
    val nationalityForbids: List<String>?,
    val needCertificateOrder: Int,
    val netPriceSource: Int?,
    val passengerQualification: String,
    val ticketByOperateCarrier: Int,
    val validatePrice: Int,
) : Serializable

data class TCShoppingAgePair(
    /**
     * 最小年龄
     */
    val min: Int,
    /**
     * 最大年龄
     */
    val max: Int
) : Serializable

data class TCTaxDetail(
    val yq: Int = 0,
    val tax: Int = 0,
    @JSONField(name = "yq_b")
    val yqB: Double = 0.0,
    @JSONField(name = "tax_b")
    val taxB: Double = 0.0,
) : Serializable

/**
 * 同程-客规查询业务response
 */
data class TCGuestBusinessResponse(
    val code: String?,
    val message: String,
    /**
     * 规则info列表
     */
    val ruleInfos: List<TCGuestBusinessRuleInfo>,
    val status: Int,
    val success: Boolean,
    val traceId: String,
) : Serializable

data class TCGuestBusinessRuleInfo(
    /**
     * 行李信息
     */
    val baggageInfo: TCGuestBusinessBaggageInfo,
    val data: String,
    /**
     * 退改规则信息
     */
    val refundChangeInfo: TCGuestBusinessRefundChangeInfo,
    val segments: List<TCSegmentItem>,
    val standardRule: Int,
) : Serializable

data class TCGuestBusinessBaggageInfo(
    /**
     * 行李文本，上海-北京：成人，托运，每人1件，每件23KG，每件长宽高之和<=158CM
     */
    val baggageText: String,
    /**
     * 手提行李额
     */
    val carryOnBaggage: List<TCGuestBusinessCarryOnBaggage>,
    /**
     * 托运行李额
     */
    val checkedBaggage: List<TCGuestBusinessCarryOnBaggage>,
) : Serializable

data class TCGuestBusinessCarryOnBaggage(
    /**
     * 乘客类型 ADT/CHD/INF
     */
    val passengerType: String,
    /**
     * 各个航段的行李额信息
     */
    val rules: List<TCGuestBusinessRule>,
) : Serializable

data class TCGuestBusinessRule(
    /**
     * 行李件数，PC
     */
    val freePieces: String,
    /**
     * 该行李额适用的航段segmentIndex，对应请求航段中的index
     */
    val segmentIndex: Int,
    /**
     * 行李的体积
     */
    val volume: String,
    /**
     * 行李重量
     */
    val weight: String,
    /**
     * 重量类型，1: 单件重量， 2:总重量
     */
    val weightType: Int,
) : Serializable

data class TCGuestBusinessRefundChangeInfo(
    /**
     * 改签文本信息
     */
    val changeText: String,
    /**
     * 退改信息
     */
    val refundChangeRules: List<TCGuestBusinessRefundChangeRule>,
    /**
     * 退票文本信息
     */
    val refundText: String,
) : Serializable

data class TCGuestBusinessRefundChangeRule(
    val allowed: Int,
    /**
     * 默认CNY
     */
    val currency: String?,
    /**
     * 时间段结束点,比如： 退规定义时飞后12小时， 返回值则是12
     */
    val endpoint: Int,
    /**
     * 根据公式计算出的费用. 例如 50.0 默认0
     */
    val fee: Double,
    /**
     * 收费标准(有可能有小数) : Serializable 默认””格式化fee字段后， 展示出来的文案：有费用时(fee>0) : Serializable： “￥50.00/人”,无费用(fee=0) : Serializable: “免费”
     */
    val feeDesc: String?,
    /**
     * 是否包含结束时间 true/false,比如： 退规定义， 起飞后12小时（不含）内及起飞后提出，每次变更收取票面价40%作为变更费”
     */
    val includeEndPoint: Boolean,
    /**
     * 是否包含开始时间 true/false比如：退规定义， 起飞前12小时(含) : Serializable之前提出，每次变更收取票面价30%作为变更费
     */
    val includeStartPoint: Boolean,
    /**
     * 乘客类型 ADT-成人，CHD-儿童，INF-婴儿
     */
    val passengerType: String,
    /**
     * 当前时间段定义的计算公式中解析出来的退票百分比(乘以100后的值） 默认0
     */
    val percent: Int,
    /**
     * 1 退票;2 改期
     */
    val ruleType: Int,
    /**
     * 航段限制
     */
    val segLimit: Int,
    /**
     * 航段index
     */
    val segmentIndex: Int,
    /**
     * 时间段开始点,比如： 退规定义时飞前12小时， 返回值则是 -12
     */
    val startPoint: Int,
    /**
     * 时间段. 如果没有定义时间段则为””.常见类似返回值为下面的几种：”2018-11-11 00:00:00前”,”2018-11-11 00:00:00至2018-11-12 03:00:00”,”2018-11-12 03:00:00后”,””
     */
    val timeRange: String?,
    /**
     * ？
     */
    val usedType: Int,
) : Serializable

data class TCOrderProductDetailResponse(
    /**
     * 小编码（支付后占编的为空）
     */
    val pnr: String,
    /**
     * 大编码
     */
    val bigPnr: String,
) : Serializable


/**
 * 同程-支付校验接口response
 */
data class TCPayCheckBusinessResponse(
    val message: String,
    /**
     * 订单号
     */
    val orderSerialNo: String,
    /**
     * 产品信息
     */
    val prePayProductItems: List<TCPayCheckPrePayProductItem>,
    val success: Boolean,
    val traceId: String,
) : Serializable

data class TCPayCheckPrePayProductItem(
    /**
     * 产品校验支付信息
     */
    val prePayProductItemDetail: String,
    /**
     * 产品类型 0 机票
     */
    val productType: String,
) : Serializable

data class TCPayCheckPrePayProductDetail(
    /**
     * 销售价（对应产品的总金额）
     */
    val price: String,
    /**
     * 编码信息
     */
    val pnrs: List<String>?
) : Serializable

/**
 * 同程-支付接口response
 */
data class TCPayResponse(
    val message: String = "",
    /**
     * 订单号
     */
    val orderSerialNo: String = "",
    val success: Boolean = false,
)

/**
 * 同程-改签列表 response
 */
data class TCChangeReShopResponse(
    val endorseFlightInfos: List<ReShopItem> = emptyList(),
    val errorCode: String = "",
    val subTraceId: String = "",
    val traceId: String = "",
)

data class ReShopItem(
    val data: String = "",
    val flightNo: String = "",
    val carrier: String = "",
    val flightShare: Boolean = false,
    val shareFlightNo: String = "",
    val realCarrier: String = "",
    val departureCityCode: String = "",
    val departureAirportCode: String = "",
    val departureTerminal: String = "",
    val arrivalCityCode: String = "",
    val arrivalAirportCode: String = "",
    val arrivalTerminal: String = "",
    val takeOffDateTime: String = "",
    val arrivalDateTime: String = "",
    val aircraft: String = "",
    val aircraftType: String = "",
    val duration: String = "0",
    val distance: String = "",
    val meal: Int = -1,
    val stopTime: Int = -1,
    val stopInfoList: List<ReShopItemStopInfo> = emptyList(),
    val cabinInfos: List<ReShopItemCabinInfo> = emptyList(),
)

data class ReShopItemStopInfo(
    val stopCityCode: String = "",
    val stopAirportCode: String = "",
    val duration: Int = 0,
    val departureDateTime: String = "",
    val arrivalDateTime: String = "",
)

data class ReShopItemCabinInfo(
    val passengerType: Int = 1,
    /**
     * A代表大于9个，1-9是剩余座位，其他字母都是不可预订
     */
    val seatCount: String = "",
    val cabinClass: String = "",
    val cabinCode: String = "",
    val cabinDesc: String = "",
    val priceInfo: CabinPriceInfo = CabinPriceInfo(),
)

data class CabinPriceInfo(
    val discount: String = "",
    val ticketPrice: String = "",
    val tax: String = "",
    val ticketPriceDiff: String = "",
    val taxDiff: String = "",
    val upgradeFee: String = "",
    val endorseFee: String = "",
    val agencyFee: String = "",
    val taxDiffDetail: Map<String, String> = emptyMap(),
    val taxDetail: Map<String, String> = emptyMap(),
    val calcEndorseFee: Boolean = true,
)

data class TCEndorseQueryEndorseFlightInfo(
    /**
     * 标识报价唯一ID
     */
    val data: String,
    /**
     * 航班号
     */
    val flightNo: String,
    /**
     * 出票航司二字码
     */
    val carrier: String,
    /**
     * 是否共享，0：否 1是
     */
    val flightShare: Int,
    /**
     * 共享航班号
     */
    val shareFlightNo: String,
    /**
     * 实际承运航司
     */
    val realCarrier: String,
    /**
     * 出发城市
     */
    val departureCityCode: String,
    /**
     * 出发机场三字码
     */
    val departureAirportCode: String,
    /**
     * 出发航站楼
     */
    val departureTerminal: String,
    /**
     * 到达城市三字码
     */
    val arrivalCityCode: String,
    /**
     * 到达机场三字码
     */
    val arrivalAirportCode: String,
    /**
     * 到达航站楼
     */
    val arrivalTerminal: String,
    /**
     * 起飞时间
     */
    val takeOffDateTime: String,
    /**
     * 到达时间
     */
    val arrivalDateTime: String,
    /**
     * 机型名称
     */
    val aircraft: String,
    /**
     * 机型
     */
    val aircraftType: Int,
    /**
     * 飞行时长
     */
    val duration: Int,
    /**
     * 飞行里程
     */
    val distance: String,
    /**
     * 是否有餐食 0 – 未知 1 – 有餐食 2 – 无参食/未知
     */
    val meal: Int,
    /**
     * 经停次数
     */
    val stopTime: Int,
    /**
     * 经停信息
     */
    val stopInfoList: List<TCEndorseQueryStopItem>,
    /**
     * 舱位信息key:乘客类型，value:舱位信息（1:成人, 2:儿童, 3:婴儿）
     */
    val cabinInfos: Map<String, TCEndorseQueryCabinInfo>,
) : Serializable

data class TCEndorseQueryStopItem(
    val stopCityCode: String,
    val stopAirportCode: String,
    val duration: Int,
    val departureDateTime: String,
    val arrivalDateTime: String,
) : Serializable

data class TCEndorseQueryCabinInfo(
    val passengerType: Int,
    val seatCount: String,
    val cabinClass: String,
    val cabinCode: String,
    val cabinDesc: String,
    val priceInfo: TCEndorseQueryPriceInfo,
) : Serializable

data class TCEndorseQueryPriceInfo(
    val discount: String,
    val ticketPrice: String,
    val tax: String,
    val taxDetail: Map<String, String>,
    val ticketPriceDiff: String,
    val taxDiff: String,
    val taxDiffDetail: Map<String, String>,
    val calcEndorseFee: Boolean,
    val upgradeFee: String,
    val endorseFee: String,
    val agencyFee: String,
) : Serializable


/**
 * 上传附件response
 */
data class TCEndorseFileUploadResponse(
    val orderSerialNo: String,
    val fileIds: List<String>,
) : Serializable

/**
 * 改签申请response
 */
data class TCEndorseApplyResponse(
    /**
     * 改签订单号
     */
    val endorseSerialNo: String = "",
    val errorCode: String? = "",
    val errorMsg: String? = "",
    val subTraceId: String? = "",
    val traceId: String? = "",
) : Serializable

/**
 * 改签支付response
 */
data class TCEndorsePayResponse(
    val errorCode: String? = "",
    val errorMsg: String? = "",
    val subTraceId: String? = "",
    val traceId: String? = "",
) : Serializable

/**
 * 改签取消response
 */
data class TCEndorseCancelResponse(
    val errorCode: String?,
    val errorMsg: String?,
) : Serializable

/**
 * 改签详情response
 */
data class TCQueryChangeOrderDetailResponse(
    val data: TCQueryChangeOrderDetailInfo = TCQueryChangeOrderDetailInfo(),
) : Serializable

data class TCQueryChangeOrderDetailInfo(
    /**
     * 业务类型（0-国内 1-国际）
     */
    val businessLineType: Int = 0,
    /**
     * 出票订单号（H0开头）
     */
    val orderSerialNo: String = "",
    /**
     * 改期单号
     */
    val endorseSerialNo: String = "",
    /**
     * 改签单状态，0:全部；1-待确认 2-待支付 3-支付完成 4-待改签 5-改签完成 6-改签取消7:改签暂不能；8:票号验证失败；9:支付中；10:票号校验中
     */
    val orderState: Int = 0,
    /**
     * 改签类型，0：全部；1:自愿；2:非自愿；5：取消位置；
     */
    val endorseType: Int = 0,
    /**
     * 非自愿改签理由。非自愿改签必传，自愿改签不传，1:航班变动；2:疫情；3:拒载；4:其他
     */
    val reason: Int? = 1,
    /**
     * 改签备注（非自愿改签理由为‘其他’时，该备注字段不能为空）
     */
    val remark: String? = "",
    /**
     * 附件地址
     */
    val urls: List<String>? = emptyList(),
    /**
     * 改签订单取消原因
     */
    val endorseDesc: List<String>? = emptyList(),
    /**
     * 退款类型(1-原路退 2-退客人卡 3-前往机场柜台退现金 4-前往营业部办理) : Serializable
     */
    val refundWayType: String? = "1",
    /**
     * 改签订单总金额（包括多人）
     */
    val totalPrice: String? = "0",
    /**
     * 创建时间（yyyy-MM-dd HH:mm:ss）
     */
    val gmtCreate: String = "",
    /**
     * 改签乘客信息
     */
    val ticketInfos: List<TCEndorseDetail2QueryTicketInfo> = emptyList(),
    /**
     * 航段信息
     */
    val segmentInfo: TCChangeOrderSegInfo = TCChangeOrderSegInfo(),
    /**
     * 请求唯一值
     */
    val traceId: String = "",
) : Serializable

data class TCChangeOrderSegInfo(
    /**
     * 改签前航班信息
     */
    val multiSegmentOriginalFlightInfo: List<TCChangeOrderSegItem> = emptyList(),
    /**
     * 改签后航班信息
     */
    val multiSegmentChangeFlightInfo: List<TCChangeOrderSegItem> = emptyList(),
) : Serializable

data class TCChangeOrderSegItem(
    /**
     * 1：去程； 2: 返程
     */
    val segmentType: Int = 1,
    /**
     * 行程序号，全局序号从1开始计数
     */
    val segmentIndex: Int = 1,
    /**
     * 航段guid
     */
    val segmentGuid: String = "",
    /**
     * 出发机场三字码
     */
    val departureAirportCode: String = "",
    /**
     * 到达机场三字码
     */
    val arriveAirportCode: String = "",
    /**
     * 出发城市名称
     */
    val departureCity: String = "",
    /**
     * 到达城市名称
     */
    val arriveCity: String = "",
    /**
     * 出发航站楼，例如T1
     */
    val departureTerminal: String? = "",
    /**
     * 到达航站楼，例如T1
     */
    val arriveTerminal: String? = "",
    /**
     * 起飞时间（yyyy-MM-dd HH:mm:ss）
     */
    val gmtTakeOffTime: String = "",
    /**
     * 到达时间（yyyy-MM-dd HH:mm:ss）
     */
    val gmtArriveTime: String = "",
    /**
     * 航班号
     */
    val flightNo: String = "",
    /**
     * 航司二字码
     */
    val airways: String = "",
    /**
     * 航司名称
     */
    val airwaysName: String = "",
    /**
     * 舱位
     */
    val cabinCode: String = "",
) : Serializable

data class TCEndorseDetail2QueryTicketInfo(
    /**
     * 原始改签乘客信息
     */
    val originalTicket: TCEndorseDetail2QueryPassengerInfo = TCEndorseDetail2QueryPassengerInfo(),
    /**
     * 改签后乘客信息
     */
    val newTicket: TCEndorseDetail2QueryPassengerInfo = TCEndorseDetail2QueryPassengerInfo(),
    /**
     * 改签费
     */
    val endorseFee: String = "",
    /**
     * 升舱费
     */
    val upgradeFee: String = "",
    /**
     * 税费差价
     */
    val taxDiff: String = "",
    /**
     * 代理费
     */
    val agencyFee: String = "",
    /**
     * 改签服务费
     */
    val endorseProcedureFee: String = "",
) : Serializable

data class TCEndorseDetail2QueryPassengerInfo(
    /**
     * 乘客类型: 1-成人，2-儿童，3-婴儿
     */
    val passengerType: Int = 1,
    /**
     * 乘客性别: 1-男 2-女
     */
    @JSONField(name = "passengerGende")
    val passengerGende: Int = 1,
    /**
     * 名
     */
    val firstName: String = "",
    /**
     * 姓
     */
    val lastName: String = "",
    /**
     * 证件类型 0 未知，1 身份证，2 护照，3 军官证，4 回乡证，5 港澳通行证，6 台胞证，7 台湾通行证，8 海员证，9 其他
     */
    val creditType: Int = 0,
    /**
     * 证件号
     */
    val creditNo: String = "",
    /**
     * 票面价
     */
    val ticketPrice: String = "",
    /**
     * 税费总金额
     */
    val tax: String = "",
    /**
     * 基建费
     */
    val buildFee: String = "",
    /**
     * 燃油费
     */
    val oilFee: String = "",
    /**
     * 客票号（多段客票号）
     */
    val ticketNos: List<TCEndorseDetail2QuerySegmentNo> = emptyList(),
) : Serializable

data class TCEndorseDetail2QuerySegmentNo(
    /**
     * 1：去程； 2: 返程
     */
    val segmentType: Int = 1,
    /**
     * 行程序号，全局序号从1开始计数
     */
    val segmentIndex: Int = 1,
    /**
     * 航段guid
     */
    val segmentGuid: String = "",
    /**
     * 客票号
     */
    val ticketNo: String = "",
    /**
     * 小编码
     */
    val pnr: String = "",
    /**
     * 大编码
     */
    val bigPnr: String = "",
) : Serializable

/**
 * 同程-可退查询接口response
 */
data class TCRefundAbleResponse(
    /**
     * 订单航段类型[单程、真往返、假往返] 1:单程 2:假往返 3:真往返 4:假联程 5:真联程
     */
    val airlineType: Int = 1,
    /**
     * 业务线归属(0:国内/1:国际) : Serializable
     */
    @JSONField(name = "buisinessType")
    val buisinessType: Int = 0,
    /**
     * 订单流水
     */
    val orderSerialNo: String = "",
    /**
     * 航段
     */
    val routePassengerInfos: List<TCRefundAbleRoutePassengerInfo> = emptyList(),
) : Serializable

data class TCRefundAbleRoutePassengerInfo(
    /**
     * 航段-
     */
    val autoCalcPenalty: Int,
    /**
     * 航段-
     */
    val bizSerialNo: String,
    /**
     * 航段-乘客退订信息
     */
    val passengerRefundInfo: List<TCRefundAblePassengerRefundInfo>,
    /**
     * 航段-
     */
    val routeGuid: String,
    /**
     * 航段-航段，0.未知. 1去程 2返程
     */
    val routeNo: Int,
    /**
     * 航段-
     */
    val routeSeq: Int,
    /**
     * 航段-乘客退改签规则信息
     */
    val tgqRuleInfos: List<TCRefundAbleTgqRuleInfo>,
) : Serializable

data class TCRefundAblePassengerRefundInfo(
    val firstName: String,
    val flightInfo: TCRefundAbleFlightInfo,
    val flightInfoList: List<TCRefundAbleFlightInfoList>,
    val lastName: String,
    val passengerGuid: String,
    val passengerType: Int,
    val refundFlag: Boolean,
    val refundFlagMessage: String,
    val refundProducts: List<TCRefundAbleRefundProduct>,
    val sex: Int,
    val tipType: Int,
) : Serializable

data class TCRefundAbleFlightInfo(
    val airLineCode: String,
    val airLineDesc: String,
    val airWaysCode: String,
    val airWaysShortName: String,
    val arrivalDate: String,
    val arriveCityDes: String,
    val arrivePortCode: String,
    val arrivePortCodeDesc: String,
    val cabinCode: String,
    val flightNo: String,
    val flyOffDate: String,
    val offCityDes: String,
    val offPortCode: String,
    val offPortCodeDes: String,
    val pnr: String,
    val realFlightNo: String,
    val ticketNo: String,
) : Serializable

data class TCRefundAbleFlightInfoList(
    /**
     * 航班信息-
     */
    val airLineCode: String,
    /**
     * 航班信息-
     */
    val airLineDesc: String,
    /**
     * 航班信息-航空公司二字码
     */
    val airWaysCode: String,
    /**
     * 航班信息-航司简称
     */
    val airWaysShortName: String,
    /**
     * 航班信息-
     */
    val arrivalDate: String,
    /**
     * 航班信息-
     */
    val arriveCityDes: String,
    /**
     * 航班信息-
     */
    val arrivePortCode: String,
    /**
     * 航班信息-
     */
    val arrivePortCodeDesc: String,
    /**
     * 航班信息-
     */
    val cabinCode: String,
    /**
     * 航班信息-航班号
     */
    val flightNo: String,
    /**
     * 航班信息-
     */
    val flyOffDate: String,
    /**
     * 航班信息-
     */
    val offCityDes: String,
    /**
     * 航班信息-
     */
    val offPortCode: String,
    /**
     * 航班信息-
     */
    val offPortCodeDes: String,
    /**
     * 航班信息-
     */
    val pnr: String,
    /**
     * 航班信息-航班号
     */
    val realFlightNo: String,
    /**
     * 航班信息-
     */
    val ticketNo: String,
) : Serializable

data class TCRefundAbleRefundProduct(
    val passengerType: Int,
    val purchaseBackMoney: Double,
    val purchasePenalty: Int,
    val purchasePrice: Double,
    val refundPath: Int,
) : Serializable

data class TCRefundAbleTgqRuleInfo(
    /**
     * 行李额规则
     */
    val baggageRule: String = "",
    /**
     * 签转规则
     */
    val changeRule: String = "",
    /**
     * 改签每个时间段的定义
     */
    val endorsePeriods: List<TCRefundAblePeriod> = emptyList(),
    /**
     * 乘客类型 （1-成人、2-儿童、3、婴儿）
     */
    val passengerType: Int = 0,
    /**
     * 退订每个时间段的定义
     */
    val refundPeriods: List<TCRefundAblePeriod> = emptyList(),
) : Serializable

data class TCRefundAblePeriod(
    /**
     * ？
     */
    val allowed: Int,
    /**
     * 结束点
     */
    val endPoint: Int,
    /**
     * 根据计算公式计算出的费用. 默认0
     */
    val fee: Int,
    /**
     * 退规描述
     */
    val feeDesc: String?,
    /**
     * 是否选中当前时间段 0.没选中 1选中
     */
    val selected: Int,
    /**
     * 开始点
     */
    val startPoint: Int,
) : Serializable

/**
 * 退票申请
 */
data class TCRefundApplyResponse(
    /**
     * 订单流水号
     */
    val orderSerialNo: String = "",
    /**
     * 退票订单号
     */
    val taskSerialNo: String = "",
) : Serializable

/**
 * 上传附件response
 */
data class TCRefundFileUploadResponse(
    val taskSerialNo: String = "",
    val orderSerialNo: String = "",
    val fileIds: List<String> = emptyList(),
) : Serializable

/**
 * 退票列表接口
 */
data class TCRefundListResponse(
    val data: List<String> = emptyList(),
    val code: String = "",
    val message: String = "",
) : Serializable

/**
 * 退票详情response
 */
data class TCRefundDetailResponse(
    val data: String,
) : Serializable

data class TCRefundDetailInfo(
    /**
     * 业务类型（0-国内 1-国际）
     */
    val businessLineType: Int,
    /**
     * 出票订单号（H0开头）
     */
    val orderSerialNo: String,
    /**
     * 退票单号
     */
    val taskSerialNo: String,
    /**
     * 退票单状态，0:待审核；1:待确认；2:待处理；3:退订中；4:退订完成；5:退订取消；6:暂不能退票；7:待核价（仅国际订单才有）；
     */
    val orderState: Int,
    /**
     * 退款状态，0：未退款，1：已退款，2：无需退款, 3退款失败,4 解冻失败,5:退款中
     */
    val financeState: Int,
    /**
     * 退票类型，0:自愿；1:非自愿；
     */
    val refundType: Int,
    /**
     * 非自愿退票理由。非自愿退票必传，自愿退票不传，21:航班变动;22:前段变动导致后段无法乘坐;23:只退票不退款;24:病退;25:疫情;26:拒载、超售;27:其他（迫降、降舱等）;
     */
    val reason: Int,
    /**
     * 退票备注
     */
    val remark: String,
    /**
     * 附件地址
     */
    val urls: List<String>,
    /**
     * 退票订单取消原因
     */
    val cancelMsg: String,
    /**
     * 退票订单总金额（包括多人）
     */
    val totalPrice: String,
    /**
     * 创建时间（yyyy-MM-dd HH:mm:ss）
     */
    val gmtCreate: String,
    /**
     * 退款时间（yyyy-MM-dd HH:mm:ss）
     */
    val gmtFinanceFinish: String,
    /**
     * 退票乘客信息
     */
    val ticketInfos: List<String>,
    /**
     * 请求唯一值
     */
    val traceId: String,
) : Serializable

data class TCRefundDetailTicketInfo(
    /**
     * 乘客类型: 1-成人，2-儿童，3-婴儿
     */
    val passengerType: Int,
    /**
     * 乘客性别: 1-男 2-女
     */
    @JSONField(name = "passengerGende")
    val passengerGende: Int,
    /**
     * 名
     */
    val firstName: String,
    /**
     * 姓
     */
    val lastName: String,
    /**
     * 证件类型 0 未知，1 身份证，2 护照，3 军官证，4 回乡证，5 港澳通行证，6 台胞证，7 台湾通行证，8 海员证，9 其他
     */
    val creditType: Int,
    val creditNo: String,
    val ticketPrice: String,
    val segmentsInfo: List<TCRefundDetailSegmentsInfo>,
    val priceInfo: List<TCRefundDetailPriceInfo>,
) : Serializable

data class TCRefundDetailSegmentsInfo(
    val segmentEndorseNo: Int,
    val segmentType: Int,
    val segmentIndex: Int,
    val segmentGuid: String,
    val departureAirportCode: String,
    val arriveAirportCode: String,
    val departureCity: String,
    val arriveCity: String,
    val departureTerminal: String,
    val arriveTerminal: String,
    val gmtTakeOffTime: String,
    val gmtArriveTime: String,
    val flightNo: String,
    val airways: String,
    val airwaysName: String,
    val cabinCode: String,
    val ticketNo: String,
) : Serializable

data class TCRefundDetailTicketNoInfo(
    /**
     * 是否是改签行程，0表示原行程，1表示第一次改签行程，2表示第二次改签行程
     */
    val segmentEndorseNo: Int,
    /**
     * 1:去程； 2:返程
     */
    val segmentType: Int,
    /**
     * 行程序号，全局序号从1开始计数
     */
    val segmentIndex: Int,
    /**
     * 航段guid
     */
    val segmentGuid: String,
    /**
     * 客票号
     */
    val ticketNo: String,
    /**
     * 小编码
     */
    val pnr: String,
    /**
     * 大编码
     */
    val bigPnr: String,
) : Serializable

data class TCRefundDetailPriceInfo(
    /**
     * 是否是改签行程，0表示原行程，1表示第一次改签行程，2表示第二次改签行程
     */
    val segmentEndorseNo: Int,
    /**
     * 退票费
     */
    val refundFee: String,
    /**
     * 误机费（仅国际订单才有）
     */
    @JSONField(name = "noshowFee")
    val noshowFee: String,
    /**
     * 已使用航段费（仅国际订单才有）
     */
    val useFlightFee: String,
    /**
     * 退票应退金额
     */
    val refundMoney: String,
    /**
     * 出票/改签支付金额
     */
    val totalMoney: String,
    /**
     * 退票服务费（仅国际订单才有）
     */
    val serviceFee: String,
) : Serializable


/**
 * 同程-订单列表查询response
 */
data class TCOrderListQueryResponse(
    val code: String = "",
    val message: String = "",
    val orderSerialNos: List<String> = emptyList(),
) : Serializable

/**
 * 同程-订单详情查询response
 */
data class TCOrderDetailQueryResponse(
    /**
     * HH000000-查询成功、
     * HH4000001-查询失败未找到对应订单 、
     * HH4060001-查询失败,系统异常请联系管理员
     * HH4060002-采购商订单不存在
     */
    val code: String = "",
    val message: String = "",
    /**
     * 产品明细信息
     */
    val products: List<TCOrderDetailQueryProduct> = emptyList(),
    /**
     * 是否成功
     */
    val success: Boolean = false,
    /**
     * 唯一请求标识
     */
    val traceId: String = "",
) : Serializable

data class TCOrderDetailQueryProduct(
    /**
     * 行李额信息
     */
    val baggageRules: List<TCOrderDetailQueryBaggageRule> = emptyList(),
    /**
     * 联系人姓名
     */
    val contactName: String = "",
    /**
     * 联系人电话
     */
    val contactPhone: String = "",
    /**
     * 订单创建时间
     */
    val createTime: String = "",
    /**
     * 订单号
     */
    val orderSerialNo: String = "",
    /**
     * 订单状态 0-待支付、1-下单取消、2-支付中、3-预定中、5-已预订、6-退款中、7-已退款
     */
    val orderState: Int = -1,
    /**
     * 乘客信息
     */
    val passengers: List<TCOrderDetailQueryPassenger> = emptyList(),
    /**
     * 支付信息
     */
    val payments: List<TCOrderDetailQueryPayment> = emptyList(),
    /**
     * 价格信息
     */
    val prices: List<TCOrderDetailQueryPrice> = emptyList(),
    /**
     * 产品类型 1 普通标准（快速出票，标准退改）；2优选（代理低价、标准退改）；3 特惠（保障出行）；4、官网（官网直营，标准退改）；5 特殊产品（个性产品，保障出行）；6青老年特惠 ； 7多人特惠
     */
    val productCode: Int = 0,
    /**
     * 业务类型 0 机票 1 辅营 2 火车票 3 酒店（目前仅有0机票）
     */
    val productType: String = "0",
    /**
     * 采购外部单号
     */
    val purchaserSerialNo: String = "",
    /**
     * 行程信息
     */
    val segments: List<TCOrderDetailQuerySegment> = emptyList(),
    /**
     * 票号信息
     */
    val tickets: List<TCOrderDetailQueryTicket> = emptyList(),
    /**
     * 订单总价
     */
    val totalPrice: String = "",
) : Serializable

data class TCOrderDetailQueryBaggageRule(
    /**
     * 托运行李数量
     */
    val baggageNumber: String = "",
    /**
     * 托运行李重量
     */
    val baggageWeight: String = "",
    /**
     * 手提行李数量
     */
    val handNumber: String = "",
    /**
     * 手提行李重量
     */
    val handWeight: String = "",
    /**
     * 乘客类型 0:未知 1:成人 2:儿童 3:婴儿
     */
    val passengerType: Int = 0,
    /**
     * 航段序号
     */
    val segmentIndex: Int = 0,
) : Serializable

data class TCOrderDetailQueryPassenger(
    /**
     * 乘客出生日期
     */
    val birthday: String = "",
    /**
     * 证件号码
     */
    val creditNo: String = "",
    /**
     * 证件号类型
     */
    val creditType: Int = 0,
    /**
     * 乘客名本次调整：跟下单传的格式保持一致；“张三”or“张/三”
     */
    val firstName: String = "",
    /**
     * 乘客性别
     */
    val gender: Int = 0,
    /**
     * 证件有效期
     */
    val gmtCreditValidate: String = "",
    /**
     * 乘客姓
     */
    val lastName: String = "",
    /**
     * 乘机人联系方式 采购下单传的电话号码or平台默认的手机号码
     */
    val linkPhone: String = "",
    /**
     * 国籍
     */
    val nation: String = "",
    /**
     * CN
     */
    val nationCode: String = "",
    /**
     * 乘客类型 0:未知 1:成人 2:儿童 3:婴儿
     */
    val passengerType: Int = 0,
) : Serializable

data class TCOrderDetailQueryPayment(
    /**
     * 支付金额
     */
    val payMoney: String = "",
    /**
     * 0:支付中 1:已支付 2:已取消
     */
    val payResult: Int = 0,
    /**
     * 支付时间
     */
    val payTime: String = "",
    /**
     * 交易流水号
     */
    val tradeNo: String = "",
) : Serializable

data class TCOrderDetailQueryPrice(
    /**
     * 票面价
     */
    val netPrice: String = "",
    /**
     * 乘客类型 0:未知 1:成人 2:儿童 3:婴儿
     */
    val passengerType: Int = 0,
    /**
     * 销售价
     */
    val price: String = "",
    /**
     * 税费
     */
    val tax: String = "",
    /**
     * 税费明细（基建+燃油） yq:燃油 tax-基建
     */
    val taxDetail: TCOrderDetailQueryTaxDetail = TCOrderDetailQueryTaxDetail(),
) : Serializable

data class TCOrderDetailQueryTaxDetail(
    val yq: String = "",
    val tax: String = "",
) : Serializable

data class TCOrderDetailQuerySegment(
    /**
     * 航司
     */
    val airline: String = "",
    /**
     * 抵达机场三字码
     */
    val arrAirport: String = "",
    /**
     * 到达城市三字码
     */
    val arrCity: String = "",
    /**
     * 抵达航站楼
     */
    val arrTerminal: String = "",
    /**
     * 抵达时间
     */
    val arrTime: String = "",
    /**
     * 舱位等级
     */
    val cabinClassCode: String = "",
    /**
     * 舱位代码
     */
    val cabinCode: String = "",
    /**
     * 出发机场三字码
     */
    val depAirport: String = "",
    /**
     * 出发城市三字码
     */
    val depCity: String = "",
    /**
     * 出发航站楼
     */
    val depTerminal: String = "",
    /**
     * 起飞时间
     */
    val depTime: String = "",
    /**
     * 航班号
     */
    val flightNo: String = "",
    /**
     * 共享航班标识 false 非共享 true共享
     */
    val flightShare: Boolean = false,
    /**
     * 实际承运航司
     */
    val operatingAirline: String = "",
    /**
     * 实际承运航班号
     */
    val operatingFlightNo: String = "",
    /**
     * 航段类型
     */
    val segmentType: Int = 0,
    /**
     * 航段序号
     */
    val sequence: Int = 0,
) : Serializable

data class TCOrderDetailQueryTicket(
    /**
     * 乘客名
     */
    val firstName: String = "",
    /**
     * 乘客姓
     */
    val lastName: String = "",
    /**
     * 编码
     */
    val pnr: String = "",
    /**
     * 客票号
     */
    val ticketNo: String = "",
    /**
     * 出票时间
     */
    val ticketTime: String = "",
) : Serializable

/**
 * 取消出票
 */
data class TCTicketCancelResponse(
    val message: String = "",
    val orderSerialNo: String = "",
    val success: Boolean = false,
    val traceId: String = "",
) : Serializable

/**
 * 出票确认
 */
data class TCTicketConfirmResponse(
    val message: String = "",
    val orderSerialNo: String = "",
    val success: Boolean = false,
    val traceId: String = "",
) : Serializable

/**
 * 同程-订单取消接口response
 */
data class TCOrderCancelResponse(
    val message: String = "",
    val orderSerialNo: String = "",
    val success: Boolean = false,
    val traceId: String = "",
) : Serializable

/**
 * 同程-生单接口response
 */
data class TCOrderBusinessResponse(
    val message: String = "",
    /**
     * 订单号(大订单号) : Serializable
     */
    val orderSerialNo: String = "",
    /**
     * 产品明细信息
     */
    val productItems: List<TCOrderProductItem> = emptyList(),
    val success: Boolean = false,
    /**
     * 下单价格
     */
    val totalPrice: String = "",
    val traceId: String = "",
) : Serializable

data class TCOrderProductItem(
    /**
     * 报错时才有
     */
    val message: String = "",
    /**
     * 报错时才有
     */
    val code: String = "",
    /**
     * 总价
     */
    val productTotalPrice: String = "",
    /**
     * 各产品类型对应返回参数
     */
    val productDetail: String = "",
    /**
     * 分类type 0 机票
     */
    val productType: String = "",
    /**
     * true 创建成功 false 创建失败
     */
    val state: Boolean = false,
    val unitKey: String = "",
    /**
     * 总价
     */
    val prdoductTotalPrice: String = "0.0",
) : Serializable

/**
 * 改签列表查询
 */
data class TCEndorseListQueryResponse(
    val data: List<String> = emptyList(),
    val code: String = "",
    val message: String = "",
    val success: Boolean = false,
    val traceId: String = "",
) : Serializable


/**
 * 精确询价
 */
data class TCAccurateSearchResponse(
    val flightUnits: List<TCAccurateSearchItem> = emptyList()
)

data class TCAccurateSearchItem(
    val baggageInfo: TCAccurateSearchItemBaggageInfo = TCAccurateSearchItemBaggageInfo(),
    val channel: Int = 0,
    val data: String = "",
    val invoiceType: Int = 0,
    val invoiceWay: Int = 0,
    val issueTicketInfo: TCAccurateSearchItemIssueTicketInfo = TCAccurateSearchItemIssueTicketInfo(),
    val limitInfo: TCAccurateSearchItemLimitInfo = TCAccurateSearchItemLimitInfo(),
    val priceType: Int = 0,
    val prices: Map<String, TCPriceInfo> = emptyMap(),
    val pricesV2: Map<String, String> = emptyMap(),
    val productCode: Int = 0,
    val productTag: String = "",
    val recommend: String = "",
    val refundChangeInfo: TCAccurateSearchItemRefundChangeInfo = TCAccurateSearchItemRefundChangeInfo(),
    val segments: List<TCSegmentItem> = emptyList(),
    val serviceTime: TCAccurateSearchItemServiceTime = TCAccurateSearchItemServiceTime(),
)

data class TCAccurateSearchItemBaggageInfo(
    val baggageText: String = "",
    val carryOnBaggage: List<TCAccurateSearchItemBaggage> = emptyList(),
    val checkedBaggage: List<TCAccurateSearchItemBaggage> = emptyList(),
)

data class TCAccurateSearchItemBaggage(
    val passengerType: String = "",
    val rules: List<TCAccurateSearchItemBaggageRule> = emptyList(),
)

data class TCAccurateSearchItemBaggageRule(
    val freePieces: String = "",
    val segmentIndex: Int = 0,
    val volume: String = "",
    val weight: String = "",
    val weightType: Int = 0,
)

data class TCAccurateSearchItemIssueTicketInfo(
    val autoTicket: Int = 0,
    val changePnrCode: Int = 1,
    val issueTicketCarrier: String = "",
    val reservationType: String = "",
)

data class TCAccurateSearchItemLimitInfo(
    val agePairs: List<AgeLimitItem> = emptyList(),
    val credentialType: String? = "",
    val groupQualification: String = "",
    @JSONField(name = "idcardPrefix")
    val idCardPrefix: String? = "",
    val maxPersonCount: Int = 0,
    val minPersonCount: Int = 1,
    val nationalityAllows: List<String> = emptyList(),
    val nationalityForbids: List<String> = emptyList(),
    val needCertificateOrder: Int = 0,
    val netPriceSource: Int = 0,
    val obsoleteType: Int = 0,
    val passengerQualification: String = "",
    val standardRule: Int = 0,
    val ticketByOperateCarrier: Int = 0,
    val validatePrice: Int = 0,
)

data class TCAccurateSearchItemRefundChangeInfo(
    val changeText: String = "",
    val refundChangeRules: List<TCAccurateSearchResponseRefundChangeRule> = emptyList(),
    val refundText: String = "",
)

data class TCAccurateSearchResponseRefundChangeRule(
    val allowed: Int = 0,
    val currency: String = "",
    val endpoint: Int,
    val fee: Int = 0,
    val feeDesc: String = "",
    val includeEndPoint: Boolean = false,
    val includeStartPoint: Boolean = false,
    val passengerType: String = "",
    val percent: Int = 0,
    val ruleType: Int = 0,
    val segLimit: Int = 0,
    val segmentIndex: Int = 0,
    val startPoint: Int = 0,
    val timeRange: String = "",
    val usedType: Int = 0,
)

data class TCAccurateSearchItemServiceTime(
    val refundAndChange: String = "",
    val ticket: String = "",
)
