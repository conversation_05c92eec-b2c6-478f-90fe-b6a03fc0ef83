package com.somytrip.model.flight.enums

/**
 * @Description:
 * @author: pigeon
 * @created: 2025-06-16 14:14
 */
enum class I18n(
    private val cn: String,
    private val tw: String,
    private val en: String,
) {
    AIRLINE_POLICY(
        "按航司客规。",
        "按航司客規。",
        "According to the airline's passenger regulations."
    ),
    NOT_FREE_CARRY_LUGGAGE(
        "无免费手提行李额。",
        "無免費手提行李額。",
        "There is no free carry-on allowance."
    ),
    NOT_FREE_CHECKED_LUGGAGE(
        "无免费托运行李额。",
        "無免費托運行李額。",
        "There is no free checked baggage allowance."
    ),
    FREE_CARRY_LUGGAGE(
        "免费手提行李额: 每件{}，每人{}件。单件行李质量不超过{}。",
        "免費手提行李額: 每件{}，每人{}件。單件行李質量不超過{}。",
        "Free carry-on luggage allowance: {} per piece, {} per person." +
                " The mass of a single piece of luggage shall not exceed {}."
    ),
    FREE_CHECKED_LUGGAGE(
        "免费托运行李额: 每件{}，每人{}件。单件行李质量不超过{}。",
        "免費托運行李額: 每件{}，每人{}件。單件行李質量不超過{}。",
        "Free checked baggage allowance: {} per piece, {} per person. The mass of a single piece of luggage shall not exceed {}."
    ),
    CAN_NOT_REFUND_CHANGE(
        "不可退改",
        "不可退改",
        "Non-returnable and non-modifiable"
    ),
    CHANGE(
        "改签",
        "改簽",
        "Change the ticket",
    ),
    REFUND(
        "退票",
        "退票",
        "Refund the ticket",
    ),
    CAN_NOT_CHANGE(
        "不允许改签",
        "不允許改簽",
        "No ticket changes are allowed"
    ),
    ALL_TIME_RANGE(
        "所有时段",
        "所有時段",
        "All time periods",
    ),
    CAN_NOT_REFUND(
        "不允许退票",
        "不允許退票",
        "Ticket refunds are not allowed"
    ),
    DATE_TIME_FORMAT_BEFORE(
        "{}年{}月{}日{}前",
        "{}年{}月{}日{}前",
        "Before {}/{}/{} {}",
    ),
    DATE_TIME_FORMAT_AFTER(
        "{}年{}月{}日{}后",
        "{}年{}月{}日{}后",
        "After {}/{}/{} {}",
    ),
    SEGMENT(
        "航段",
        "航段",
        "Seg",
    ),
    CARRY_BAG(
        "手提行李",
        "手提行李",
        "Carry-on luggage",
    ),
    CHECK_BAG(
        "托运行李",
        "托運行李",
        "check-in baggage",
    ),
    ADULT(
        "成人",
        "成人",
        "Adult",
    ),
    CHILD(
        "儿童",
        "兒童",
        "Children",
    ),
    INF(
        "婴儿",
        "嬰兒",
        "Baby",
    ),
    ;

    fun cn(): String {
        return cn
    }

    fun tw(): String {
        return tw
    }

    fun en(): String {
        return en
    }
}