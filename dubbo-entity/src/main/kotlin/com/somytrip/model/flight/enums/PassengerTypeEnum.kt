package com.somytrip.model.flight.enums

import cn.hutool.core.date.DateUtil
import com.somytrip.model.flight.common.CommonValFunc

/**
 * @Description: 乘客类型
 * @author: pigeon
 * @created: 2024-01-05 11:24
 */
enum class PassengerTypeEnum(
    private val tcValue: String,
    private val tcIntValue: Int,
    private val display: String,
    private val fr24Value: String = "",
    private val zfValue: String,
) {
    ADULT(CommonValFunc.ADT, 1, "成人", CommonValFunc.ADT, "adult"),
    CHILD(CommonValFunc.CHD, 2, "儿童", CommonValFunc.CHD, "child"),
    INFANT(CommonValFunc.INF, 3, "婴儿", CommonValFunc.INF, "infant");

    fun tcIntValue() = tcIntValue
    fun tcValue() = tcValue
    fun display() = display
    fun fr24Value() = fr24Value
    fun zfValue() = zfValue

    companion object {
        fun birthDayToEnum(birthDay: String): PassengerTypeEnum {
            if (birthDay.isBlank()) {
                return ADULT
            }
            val age = DateUtil.ageOfNow(birthDay)
            return when (age) {
                in 0 until 2 -> {
                    INFANT
                }

                in 2 until 16 -> {
                    CHILD
                }

                else -> {
                    ADULT
                }
            }
        }

        fun birthDayToEnum(age: Int): PassengerTypeEnum {
            return when (age) {
                in 0 until 2 -> {
                    INFANT
                }

                in 2 until 16 -> {
                    CHILD
                }

                else -> {
                    ADULT
                }
            }
        }

        fun zfValueToEnum(zfValue: String): PassengerTypeEnum {
            return entries.find { it.zfValue == zfValue }
                ?: ADULT
        }

        fun tcValueToEnums(value: String): PassengerTypeEnum {
            return entries.find { it.tcValue == value }
                ?: ADULT
        }

        fun fr24ValueToEnum(fr24Value: String): PassengerTypeEnum {
            return entries.find { it.fr24Value == fr24Value }
                ?: ADULT
        }

        fun tcValueToEnums(value: Int): PassengerTypeEnum {
            return entries.find { it.tcIntValue == value }
                ?: ADULT
        }
    }
}
