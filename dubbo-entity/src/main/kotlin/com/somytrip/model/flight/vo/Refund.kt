package com.somytrip.model.flight.vo

import com.somytrip.model.flight.enums.ProviderSourceEnum
import com.somytrip.model.flight.enums.RefundReasonEnum
import com.somytrip.model.flight.enums.RefundTypeEnum

/**
 * @Description: 退款
 * @author: pigeon
 * @created: 2024-08-31 15:10
 */


data class ApplyRefundInfoVo(
    val orderNo: String = "",
    val remark: String = "",
    val refundReason: RefundReasonEnum = RefundReasonEnum.VOLUNTARY,
    val filePaths: List<String> = emptyList(),
)

data class ApplyRefundVo(
    val ticketNos: List<String> = emptyList(),
    val orderNo: String = "",
    val type: RefundTypeEnum = RefundTypeEnum.VOLUNTARY,
    val remark: String = "",
    val fileIds: List<String> = emptyList(),
    val reason: RefundReasonEnum = RefundReasonEnum.VOLUNTARY,
)

data class ApplyRefundResultVo(
    val success: Boolean = false,
    val data: String = "",
)

data class QueryRefundFeeVo(
    val ticketNos: List<String> = emptyList(),
    val orderNo: String = "",
    val provider: ProviderSourceEnum = ProviderSourceEnum.DEFAULT,
    val type: RefundTypeEnum = RefundTypeEnum.VOLUNTARY
)

data class RefundFeeResultVo(
    val refundFee: String,
)

data class QueryRefundFeeResultVo(
    val payPrice: String = "",
    val subitems: List<RefundFeeResultVo> = emptyList(),
    val serviceFee: String = "",
)

data class FileVo(
    val file: ByteArray,
    val fileName: String = "",
)

data class UploadFilesVo(
    val files: List<FileVo>,
    val orderNo: String = "",
    /**
     * 1 退票
     * 2 改签
     */
    val type: Int = 1,
)