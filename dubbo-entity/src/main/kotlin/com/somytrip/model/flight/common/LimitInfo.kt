package com.somytrip.model.flight.common

import com.somytrip.model.flight.tc.TCAccurateSearchItemLimitInfo

/**
 * @Description: 限制信息
 * @author: pigeon
 * @created: 2024-08-22 14:55
 */
data class LimitInfo(
    /**
     * 最小购票人数
     */
    val minPersonCount: Int = 0,
    /**
     * 最大购票人数
     */
    val maxPersonCount: Int = 0,
    /**
     * 年龄限制
     */
    val ages: List<AgeLimitItem> = emptyList(),
    /**
     * 允许的国籍列表 为空代表所有
     */
    val allowNations: List<String> = emptyList(),
    /**
     * 不允许的国籍列表 为空代表没有
     */
    val forbidNations: List<String> = emptyList(),
) {
    constructor(info: TCAccurateSearchItemLimitInfo) : this(
        minPersonCount = info.minPersonCount,
        maxPersonCount = info.maxPersonCount,
        ages = info.agePairs,
        allowNations = info.nationalityAllows,
        forbidNations = info.nationalityForbids,
    )
}
