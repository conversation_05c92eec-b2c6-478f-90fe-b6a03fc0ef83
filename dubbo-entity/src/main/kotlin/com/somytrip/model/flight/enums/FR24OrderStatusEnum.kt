package com.somytrip.model.flight.enums


/**
 * @Description: 航路24订单状态
 * @author: pigeon
 * @created: 2024-08-23 16:14
 */
enum class FR24OrderStatusEnum(val code: Int, val description: String) {
    // 生单成功
    CREATED(11, "生单成功"),

    // 出票中
    TICKETING(12, "出票中"),

    // 出票完成
    TICKETED(13, "出票完成"),

    // 等待确认
    AWAITING_CONFIRMATION(14, "等待确认"),

    // 订单已取消
    CANCELLED(15, "订单已取消"),

    // 支付成功
    PAYMENT_SUCCESSFUL(67, "支付成功"),

    // 改期
    RESCHEDULE_AWAITING_REVIEW(47, "改期待审核"),
    RESCHEDULE_AWAITING_PAYMENT(48, "改期待付款"),
    RESCHEDULE_PAYMENT_COMPLETED(49, "改期付款完成待处理"),
    RESCHEDULE_FAILED(50, "改期失败"),
    RESCHEDULE_REJECTED(51, "改期驳回"),
    RESCHEDULE_AWAITING_REFUND(52, "改期待退款"),
    REFUND_IN_PROGRESS(53, "退款中"),
    REFUND_COMPLETED(54, "退款完成"),
    RESCHEDULE_COMPLETED(61, "改期完成"),

    // 退票
    REFUND_AWAITING_PRICE_VERIFICATION(72, "待核价"),
    PRICE_VERIFICATION_COMPLETED(73, "核价完成"),
    REFUND_AWAITING_REVIEW(25, "退票待审核"),
    REFUND_TEMPORARILY_UNAVAILABLE(26, "暂不能退票"),
    REFUND_IN_PROCESS(27, "已审核正退票"),
    REFUND_AWAITING_REFUND(29, "已退票待退款"),
    REFUND_SUCCESSFUL(30, "退款完成"),

    // 废票
    VOID_AWAITING_REVIEW(36, "废票待审核"),
    VOID_REJECTED(37, "废票驳回"),
    VOID_COMPLETED(38, "废票完成"),
    ;

    companion object {
        fun codeToEnum(code: Int): FR24OrderStatusEnum {
            return entries.find { it.code == code } ?: CANCELLED
        }
    }
}