package com.somytrip.model.flight.common

import com.somytrip.model.flight.tc.TCAccurateSearchItemServiceTime

/**
 * @Description: 服务时间
 * @author: pigeon
 * @created: 2024-08-20 17:46
 */
data class ServiceTime(
    val refundChange: List<ServiceTimeItem> = emptyList(),
    val ticket: List<ServiceTimeItem> = emptyList(),
) {
    constructor(time: TCAccurateSearchItemServiceTime) : this(
        refundChange = time.refundAndChange.split(CommonValFunc.TC_SERVICE_TIME_SPLIT_1)
            .map { ServiceTimeItem(it.split(CommonValFunc.TC_SERVICE_TIME_SPLIT_2)) },
        ticket = time.ticket.split(CommonValFunc.TC_SERVICE_TIME_SPLIT_1)
            .map { ServiceTimeItem(it.split(CommonValFunc.TC_SERVICE_TIME_SPLIT_2)) }
    )
}
