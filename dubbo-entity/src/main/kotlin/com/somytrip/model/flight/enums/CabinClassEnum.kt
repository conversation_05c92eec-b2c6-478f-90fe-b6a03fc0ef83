package com.somytrip.model.flight.enums


/**
 * @Description:
 * @author: pigeon
 * @created: 2024-02-27 17:34
 */
enum class CabinClassEnum(
    val tcValue: String,
    val fr24Value: String,
    val display: String,
    val zfValue: String,
) {
    FIRST_CLASS("F", "F", "头等舱", "first"),
    BUSINESS_CLASS("C", "C", "商务舱", "business"),
    ECONOMY_CLASS("Y,W", "Y", "经济舱", "economy"),
    PREMIUM_CLASS("S", "P", "超级经济舱", "premium_economy")
    ;

    fun zfValue() = zfValue
    fun display() = display
    fun fr24Value() = fr24Value
    fun tcValue() = tcValue

    fun getValue() = this.display;

    companion object {
        fun zfValueToEnums(value: String): CabinClassEnum {
            return entries.find { it.zfValue == value } ?: ECONOMY_CLASS
        }

        fun tcValueToEnums(value: String): CabinClassEnum {
            for (item in entries) {
                if (item.tcValue.contains(value)) {
                    return item;
                }
            }
            println("CabinClassEnum init error [${value}]")
            return ECONOMY_CLASS
        }

        fun fr24ToEnums(value: String): CabinClassEnum {
            return entries.find { it.fr24Value == value } ?: ECONOMY_CLASS
        }
    }
}