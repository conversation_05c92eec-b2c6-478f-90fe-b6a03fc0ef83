package com.somytrip.model.flight.enums

/**
 * @Description: 智飞科技订单状态枚举
 * @author: pigeon
 * @created: 2025-01-12 14:36
 */
enum class ZfOrderStatus(
    private val value: String
) {
    REVIEWING("reviewing"),
    TO_BE_PAID("to_be_paid"),
    RESERVED("reserved"),
    PROCEEDING("proceeding"),
    ISSUED("issued"),
    CHG_REVIEWING("chg_reviewing"),
    CHG_TO_BE_PAID("chg_to_be_paid"),
    CHG_PROCEEDING("chg_proceeding"),
    CHG_REJECTED("chg_rejected"),
    CHANGED("changed"),
    RFD_REVIEWING("rfd_reviewing"),
    RFD_TO_BE_CONFIRM("rfd_to_be_confirm"),
    RFD_PROCEEDING("rfd_proceeding"),
    RFD_REJECTED("rfd_rejected"),
    REFUNDED_REIMBURSING("refunded_reimbursing"),
    REFUNDED_REIMBURSED("refunded_reimbursed"),
    VOID_PROCEEDING("void_proceeding"),
    VOIDED_REIMBURSING("voided_reimbursing"),
    VOIDED_REIMBURSED("voided_reimbursed"),
    VOIDED_REJECTED("void_rejected"),
    FAILED("failed"),
    FAILED_REIMBURSING("failed_reimbursing"),
    FAILED_REIMBURSED("failed_reimbursed"),
    CLOSED("closed"),
    CANCELED("canceled"),
    HOLDING("holding"),
    ;

    fun value() = value

    fun eq(o: String): Boolean {
        return this.value == o
    }
}