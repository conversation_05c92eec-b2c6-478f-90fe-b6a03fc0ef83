package com.somytrip.model.flight.enums


/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-12 10:21
 */
enum class RefundChangeRuleTypeEnum(
    val tcValue: Int,
    val display: String
) {
    REFUND(1, "退票"),
    CHANGE(2, "改期"),
    UNKNOWN(-1, "未知"),
    ;

    companion object {
        fun tcValueToEnums(value: Int): RefundChangeRuleTypeEnum {
            return entries.find { it.tcValue == value }
                ?: UNKNOWN
        }
    }
}