package com.somytrip.model.flight.enums

import com.baomidou.mybatisplus.annotation.EnumValue

/**
 * @Description: 智飞订单类型
 * @author: pigeon
 * @created: 2025-01-15 10:52
 */
enum class ZfOrderType(
    @EnumValue
    val code: Int = 0,
    val value: String = ""
) {
    // origin
    //change
    //refund
    //void
    /**
     * 原订单
     */
    ORIGIN(0, "origin"),
    CHANGE(1, "change"),
    REFUND(2, "refund"),
    VOID(3, "void"),
    ;
}