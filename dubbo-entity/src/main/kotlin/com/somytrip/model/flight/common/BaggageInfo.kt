package com.somytrip.model.flight.common

import com.somytrip.model.flight.enums.BaggageInfoEnum
import com.somytrip.model.flight.tc.TCAccurateSearchItemBaggageInfo

/**
 * @Description: 行李相关信息
 * @author: pigeon
 * @created: 2024-08-20 17:49
 */
data class BaggageInfo(
    val description: String = "",
    val rules: List<BaggageInfoItem> = emptyList(),
) {
    constructor() : this("")
    constructor(info: TCAccurateSearchItemBaggageInfo) : this(
        description = info.baggageText,
        rules = (info.carryOnBaggage.map { BaggageInfoItem(it, BaggageInfoEnum.HAND) }
                + info.checkedBaggage.map { BaggageInfoItem(it, BaggageInfoEnum.CHECKED) })
    )
}
