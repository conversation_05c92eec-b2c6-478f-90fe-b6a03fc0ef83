package com.somytrip.model.flight.enums


/**
 * @Description:
 * @author: pigeon
 * @created: 2024-08-20 17:54
 */
enum class GdsEnum(
    val tcValue: String,
    val fr24Value: String,
) {
    TravelSky("1E", ""),
    Amadeus("1A", ""),
    Abacus("1B", ""),
    Sabre("1S", ""),
    WorldSpan("1P", ""),
    Galileo("1G", ""),

    /**
     * GDS
     */
    GDS("", "GDS"),

    /**
     * 航司官网（包含APP）
     */
    WEB("", "GDS"),

    /**
     * 代理账号或航司API
     */
    AGT("", "AGT"),

    /**
     * 航司NDC
     */
    NDC("", "NDC"),

    /**
     * 未知
     */
    OTH("", "OTH"),
    Unknown("00", "");

    companion object {
        fun tcToEnum(value: String): GdsEnum {
            return entries.find { it.tcValue == value } ?: Unknown
        }

        fun fr24ToEnum(value: String): GdsEnum {
            return entries.find { it.fr24Value == value } ?: OTH
        }
    }
}