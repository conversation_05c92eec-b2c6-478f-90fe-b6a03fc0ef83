package com.somytrip.model.flight.common

import com.somytrip.model.flight.enums.PassengerTypeEnum
import com.somytrip.model.flight.enums.PassengerTypeEnum.Companion.tcValueToEnums
import com.somytrip.model.flight.enums.RefundChangeRuleTypeEnum
import com.somytrip.model.flight.tc.TCAccurateSearchResponseRefundChangeRule

/**
 * @Description: 退改信息规则Item
 * @author: pigeon
 * @created: 2024-08-22 11:42
 */
data class ReverseInfoItem(
    val currency: String = "CNY",
    val endpoint: Int = 0,
    val startPoint: Int = 0,
    val includeEndPoint: Boolean = false,
    val includeStartPoint: Boolean = false,
    val passengerType: PassengerTypeEnum = PassengerTypeEnum.ADULT,
    val ruleType: RefundChangeRuleTypeEnum = RefundChangeRuleTypeEnum.UNKNOWN,
    val fee: Int = 0,
    val percent: Int = 0,
    val feeDesc: String = "",
    val segmentIndex: Int = 0,
    val timeRange: String = "",
) {
    constructor(item: TCAccurateSearchResponseRefundChangeRule) : this(
        currency = item.currency,
        endpoint = item.endpoint,
        fee = item.fee,
        feeDesc = item.feeDesc,
        includeEndPoint = item.includeEndPoint,
        includeStartPoint = item.includeStartPoint,
        passengerType = tcValueToEnums(item.passengerType),
        percent = item.percent,
        ruleType = RefundChangeRuleTypeEnum.tcValueToEnums(item.ruleType),
        segmentIndex = item.segmentIndex,
        startPoint = item.startPoint,
        timeRange = item.timeRange,
    )

}
