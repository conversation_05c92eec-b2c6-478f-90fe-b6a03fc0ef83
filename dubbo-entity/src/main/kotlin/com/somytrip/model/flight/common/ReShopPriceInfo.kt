package com.somytrip.model.flight.common

import com.somytrip.model.flight.enums.CabinClassEnum
import com.somytrip.model.flight.enums.PassengerTypeEnum

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-10-31 9:37
 */
data class ReShopPriceInfo(
    /**
     * 手续费
     */
    val handlingFee: String = "0",
    /**
     * 税费差价
     */
    val taxDiff: String = "0",
    /**
     * 票价差价
     */
    val ticketDiff: String = "0",
    /**
     * 服务费
     */
    val serviceFee: String = "0",
    /**
     * 税费详情
     */
    val taxDetail: TaxDetail = TaxDetail(),
    /**
     * 乘客类型
     */
    val passengerType: PassengerTypeEnum = PassengerTypeEnum.ADULT,
    /**
     * 仓等
     */
    val cabin: CabinClassEnum = CabinClassEnum.ECONOMY_CLASS,
    /**
     * 舱位code
     */
    val cabinCode: String = "",
    /**
     * 票面价
     */
    val price: String = "0",
    val taxTotal: String = "0",

    )
