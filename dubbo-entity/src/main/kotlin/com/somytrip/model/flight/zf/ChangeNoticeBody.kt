package com.somytrip.model.flight.zf

/**
 * @Description: 智飞航变通知
 * @author: pigeon
 * @created: 2025-03-18 10:03
 */
data class ChangeNoticeBody(
    val title: String = "",
    /**
     *  change 航班变更
     * cancel_no_save  航班取消（无保护航班，航班号发生改变）
     * cancel_save   // 航班取消（保护航班，航班号发生改变）
     * restore       // 航班恢复
     */
    val type: String = "",
    val notifyData: String = "",
    val orderId: String = "",
    val originOrderId: String = "",
    val pnr: String = "",
    val flightType: String = "",
    val flightTypeTitle: String = "",
    val originFlight: SegmentInfo = SegmentInfo(),
    val privateFlight: SegmentInfo? = null,
)