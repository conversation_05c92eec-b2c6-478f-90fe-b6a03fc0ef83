package com.somytrip.model.flight.enums


/**
 * @Description: 产品枚举
 * @author: pigeon
 * @created: 2024-03-12 15:02
 */
enum class ProductTagEnum(
    val tcValue: Int,
    val display: String
) {
    NORMAL_PRODUCT(1, "普通产品（快速出票，标准退改签）"),
    PREFERRED_PRODUCT(2, "优选产品"),
    DISCOUNT_PRODUCT(3, "特惠产品（保障出行）"),
    OFFICIAL_PRODUCT(4, "官网产品（官网直营，标准退改）"),
    SPECIAL_PRODUCT(5, "特殊产品（个性产品，保障出行）"),
    SENIOR_DISCOUNT(6, "青老年特惠"),
    GROUP_DISCOUNT(7, "多人特惠"),
    BUSINESS_STANDARD(8, "商旅标品"),
    DISCOUNT_O24(9, "特惠O24（进单24小时出票）"),
    SELF_OPERATED_PRODUCT(10, "自营产品（快速出票，标准退改）"),
    PRODUCT_11(11, "11"),
    ORDINARY_BOUTIQUE(12, "普通-精品"),
    DISCOUNT_UNLIMITED(13, "特惠不限"),
    PRODUCT_1001(1001, "普通产品（快速出票，标准退改签）"),
    PRODUCT_1002(1002, "优选产品"),
    PRODUCT_1003(1003, "特惠产品（保障出行）"),
    PRODUCT_1004(1004, "特殊产品（个性产品，保障出行）"),
    PRODUCT_1005(1005, "官网产品（官网直营，标准退改）"),
    PRODUCT_1006(1006, "自营产品（快速出票，标准退改）"),
    PRODUCT_21(21, "特惠1h"),
    FR24_PRODUCT(0, "航路24"),
    TC_RE_SHOP_PRODUCT(0, "同程改签"),
    DEFAULT(-1, "默认"),
    ZF_PRODUCT(0, "智飞科技")
    ;

    companion object {
        fun tcValueToEnums(value: String): ProductTagEnum {
            return entries.firstOrNull { it.tcValue == value.toInt() }
                ?: throw RuntimeException("$value is not found")
        }
    }
}