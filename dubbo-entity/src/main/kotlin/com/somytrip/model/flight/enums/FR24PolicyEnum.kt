package com.somytrip.model.flight.enums

/**
 * @Description: FR24 退改政策
 * @author: pigeon
 * @created: 2024-08-20 9:17
 */
enum class FR24PolicyEnum(
    private val refundDisplay: String,
    private val changeDisplay: String,
) {
    notAllowed("不可退", "不可改"),
    withCondition("有条件退", "有条件改"),
    withNoPenalty("免费退", "免费改"),
    taxRefundOnly("仅可退部分税", ""),
    airlinePolicyApplied("按航司客规", "按航司客规"),
    verified("需核实", "需核实"),
    ;

    fun refundDisplay() = refundDisplay;
    fun changeDisplay() = changeDisplay;
}