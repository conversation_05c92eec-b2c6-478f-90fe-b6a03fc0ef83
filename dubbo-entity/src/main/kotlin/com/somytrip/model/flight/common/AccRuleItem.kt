package com.somytrip.model.flight.common

/**
 * @Description: 退改规则Item
 * @author: pigeon
 * @created: 2024-08-20 17:40
 */
data class AccRuleItem(
    /**
     * 价格详情
     */
    val prices: List<PriceItem> = emptyList(),
    /**
     * 行程详情
     */
    val segments: List<SegmentItem> = emptyList(),
    /**
     * 商品详情
     */
    val productInfo: ProductInfo = ProductInfo(),
    /**
     * 退改详情
     */
    val refundChangeInfo: ReverseInformation = ReverseInformation(),
    /**
     * 服务时间详情
     */
    val serviceTime: ServiceTime = ServiceTime(),
    /**
     * 行李详情
     */
    val baggageInfo: BaggageInfo = BaggageInfo(),
    /**
     * 票务详情
     */
    val issueTicketInfo: IssueTicketInfo = IssueTicketInfo(),
    /**
     * 限制详情
     */
    val limitInfo: LimitInfo = LimitInfo(),
) {
//    constructor(item: TCAccurateSearchItem, func: (x: Double) -> Int) : this(
//        baggageInfo = BaggageInfo(item.baggageInfo),
//        issueTicketInfo = IssueTicketInfo(item.issueTicketInfo, item.invoiceWay, item.invoiceType),
//        limitInfo = LimitInfo(item.limitInfo),
//        prices = item.prices.values.map { PriceItem(it, func) },
//        productInfo = ProductInfo(
//            item.productCode.toString(),
//            ProductTagEnum.tcValueToEnums(item.productTag),
//            ProviderSourceEnum.TC,
//            item.data
//        ),
//        refundChangeInfo = ReverseInformation(item.refundChangeInfo),
//        serviceTime = ServiceTime(item.serviceTime),
//        segments = item.segments.map { SegmentItem(it) }
//    )
}