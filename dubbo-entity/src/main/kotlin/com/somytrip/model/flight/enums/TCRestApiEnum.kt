package com.somytrip.model.flight.enums

/**
 * @Description: 同程api code enum
 * @author: pigeon
 * @created: 2024-01-25 11:08
 */
enum class TCRestApiEnum(
    var value: String,
) {
    /**
     * 精确行程报价接口
     */
    FLIGHT_SHOPPING_SEARCH_API("flight.shopping.search"),

    /**
     * 客规查询接口
     */
    FLIGHT_GUEST_RULE_API("flight.guest.rule"),

    /**
     * 精确询价接口
     */
    FLIGHT_ACCURATE_SEARCH_API("flight.accurate.search"),

    /**
     * 生单接口
     */
    ORDER_API("order"),

    /**
     * 订单取消接口
     */
    ORDER_CANCEL_API("order.cancel"),

    /**
     * 支付校验接口
     */
    PAY_CHECK_API("pay.check"),

    /**
     * 支付接口
     */
    PAY_API("pay"),

    /**
     * 支付通知接口
     */
    PAY_NOTIFY("PayResult"),

    /**
     * 出票通知
     */
    TICKET_NOTIFY("TicketResult"),

    /**
     * 取消出票
     */
    TICKET_CANCEL("ticket.cancel"),

    /**
     * 确认出票
     */
    TICKET_CONFIRM("ticket"),

    /**
     * 订单列表查询接口
     */
    ORDER_LIST_QUERY("order.list.query"),

    /**
     * 订单详情查询
     */
    ORDER_DETAIL_QUERY("order.detail.query"),

    /**
     * 可退查询接口
     */
    REFUNDABLE_API("refundable"),

    /**
     * 退票申请
     */
    REFUND_APPLY_API("refund.apply"),

    /**
     * 退票通知
     */
    REFUND_NOTIFY("RefundResult"),

    /**
     * 附件上传
     */
    REFUND_FILE_UPLOAD("refund.file.upload"),

    /**
     * 退票报价结果通知
     */
    REFUND_FEE_NOTICE_INTERNATIONAL("refundFeeNotify"),

//    /**
//     * 确认退票
//     */
//    CONFIRM_REFUND_INTERNATIONAL("confirm_refund"),

    /**
     * 退票列表
     */
    REFUND_LIST_QUERY("refund.list.query"),

    /**
     * 退票订单详情
     */
    REFUND_DETAIL2_QUERY("refund.detail2.query"),

    /**
     * 改签查询
     */
    ENDORSE_QUERY("endorse.query"),

    /**
     * 附件上传
     */
    ENDORSE_UPLOAD("endorse.upload"),

    /**
     * 改签申请
     */
    ENDORSE_APPLY("endorse.apply"),

    /**
     * 改签支付
     */
    ENDORSE_PAY("endorse.pay"),

    /**
     * 改签单取消
     */
    ENDORSE_CANCEL("endorse.cancel"),

    /**
     * 改签单确认通知
     */
    ENDORSE_CONFIRM("endorseConfirm"),

    /**
     * 改签结果通知
     */
    ENDORSE_RESULT_NOTIFY("EndorseResult"),

    /**
     * 改签列表
     */
    ENDORSE_LIST_QUERY("endorse.list.query"),

    /**
     * 改签详情接口
     */
    ENDORSE_DETAIL2_QUERY("endorse.detail2.query"),

    /**
     * 航变通知
     */
    CHANGE_NOTIFY("ChangeNotify"),
}