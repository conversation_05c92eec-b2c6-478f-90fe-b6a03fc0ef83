package com.somytrip.model.flight.common

/**
 * @Description: 退改信息
 * @author: pigeon
 * @created: 2024-08-22 11:41
 */
data class ReverseInformation(
    val changeDescription: String = "",
    val refundDescription: String = "",
    val rules: List<ReverseInfoItem> = emptyList(),
) {
//    constructor(info: TCAccurateSearchItemRefundChangeInfo) : this(
//        changeDescription = info.changeText,
//        refundDescription = info.refundText,
//        rules = info.refundChangeRules.map { ReverseInfoItem(it) }
//    )
}
