package com.somytrip.model.flight.zf

import com.alibaba.fastjson2.into
import com.alibaba.fastjson2.toJSONString

/**
 * @Description: 公用
 * @author: pigeon
 * @created: 2025-01-07 16:16
 */

data class Journey(
    /**
     * 出发时间 yyyy-MM-dd
     */
    val departureDate: String = "",
    /**
     * 到达机场码
     */
    val destination: String = "",
    /**
     * 出发机场码
     */
    val origin: String = "",
)

data class JourneyResult(
    /**
     * departure city IATA code
     */
    val origin: String = "",
    /**
     * arrival city IATA code
     */
    val destination: String = "",
    /**
     * departure date, formatted as YYYY-MM-DD
     */
    val departureDate: String = "",
    /**
     * departure time, formatted as hh:mm
     */
    val departureTime: String = "",
    /**
     * arrival date, formatted as YYYY-MM-DD
     */
    val arrivalDate: String = "",
    /**
     * arrival time, formatted as hh:mm
     */
    val arrivalTime: String = "",
    /**
     * duration of the journey, formatted as hh:mm
     */
    val duration: String = "",
    /**
     * segment numbers
     * 0 - unlimited
     * 1 - 1 segment only, or no-stop
     * 2 - 2 segments or less, 1-stop or no-stop
     * 3 - 3 segments or less, 2 stop at most
     */
    val transferNum: Int = 0,
    val segments: List<SegResult> = emptyList(),
)

data class SegResult(
    val coreSegmentId: String = "",
    val cabinClass: String = "",
    val cabinCode: String = "",
    val subCabinCode: String = "",
    /**
     * stay time at a airport
     */
    val stayTime: String = "",
    val availability: Int = 0,
    val baggageRules: List<BaggageRule> = emptyList(),
)

data class BaggageRule(
    val carryOn: Baggage = Baggage(),
    val checked: Baggage = Baggage(),
    val passengerType: String = "",
)

data class Baggage(
    /**
     * 0 for no frees baggage,
     * -1 for depend on airline rules,
     * other shows pieces of frees baggages
     */
    val pieces: Int = 0,
    /**
     * free baggage weight for 1 piece
     */
    val weight: Int = 0,
    /**
     * unit of weight, default to KG
     */
    val unit: String = "",
    val dimension: String = "",
    val description: String = "",
)

data class Passenger(
    val adult: Int = 1,
    val child: Int = 0,
    val infant: Int = 0,
)

data class Stop(
    /**
     * IATA airport code of the stopover airport
     */
    val airport: String = "",
    /**
     * time when the flight arrival at the airport
     */
    val arrivalAt: String = "",
    /**
     * IATA city code of the stopover city
     */
    val city: String = "",
    /**
     * time when the flight departure from the airport
     */
    val departureAt: String = "",
    /**
     * stop time
     */
    val duration: String = "",
)

data class Seg(
    val id: String = "",
    val airline: String = "",
    val flightNo: String = "",
    val isCodeShare: Boolean = false,
    val opAirline: String = "",
    val opFlightNo: String = "",
    val equipment: String = "",
    val departure: String = "",
    val departureDate: String = "",
    val departureTime: String = "",
    val departureTerminal: String = "",
    val arrival: String = "",
    val arrivalDate: String = "",
    val arrivalTime: String = "",
    val arrivalTerminal: String = "",
    val distance: Int = 0,
    val flightTime: String = "",
    val stopovers: List<Stop> = emptyList(),
)

data class GroupInfo(
    /**
     * max people of a group
     */
    val max: Int = 0,
    /**
     * min people of a group
     */
    val min: Int = 0,
)

data class LimitPair(
    val maxAge: Int = 0,
    val minAge: Int = 0,
)

data class LimitInfo(
    /**
     * allowed age pairs
     */
    val agePairs: List<LimitPair> = emptyList(),
    /**
     * allowed traval documents
     *
     * Allowed values:
     * passport
     * tphm
     * tptw
     * rphmt
     * idcard
     * fpidcard
     * eep
     * ttpmr
     * hhr
     */
    val allowedTravelDocuments: List<String> = emptyList(),
    /**
     * groupInfo
     */
    val groupInfo: GroupInfo = GroupInfo(),
    /**
     * allowed nationalities of passenger
     */
    val nationalityAllows: List<String> = emptyList(),
    /**
     * forbidden nationalities of passenger
     */
    val nationalityForbids: List<String> = emptyList(),
    /**
     * Allowed values:
     * normal
     * student
     * youth
     * labor
     * seaman
     */
    val passengerQualification: String = "",
)

data class PriceDetail(
    /**
     * IATA code of the airline which sales the ticket
     */
    val saleAirline: String = "",
    /**
     * max number of passengers
     */
    val maxCount: Int = 0,
    /**
     * IATA code of the airline which will issue the ticket
     */
    val issuingAirline: String = "",
    /**
     * invoce type
     *
     * Allowed values:
     * Itinerary
     * Bill
     * ItineraryWithBill
     * none
     */
    val invoiceType: String = "",
    /**
     * product type
     * 'fast-standard': Immediate Ticketing
     * 'preference-standard': Premium Service < 60 Mins
     * 'normal-standard': Rapid Ticketing: < 2 hrs
     * 'website': Immediate Ticketing < 30 Mins
     * 'special-standard': Ticketing Speed: <48 hrs
     * 'special-48': Secure Ticketing: <48 hrs
     * 'special-before-24': Secure Ticketing: 24 hrs Ahead
     * 'special-before-4': Secure Ticketing: 4 hrs Ahead
     */
    val productType: String = "",
    /**
     * limit rules
     */
    val limitInfo: LimitInfo = LimitInfo(),
    /**
     * 价格
     */
    val priceList: List<PriceItem> = emptyList(),
)

data class OriginPrice(
    val passengerType: String = "",
    val num: Int = 0,
    val publishPrice: String = "",
    val price: String = "",
    val tax: String = "",
    val salePrice: String = "",
)

data class ChangePrice(
    val passengerType: String = "",
    /**
     * passenger count
     */
    val num: Int = 0,
    /**
     * face price difference
     */
    val priceDiff: String = "",
    /**
     * tax difference
     */
    val taxDiff: String = "",
    /**
     * change fee
     */
    val changeFee: String = "",
    /**
     * service fee
     */
    val serviceFee: String = "",
    /**
     * total change price
     */
    val salePrice: String = "",
)

data class RefundPrice(
    val passengerType: String = "",
    /**
     * passenger count
     */
    val num: Int = 0,
    /**
     * refundable amount
     */
    val refundableAmount: String = "",
    /**
     * refund fee
     */
    val refundFee: String = "",
    /**
     * service fee
     */
    val serviceFee: String = "",
    /**
     * total refund amount
     */
    val refundPrice: String = "",
)


data class VoidPrice(
    val passengerType: String = "",
    /**
     * passenger count
     */
    val num: Int = 0,
    /**
     * ticket price
     */
    val orderPrice: String = "",
    /**
     * void service fee
     */
    val serviceFee: String = "",
    /**
     * total refund amount
     */
    val refundPrice: String = "",
)

data class NotifyPrice(
    val num: Int = 0,
    val tax: String = "",
    val price: String = "",
    val currency: String = "",
    val brandCode: String = "",
    val salePrice: String = "",
    val exchangeRate: String = "",
    val publishPrice: String = "",
    val fareBasisCode: String = "",
    val passengerType: String = "",
)

data class ExtraInfo(
    val changeReason: Int = 0,
    val fileList: List<String> = emptyList(),
    val reasonDetail: String = "",
    val refundReason: Int = 0,
)

data class OrderPriceDetail(
    val priceList: List<Any> = emptyList(),
    /**
     * Allowed values:
     * fast-standard
     * preference-standard
     * normal-standard
     * website
     * special-standard
     * special-48
     * special-before-24
     * special-before-4
     */
    val productType: String = "",
    val priceTotal: String = "",
    val transactionFee: String = "",
) {
    fun originPrices(): List<OriginPrice> {
        return this.priceList.map {
            it.toJSONString().into<OriginPrice>()
        }
    }

    fun changePrices(): List<ChangePrice> {
        return this.priceList.map {
            it.toJSONString().into<ChangePrice>()
        }
    }

    fun refundPrices(): List<RefundPrice> {
        return this.priceList.map {
            it.toJSONString().into<RefundPrice>()
        }
    }

    fun voidPrices(): List<VoidPrice> {
        return this.priceList.map {
            it.toJSONString().into<VoidPrice>()
        }
    }
}

data class TaxItem(
    val amount: String = "",
    /**
     * Allowed values:
     * fuelTax
     * airportTax
     * tax
     */
    val type: String = "",
)

data class PriceItem(
    /**
     * passenger type
     * Allowed values:
     * adult
     * child
     * infant
     */
    val passengerType: String = "",
    /**
     * fare basis
     */
    val fareBasisCode: String = "",
    /**
     * brand code
     */
    val brandCode: String = "",
    /**
     * publish price
     */
    val publishPrice: String = "",
    /**
     * face price
     */
    val price: String = "",
    /**
     * tax
     */
    val tax: String = "",
    /**
     * sale price
     */
    val salePrice: String = "",
    val taxDetail: List<TaxItem> = emptyList(),
    /**
     * passenger number
     */
    val num: Int = 0,
    val currency: String = "",
    val exchangeRate: Int = 0,
)

data class FareRule(
    val passengerType: String = "",
    val canVoid: Boolean = false,
    val currency: String = "",
    /**
     * change 和 refund
     *
     * key
     * *  代表所有
     * >n 如果n大于0，代表起飞前n小时前
     *      如果n小于0，代表起飞后n小时前
     * <n 如果n大于0，代表起飞前n小时后
     *      如果n小于0，代表起飞后n小时后
     *
     * value
     * 数字代表费用   -1代表不改签或不退款  0代表免费退改 大于0的数字就是退改费具体金额
     *
     * 如果change refund 为空，代表以航司为准
     */
    val change: Map<String, String> = emptyMap(),
    val changeDescription: String = "",
    val refund: Map<String, String> = emptyMap(),
    val refundDescription: String = "",
    val originText: String = "",
    val longestStay: String = "",
    val shortestStay: String = "",
)

data class Solution(
    /**
     * solution ID, used when validation
     */
    val solutionId: String = "",
    /**
     *
     */
    val issueWay: String = "",
    /**
     * supplier code
     */
    val source: String = "",
    /**
     * 下单Key
     */
    val orderKey: String = "",
    /**
     * price detail
     */
    val priceDetail: PriceDetail = PriceDetail(),
    /**
     * journey infomation
     */
    val journeys: List<JourneyResult> = emptyList(),
    /**
     * fareRules
     */
    val fareRules: List<FareRule> = emptyList(),
    /**
     * requiredPassengerInfos
     */
    val requiredPassengerInfos: List<String> = emptyList(),
)

data class OrderPassenger(
    /**
     * Allowed values:
     * adult
     * child
     * infant
     */
    val type: String = "",
    /**
     * surname or last name
     */
    val surname: String = "",
    /**
     * given names or First name.
     */
    val givenNames: String = "",
    /**
     * Date of birth [yyyy-mm-dd]
     */
    val birthday: String = "",
    /**
     * gender
     * Allowed values:
     * male
     * female
     */
    val gender: String = "",
    /**
     * Nationality [country code].
     */
    val nationality: String = "",
    /**
     * Type of travel document:
     * passport as passport
     * idcard as Chinese Identity
     * rphmt - Mainland Residents' Travel Permit to Hong Kong and Macau
     * tphm - Taiwan Compatriot Permit
     * eep - Hong Kong and Macao Travel Permit
     * tptw - Taiwan Compatriot Permit
     * ttpmr - Mainland Travel Permit for Taiwan Residents
     * fpridcard - Residence Permit for Foreigners
     *
     * Passengers could make reservations with Chinese Identity if the itinerary is China domestic journey (NOT including Hong Kong, Macao and Taiwan). If using Chinese Identity please input name in Chinese character.
     *
     * Allowed values:
     * passport
     * tphm
     * tptw
     * rphmt
     * idcard
     * fpidcard
     * eep
     * ttpmr
     * hhr
     */
    val travelDocument: String = "",
    /**
     * travelDocumentNumber
     */
    val travelDocumentNumber: String = "",
    /**
     * Travel document expired date. [yyyy-mm-dd]
     */
    val travelDocumentExpireDate: String = "",
    /**
     * phone region
     */
    val region: String = "",
    /**
     * phone number
     */
    val phone: String = "",
    /**
     * email
     */
    val email: String = "",
    /**
     * Frequent flyer card type
     */
    val airline: String = "",
    /**
     * Frequent flyer card number
     */
    val cardNo: String = "",
)

data class CoreSeg(
    /**
     * airline IATA code
     */
    val airline: String = "",
    /**
     * IATA code of the arrival airport
     */
    val arrival: String = "",
    /**
     * arrival date (local time of the airport)
     */
    val arrivalDate: String = "",
    /**
     * arrival terminal
     */
    val arrivalTerminal: String = "",
    /**
     * arrival time (local time of the airport)
     */
    val arrivalTime: String = "",
    val createdAt: String = "",
    /**
     * IATA code of the departure airport
     */
    val departure: String = "",
    /**
     * departure date (local time of the airport)
     */
    val departureDate: String = "",
    /**
     * departure terminal
     */
    val departureTerminal: String = "",
    /**
     * scheduled departure time (local time of the airport)
     */
    val departureTime: String = "",
    /**
     * distance
     */
    val distance: String = "",
    /**
     * equipment code
     */
    val equipment: String = "",
)

data class OrderSeg(
    /**
     * segment id
     */
    val id: Int = 0,
    /**
     * order id
     */
    val orderId: String = "",
    /**
     * segment index in a journey
     */
    val index: Int = 0,
    /**
     * journey index
     */
    val journeyIndex: Int = 0,
    /**
     * core segment id
     */
    val coreSegmentId: String = "",
    /**
     * core segment infomation
     */
    val coreSegment: CoreSeg = CoreSeg(),
    /**
     * cabin class
     *
     * Allowed values:
     * economy
     * premium_economy
     * first
     * business
     */
    val cabinClass: String = "",
    /**
     * cabin code
     */
    val cabinCode: String = "",
    /**
     * stayTime
     */
    val stayTime: String = "",
    val pnr: String = "",
    val airlinePnr: String = "",
    val baggageRules: List<Baggage> = emptyList(),
    val fareBasisCode: String = "",
    val originSegmentId: String = "",
    val parentSegmentId: String = "",
    val lastChangeId: String = "",
    val createdAt: String = "",
    val updatedAt: String = "",
)

data class TicketItem(
    /**
     * ticket id
     */
    val id: Int = 0,
    /**
     * parent ticket id(for change & refund order)
     */
    val parentTicketId: Int = 0,
    val segmentId: Int = 0,
    val passengerId: Int = 0,
    val ticketNo: String = "",
    val pnr: String = "",
    val issueDate: String = "",
    val expireDate: String = "",
    val changeOrderId: String = "",
    val refundOrderId: String = "",
    val voidOrderId: String = "",
    /**
     * Allowed values:
     * pending
     * processing
     * changed
     * refunded
     * voided
     */
    val status: String = "",
    val remark: String = "",
    val createdAt: String = "",
    val updatedAt: String = "",
)

data class TicketPassenger(
    /**
     * passenger id
     */
    val id: Int = 0,
    /**
     * parent passenger id(for change & refund order)
     */
    val parentPassengerId: Int = 0,
    val orderId: String = "",
    /**
     * Allowed values:
     * adult
     * child
     * infant
     */
    val type: String = "",
    /**
     * index of the passenger in this order
     */
    val index: Int = 0,
    /**
     * infant or child's associated adult index
     */
    val associatedIndex: Int = 0,
    val surname: String = "",
    val givenNames: String = "",
    /**
     * Allowed values:
     * male
     * female
     */
    val gender: String = "",
    val birthday: String = "",
    /**
     * travelDocument
     * Allowed values:
     * passport
     * tphm
     * tptw
     * rphmt
     * idcard
     * fpidcard
     * eep
     * ttpmr
     * hhr
     */
    val travelDocument: String = "",
    val travelDocumentExpireDate: String = "",
    val travelDocumentNumber: String = "",
    val email: String = "",
    val region: String = "",
    val phone: String = "",
    val tickets: List<TicketItem> = emptyList(),
    val airline: String = "",
    val cardNo: String = "",
    val createdAt: String = "",
    val updatedAt: String = "",
)

data class OrderJourney(
    val segments: List<OrderSeg> = emptyList(),
    /**
     * order id
     */
    val orderId: String = "",
    /**
     * journey id
     */
    val id: Int = 0,
    /**
     * arrival date [yyyy-mm-dd]
     */
    val arrivalDate: String = "",
    /**
     * arrival time [hh:mm]
     */
    val arrivalTime: String = "",
    /**
     * departure date [yyyy-mm-dd]
     */
    val departureDate: String = "",
    /**
     * departure time [hh:mm]
     */
    val departureTime: String = "",
    /**
     * IATA code of arrival city
     */
    val destination: String = "",
    /**
     * journey duration
     */
    val duration: String = "",
    /**
     * IATA code of departure city
     */
    val origin: String = "",
    /**
     * stop numbers
     */
    val transferNum: Int = 0,
    val createdAt: String = "",
    val updatedAt: String = "",
)

data class ChangeSegInfo(
    val coreSegmentId: String = "",
    val cabinClass: String = "",
    val cabinCode: String = "",
    val availability: String = "",
    val baggageRules: List<BaggageRule> = emptyList(),
    val stayTime: String = "",
)

data class ChangeSeg(
    val segmentInfo: ChangeSegInfo = ChangeSegInfo(),
    val segmentKey: String = "",
)

data class ChangeSegment(
    val airline: String = "",
    val arrival: String = "",
    val arrivalDate: String = "",
    val arrivalTerminal: String = "",
    val arrivalTime: String = "",
    val departure: String = "",
    val departureDate: String = "",
    val departureTerminal: String = "",
    val departureTime: String = "",
    val distance: String = "",
    val equipment: String = "",
    val flightNo: String = "",
    val flightTime: String = "",
    val id: String = "",
    val isCodeShare: Boolean = false,
    val opAirline: String = "",
    val opFlightNo: String = "",
    val stopovers: List<Stop> = emptyList(),
)

data class ChangeReqSeg(
    val id: Int = 0,
    val segmentKey: String = "",
    /**
     * Allowed values:
     * economy
     * premium_economy
     * first
     * business
     */
    val cabinClass: String = "",
    val cabinCode: String = "",
)


class Resp<T>(
    /**
     * 通用response
     */
    val data: T? = null,
    /**
     * 错误码
     */
    val code: Int = 0,
    /**
     * 错误信息
     */
    val message: String = "",
    /**
     * 错误信息
     */
    val realMessage: String = "",
) {
    fun print(): String {
        return """{"code": $code, "message": "$message", "realMessage": $realMessage""}"""
    }
}

data class SegmentInfo(
    val depAirCity: String = "",
    val depAirCityCN: String = "",
    val descAirCity: String = "",
    val descAirCityCN: String = "",
    val depAirPort: String = "",
    val depAirPortCN: String = "",
    val descAirPort: String = "",
    val descAirPortCN: String = "",
    val depTerminal: String = "",
    val descTerminal: String = "",
    val depDate: String = "",
    val descDate: String = "",
    val depTime: String = "",
    val descTime: String = "",
    val airline: String = "",
    val airlineCN: String = "",
    val flightNo: String = "",
    val equipment: String = "",
    val cabinClass: String = "",
    val flightTime: String = "",
)


data class PricingSeg(
    /**
     * 行程编号，从1开始
     */
    val journeyIndex: Int = 0,
    /**
     * 出发机场
     */
    val departure: String = "",
    /**
     * 到达机场
     */
    val arrival: String = "",
    /**
     * 出发时间
     */
    val departureDate: String = "",
    /**
     * 航班号
     */
    val flightNo: String = "",
    /**
     * 仓等
     */
    val cabinClass: String = "",
)