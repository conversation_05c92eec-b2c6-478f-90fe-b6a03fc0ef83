package com.somytrip.model.flight.tc

import java.io.Serializable

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-04 11:10
 */

data class TCNotifyBody(
    /**
     * 用于确定本次接口访问需要完成的业务，“PayResult”
     */
    val serviceCode: String,
    /**
     * 用于区分合作伙伴
     */
    val pid: String,
    /**
     * 用于保证数据通信安全
     */
    val sign: String,
    /**
     * 请求唯一码
     */
    val requestID: String,
    /**
     * 时间戳
     */
    val timestamp: String,
    /**
     * 详见Notify Element节点
     */
    val params: String,
    /**
     * 唯一性校验
     */
    val businessRequest: String,
    val businessType: Int,
) : Serializable

/**
 * 同程-支付通知
 */
data class TCPayNotify(
    /**
     * 业务类型（0：国内，1：国际）
     */
    val businessType: Int,
    /**
     * 订单号
     */
    val orderNo: String,
    /**
     * 改签单号
     */
    val endorseSerialNo: String?,
    /**
     * 支付类型；0-订单支付（默认值）；1-改签支付（改签补差选用）
     */
    val payType: Int,
    /**
     * 支付结果；0-支付成功；1-支付失败
     */
    val payResult: Int,
    /**
     * 订单实付金额
     */
    val payMoney: String,
    /**
     * 支付时间（yyyy-MM-dd HH:mm:ss）
     */
    val payTime: String,
    /**
     * 待授权office号，换编订单需要对该office号进行授权，否则可能无法出票
     */
    val toBeAuthOffice: String?,
    /**
     * 交易号
     */
    val tradeNo: String?,
) : Serializable

/**
 * 同程-出票通知
 */
data class TCTicketNotify(
    /**
     * 业务类型（0：国内，1：国际）
     */
    val businessType: Int,
    /**
     * 出票状态；0-出票成功；1-出票失败
     */
    val outTicketState: Int,
    /**
     * 订单号
     */
    val orderNo: String,
    /**
     * 采购方平台订单号；
     */
    val externalOrderNo: String,
    /**
     * 出票时间（yyyy-MM-dd HH:mm:ss）
     */
    val outTicketTime: String,
    /**
     * 票号数组，详细参考TicketNoItem Element
     */
    val ticketNoItem: List<TicketNotifyNoItem>,
    /**
     * 出票失败必填，定义如下：HH510001，编码未授权；HH510002，编码已取消；HH510003，编码已出票；HH510004，编码行程信息有误；HH510005，编码座位状态不合法；HH510006， 编码缺失DOCA、CTCT、CTCM、FOID项；HH10000，其它错误，XXX；说明：Map<错误码,错误描述>
     */
    val errorInfo: Map<String, String>?
) : Serializable

data class TicketNotifyNoItem(
    /**
     * 乘机人姓名
     */
    val psrName: String,
    /**
     * 票号
     */
    val tktNo: String,
    /**
     * 证件号
     */
    val idNo: String,
    /**
     * 编号
     */
    val pnr: String,
) : Serializable

/**
 * 改签确认通知
 */
data class TCEndorseConfirmNotify(
    val businessType: Int = 0,
    val orderSerialNo: String = "",
    val endorseSerialNo: String = "",
    val purchaserSerialNo: String = "",
    val endorseType: Int = 0,
    val ticketNos: List<String> = emptyList(),
    val priceInfos: List<TCEndorseConfirmPriceInfo> = emptyList(),
    val totalPrice: String = "",
    val originalFlightInfo: TCEndorseFlightInfo = TCEndorseFlightInfo(),
    val changeFlightInfo: TCEndorseFlightInfo = TCEndorseFlightInfo(),
    val multiSegmentOriginalFlightInfo: List<TCEndorseFlightInfo> = emptyList(),
    val multiSegmentChangeFlightInfo: List<TCEndorseFlightInfo> = emptyList(),
) : Serializable

data class TCEndorseFlightInfo(
    val arrDateTime: String = "",
    val arrivalAirportCode: String = "",
    val cabin: String = "",
    val depDateTime: String = "",
    val departureAirportCode: String = "",
    val flightNo: String = "",
    val segmentIndex: Int = 0,
    val segmentType: Int = 0,
)


data class TCEndorseConfirmPriceInfo(
    /**
     * 乘客类型（0:未知；1:成人；2:儿童；3:婴儿）
     */
    val passengerType: Int = 0,
    /**
     * 升舱费
     */
    val upgradeFee: String = "",
    /**
     * 改签费
     */
    val endorseFee: String = "",
    /**
     * 出票代理费（仅第一次自愿改签收取）
     */
    val agencyFee: String = "",
    /**
     * 改签票面价
     */
    val changePrice: String = "",
    /**
     * 税费差价
     */
    val taxDiff: String = "",
    /**
     * 手续费
     */
    val endorseProcedureFee: String = "",
    /**
     * Key为税项（tax-机建差价(整数) yq-燃油费差价（整数））
     */
    val taxDetail: Map<String, String> = emptyMap(),
) : Serializable

/**
 * 改签结果通知
 */
data class TCEndorseResultNotify(
    /**
     * 业务类型（0：国内，1：国际）
     */
    val businessType: Int = 0,
    /**
     * 易旅行改签订单号
     */
    val endorseSerialNo: String = "",
    /**
     * 改签状态（1：改签成功 2：改签失败）
     */
    val endorseState: Int = 0,
    /**
     * 改签处理描述（主要体现为审核拒绝理由）
     */
    val endorseDesc: String? = "",
    /**
     * 退款类型(1-原路退 2-退客人卡 3-营业部柜台退款) : Serializable
     */
    val refundWayType: Int = 0,
    /**
     * 改签乘机人，参考ChangePassenger Element
     */
    val ticketDetails: List<TCEndorseResultPassenger> = emptyList(),
) : Serializable

data class TCEndorseResultPassenger(
    /**
     * 乘机人姓名本次调整：跟下单或提交改签传的格式保持一致；“张三”or“张/三”
     */
    val passengerName: String = "",
    /**
     * 证件号
     */
    val creditNo: String = "",
    /**
     * pnr
     */
    val pnr: String = "",
    /**
     * 原pnr
     */
    val originPnr: String = "",
    /**
     * 票号（改签后票号）
     */
    val newTicketNo: String = "",
    /**
     * 老票号（改签前票号）
     */
    val originTicketNo: String = "",
    /**
     * 行程&票号信息 新增，兼容往返和转机多航段
     */
    val ticketNos: List<TCEndorseResultTicketNo> = emptyList(),
) : Serializable

data class TCEndorseResultTicketNo(
    /**
     * 航段类型 1：去程； 2: 返程
     */
    val segmentType: Int = 1,
    /**
     * 航段序号
     */
    val sequence: Int = 1,
    /**
     * 编码
     */
    val pnr: String = "",
    /**
     * 大编码
     */
    val bigPnr: String? = "",
    /**
     * 票号
     */
    val ticketNo: String = "",
) : Serializable

/**
 * 航变通知
 */
data class TCFlightChangeNotify(
    val businessType: Int = 0,
    val orderNo: String = "",
    val endorseSerialNo: String? = "",
    val changeType: Int = 0,
    val oldFlightInfo: TCFlightChangeNotifyFlightInfo = TCFlightChangeNotifyFlightInfo(),
    val newFlightInfo: TCFlightChangeNotifyFlightInfo = TCFlightChangeNotifyFlightInfo(),
) : Serializable

data class TCFlightChangeNotifyFlightInfo(
    val segmentType: Int = 0,
    val segmentIndex: Int = 0,
    val flightNo: String = "",
    val deptDateTime: String = "",
    val deptAirPortCode: String = "",
    val arrAirPortCode: String = "",
    val arrDateTime: String = "",
) : Serializable

/**
 * 退票通知
 */
data class TCRefundResultNotify(
    val businessType: Int = 0,
    val externalOrderNo: String = "",
    val orderNo: String = "",
    val refundOrderNo: String = "",
    val refundState: Int = 0,
    val cancelReason: Int = 0,
    val remark: String = "",
    val passengerTickets: List<TCRefundResultNotifyTicket> = emptyList(),
    val refundTime: String = "",
) : Serializable

data class TCRefundResultNotifyTicket(
    val lastName: String = "",
    val firstName: String = "",
    val ticketNo: String = "",
    val payMoney: String = "",
    val tax: String = "",
    val taxDetails: Map<String, String> = emptyMap(),
    val refundFee: String = "",
    val refundMoney: String = "",
) : Serializable

/**
 * 退票费用通知
 */
data class TCRefundFeeNotify(
    val businessType: Int = 0,
    val orderSerialNo: String = "",
    val refundOrderNo: String = "",
    val externalOrderNo: String = "",
    val refundType: Int = 0,
    val passengerInfo: List<TCRefundFeeNotifyPassenger> = emptyList(),
    val type: Int = 0,
) : Serializable

data class TCRefundFeeNotifyPassenger(
    val lastName: String = "",
    val firstName: String = "",
    val type: Int = 0,
    val certNo: String = "",
    val netPrice: String = "",
    val tax: String = "",
    val refundFee: String = "",
    val refundMoney: String = "",
    val segmentInfo: List<TCRefundFeeNotifyPassengerSegment> = emptyList(),
) : Serializable

data class TCRefundFeeNotifyPassengerSegment(
    val segmentNo: Int = 0,
    val depAirport: String = "",
    val arrAirport: String = "",
    val airline: String = "",
    val flightNo: String = "",
    val cabinCode: String = "",
    val depDateTime: String = "",
    val arrDateTime: String = "",
    val refundFee: String = "",
) : Serializable