package com.somytrip.model.flight.enums

/**
 * @Description: fr24接口定义
 * @author: pigeon
 * @created: 2024-08-13 15:36
 */
enum class FR24UriEnum(
    val uri: String
) {
    /**
     * 航班搜索接口
     */
    SHOPPING("/api/new/shopping.do"),

    /**
     * 校验接口
     */
    PRICING("/api/new/pricing.do"),

    /**
     * 生单接口
     */
    BOOKING("/api/new/booking.do"),

    /**
     * 申请出票接口
     */
    TICKETING("/api/new/ticketing.do"),

    /**
     * 订单详情接口
     */
    ORDER_DETAIL("/api/new/orderDetail.do"),

    /**
     * 售前辅营搜索接口
     */
    PRESALE_ANCILLARY_SHOPPING("/api/new/presaleAncillaryShopping.do"),

    /**
     * 售后行李搜索接口
     */
    POST_ANCILLARY_SHOPPING("/api/new/postsaleAncillaryShopping.do"),

    /**
     * 辅营生单接口
     */
    ANCILLARY_BOOKING("/api/new/ancillaryBooking.do"),

    /**
     * 辅营支付接口
     */
    ANCILLARY_PURCHASE("/api/new/ancillaryPurchase.do"),

    /**
     * 辅营订单详情
     */
    ANCILLARY_ORDER_DETAIL("/api/new/ancillaryOrderDetail.do"),

    /**
     * 改期搜索接口
     */
    CHANGE_RE_SHOP("/api/new/changeReshop.do"),

    /**
     * 改期申请接口
     */
    RE_ISSUE_REQUEST("/api/new/reissueRequest.do"),

    /**
     * 退票申请接口
     */
    REFUND_REQUEST("/api/new/refundRequest"),

    /**
     * 退票确认接口
     */
    REFUND_CONFIRM("/api/new/refundConfirm.do"),

    /**
     * 废票提交接口
     */
    VOID_REQUEST("/api/new/voidRequest.do"),
    ;

    override fun toString(): String {
        return uri
    }
}