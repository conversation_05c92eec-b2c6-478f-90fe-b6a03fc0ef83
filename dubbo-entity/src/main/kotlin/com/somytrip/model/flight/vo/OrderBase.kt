package com.somytrip.model.flight.vo

import com.somytrip.model.flight.common.AccRuleItem
import com.somytrip.model.flight.tc.TCOrderProductItem

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-08-29 11:45
 */
data class OrderReqBase(
    val totalTax: String = "",
    val totalPrice: String = "",
    val request: CreateOrderReq = CreateOrderReq(),
    val ruleUnit: AccRuleItem = AccRuleItem(),
) {
    constructor() : this("")
}


data class OrderBaseVo(
    val totalPrice: String = "",
    val orderSerialNo: String = "",
    val accurate: OrderReqBase = OrderReqBase(),
    val products: List<TCOrderProductItem> = emptyList(),
) {
    constructor() : this("")
}