package com.somytrip.model.flight.common

import com.somytrip.entity.vo.flight.AirLineCodeVo

/**
 * @Description: 航班信息
 * @author: pigeon
 * @created: 2024-08-20 10:09
 */
data class SegmentItem(
    /**
     * 实际航司
     */
    val operationAirline: String = "",
    /**
     * 实际航班号
     */
    val operationFlightNo: String = "",
    /**
     * 航司 IATA 二字码，必须与 flightNumber 航司相同
     */
    val airline: String = "",
    /**
     * 往返 0 去程 1 返
     */
    val tripType: Int = 0,
    /**
     * 共享航班标识 false 非共享 true共享
     */
    val flightShare: Boolean = false,
    /**
     * 航班号，如：CA123
     */
    val flightNo: String = "",
    /**
     * 机型
     */
    val aircraft: String = "",
    /**
     * 机型类型
     */
    val aircraftType: String = "",
    /**
     * 到达机场 IATA 三字码
     */
    val arriveAirport: String = "",
    /**
     * 抵达航站楼，使用简写，例如T1
     */
    val arriveAirportTerm: String = "",
    /**
     * 到达时间，格式：YYYY-MM-DD HH:MM:SS
     */
    val arriveDateTime: String = "",
    /**
     * 到达机场 IATA 三字码
     */
    val departAirport: String = "",
    /**
     * 抵达航站楼，使用简写，例如T1
     */
    val departAirportTerm: String = "",
    /**
     * 到达时间，格式：YYYY-MM-DD HH:MM:SS
     */
    val departDateTime: String = "",

    /**
     * 餐食信息
     * 未知 "" 有 "有餐食" 无 "无餐食"
     */
    val meal: String = "",
    /**
     * 航班里程数 单位：km
     */
    val mileage: String = "",
    /**
     * 行程序号从1开始
     */
    val segmentIndex: Int = 1,
    /**
     * 经停机场iata码
     */
    val stops: List<SegmentStopItem> = emptyList(),
    /**
     * 1d2h3m
     */
    val stopTime: String = "",
    /**
     * 1d2h3m
     */
    val duration: String = "",
    /**
     * 1d2h3m
     */
    val durationTotal: String = "",
    val arriveAirportDisplay: String = "",
    val departAirportDisplay: String = "",
    val airlineDisplay: AirLineCodeVo = AirLineCodeVo(null),
    val stopsDisplay: List<String> = emptyList(),
    var transitTime: String = "",
    val cabinCode: String = "",
) {
    constructor() : this("")
//    constructor(item: TCSegmentItem) : this(
//        operationAirline = item.operatingAirline,
//        operationFlightNo = item.operatingFlightNo,
//        airline = item.marketingAirline,
//        tripType = when (item.segmentType) {
//            1 -> 0 // 去程
//            2 -> 1 // 返程
//            else -> 0 // 默认为去程
//        },
//        flightShare = item.flightShare,
//        flightNo = item.marketingFlightNo,
//        aircraft = item.aircraft,
//        aircraftType = item.aircraftType.toString(),
//        arriveAirport = item.arrAirport,
//        arriveAirportTerm = item.arrAirportTerm,
//        arriveDateTime = item.arrDateTime,
//        departAirport = item.depAirport,
//        departAirportTerm = item.depAirportTerm,
//        departDateTime = item.depDateTime,
//        meal = when (item.meal) {
//            1 -> "有餐食" // 有
//            2 -> "无餐食" // 无
//            else -> "" // 默认未知
//        },
//        mileage = item.mileage.toString(),
//        segmentIndex = item.segmentIndex,
//        stops = item.stops.map { SegmentStopItem(it) },
//        stopTime = CommonValFunc.formatIntToDuration(item.stopTime),
//        duration = CommonValFunc.formatIntToDuration(item.duration),
//        durationTotal = CommonValFunc.formatIntToDuration(item.duration),
//        arriveAirportDisplay = item.arrCityName,
//        departAirportDisplay = item.depCityName,
//        airlineDisplay = AirLineCodeVo(null),
//        stopsDisplay = item.stops.map { it.stopAirport },
//        transitTime = CommonValFunc.formatIntToDuration(item.stopTime)
//    )
}
