package com.somytrip.model.flight.vo

import com.somytrip.model.flight.common.ShopListRsp
import jakarta.validation.constraints.Size

/**
 * @Description: 攻略使用
 * @author: pigeon
 * @created: 2024-08-30 15:34
 */
data class TripPlanVo(
    @Size(max = 6)
    val cityPairs: List<List<Int>> = emptyList(),
    @Size(max = 6)
    val dates: List<String> = emptyList(),
)

data class TripPlanRsp(
    @Size(max = 6)
    val data: List<TripPlanItem> = emptyList(),
    val success: Boolean = false,
    val message: String = "",
)

data class TripPlanItem(
    val low: ShopListRsp = ShopListRsp(),
    val mid: ShopListRsp = ShopListRsp(),
    val high: ShopListRsp = ShopListRsp(),
)