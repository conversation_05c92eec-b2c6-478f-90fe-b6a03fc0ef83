package com.somytrip.model.flight.common

import com.somytrip.model.flight.enums.RuleWightTypeEnum
import com.somytrip.model.flight.tc.TCAccurateSearchItemBaggageRule

/**
 * @Description: 行李信息规则Item
 * @author: pigeon
 * @created: 2024-08-22 14:53
 */
data class BaggageRuleItem(
    val freePrices: String = "",
    val segmentIndex: Int = 0,
    val volume: String = "",
    val weight: String = "",
    val weightType: RuleWightTypeEnum = RuleWightTypeEnum.DEFAULT,
) {
    constructor(item: TCAccurateSearchItemBaggageRule) : this(
        freePrices = item.freePieces,
        segmentIndex = item.segmentIndex,
        volume = item.volume,
        weight = item.weight,
        weightType = RuleWightTypeEnum.tcValueToEnums(item.weightType)
    )
}
