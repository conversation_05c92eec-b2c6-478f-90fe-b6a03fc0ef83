package com.somytrip.model.flight.tc

import com.alibaba.fastjson2.annotation.JSONField
import com.somytrip.model.po.TCOrderResponsePoProduct
import java.io.Serializable

/**
 * @Description: 同程Request
 * @author: pigeon
 * @created: 2023-12-26 15:18
 */
data class TCFlightRequest(
    var serviceCode: String = "",
    var pid: String = "",
    var sign: String = "",
    var requestID: String = "",
    var timestamp: String = "",
    var businessRequest: String = "",
) : Serializable

/**
 * 模糊查询
 */
data class TCShoppingSearchRequest(
    /**
     * 1 单程（仅支持） 2 往返；
     */
    val tripType: String,
    /**
     * 0 国内，1 国际；
     */
    val businessLineType: Int,
    /**
     * 产品类型：1-普通产品（快速出票，标准退改签）；2-优选产品； 3-特惠产品（保障出行）；4-官网产品（官网直营，标准退改）；5-特殊产品（个性产品，保障出行）；6-青老年特惠；7-多人特惠；8-商旅标品；9-特惠O24（进单24小时出票）；10-自营产品（快速出票，标准退改 13-特惠不限）；
     */
    val productTagList: List<String>?,
    /**
     * 参数列表有，但是示例没有
     * 航司查询二字码列表，空是查询全部航司；
     */
    val carrierCodes: List<String>?,
    /**
     * 乘客信息
     */
    val passengers: List<TCShoppingSearchPassenger>,
    /**
     * 航段信息,当前仅支持一段；
     */
    val segments: List<TCShoppingSearchSegmentItem>,
    /**
     * 参数列表没有，但是示例有
     */
    val productType: Int?,
) : Serializable

data class TCShoppingSearchPassenger(
    /**
     * 乘客数量，默认为1
     */
    val count: Int,
    /**
     * 乘客类型： 成人–ADT（仅支持） 儿童–CHD 婴儿-INF
     */
    val passengerType: String,
) : Serializable

data class TCShoppingSearchSegmentItem(
    /**
     * 航程编号,从1开始
     */
    @JSONField(name = "segmentNO")
    val segmentNo: Int,
    /**
     * 出发机场
     */
    val depAirport: String,
    /**
     * 出发城市三字码
     */
    val depCity: String?,
    /**
     * 到达机场
     */
    val arrAirport: String,
    /**
     * 到达城市三字码
     */
    val arrCity: String?,
    /**
     * 出发日期yyyy-MM-dd
     */
    val depDate: String,
) : Serializable

/**
 * 同程-客规查询接口request
 */
data class TCGuestRuleRequest(
    /**
     * 模糊查询response的data字段，为缓存
     */
    val datas: List<String>
) : Serializable

/**
 * 同程-精确询价接口request
 */
data class TCAccurateSearchRequest(
    /**
     * 0 国内，默认值；1 国际
     */
    val businessLineType: Int,
    /**
     * 1 单程；2 往返(暂不支持)
     */
    val tripType: Int,
    /**
     * 产品标签：1-普通产品（快速出票，标准退改签）；2-优选产品； 3-特惠产品（保障出行）；4-官网产品（官网直营，标准退改）；5-特殊产品（个性产品，保障出行）；6-青老年特惠；7-多人特惠；8-商旅标品；9-特惠O24（进单24小时出票）；10-自营产品（快速出票，标准退改）；
     */
    val productCodes: List<Int>?,
    /**
     * 是否要求换编码：0-不限(默认) : Serializable，1-原编开票，2-换编开票。
     */
    val changePnrCode: Int,
    /**
     * 乘客人总数最多不超过9人
     */
    val adultNum: Int,
    /**
     * 当前暂不支持儿童
     */
    val chdNum: Int?,
    /**
     * 婴儿
     */
    val infNum: Int?,
    /**
     * 出票航司
     */
    val mainAirline: String,
    /**
     * 是否需低价推荐，0-不需要（默认），1-需要低价 (若需要低价，会返回比请求舱位更低舱位的价格，包含行程单和发票，采购商需按需进行筛选使用。) : Serializable
     */
    val recommend: String,
    /**
     * 行程信息
     */
    val segments: List<TCAccurateSearchSegment>,
) : Serializable

data class TCAccurateSearchSegment(
    /**
     * 1：去程； 2: 返程
     */
    val segmentType: Int,
    /**
     * 行程序号，全局序号从1开始计数
     */
    val segmentIndex: Int,
    /**
     * 出发城市三字码
     */
    val depCity: String?,
    /**
     * 出发机场三字码
     */
    val depAirport: String,
    /**
     * 出发航站楼，使用简写，例如T1
     */
    val depTerminal: String?,
    /**
     * 到达城市三字码
     */
    val arrCity: String?,
    /**
     * 到达机场 IATA 三字码
     */
    val arrAirport: String,
    /**
     * 抵达航站楼，使用简写，例如T1
     */
    val arrTerminal: String?,
    /**
     * 起飞时间，格式：YYYY-MM-DD HH:mm:ss 国内时间可不提供具体时间，日期后跟“00:00:00”
     */
    val depDate: String,
    /**
     * 到达时间，格式：YYYY-MM-DD HH:mm:ss，国内可为空
     */
    val arrDate: String,
    /**
     * 航司二字码
     */
    val airline: String,
    /**
     * 航班号，如：CA123
     */
    val flightNo: String,
    /**
     * 舱位
     */
    val cabinCode: String,
    /**
     * 舱等，头等：F，商务：C，超经：S，经济：Y
     */
    val cabinClass: String?,
    val flightShare: Boolean,
    /**
     * 机型 ，IATA标准3字码
     */
    val aircraftCode: String?,
    val operatingFlightNo: String?,
    val operatingAirline: String?,
) : Serializable


/**
 * 同程-支付校验接口
 */
data class TCPayCheckRequest(
    /**
     * 生成订单成功时返回的订单号
     */
    val orderSerialNo: String = "",
    /**
     * 订单总金额
     */
    val totalPrice: String = "",
    /**
     * 外部订单号(代理商自己产生的订单号)
     */
    val purchaserSerialNo: String? = "",
    /**
     * 商品信息
     */
    val products: List<TCPayCheckProduct> = emptyList(),
) : Serializable

data class TCPayCheckProduct(
    /**
     * 产品类型 0 机票
     */
    val productType: String = "0",
    /**
     * 销售价（对应产品的总金额）
     */
    val price: String = "",
    /**
     * 商品明细
     */
    val prePayProductDetail: TCPayCheckProductDetail? = TCPayCheckProductDetail(),
) : Serializable {
    constructor(product: TCOrderResponsePoProduct) : this(product.productType, product.productTotalPrice, null)
}

data class TCPayCheckProductDetail(
    /**
     * 编码集合
     */
    @JSONField(name = "Pnrs")
    val pnrs: List<String>? = emptyList()
) : Serializable

/**
 * 同程-支付接口
 */
data class TCPayRequest(
    /**
     * 订单号
     */
    val orderSerialNo: String = "",
    /**
     * 订单总金额
     */
    val totalPrice: String = "",
) : Serializable

/**
 * 同程-生单接口request
 */
data class TCOrderRequest(
    /**
     * 归属业务线 0 国内 1 国际
     */
    val buisinessLineType: Int = 0,
    /**
     * 联系人
     */
    val contact: TCOrderContact = TCOrderContact(),
    /**
     * 分销产品信息
     */
    val products: List<TCOrderProduct> = emptyList(),
    /**
     * 订单总金额
     */
    val totalPrice: String = "",
) : Serializable

data class TCOrderContact(
    /**
     * 联系人姓名，不单独区分姓和名
     */
    val name: String = "",
    /**
     * 详细地址
     */
    val address: String? = "",
    /**
     * 邮编
     */
    val postcode: String? = "",
    /**
     * 邮箱
     */
    val email: String? = "",
    /**
     * 联系人手机号
     */
    val phone: String = "",
    /**
     * 联系人传真
     */
    val fax: String? = "",
) : Serializable

data class TCOrderProduct(
    /**
     * 对应报价产品的缓存data
     */
    val data: String,
    /**
     * 销售产品对应的总金额
     */
    val price: String,
    /**
     * 产品类型 0 机票 1 辅营 2 火车票 3 酒店
     */
    val productType: Int,
    /**
     * 各产品请求参数
     */
    val productDetail: TCOrderProductDetail,
) : Serializable

data class TCOrderProductDetail(
    /**
     * 用于换编码政策出票后向用户PNR授权，模糊报价不用传
     */
    val officeNo: String?,
    /**
     * 乘客信息
     */
    val passengers: List<TCOrderPassenger>,
    /**
     * 价格信息
     */
    val prices: List<TCOrderPrice>,
    /**
     * ？
     */
    val bigPnrCode: String?,
) : Serializable

data class TCOrderPassenger(
    /**
     * 姓
     */
    val lastName: String,
    /**
     * 名证件类型为身份证、港澳居住证、台湾居住证时，firstName传【姓名】；当证件类型不为以上类型时，firstName传【名】，lastName传【姓】
     */
    val firstName: String,
    /**
     * 性别(0:未知 1:男 2:女) : Serializable
     */
    val gender: Int,
    /**
     * 乘客类型(0:未知 1:成人 2:儿童 3:婴儿) : Serializable
     */
    val type: Int,
    /**
     * 0:未知，1:身份证，2:护照，3:军官证，4:回乡证，5:港澳通行证，6:台胞证，7:台湾通行证, 8:海员证, 9 其他10:签证 ，11：户口薄，13外国人永久居留证，14：港澳居住证，15：台湾居民居住证
     */
    val creditType: Int,
    /**
     * 国籍
     */
    val nation: String?,
    /**
     * 国籍二字码(调整为必传) : Serializable
     */
    val nationCode: String,
    /**
     * 证件号
     */
    val creditNo: String,
    /**
     * 证件有效期，格式为yyyy-MM-dd(调整为必传) : Serializable
     */
    val gmtCreditValidate: String,
    /**
     * 证件发行国家，国家二字码 (调整为必传
     */
    val cardIssuePlace: String,
    /**
     * 出生日期，格式为yyyy-MM-dd
     */
    val birthday: String,
    /**
     * 联系电话 当marketingAirline（航司）=MU 且 invoiceWay（开票方式）=3（官网）时，该字段必填
     */
    val linkPhone: String?,
) : Serializable

data class TCOrderPrice(
    /**
     * 销售价
     */
    val price: String,
    /**
     * 税费
     */
    val tax: String,
    /**
     * 乘客类型(0:未知, 1:成人, 2:儿童, 3:婴儿) : Serializable
     */
    val type: Int,
) : Serializable

/**
 * 同程-订单取消接口request
 */
data class TCOrderCancelRequest(
    /**
     * 生成订单成功时返回的订单号
     */
    val orderSerialNo: String,
    /**
     * 取消的商品信息
     */
    val cancelProducts: List<TCOrderCancelProduct>,
    /**
     * 订单取消原因
     */
    val reason: String?,
) : Serializable

data class TCOrderCancelProduct(
    /**
     * 取消商品详情
     */
    val cancelProductDetail: TCOrderCancelProductDetail?,
    /**
     * 产品类型 0 机票，1 辅营
     */
    val productType: String,
) : Serializable

data class TCOrderCancelProductDetail(
    /**
     * 编码
     */
    val pnr: String?,
) : Serializable

/**
 * 同程-订单列表查询接口
 */
data class TCOrderListQueryRequest(
    val startCreateTime: String,
    val endCreateTime: String,
    val businessLineType: Int,
    val orderStates: List<Int>,
    val traceId: String,
) : Serializable

/**
 * 同程-订单详情查询接口
 */
data class TCOrderDetailRequest(
    val orderSerialNo: String,
    val traceId: String,
) : Serializable

/**
 * 取消出票
 */
data class TCTicketCancelRequest(
    val orderSerialNo: String,
    val purchaserSerialNo: String?
) : Serializable

/**
 * 出票确认
 */
data class TCTicketConfirmRequest(
    val orderSerialNo: String,
    val purchaserSerialNo: String?,
    val originPnr: String?,
) : Serializable

/**
 * 同程-改签列表查询
 */
data class TCEndorseListQueryRequest(
    /**
     * 改期单创建开始时间,格式:yyyy-MM-dd HH:mm:ss
     */
    val startCreateTime: String,
    /**
     * 改期单创建截止时间,格式:yyyy-MM-dd HH:mm:ss
     */
    val endCreateTime: String,
    /**
     * 业务类型（0-国内 1-国际）
     */
    val businessLineType: Int,
    /**
     * 改签单状态，0:全部；1:待确认；2:待改签；3:待支付；4:待审核；5:改签完成；6:改签取消；7:改签暂不能；8:票号验证失败；9:支付中；10:票号校验中
     */
    val orderStates: List<Int>,
    /**
     * 改签类型，0：全部；1:自愿；2:非自愿；3：改名字；4：改证件号；5：取消位置；6：航班确认；7：升舱换开；8：签转外航；
     */
    val endorseType: List<Int>,
    /**
     * 请求唯一值
     */
    val traceId: String,
) : Serializable

/**
 * 同程-改签查询
 */
data class TCChangeReShopRequest(
    val businessLineType: Int = 0,
    /**
     * 改签类型， 1：自愿改签，2：非自愿改签 3：改名字 4：改证件
     */
    val endorseType: Int = 1,
    /**
     * 改签日期
     */
    val changeDate: String = "",
    /**
     * 订单流水号
     */
    val orderSerialNo: String = "",
    /**
     * 外部订单号(代理商自己产生的订单号)
     */
    val purchaserSerialNo: String = "",
    /**
     * 改签原航班号，仅支持单航班改签
     */
    val oriFlightNo: String = "",
    /**
     * 改签乘客信息
     */
    val passengerInfoList: List<TCChangeReShopPassengerItem> = emptyList(),
) : Serializable

data class TCChangeReShopPassengerItem(
    /**
     * 1 成人 2 儿童 3 婴儿
     */
    val passengerType: Int = 1,
    /**
     * 票号
     */
    val ticketNo: String = "",
) : Serializable


/**
 * 改签申请-上传附件
 */
data class TCEndorseFileUploadRequest(
    /**
     * 订单流水号
     */
    val orderSerialNo: String,
    /**
     * 上传的文件(目前仅支持一个文件上传) : Serializable
     */
    val file: List<TCEndorseQueryUploadFile>,
) : Serializable

data class TCEndorseQueryUploadFile(
    /**
     * 文件二进制数据
     */
    val fileByte: ByteArray,
    /**
     * 文件名称
     */
    val fileName: String,
) : Serializable

data class TCEndorseApplyRequest(
    /**
     * 改签类型， 1：自愿改签，2：非自愿改签 3：改名字 4：改证件 3：改名字 4：改证件暂不支持
     */
    val endorseType: Int = 1,
    /**
     * 订单流水号
     */
    val orderSerialNo: String,
    /**
     * 三方订单号
     */
    val purchaserSerialNo: String,
    /**
     * 原始航班号
     */
    val oriFlightNo: String,
    /**
     * 原出票订单需要改签的航段信息，无查询时填写新增多段
     */
    val multiSegmentOriginalFlightInfo: List<TCEndorseApplyFlightInfo>,
    /**
     * 改签新的的航班信息，无查询时填写新增多段
     */
    val multiSegmentChangeFlightInfo: List<TCEndorseApplyFlightInfo>,
    /**
     * 乘客信息
     */
    val passengerInfoList: List<TCEndorseApplyPassengerInfo>,
    /**
     * 改签新航班信息
     */
    val changeFlightInfo: TCEndorseApplyFlightInfo? = null,
    /**
     * 改签备注（非自愿改签理由为‘其他’时，该备注字段不能为空）
     */
    val remark: String? = "",
    /**
     * 上传文件的ID，如需上传附件，请先调用附件上传接口
     */
    val fileKeys: List<String>? = emptyList(),
    /**
     * 非自愿改签理由。非自愿改签必传，自愿改签不传，1:航班变动；2:疫情；3:拒载；4:其他
     */
    val reason: Int? = 0,
    /**
     * 0 无查询直接提交 1 有查询提交
     */
    val scene: Int = 0,
    /**
     * 查询返回的运价data【有查询提交必填】
     */
    val data: String? = "",
) : Serializable

data class TCEndorseApplyFlightInfo(
    /**
     * 航段类型新增 1，去程，2，回程
     */
    val segmentType: Int = 0,
    /**
     * 航段序号新增
     */
    val segmentIndex: Int = 0,
    /**
     * 航班号，如CA1234
     */
    val flightNo: String = "",
    /**
     * 出票航司二字码
     */
    val carrier: String = "",
    /**
     * 舱位
     */
    val cabinCode: String = "",
    /**
     * 是否共享，0：否 1是
     */
    val flightShare: Int = 0,
    /**
     * 出发机场三字码
     */
    val departureAirportCode: String = "",
    /**
     * 到达机场三字码
     */
    val arrivalAirportCode: String = "",
    /**
     * 起飞时间 格式：yyyyMMddHHmm
     */
    val takeOffDateTime: String = "",
    /**
     * 到达时间 格式：yyyyMMddHHmm
     */
    val arrivalDateTime: String = "",
    /**
     * 起飞时间 格式：yyyyMMddHHmm
     */
    val departureDateTime: String = "",
    /**
     * 机型名称
     */
    val aircraft: String = "",
    /**
     * 机型
     */
    val aircraftType: Int = 0,
    /**
     * 飞行时长
     */
    val duration: Int = 0,
    /**
     * 飞行里程
     */
    val distance: String = "",
    /**
     * 是否有餐食 0 – 未知 1 – 有餐食 2 – 无参食/未知
     */
    val meal: Int = 0,
    /**
     * 经停次数
     */
    val stopTime: Int = 0,
    /**
     * 到达航站楼
     */
    val arrivalTerminal: String = "",
    /**
     * 出发航站楼
     */
    val departureTerminal: String = "",
    /**
     * 舱等
     */
    val cabinClass: String = "",
    /**
     * 共享航班号
     */
    val shareFlightNo: String = "",
    /**
     * 实际承运航司
     */
    val realCarrier: String = "",
) : Serializable

data class TCEndorseApplyPassengerInfo(
    /**
     * 原出票订单需要改签的票号，多段票号英文逗号隔开，例如“9876543210980,9876543210981,9876543210982”
     */
    val ticketNo: String,
    /**
     * 姓
     */
    val lastName: String,
    /**
     * 名（国内下单将姓名都传入该字段，lastName传空，英文名字用“/”分割。例如：“firstName”：“张三”；“firstName”：“zhang/san”；Ps改名字时必填
     */
    val firstName: String,
    /**
     * 乘客类型(0:未知 1:成人 2:儿童 3:婴儿)
     */
    val passengerType: Int,
    /**
     * 0:未知，1:身份证，2:护照，3:军官证，4:回乡证，5:港澳通行证，6:台胞证，7:台湾通行证, 8:海员证, 9 其他10:签证 ，11：户口薄，13外国人永久居留证，14：港澳居住证，15：台湾居民居住证；Ps:改证件时必填
     */
    val creditType: Int? = null,
    /**
     * 证件号；Ps:改证件时必填
     */
    val creditNo: String? = null,
    /**
     * 国籍
     */
    val nation: String? = null,
    /**
     * 国籍二字码(国际必传)
     */
    val nationCode: String? = null,
    /**
     * 证件有效期，格式为yyyy-MM-dd
     */
    val gmtCreditValidate: String? = null,
    /**
     * 证件发行国家，国家二字码
     */
    val cardIssuePlace: String? = null,
    /**
     * 出生日期，格式为yyyy-MM-dd
     */
    val birthday: String? = null,
    /**
     * 改签费
     */
    val endorseFee: String? = null,
    /**
     * 升舱费
     */
    val upgradeFee: String? = null,
) : Serializable

data class TCEndorsePayRequest(
    /**
     * 鸿鹄改签订单号
     */
    val endorseSerialNo: String,
    /**
     * 订单总金额
     */
    val totalPay: String,
) : Serializable

data class TCEndorseCancelRequest(
    /**
     * 鸿鹄改签订单号
     */
    val endorseSerialNo: String,
) : Serializable

data class TCQueryChangeOrderDetailRequest(
    /**
     * 易旅行改签单号（EN开头）
     */
    val endorseSerialNo: String,
) : Serializable

/**
 * 同程-可退查询接口
 */
data class TCRefundAbleRequest(
    /**
     * 订单流水
     */
    val orderSerialNo: String,
    /**
     * 退票类型， 0：自愿退，1：非自愿退
     */
    val refundType: Int,
    /**
     * 乘客列表
     */
    @JSONField(name = "pssengerInfo")
    val passengerInfo: List<TCRefundAblePassengerInfo>,
    val traceId: String,
) : Serializable

data class TCRefundAblePassengerInfo(
    /**
     * 乘客证件号码
     */
    val certNo: String,
    /**
     * 姓名
     */
    val name: String,
    /**
     * 票号
     */
    val ticketNo: String,
) : Serializable

/**
 * 同程-退票申请接口
 */
data class TCRefundApplyRequest(
    /**
     * 订单流水号
     */
    val orderSerialNo: String = "",
    /**
     * 三方订单号
     */
    val outOrderNo: String = "",
    /**
     * 退票类型， 0：自愿退，1：非自愿退，
     */
    val refundType: Int = 0,
    /**
     * 退票备注
     */
    val remark: String = "",
    /**
     * 上传文件的ID，如需上传附件，请先调用附件上传接口
     */
    val fileKeys: List<String> = emptyList(),
    /**
     * 非自愿退票申请理由必填，如果是自愿退票，该参数可以不传，：21:航班变动（不满足航变要求的会提交失败）;22:前段变动导致后段无法乘坐（需要填写备注信息）;23:只退票不退款（此理由退款金额为0，请慎重选择）;24:病退（需要上传附件才能提交成功）;25:疫情（不满足疫情要求的会提交失败）;26:拒载、超售（需要上传附件才能提交成功）;27:其他（迫降、降舱等）;
     */
    val refundReason: Int = 0,
    /**
     * 申请的乘机人信息
     */
    val applyPassengerInfo: List<TCRefundApplyPassengerInfo> = emptyList(),
    /**
     * 退票航段信息【注：传入需要退订的原行程航段，相应改签航段会关联退掉，为空表示退该乘客的所有航段】
     */
    val segmentInfo: List<TCRefundApplyFlightInfo> = emptyList(),
) : Serializable

data class TCRefundApplyPassengerInfo(
    /**
     * 乘客证件号码
     */
    val certNo: String,
    /**
     * 姓名本次调整：跟下单传的格式保持一致；“张三”or“张/三”
     */
    val name: String,
    /**
     * 票号
     */
    val ticketNo: String,
) : Serializable

data class TCRefundApplyFlightInfo(
    /**
     * 1:去程；2:回程新增
     */
    val segmentType: Int,
    /**
     * 航段序号新增
     */
    val segmentNo: Int,
    /**
     * 航班号，如CA1234
     */
    val flightNo: String,
    /**
     * 出票航司二字码
     */
    val airline: Int,
    /**
     * 舱位
     */
    val cabinCode: Int,
    /**
     * 是否共享，0：否 1是
     */
    val flightShare: Int,
    /**
     * 共享航班号
     */
    val shareFlightNo: String,
    /**
     * 实际承运航司
     */
    val realCarrier: String,
    /**
     * 出发机场三字码
     */
    val depAirport: String,
    /**
     * 到达机场三字码
     */
    val arrAirport: String,
    /**
     * 起飞时间 格式：yyyy-MM-dd HH:mm
     */
    val depDateTime: String,
    /**
     * 到达时间 格式：yyyy-MM-dd HH:mm
     */
    val arrDateTime: String,
) : Serializable

/**
 * 退票申请-上传附件
 */
data class TCRefundFileUploadRequest(
    /**
     * 订单流水号
     */
    val orderSerialNo: String,
    /**
     * 退票单号
     */
    val taskSerialNo: String,
    /**
     * 三方订单号
     */
    val outOrderNo: String,
    /**
     * 上传的文件(目前仅支持一个文件上传)
     */
    val file: List<TCRefundFileUploadFile>,
) : Serializable

data class TCRefundFileUploadFile(
    val fileByte: ByteArray,
    val fileName: String,
) : Serializable

/**
 * 退票列表接口
 */
data class TCRefundListRequest(
    /**
     * 退票单创建开始时间,格式:yyyy-MM-dd HH:mm:ss
     */
    val startCreateTime: String,
    /**
     * 退票单创建截止时间,格式:yyyy-MM-dd HH:mm:ss
     */
    val endCreateTime: String,
    /**
     * 业务类型（0-国内 1-国际）
     */
    val businessLineType: String,
    /**
     * 退票单状态，不传返回全部状态订单，0:待审核；1:待确认；2:待处理；3:退订中；4:退订完成；5:退订取消；6:暂不能退票；7:待核价（仅国际订单才有）；
     */
    val orderStates: String,
    /**
     * 请求唯一值
     */
    val traceId: String,
) : Serializable

/**
 * 退票详情
 */
data class TCRefundDetailRequest(
    /**
     * 易旅行退票单号（RB开头）
     */
    val refundSerialNo: String,
) : Serializable

data class TCRefundOrderConfirmRequest(
    /**
     * 易旅行退票订单号
     */
    val taskSerialNo: String,
    /**
     * 外部订单号(代理商自己产生的订单号) : Serializable
     */
    val purchaserOrderSerialNo: String?,
    /**
     * 是否确认退票，1:确认退票；2:取消退票
     */
    val confirmRefund: String,
) : Serializable