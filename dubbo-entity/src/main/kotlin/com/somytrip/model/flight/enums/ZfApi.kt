package com.somytrip.model.flight.enums

/**
 * @Description: 智飞科技APIs
 * @author: pigeon
 * @created: 2025-01-10 16:34
 */
enum class ZfApi(
    private val uri: String,
) {
    /**
     * shopping
     */
    SHOP("/api/v2/flight/shopping"),

    /**
     * 精确查询
     */
    PRICING("/api/v2/flight/pricing"),

    /**
     * 规则
     */
    VERIFY("/api/v2/flight/solutions/{}/verification"),

    /**
     * 创建订单
     */
    BOOKING("/api/v1/flight/orders"),

    /**
     * 支付订单
     */
    PAY("/api/v1/flight/orders/{}/payments"),

    /**
     * 取消订单
     */
    CANCEL("/api/v1/flight/orders/{}"),

    /**
     * 订单详情
     */
    DETAIL("/api/v1/flight/orders/{}"),

    /**
     * 改签列表
     */
    CHANGE_LIST("/api/v1/flight/orders/{}/change/search"),

    /**
     * 改签申请
     */
    CHANGE("/api/v1/flight/orders/{}/change"),

    /**
     * 退票申请
     */
    REFUND("/api/v1/flight/orders/{}/refund"),

    /**
     * 确认退票
     */
    REFUND_CONFIRM("/api/v1/flight/orders/{}/confirmation"),
    ;

    fun uri(): String = uri
}