package com.somytrip.model.flight.common

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-08-07 18:00
 */
object CommonValFunc {
    const val TC_REST_API_SUCCESS_CODE = "HH000000";
    const val ADT = "ADT";
    const val CHD = "CHD";
    const val INF = "INF";
    const val TC_SERVICE_TIME_SPLIT_1 = '&';
    const val TC_SERVICE_TIME_SPLIT_2 = '|';
    const val EN_NAME_SPLIT = '/';
    const val TC_SERVICE_TIME_SPLIT_3 = '-';
    const val FR24_SEGMENT_SPLIT = '^';
    const val FR24_E = 'e';
    const val FR24_TAX_YQ = "FS";
    const val FR24_TAX_TAX = "AT";
    const val FR24_DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm";
    const val DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm";
    const val BIRTHDAY_DATE_FORMAT = "yyyy-MM-dd";
    val DATE_HOUR_SUBSTRING_RANGE = 11..12
    val DATE_MINUTE_SUBSTRING_RANGE = 14..15
    const val FR24_CODE_SUCCESS = "000000";
    const val FR24_MESSAGE_SUCCESS = "success";
    val TC_MESSAGE_REGEX = Regex("\"message\":\"(.*?)\"[,)}]")
    val TC_WEEK_TIME = listOf(7, 1, 2, 3, 4, 5, 6)
    const val REDIS_KEY_FOR_ACC_RULE_ITEM = "acc:rule:item:";
    const val REDIS_KEY_FOR_OLD_ACC_RULE_ITEM = "old:acc:rule:item:";
    const val REDIS_KEY_FOR_SHOP_LIST = "shop:list:";
    const val REDIS_KEY_FOR_SHOP_LIST_V2 = "shop:list:v2:";
    const val REDIS_KEY_FOR_MAP = "air:map:"
    const val BUCKET = "flight"

    fun formatIntToDuration(v: Int): String {
        val day = v / (60 * 24)
        val hour = (v / 60) % 24
        val min = v % 60

        return when {
            day > 0 && hour > 0 && min > 0 -> "${day}d${hour}h${min}m"
            day == 0 && hour > 0 && min > 0 -> "${hour}h${min}m"
            day == 0 && hour == 0 && min > 0 -> "${min}m"
            else -> ""
        }
    }
}
