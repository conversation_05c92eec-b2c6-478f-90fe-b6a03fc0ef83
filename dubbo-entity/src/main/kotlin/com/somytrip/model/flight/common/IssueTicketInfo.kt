package com.somytrip.model.flight.common

import com.somytrip.model.flight.enums.BillMaterialsEnum
import com.somytrip.model.flight.enums.BillMethodEnum
import com.somytrip.model.flight.enums.GdsEnum
import com.somytrip.model.flight.tc.TCAccurateSearchItemIssueTicketInfo

/**
 * @Description: 出票信息
 * @author: pigeon
 * @created: 2024-08-22 11:06
 */
data class IssueTicketInfo(
    /**
     * 是否自动出票
     */
    val autoTicket: Boolean = false,
    /**
     * 是否换pnr出票
     */
    val isChangePnrIssueTicket: Boolean = false,
    /**
     * 开票航司
     */
    val billAirline: String = "",
    /**
     * GDS 全球票务系统
     */
    val gds: GdsEnum = GdsEnum.Unknown,
    /**
     * 开票方式
     */
    val billMethod: BillMethodEnum = BillMethodEnum.OTHER,
    /**
     * 开票材料
     */
    val billMaterials: BillMaterialsEnum = BillMaterialsEnum.NOT_PROVIDER,
) {
    constructor(info: TCAccurateSearchItemIssueTicketInfo, billMethod: Int, billMaterials: Int) : this(
        autoTicket = info.autoTicket == 1,
        isChangePnrIssueTicket = info.changePnrCode == 2,
        gds = GdsEnum.tcToEnum(info.reservationType),
        billAirline = info.issueTicketCarrier,
        billMethod = BillMethodEnum.tcToEnum(billMethod),
        billMaterials = BillMaterialsEnum.tcToEnum(billMaterials),
    )
}
