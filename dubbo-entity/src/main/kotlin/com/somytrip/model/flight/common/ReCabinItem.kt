package com.somytrip.model.flight.common

import com.somytrip.model.flight.enums.CabinClassEnum
import com.somytrip.model.flight.enums.PassengerTypeEnum

/**
 * @Description: 舱位信息
 * @author: pigeon
 * @created: 2024-08-20 10:04
 */
data class ReCabinItem(
    /**
     * 乘客类型
     */
    val passengerType: PassengerTypeEnum = PassengerTypeEnum.ADULT,
    /**
     * 价格描述
     */
    val priceInfo: ReShopPriceInfo = ReShopPriceInfo(),
    /**
     * 舱位等级
     */
    val cabinClass: CabinClassEnum = CabinClassEnum.ECONOMY_CLASS,
    /**
     * 舱位等级 display
     */
    val cabinClassDisplay: String = cabinClass.display,
    /**
     * 舱位编号
     */
    val cabinCode: String = "",
    /**
     * 剩余座位数量
     */
    val seat: Int = 0,
    /**
     * segment同segmentIndex
     */
    val segmentIndex: Int = 1,
)
