package com.somytrip.model.flight.enums

/**
 * @Description: 时间段枚举
 * @author: pigeon
 * @created: 2024-03-26 11:22
 */
enum class TimeRangeEnum(
    val display: String,
    private val range: IntRange,
) {
    BEFORE_DAWN("00:00-06:00", 0 until 6),
    MOR<PERSON>("06:00-12:00", 6 until 12),
    NOON("12:00-18:00", 12 until 18),
    NIGHT("18:00-24:00", 18 until 24),
    ;

    fun timeInterval() = range
}