package com.somytrip.model.flight.common

/**
 * @Description: 改签结果
 * @author: pigeon
 * @created: 2024-10-31 9:34
 */
data class ReShopRsp(
    val segments: List<SegmentItem> = emptyList(),
    val cabins: List<ReCabinItem> = emptyList(),
    val productInfo: ProductInfo = ProductInfo(),
    val sourceType: Int = 0,
    val durationTotal: String = "",
    val prices: List<ReShopPriceInfo> = emptyList(),
)
