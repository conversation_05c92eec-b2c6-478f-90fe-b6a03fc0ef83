package com.somytrip.model.flight.vo

import cn.hutool.core.date.LocalDateTimeUtil
import com.somytrip.model.flight.common.CabinItem
import com.somytrip.model.flight.common.ProductInfo
import com.somytrip.model.flight.common.SegmentItem
import com.somytrip.model.flight.enums.*
import java.time.LocalDateTime
import java.util.Locale

/**
 * @Description: 查询操作实体
 * @author: pigeon
 * @created: 2024-08-27 10:07
 */

data class ShopListReq(
    val adultNum: Int = 0,
    val childNum: Int = 0,
    val infantNum: Int = 0,
    val sourceType: Int = 0,
    val tripType: Int = 0,
    val segments: List<SegmentVo> = emptyList(),
    val screenCondition: ScreenConditionVo = ScreenConditionVo(),
    val passengers: List<PassengerVo> = emptyList(),
    var timezone: String = "UTC+8"
) {

    constructor(
        adultNum: Int,
        childNum: Int,
        infantNum: Int,
        sourceType: Int,
        tripType: Int,
        segments: List<SegmentVo>,
        screenCondition: ScreenConditionVo,
        passengers: List<PassengerVo>,
    ) : this(
        adultNum,
        childNum,
        infantNum,
        sourceType,
        tripType,
        segments,
        screenCondition,
        passengers,
        "UTC+8"
    )

    fun check(): Boolean {
        val now = LocalDateTime.now().toLocalDate()
        return this.segments.all { now <= LocalDateTimeUtil.parseDate(it.departTime, "yyyy-MM-dd") }
    }

    fun adultNum(): Int {
        return if (adultNum == 0) {
            passengers.count { it.type == PassengerTypeEnum.ADULT }
        } else {
            adultNum
        }
    }

    fun childNum(): Int {
        return if (childNum == 0) {
            passengers.count { it.type == PassengerTypeEnum.CHILD }
        } else {
            childNum
        }
    }

    fun infantNum(): Int {
        return if (infantNum == 0) {
            passengers.count { it.type == PassengerTypeEnum.INFANT }
        } else {
            infantNum
        }
    }
}

data class ScreenConditionVo(
    val stylePreference: List<StylePreferenceEnum> = emptyList(),
    val timeRange: List<TimeRangeEnum> = emptyList(),
    val cabin: List<CabinClassEnum> = emptyList(),
    val airline: List<String> = emptyList(),
    val airports: List<String> = emptyList(),
    val sort: SortEnum = SortEnum.PRICE_LOW_HIGH,
    val flightNos: List<String> = emptyList(),
)

data class PassengerVo(
    val type: PassengerTypeEnum = PassengerTypeEnum.ADULT,
    val num: Int = 1,
)

data class SegmentVo(
    val departCode: String = "",
    val arriveCode: String = "",
    val departTime: String = "",
)

data class AccurateReq(
    val adultNum: Int = 0,
    val childNum: Int = 0,
    val infantNum: Int = 0,
    /**
     * 0 国内
     * 1 国际
     */
    val sourceType: Int = 0,
    /**
     * 航段类型
     * 0 单程
     * 1 往返
     * ... 多段
     */
    val tripType: Int = 0,
    /**
     * 航司
     */
    val mainAirline: String = "",
    /**
     * 是否启用推荐
     */
    val recommend: Int = 0,
    /**
     * 下单标识
     */
    val productInfos: List<ProductInfo> = emptyList(),
    /**
     * 航段
     */
    val segments: List<SegmentItem> = emptyList(),
    /**
     * 乘客舱等
     */
    val cabins: List<CabinItem> = emptyList(),
    var locale: Locale = Locale.CHINA
) {
    constructor(
        adultNum: Int,
        childNum: Int,
        infantNum: Int,
        sourceType: Int,
        tripType: Int,
        mainAirline: String,
        recommend: Int,
        productInfos: List<ProductInfo>,
        segments: List<SegmentItem>,
        cabins: List<CabinItem>,
    ) : this(
        adultNum,
        childNum,
        infantNum,
        sourceType,
        tripType,
        mainAirline,
        recommend,
        productInfos,
        segments,
        cabins,
        Locale.CHINA
    )
}


data class ChangeShopReq(
    val orderNo: String = "",
    val segments: List<SegmentVo> = emptyList(),
    /**
     * 改签乘客下标
     */
    val tickets: List<String> = emptyList(),
)

data class ChangeApplyReq(
    val orderNo: String = "",
    val productInfo: ProductInfo = ProductInfo(),
    /**
     * 改签票号
     */
    val passengers: List<String> = emptyList(),
    /**
     * 新需要改签segments
     */
    val changeSegments: List<SegmentItem> = emptyList(),
    val fileKeys: List<String> = emptyList(),
)