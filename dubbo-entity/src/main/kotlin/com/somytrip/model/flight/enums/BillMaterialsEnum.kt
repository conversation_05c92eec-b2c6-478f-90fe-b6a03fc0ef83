package com.somytrip.model.flight.enums


/**
 * @Description: 开票材料
 * @author: pigeon
 * @created: 2024-08-22 11:13
 */
enum class BillMaterialsEnum(
    val tcValue: Int,
    val display: String,
) {
    /**
     * 行程单
     */
    TRAVEL_ITINERARY(0, "行程单"),
    INVOICE(1, "发票"),
    ELECTRONIC_ITINERARY(2, "电子行程单"),
    TRAVEL_ITINERARY_AND_DIFF_INVOICE(3, "行程单+差额发票"),
    NOT_PROVIDER(4, "不提供"),
    ;

    companion object {
        fun tcToEnum(value: Int): BillMaterialsEnum {
            return entries.find { it.tcValue == value } ?: NOT_PROVIDER
        }
    }
}