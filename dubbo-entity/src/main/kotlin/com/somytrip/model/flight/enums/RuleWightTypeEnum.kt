package com.somytrip.model.flight.enums


/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-12 14:41
 */
enum class RuleWightTypeEnum(
    val tcValue: Int,
    val display: String,
) {
    ONLY_ONE(1, "单价重量"),
    TOTAL_COUNT(2, "总重量"),
    DEFAULT(-1, "未知"),
    ;

    companion object {
        fun tcValueToEnums(value: Int): RuleWightTypeEnum {
            return entries.find { it.tcValue == value }
                ?: DEFAULT
        }
    }
}