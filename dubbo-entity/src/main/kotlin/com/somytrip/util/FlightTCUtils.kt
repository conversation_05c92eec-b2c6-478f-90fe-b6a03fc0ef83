package com.somytrip.util

import com.somytrip.model.flight.common.CommonValFunc
import java.time.Duration
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-12 14:56
 */
object FlightTCUtils {

    fun calculateMinutesDifference(startTimeStr: String, endTimeStr: String): Long {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        // 解析字符串为 LocalDateTime 对象
        val startTime = LocalDateTime.parse(startTimeStr, formatter)
        val endTime = LocalDateTime.parse(endTimeStr, formatter)
        // 计算两个时间点的持续时间
        val duration = Duration.between(startTime, endTime)
        // 将持续时间转换为分钟
        return duration.toMinutes()
    }

    fun convertMin(startTimeStr: String, endTimeStr: String): String {
        return convertMin(calculateMinutesDifference(startTimeStr, endTimeStr).toInt())
    }

    /**
     * 可能出现的情况
     * 1天0时1分 return 1d1m
     * 0天0时30分 return 30m
     * 0天2时0分 return 2h
     * 1天2时0分 return 1d2h
     */
    fun convertMin(v: Int): String {
        return CommonValFunc.formatIntToDuration(v);
    }


    fun convertMeal(v: Int): String {
        return when (v) {
            1 -> "有餐食";
            2 -> "有餐食";
            else -> ""
        }
    }
}