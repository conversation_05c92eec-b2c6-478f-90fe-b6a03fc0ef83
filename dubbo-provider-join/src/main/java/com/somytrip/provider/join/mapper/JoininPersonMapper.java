package com.somytrip.provider.join.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.bean.JoininPerson;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

@Deprecated
@Component
public interface JoininPersonMapper extends BaseMapper<JoininPerson> {

    String typeStr = "is_extension_worker, is_guide, is_driver, is_chartered_driver, is_interpreter";
    String typeValues = "#{typeList[0]}, #{typeList[1]}, #{typeList[2]}, #{typeList[3]}, #{typeList[4]}";
    String typeUpdateStr = "is_extension_worker = #{typeList[0]}, is_guide = #{typeList[1]}, " +
            "is_driver = #{typeList[2]}, is_chartered_driver = #{typeList[3]}, is_interpreter = #{typeList[4]}, ";

//    @Insert("replace into joinin_person (uid, " + typeStr + ", create_time, update_time) " +
//            "values (#{uid}, " + typeValues + ", #{createTime}, #{createTime})")
//    Integer initPerson(String uid, Item<Boolean> typeList, String createTime);

    @Insert("replace into joinin_person (uid, " + typeStr + ", real_name, id_type, id_number, id_pic1, id_pic2, " +
            "email, create_time, update_time) values (#{uid}, " + typeValues + ", #{realName}, #{idType}, " +
            "#{idNumber}, #{idPic1}, #{idPic2}, #{email}, #{createTime}, #{createTime}) ")
    Integer initPerson(String uid, List<Boolean> typeList, String realName, String idType, String idNumber,
                       String idPic1, String idPic2, String email, String createTime);

    @Update("update joinin_person set real_name = #{realName}, id_type = #{idType}, id_number = #{idNumber}, " +
            "id_pic1 = #{idPic1}, id_pic2 = #{idPic2}, email = #{email}, update_time = #{updateTime} " +
            "where uid = #{uid}")
    Integer joinID(String uid, String realName, String idType, String idNumber, String idPic1, String idPic2,
                   String email, String updateTime);

    @Update("update joinin_person set bank_city = #{bankCity}, bank_name = #{bankName}, bank_detail = #{bankDetail}, " +
            "sub_bank_name = #{subBankName}, bank_number = #{bankNumber}, update_time = #{updateTime}" +
            " where uid = #{uid}")
    Integer joinAccount(String uid, String bankCity, String bankName, String bankDetail, String subBankName,
                        String bankNumber, String updateTime);

    @Update("update joinin_person set phone = #{phone}, safe_code = #{safeCode}, update_time = #{updateTime} " +
            "where uid = #{uid}")
    Integer joinSafeCode(String uid, String phone, String safeCode, String updateTime);

    @Update("update joinin_person set pic_guide = #{path}, update_time = #{updateTime} where uid = #{uid}")
    Integer joinGuidePic(String uid, String path, String updateTime);

    @Update("update joinin_person set pic_chartered_driver = #{path}, update_time = #{updateTime} where uid = #{uid}")
    Integer joinCharteredDriverPic(String uid, String path, String updateTime);

    @Update("update joinin_person set pic_driver = #{path}, update_time = #{updateTime} where uid = #{uid}")
    Integer joinDriverPic(String uid, String path, String updateTime);

    @Update("update joinin_person set pic_interpreter = #{path}, update_time = #{updateTime} where uid = #{uid}")
    Integer joinInterpreterPic(String uid, String path, String updateTime);

    @Update("update joinin_person set status = '已提交', update_time = #{updateTime} where uid = #{uid}")
    Integer joinCommit(String uid, String updateTime);

    boolean checkIsTPlus7(String uid);

    /**
     * 查询个人加盟距今多少天
     */
    @Select("select datediff(now(), create_time) from joinin_person " +
            "where uid = #{uid} and status = '已提交'")
    Integer queryPersonJoinDays(String uid);

    @Select("select count(*) from joinin_person where uid = #{uid} and status = '已提交'")
    Integer queryPersonJoin(String uid);
}
