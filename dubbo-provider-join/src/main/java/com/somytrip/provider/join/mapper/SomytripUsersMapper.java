package com.somytrip.provider.join.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.dto.user.SomytripUsers;
import org.apache.ibatis.annotations.*;

import java.util.Date;

@Mapper
public interface SomytripUsersMapper extends BaseMapper<SomytripUsers> {
    @Override
    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    int insert(SomytripUsers somytripUsers);

    @Insert("INSERT INTO trip_gpt_user_control " +
            " (userId, today, create_time, count, update_time) " +
            " VALUES (#{userId}, #{stringDate}, NOW(), #{count}, NOW()) " +
            " ON DUPLICATE KEY UPDATE count = #{count}, update_time = NOW()")
    int insertIntoTripGptUserControl(@Param("userId") String userId,
                                     @Param("stringDate") String stringDate,
                                     @Param("count") int count);


    @Select("select * from wx_user_thousand_sign order by id desc limit 1")
    JSONObject queryThousandSign();

    @Select("select value from config where name = 'thousand_sign_mobile'")
    String queryThousandSignMobile();

    @Update("update wx_user_thousand_sign set is_sign = true where count = #{count}")
    void thousandSign(@Param("count") String count);

    @Select("select value = (select mobile from somytrip_users where id = #{uid})" +
            " from config where name = 'vip_mobile'")
    boolean isVVip(@Param("uid") Integer uid);

    @Select("select end_time from member_card_user where user_id=#{userId} and end_time > NOW()")
    Date vipInfo(@Param("userId") Integer userId);

    @Select("select id from somytrip_users where id=#{userId} and is_del=false")
    Boolean queryUserExist(@Param("userId") Integer userId);

    @Update("UPDATE somytrip_users SET nickname = #{nickname}, auth_image = #{authImage}, gender = #{gender}, " +
            "country = #{country}, province = #{province}, city = #{city}, age = #{age}, " +
            "constellation = #{constellation}, personalized_signature = #{personalizedSignature}, " +
            "personality_label = #{personalityLabel}, display_filed = #{displayFiled} " +
            "WHERE id = #{id}")
    void editUser(SomytripUsers user);
}
