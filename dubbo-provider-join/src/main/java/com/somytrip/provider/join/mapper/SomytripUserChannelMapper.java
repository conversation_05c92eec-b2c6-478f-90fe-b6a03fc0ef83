package com.somytrip.provider.join.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.dto.user.SomytripUserChannel;
import org.apache.ibatis.annotations.Select;

@Deprecated(forRemoval = true)
public interface SomytripUserChannelMapper extends BaseMapper<SomytripUserChannel> {

    @Select("<script> select * from somytrip_user_channels" +
            "  where channel=#{channel} " +
            "    and channel_type=#{channelType} " +
            "<if test='openId != null and unionId != null'>and channel_unique_id=#{unionId}</if>" +
            "<if test='openId != null'>and channel_id=#{openId}</if>" +
            " limit 1 </script>")
    SomytripUserChannel getChannelByOpenIdOrUnionId(String channel, String channelType, String openId, String unionId);

}
