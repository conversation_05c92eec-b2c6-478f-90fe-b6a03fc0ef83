package com.somytrip.provider.join.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.somytrip.api.service.join.WxUserQrService;
import com.somytrip.bean.WxUserQr;
import com.somytrip.provider.join.mapper.WxUserQrMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.join.service.impl
 * @className: WxUserQrServiceImpl
 * @author: shadow
 * @description:
 * @date: 2024/4/30 17:34
 * @version: 1.0
 */
@Service
public class WxUserQrServiceImpl implements WxUserQrService {

    @Resource
    private WxUserQrMapper wxUserQrMapper;

    @Override
    public String getQrPath(String uid) {
        QueryWrapper<WxUserQr> qw = new QueryWrapper<>();
        qw.eq("uid", uid);
        WxUserQr qr = wxUserQrMapper.selectOne(qw);
        if (qr == null) {
            return null;
        }
        return qr.getQr();
    }

    @Override
    public void saveQrPath(String uid, String path) {

        WxUserQr qr = new WxUserQr();
        qr.setUid(uid);
        qr.setQr(path);
        wxUserQrMapper.insertOrUpdate(qr);
    }
}
