package com.somytrip.provider.join.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.bean.JoininHotel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

@Deprecated(forRemoval = true)
@Component
public interface JoininHotelMapper extends BaseMapper<JoininHotel> {

    // 查询是否存在，根据用户ID
    @Select("select t1.*, t2.audit_status, t2.opinion from joinin_hotel t1 left join join_audit t2 on t1.id=t2.join_id and t2.join_type='酒店加盟' where t1.id=#{uid}")
    JoininHotel queryJoininHotel(int uid);

    // 查询酒店审核信息列表
    @Select("select t1.*, t2.audit_status from joinin_hotel t1 left join join_audit t2 on t1.id=t2.join_id and t2.join_type='酒店加盟'")
    List<JoininHotel> queryJoinInHotelList();

    // 第一页数据更新   				// Integer uid, String businessLicenseFile, String coName, String socialCode, String email, String idCardPic1, String idCardPic2, String manager, String managerId
    @Update("update joinin_hotel set business_license_file=#{businessLicenseFile}, co_name=#{coName}, " +
            "social_code=#{socialCode}, email=#{email}, "
            + "id_card_pic1=#{idCardPic1}, id_card_pic2=#{idCardPic2}, manager=#{manager}, manager_id=#{managerId}, step='身价认证', create_time=SYSDATE() where uid=#{uid}")
    int updateFirstPage(@Param("uid") int uid, @Param("businessLicenseFile") String businessLicenseFile, @Param("coName") String coName,
                        @Param("socialCode") String socialCode, @Param("email") String email, @Param("idCardPic1") String idCardPic1,
                        @Param("idCardPic2") String idCardPic2, @Param("manager") String manager, @Param("managerId") String managerId);

    // 收款账户数据更新
    @Update("update joinin_hotel set account_holder=#{accountHolder}, hold_city=#{holdCity}, " +
            "hold_address=#{holdAddress}, hold_bank=#{holdBank}, "
            + "hold_bank_sub=#{holdBankSub}, account=#{account}, step='收款账户', create_time=SYSDATE() where uid=#{uid}")
    int updateBankInfo(@Param("uid") int uid, @Param("accountHolder") String accountHolder, @Param("holdCity") String holdCity,
                       @Param("holdAddress") String holdAddress, @Param("holdBank") String holdBank, @Param("holdBankSub") String holdBankSub, @Param("account") String account);

    // 安全码数据更新
    @Update("update joinin_hotel set phone=#{phone}, key_code=#{keyCode}, step='安全码设置', create_time=SYSDATE() where uid=#{uid}")
    int updateSafeKeyInfo(@Param("uid") int uid, @Param("phone") String phone, @Param("keyCode") String keyCode);

    // 转账数据更新
    @Update("update joinin_hotel set amount=#{amount}, smt_bank_name=#{smtBankName}, smt_account=#{smtAccount}, step='账户确认', create_time=SYSDATE() where uid=#{uid}")
    int updatePayInfo1(@Param("uid") int uid, @Param("amount") String amount, @Param("smtBankName") String smtBankName, @Param("smtAccount") String smtAccount);

    // 转账数据更新(提交转账)
    @Update("update joinin_hotel set pay_status=1,pay_commit_time=SYSDATE(), create_time=SYSDATE() where uid=#{uid}")
    int updatePayInfo2(@Param("uid") int uid);

    // 转账数据更新(确认转账)
    @Update("update joinin_hotel set pay_status=2, pay_ok_time=SYSDATE(), create_time=SYSDATE() where uid=#{uid}")
    int updatePayInfo3(@Param("uid") int uid);

    // 协议文件数据更新
    @Update("update joinin_hotel set protocols_file=#{filePath}, step='加盟完成', create_time=SYSDATE() where uid=#{uid}")
    int updateProtocolInfo(@Param("uid") int uid, @Param("filePath") String filePath);

}
