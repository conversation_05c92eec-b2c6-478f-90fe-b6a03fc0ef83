package com.somytrip.provider.join.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.bean.WxUserAccountBook;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface AccountBookMapper extends BaseMapper<WxUserAccountBook> {

    // 查询账本数据
    @Select("select id, name, budget from wx_user_account_book where uid = #{uid}")
    List<JSONObject> accountBooks(String uid);

    @Update("update wx_user_account_book set name = #{name} where id = #{id}")
    Integer editBookName(String id, String name);

    // 查询指定账本账目
    @Select("select id, type, payout, create_time date from wx_user_account_record " +
            "where account_book_id = #{id} order by id desc")
    List<JSONObject> accountRecords(String id);

    // 添加账目记录
    @Insert("insert into wx_user_account_record values (null, #{uid}, #{id}, #{type}, #{payout}, #{time})")
    Integer addAccountRecord(String uid, String id, String type, String payout, String time);

    // 分类统计账目
    @Select("select type name, count(*) count, sum(payout) num, " +
            "truncate(sum(payout) / (select sum(payout) from wx_user_account_record where account_book_id = #{id}) * 100, 2) ratio " +
            "from wx_user_account_record where account_book_id = #{id} group by type order by num desc")
    List<JSONObject> accountTypes(String id);
}
