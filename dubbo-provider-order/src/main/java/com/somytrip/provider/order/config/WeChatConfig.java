package com.somytrip.provider.order.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Description:
 * @author: pigeon
 * @created: 2023-11-05 10:56
 */
@Configuration
@ConfigurationProperties("wechat.config")
@Data
public class WeChatConfig {
    /**
     * 小程序 appid
     */
    private String appAppletId;
    /**
     * 小程序端用户登陆授权的secret
     */
    private String appAppletSecret;
    /**
     * 微信支付v3关键参数
     */
    private String apiV3Key;
    /**
     * application appid
     */
    private String appApplicationId;
    /**
     * app端用户登陆授权的secret
     */
    private String appApplicationSecret;
    /**
     * 商户号
     */
    private String mchId;
    /**
     * 商户证书序列号
     */
    private String merchantSerialNumber;
    /**
     * 商户证书本地路径
     */
    private String generateCertificatesPath;
    /**
     * RSA密钥证书
     */
    private String privateMerchKeyPath;
}
