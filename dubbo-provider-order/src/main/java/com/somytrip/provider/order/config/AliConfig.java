package com.somytrip.provider.order.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @创建时间 2023/8/15 9:14
 */
@Configuration
@ConfigurationProperties(prefix = "ali.config")
@Data
public class AliConfig {
    private Applet applet;

    public record Applet(
            String protocol,
            String gatewayHost,
            String signType,
            String appId,
            String merchantPrivateKey,
            String alipayPublicKey,
            String encryptKey
    ) {
    }
}
