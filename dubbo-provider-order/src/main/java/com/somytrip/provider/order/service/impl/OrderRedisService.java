package com.somytrip.provider.order.service.impl;

import com.somytrip.constant.OrderConstant;
import com.somytrip.utils.RedisKeyUtil;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class OrderRedisService {

    /**
     * 订单编号过期时间，单位：秒
     */
    private final Integer ORDER_NO_EXPIRE = 60;
    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 判断key是否存在
     *
     * @param orderNo
     * @return
     */
    public Boolean existOrderNoFlag(String orderNo) {
        String orderNoCacheKey = RedisKeyUtil.redisKeyReplace(OrderConstant.ORDER_NO_CACHE_KEY, orderNo);
        return redisTemplate.hasKey(orderNoCacheKey);
    }

    /**
     * 设置订单编号缓存
     *
     * @param orderNo
     */
    public void setOrderNoCache(String orderNo) {
        String orderNoCacheKey = RedisKeyUtil.redisKeyReplace(OrderConstant.ORDER_NO_CACHE_KEY, orderNo);
        redisTemplate.opsForValue().set(orderNoCacheKey, "yes", ORDER_NO_EXPIRE, TimeUnit.SECONDS);
    }

    /**
     * 删除订单编号缓存
     *
     * @param orderNo
     */
    public void delOrderNoCache(String orderNo) {
        String orderNoCacheKey = RedisKeyUtil.redisKeyReplace(OrderConstant.ORDER_NO_CACHE_KEY, orderNo);
        redisTemplate.delete(orderNoCacheKey);
    }
}
