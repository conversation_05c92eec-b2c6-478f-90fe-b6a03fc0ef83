package com.somytrip.provider.order.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RedisPubSubService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ChannelTopic channelTopic;

    public void publish(String message) {
        log.info("订单消息发布，参数：{}", message);
        redisTemplate.convertAndSend(channelTopic.getTopic(), message);
    }
}
