package com.somytrip.provider.order.config;

import org.springframework.context.annotation.Configuration;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-01-31 14:59
 */
@Configuration
public class WebConfig {
//    @Bean
//    WebClient webClient(ObjectMapper objectMapper) {
//        return WebClient.builder()
//                .baseUrl("https://qyapi.weixin.qq.com")
//                .build();
//    }

//    @SneakyThrows
//    @Bean
//    BotMethodService postClient(WebClient webClient) {
//        HttpServiceProxyFactory httpServiceProxyFactory =
//                HttpServiceProxyFactory.builder(WebClientAdapter.forClient(webClient))
//                        .build();
//        return httpServiceProxyFactory.createClient(BotMethodService.class);
//    }
}
