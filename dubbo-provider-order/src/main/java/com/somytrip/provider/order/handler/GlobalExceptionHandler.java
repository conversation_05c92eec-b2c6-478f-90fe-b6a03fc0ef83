package com.somytrip.provider.order.handler;

import com.somytrip.entity.response.ResponseResult;
import com.somytrip.exception.OrderException;
import com.somytrip.utils.JSONResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * @Description: 全局异常处理
 * @author: pigeon
 * @created: 2023-11-03 17:21
 */
@ControllerAdvice
@RestControllerAdvice(basePackages = {"com.somytrip.order.controller"})
@Slf4j
public class GlobalExceptionHandler {
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseResult handleValidationException(MethodArgumentNotValidException ex) {
        // 获取具体的错误信息
        String errorMsg = Objects.requireNonNull(ex.getBindingResult().getFieldError()).getDefaultMessage();
        log.info("参数校验出错: {}", errorMsg);
        // 返回错误信息（这里可以根据实际需求返回自定义的错误对象）
        return JSONResultUtils.fail(HttpStatus.BAD_REQUEST.value(), errorMsg);
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(OrderException.class)
    public ResponseResult handleOrderException(OrderException ex) {
        // 获取具体的错误信息
        log.info("参数校验出错: {}", ex.getMessage());
        // 返回错误信息（这里可以根据实际需求返回自定义的错误对象）
        return JSONResultUtils.fail(ex.getCode(), ex.getMessage());
    }
}
