package com.somytrip.provider.order.mq.impl;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.somytrip.api.service.SmsService;
import com.somytrip.api.service.bot.BotService;
import com.somytrip.api.service.flight.ManyFlightService;
import com.somytrip.api.service.hotel.v2.HotelOrderV2Service;
import com.somytrip.api.service.order.OrderBaseService;
import com.somytrip.api.service.order.OrderStateService;
import com.somytrip.api.service.pay.YeePayRefundOrderService;
import com.somytrip.api.service.ticket.ManyTicketService;
import com.somytrip.api.service.tourism.TourismPayCompleteService;
import com.somytrip.entity.dto.hotel.CancelOrderDto;
import com.somytrip.entity.dto.order.*;
import com.somytrip.entity.enums.SmsTypeEnums;
import com.somytrip.entity.enums.hotel.OrderCancelCode;
import com.somytrip.entity.enums.order.*;
import com.somytrip.entity.enums.thirdparty.ThirdServiceTypeEnum;
import com.somytrip.entity.po.bot.Text;
import com.somytrip.entity.po.bot.TextMessage;
import com.somytrip.entity.response.ResponseResult;
import com.somytrip.entity.special.SpecialProduct;
import com.somytrip.entity.vo.order.QueryOrderVo;
import com.somytrip.entity.vo.yeepay.YeePayCreateRefundOrderVo;
import com.somytrip.provider.order.mq.MqReceiverService;
import com.somytrip.provider.order.mq.MqSenderService;
import com.somytrip.utils.CommonUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @Description:
 * @author: pigeon
 * @created: 2023-11-15 15:01
 */

@Slf4j
@Service
public class MqReceiverImpl implements MqReceiverService {
    @DubboReference(check = false)
    private BotService botService;
    @Resource
    private OrderBaseService orderBaseService;
    @Resource
    private YeePayRefundOrderService refundOrderService;
    @Resource
    private TourismPayCompleteService tourismPayCompleteService;
    @DubboReference(check = false)
    private SmsService smsService;
    @DubboReference
    private HotelOrderV2Service hotelOrderV2Service;
    @DubboReference(check = false)
    private ManyTicketService manyTicketService;
    @DubboReference(check = false)
    private ManyFlightService manyFlightService;
    @Resource
    private OrderStateService orderStateService;
    @Resource
    private MqSenderService mqSenderService;

    @Override
    public boolean consumer(ConsumerRecord<String, String> record) {
        log.info("receive message: {}", record.value());
        OrderMessageMqDTO dto = JSON.parseObject(record.value(), OrderMessageMqDTO.class);
        // 签证
        if (EnumUtil.equals(OrderBusinessTypeEnum.VISA, dto.getBusinessType())) {
            if (!this.dealVisaOrder(dto)) {
                log.error("VISA 消费失败: {}", record.value());
                botService.sendMessage("VISA mq消费失败, 请求参数: " + record.value());
                return false;
            }
        }
        // 个人加盟服务
        if (EnumUtil.equals(OrderBusinessTypeEnum.JPS, dto.getBusinessType())) {
            if (!this.dealJpsOrder(dto)) {
                log.error("JPS  消费失败: {}", record.value());
                botService.sendMessage("JPS mq消费失败，请求参数：" + record.value());
                return false;
            }
        }
        // 酒店
        if (EnumUtil.equals(OrderBusinessTypeEnum.HOTEL, dto.getBusinessType())) {
            if (!this.dealHotelOrder(dto)) {
                log.error("HOTEL 消费失败: {}", record.value());
                botService.sendMessage("HOTEL mq消费失败，请求参数：" + record.value());
                return false;
            }
        }
        // 补差价订单
        if (EnumUtil.equals(OrderBusinessTypeEnum.DIFF_PRICE, dto.getBusinessType())) {
            if (!dealDiffPriceOrder(dto)) {
                log.error("DIFF_PRICE 消费失败: {}", record.value());
                botService.sendMessage("DIFF_PRICE mq消费失败，请求参数：" + record.value());
                return false;
            }
        }
        // 机票订单
        if (EnumUtil.equals(OrderBusinessTypeEnum.FLIGHT, dto.getBusinessType())) {
            if (!dealFlightOrder(dto)) {
                log.error("FLIGHT 消费失败: {}", record.value());
                botService.sendMessage("FLIGHT mq消费失败，请求参数：" + record.value());
                return false;
            }
        }
        // 特色游订单
        if (EnumUtil.equals(OrderBusinessTypeEnum.SPECIAL_TRAVEL, dto.getBusinessType())) {
            if (!dealSpecialOrder(dto)) {
                log.error("SPECIAL_TRAVEL 消费失败: {}", record.value());
                botService.sendMessage("SPECIAL_TRAVEL mq消费失败，请求参数：" + record.value());
                return false;
            }
        }
        if (EnumUtil.equals(OrderBusinessTypeEnum.TICKET, dto.getBusinessType())) {
            if (!dealTicketScenic(dto)) {
                log.error("TICKET 消费失败: {}", record.value());
                botService.sendMessage("TICKET mq消费失败，请求参数：" + record.value());
                return false;
            }
        }
        log.info("mq消费成功: {}", record.value());
        return true;
    }

    /**
     * 处理票务相关
     *
     * @param dto 消息msg
     * @return true false
     */
    private boolean dealTicketScenic(OrderMessageMqDTO dto) {
        // 处理门票订单
        String orderNo = dto.getOrderNo();
        try {
            if (OrderMessageMqEnum.pay_success.equals(dto.getType())) {
                ResponseResult<Boolean> result = manyTicketService.payOrder(orderNo);
                log.info("pay ticket order: {} result: {}", orderNo, result);
                if (result.getData()) {
                    String orderCustomStatus = OrderCustomStateEnum.SCENIC_TICKET_PRE.name();
                    //查询门票产品订单
                    ResponseResult responseResult = orderBaseService.queryOrderProductList(orderNo);
                    List<OrderCostBreakDownDTO> list = (List<OrderCostBreakDownDTO>) responseResult.getData();
                    for (OrderCostBreakDownDTO costBreakDown : list) {
                        try {
                            String orderStatus = costBreakDown.getOrderStatus();
                            if (ScenicProductStatusEnum.ORDER_FAIL.name().equals(orderStatus)) {
                                //门票部分退款
                                YeePayCreateRefundOrderVo vo = new YeePayCreateRefundOrderVo();
                                vo.setAmount(costBreakDown.getAfterDiscountPrice());
                                vo.setOrderNo(orderNo);
                                vo.setRemark("预订失败，系统自动退款");
                                refundOrderService.createRefundOrder(vo);
                                orderCustomStatus = OrderCustomStateEnum.SCENIC_TICKET_PART_REFUND.name();
                            }
                        } catch (Exception e) {
                            log.error("门票订单退款失败，订单编号：{}", costBreakDown.getCostCode(), e);
                        }
                    }
                    //修改主订单-子状态为预定成功
                    OrderMasterDTO orderMasterDTO = orderBaseService.queryOrderMasterPublic(orderNo);
                    orderMasterDTO.setOrderCustomStatus(orderCustomStatus);
                    orderMasterDTO.setOrderStatus(OrderStatusEnum.PAY_SUCCESS.getCode());
                    orderBaseService.updateOrderMasterPublic(orderMasterDTO);
                    log.info("创建票务订单成功: {}", JSON.toJSONString(dto.getOrderNo()));
                } else {
                    log.info("创建票务订单失败，准备退款: {}", JSON.toJSONString(dto));
                    OrderMasterDTO orderMasterDTO = orderBaseService.queryOrderMasterPublic(orderNo);
                    orderMasterDTO.setOrderCustomStatus(OrderCustomStateEnum.BOOKING_FAIL.name());
                    orderBaseService.updateOrderMasterPublic(orderMasterDTO);
                    orderBaseService.cancelOrder(dto.getOrderNo());
                }
            } else if (OrderMessageMqEnum.apply_refund_money.equals(dto.getType())) {
                //订单全部退款
                OrderFinanceDTO orderFinanceDTO = orderBaseService.queryOrderFinancePublic(orderNo);
                YeePayCreateRefundOrderVo vo = new YeePayCreateRefundOrderVo();
                vo.setAmount(orderFinanceDTO.getEstimatedPrice());
                vo.setOrderNo(dto.getOrderNo());
                vo.setRemark("预订失败，系统自动退款");
                refundOrderService.createRefundOrder(vo);
            } else {
                log.error("not support scenic order messageType: {}", JSON.toJSONString(dto));
            }
        } catch (Exception e) {
            log.error("dto: {}", dto, e);
            return false;
        }
        return true;
    }

    /**
     * 特色游
     *
     * @param dto 消息msg
     * @return true false
     */
    private boolean dealSpecialOrder(OrderMessageMqDTO dto) {
        try {
            log.info("消费特色游订单: {}", JSON.toJSONString(dto));
            if (dto.getType() == OrderMessageMqEnum.apply_refund_money) {
                orderStateService.dealSpecialRefund(dto);
                log.info("特色游申请退款处理完成: {}", JSON.toJSONString(dto));
            } else if (dto.getType() == OrderMessageMqEnum.pay_success) {
                log.info("准备消费特色游订单: {}", dto.getOrderNo());
                OrderBigFieldDTO orderBigFieldDTO = orderBaseService.queryOrderBigFieldPublic(dto.getOrderNo());
                final String key = "guestList";
                if (orderBigFieldDTO == null || !orderBigFieldDTO.getField1().containsKey(key)) {
                    log.info("{} 对应信息不存在 消费失败: {}", key, dto);
                    return true;
                }
                SpecialProduct.Vo specialProduct = orderBigFieldDTO.getField1().getJSONObject("specialProduct").toJavaObject(SpecialProduct.Vo.class);
                List<SpecialOrderDetail.Guest> guestList = orderBigFieldDTO.getField1().getJSONArray(key).toList(SpecialOrderDetail.Guest.class);
                for (SpecialOrderDetail.Guest guest : guestList) {
                    log.info("specialNotify: {}, {}", specialProduct.getId(), StrUtil.hide(guest.getMobile(), 3, 4));
                    smsService.sendMessage(SmsTypeEnums.specialNotify, guest.getMobile(), guest.getName(), specialProduct.getName());
                }
            } else if (dto.getType() == OrderMessageMqEnum.refund_success) {
                log.info("退款成功: {}", JSON.toJSONString(dto));
            } else {
                log.info("else消费特色游订单: {}", JSON.toJSONString(dto));
            }
        } catch (Exception e) {
            log.error("消费特色游订单失败: {}", JSON.toJSONString(dto), e);
            botService.sendMessage(StrUtil.format("特色游订单处理失败: {}", JSON.toJSONString(dto)));
            return false;
        }
        return true;
    }

    /**
     * 处理机票订单
     *
     * @param dto 消息msg
     * @return true false
     */
    private boolean dealFlightOrder(OrderMessageMqDTO dto) {
        try {
            if (OrderMessageMqEnum.pay_success.equals(dto.getType())) {
                if (tourismPayCompleteService.payFlightOrder(dto.getOrderNo())) {
                    log.info("机票订单支付成功 waiting change status: {}", dto);
                    OrderMasterDTO orderMasterDTO = orderBaseService.queryOrderMasterPublic(dto.getOrderNo());
                    orderMasterDTO.setOrderCustomStatus(OrderCustomStateEnum.INTENDED_ING.name());
                    orderBaseService.updateOrderMasterPublic(orderMasterDTO);
                    log.info("机票订单支付成功，状态修改成功: {}", dto);
                } else {
                    log.warn("支付机票订单失败: {}", dto);
                    botService.sendMessage("支付机票订单失败订单: " + dto.getOrderNo());
                    OrderMessageMqDTO applyRefund = new OrderMessageMqDTO();
                    applyRefund.setOrderNo(dto.getOrderNo());
                    applyRefund.setRemark("机票订单第三方支付失败，自动退款");
                    applyRefund.setType(OrderMessageMqEnum.apply_refund_money);
                    applyRefund.setBusinessType(dto.getBusinessType());
                    applyRefund.setCurRetryCount(0);
                    mqSenderService.send(applyRefund);
                    return true;
                }
            } else if (OrderMessageMqEnum.refund_success.equals(dto.getType())) {
                log.info("机票退款成功: {}", dto);
                return true;
            } else if (OrderMessageMqEnum.apply_refund_money.equals(dto.getType())) {
                ResponseResult responseResult = orderBaseService.queryOrderBase(dto.getOrderNo());
                QueryOrderVo data = (QueryOrderVo) responseResult.getData();
                botService.sendMessage(CommonUtil.flightRefundDetail(data, dto.getRemark()));
                if (manyFlightService.canRefund(dto.getOrderNo())) {
                    OrderFinanceDTO orderFinanceDTO = orderBaseService.queryOrderFinancePublic(dto.getOrderNo());
                    YeePayCreateRefundOrderVo vo = new YeePayCreateRefundOrderVo();
                    vo.setOrderNo(dto.getOrderNo());
                    vo.setAmount(orderFinanceDTO.getEstimatedPrice());
                    if (StrUtil.isBlank(dto.getRemark())) {
                        vo.setRemark("system refund");
                    } else {
                        vo.setRemark(dto.getRemark());
                    }
                    log.info("发起机票订单自动退款: {}", vo);
                    refundOrderService.createRefundOrder(vo);
                    OrderMasterDTO orderMasterDTO = orderBaseService.queryOrderMasterPublic(dto.getOrderNo());
                    orderMasterDTO.setOrderCustomStatus(OrderCustomStateEnum.BOOKING_FAIL.name());
                    orderBaseService.updateOrderMasterPublic(orderMasterDTO);
                    log.info("自动退款修改订单状态成功: {}", JSON.toJSONString(dto));
                    return true;
                }
                log.warn("支付机票订单失败&不能自动退款订单: {}", JSON.toJSONString(dto));
                botService.sendMessage("支付机票订单失败&不能自动退款订单: " + dto.getOrderNo());
                return false;
            } else {
                log.warn("未定义的类型: {}", dto);
                return false;
            }
        } catch (Exception e) {
            log.error("消费机票订单报错: {}--", dto, e);
            return false;
        }
        return true;
    }

    /**
     * 处理补差价消息
     *
     * @param dto 消息msg
     * @return true false
     */
    private boolean dealDiffPriceOrder(OrderMessageMqDTO dto) {
        try {
            OrderMasterDTO childOrder = orderBaseService.queryOrderMasterPublic(dto.getOrderNo());
            OrderBigFieldDTO orderBigFieldDTO = orderBaseService.queryOrderBigFieldPublic(childOrder.getParentOrderNo());
            if (EnumUtil.equals(ThirdServiceTypeEnum.visa, orderBigFieldDTO.getThirdServiceType())) {
                log.info("上上签补差价: {}", childOrder);
                OrderFinanceDTO orderFinanceDTO = orderBaseService.queryOrderFinancePublic(childOrder.getOrderNo());
                Integer amount = orderFinanceDTO.getEstimatedPrice();
                String orderId = orderBigFieldDTO.getField2().getString("order_id");
                log.info("上上签补差价: {}, {}", orderId, amount);
                return tourismPayCompleteService.diffVisaOrder(orderId, amount);
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 处理签证订单问题
     *
     * @param dto 消息msg
     * @return true false
     */
    private boolean dealVisaOrder(OrderMessageMqDTO dto) {
        try {
            // 支付成功
            if (OrderMessageMqEnum.pay_success.equals(dto.getType())) {
                if (tourismPayCompleteService.payVisaOrder(dto.getOrderNo())) {
                    OrderMasterDTO orderMasterDTO = orderBaseService.queryOrderMasterPublic(dto.getOrderNo());
                    orderMasterDTO.setOrderStatus(OrderStatusEnum.STOCK_DELIVERY.getCode());
                    orderBaseService.updateOrderMasterPublic(orderMasterDTO);
                    return true;
                }
            }
            // 退款成功
            if (OrderMessageMqEnum.refund_success.equals(dto.getType())) {
                OrderMasterDTO orderMasterDTO = orderBaseService.queryOrderMasterPublic(dto.getOrderNo());
                orderMasterDTO.setOrderStatus(OrderStatusEnum.AGREE_REFUND_MONEY.getCode());
                orderBaseService.updateOrderMasterPublic(orderMasterDTO);
                ResponseResult responseResultBean = orderBaseService.queryOrderBase(dto.getOrderNo());
                QueryOrderVo queryOrderVo = (QueryOrderVo) responseResultBean.getData();
                log.info("查询订单返回: {}", queryOrderVo.getOrderMaster());
                Text text = new Text();
                text.setContent(CommonUtil.buildRefundDetails(queryOrderVo, dto.getRemark()));
                text.setMentionedList(CommonUtil.VISA_DEAL_PEOPLE_LIST);
                TextMessage textMessage = new TextMessage();
                textMessage.setText(text);
                botService.sendMessage(textMessage);
                if (tourismPayCompleteService.diffVisaChildOrder(dto.getOrderNo())) {
                    log.info("发送父子订单退款消费成功");
                } else {
                    log.info("发送父子订单退款消费失败");
                }
                return true;
            }
            if (OrderMessageMqEnum.apply_refund_money.equals(dto.getType())) {
                log.info("处理申请退款订单-补差价退款: {}", dto);
                YeePayCreateRefundOrderVo vo = new YeePayCreateRefundOrderVo();
                OrderFinanceDTO orderFinanceDTO = orderBaseService.queryOrderFinancePublic(dto.getOrderNo());
                if (Optional.ofNullable(orderFinanceDTO).isEmpty()) {
                    log.error("订单不存在");
                    return false;
                }
                vo.setOrderNo(dto.getOrderNo());
                vo.setRemark(dto.getRemark());
                vo.setAmount(orderFinanceDTO.getEstimatedPrice());
                ResponseResult responseResultBean = refundOrderService.createRefundOrder(vo);
                if (responseResultBean.isSuccess()) {
                    OrderMasterDTO orderMasterDTO = orderBaseService.queryOrderMasterPublic(vo.getOrderNo());
                    orderMasterDTO.setOrderStatus(OrderStatusEnum.APPLY_REFUND_MONEY.getCode());
                    orderBaseService.updateOrderMasterPublic(orderMasterDTO);
                }
                log.info("取消订单-退款返回");
                return responseResultBean.isSuccess();
            }
            log.info("上上签签证订单消费失败-不支持的消费类型: {}", dto);
        } catch (Exception e) {
            log.error("", e);
            log.info("上上签签证订单消费失败: {}", dto);
        }
        return false;
    }

    /**
     * 处理JPS消息
     *
     * @param dto 消息msg
     * @return true false
     */
    private boolean dealJpsOrder(OrderMessageMqDTO dto) {
        try {
            if (OrderMessageMqEnum.pay_success.equals(dto.getType())) {
                if (tourismPayCompleteService.payJpsOrder(dto.getOrderNo())) {
                    return true;
                }
            }
            if (OrderMessageMqEnum.refund_success.equals(dto.getType())) {
                ResponseResult responseResultBean = orderBaseService.queryOrderBase(dto.getOrderNo());
                QueryOrderVo queryOrderVo = (QueryOrderVo) responseResultBean.getData();
                log.info("查询订单返回: {}", queryOrderVo.getOrderMaster());
                Text text = new Text();
                text.setContent(CommonUtil.buildRefundDetails(queryOrderVo, dto.getRemark()));
                text.setMentionedList(Collections.singletonList(CommonUtil.ALL));
                TextMessage textMessage = new TextMessage();
                textMessage.setText(text);
                botService.sendMessage(textMessage);
                return true;
            }
            log.info("个人加盟服务订单消费失败-不支持的消费类型: {}", dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.info("个人加盟服务订单消费失败: {}", dto);
        }
        return false;
    }

    /**
     * 处理酒店消息
     *
     * @param dto 消息msg
     * @return true false
     */
    private boolean dealHotelOrder(OrderMessageMqDTO dto) {
        try {
            JSONObject field1 = new JSONObject();
            if (OrderMessageMqEnum.pay_success.equals(dto.getType())) {
                if (tourismPayCompleteService.payHotelOrder(dto.getOrderNo())) {
                    log.info("hotel pay success deal: {}", JSON.toJSONString(dto));
                    try {
                        OrderBigFieldDTO orderBigFieldDTO = orderBaseService.queryOrderBigFieldPublic(dto.getOrderNo());
                        field1 = orderBigFieldDTO.getField1();
                        JSONObject contact = field1.getJSONObject("contact");
                        if (contact == null) {
                            log.info("just contact is null: {}", field1.getJSONObject("contactInfo"));
                            contact = field1.getJSONObject("contactInfo");
                        }
                        String mobile = contact.getString("mobile");
                        if (StrUtil.isBlank(mobile)) {
                            mobile = contact.getString("phone");
                        }
                        String name = contact.getString("name");
                        String lastName = contact.getString("lastName");
                        String firstName = contact.getString("firstName");
                        if (StrUtil.isBlank(name)) {
                            name = firstName + " " + lastName;
                        }
                        if (StrUtil.isNotBlank(lastName)) {
                            name = name.replaceAll("/", " ");
                        }
                        String[] values = {name,
                                field1.getJSONObject("orderDateRange").getString("checkIn"),
                                field1.getString("orderName")};
                        log.info("send hotel booking mobile: {}, value: {}", mobile, JSON.toJSONString(values));
                        smsService.sendMessage(SmsTypeEnums.hotelCheckIn, mobile, values);
                        log.info("酒店预定通知发送成功: {}", dto);
                    } catch (Exception e) {
                        log.error("酒店预定通知发送失败: {}", dto, e);
                    }
                    return true;
                } else {
                    log.info("hotel pay success not pay: {}", JSON.toJSONString(dto));
                    try {
                        OrderBigFieldDTO orderBigFieldDTO = orderBaseService.queryOrderBigFieldPublic(dto.getOrderNo());
                        field1 = orderBigFieldDTO.getField1();
                        JSONObject contact = field1.getJSONObject("contact");
                        if (contact == null) {
                            contact = field1.getJSONObject("contactInfo");
                        }
                        String mobile = contact.getString("mobile");
                        if (StrUtil.isBlank(mobile)) {
                            mobile = contact.getString("phone");
                        }
                        smsService.sendMessage(SmsTypeEnums.hotelCancel,
                                mobile,
                                field1.getJSONObject("orderDateRange").getString("checkIn"),
                                field1.getJSONObject("orderDateRange").getString("checkOut"),
                                field1.getString("orderName"), "系统取消");
                        log.info("酒店取消通知发送成功: {}", dto);
                    } catch (Exception e) {
                        log.error("酒店取消通知发送失败: {}", dto, e);
                    }
                    return true;
                }
            }
            if (OrderMessageMqEnum.apply_refund_money.equals(dto.getType())) {
                CancelOrderDto cancelOrderDto = new CancelOrderDto();
                cancelOrderDto.setUid(field1.getLong("uid"));
                cancelOrderDto.setOrderNo(dto.getOrderNo());
                cancelOrderDto.setReason(OrderCancelCode.Other.getMessage());
                cancelOrderDto.setCancelType(OrderCancelCode.Other.getCode());
                return hotelOrderV2Service.cancelOrder(cancelOrderDto);
//                return true;
            }
            if (OrderMessageMqEnum.refund_success.equals(dto.getType())) {
                return true;
            }
            log.info("酒店订单消费失败-不支持的消费类型: {}", dto);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.info("酒店订单消费失败: {}", dto);
        }
        return false;
    }
}
