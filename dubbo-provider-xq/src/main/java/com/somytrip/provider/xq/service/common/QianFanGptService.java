package com.somytrip.provider.xq.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.somytrip.api.service.tourism.AiToolService;
import com.somytrip.entity.enums.xq.AIMessageTypeEnum;
import com.somytrip.entity.xiaoqi.ChatTripGptRecords;
import com.somytrip.entity.xiaoqi.GptSessionMessageMap;
import com.somytrip.provider.xq.config.QianFanConfig;
import com.somytrip.provider.xq.constant.IntentionEnum;
import com.somytrip.provider.xq.constant.QuestionWordLangEnum;
import com.somytrip.provider.xq.constant.ToolMethodConstant;
import com.somytrip.provider.xq.dto.ChatMessageReqDTO;
import com.somytrip.provider.xq.dto.ChatMessageResDTO;
import com.somytrip.provider.xq.mapper.ChatTripGptRecordsMapper;
import com.somytrip.provider.xq.mapper.GptSessionMessageMapMapper;
import com.somytrip.provider.xq.service.FunctionCallService;
import com.somytrip.provider.xq.service.handler.DeepSeekStreamResponseHandler;
import com.somytrip.provider.xq.service.handler.XiaoQiStreamResponseHandler;
import com.somytrip.provider.xq.util.HanLPUtil;
import com.somytrip.provider.xq.util.LanguageCheckerUtil;
import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.data.message.*;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.chat.request.json.JsonObjectSchema;
import dev.langchain4j.model.output.Response;
import jakarta.annotation.Resource;
import jakarta.websocket.Session;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RefreshScope
@Component
@Slf4j
public class QianFanGptService {

    @DubboReference
    private AiToolService aiToolService;
    @Resource
    private GptSessionMessageMapMapper gptSessionMessageMapMapper;
    @Resource
    private ChatTripGptRecordsMapper chatTripGptRecordsMapper;
    @Resource
    private Map<String, StreamingChatLanguageModel> stringStreamingChatLanguageModelMap;
    @Resource
    private FunctionCallService functionCallService;
    @Resource
    private QianFanConfig qianFanConfig;
    @Value("${llm.strategy.intentionModelName:intentionDoubao}")
    private String intentionModelName;
    @Value("${llm.strategy.intentionSystemPrompt}")
    private String intentionSystemPrompt;
    @Value("${llm.strategy.intentionUserMsgCount:1}")
    private Integer intenionUserMsgCount;
    @Value("${deepseek.system-prompt:''}")
    private String deepSeekSystemPrompt;
    /**
     * 模型策略选择
     */
    @Value("${llm.strategy.type}")
    private String llmModelType;
    @Resource
    private Generation generation;
    @Resource
    private GenerationParam generationParam;
    /**
     * 大模型系统Prompt模版
     */
    @Value("${llm.system-prompt.template}")
    private String llmSystemPrompt;
    @Value("${llm.rag.enable:true}")
    private Boolean enableRagFlag;
    @Resource
    private Map<String, ChatLanguageModel> intentionLanguageModelMap;
    @Value("${scenic.scope.enable:false}")
    private Boolean scenicEnable;
    @Value("${scenic.bracket.flag:false}")
    private Boolean enableBracketFlag;
    @Resource
    private LlmCommonService llmCommonService;
    @Value("${llm.funcall.enable:false}")
    private Boolean functionCallingFlag;


    public void chatStream(Session session, ChatMessageReqDTO dto, String uid, Integer versionNum) {
        String lang = LanguageCheckerUtil.checkLanguage(dto.getContent()).getName();
        List<ChatMessage> chatMessages = new ArrayList<>();
        List<Message> userMessageList = new ArrayList<>();
        String systemPrompt = qianFanConfig.getSystemPrompt();
        IntentionEnum intentionEnum = IntentionEnum.DEFAULT_STRATEGY;
        String content = dto.getContent();
        log.info("chatMessages size: {}", chatMessages.size());
        ChatMessageReqDTO chatMessageReqDTO = ChatMessageReqDTO
                .builder()
                .sessionId(dto.getSessionId())
                .messageType(AIMessageTypeEnum.USER)
                .content(dto.getContent())
                .build();
        //保存聊天列表记录
        LocalDateTime currentTime = LocalDateTime.now();
        QueryWrapper<GptSessionMessageMap> queryWrapperSessionMap = new QueryWrapper<>();
        queryWrapperSessionMap.eq("session_id", dto.getSessionId());
        Long sessionMapCount = gptSessionMessageMapMapper.selectCount(queryWrapperSessionMap);
        if (sessionMapCount <= 0) {
            GptSessionMessageMap gptSessionMessageMap = new GptSessionMessageMap();
            gptSessionMessageMap.setSessionId(dto.getSessionId());
            gptSessionMessageMap.setUserId(Long.valueOf(uid));
            gptSessionMessageMap.setContent(JSON.toJSONString(chatMessageReqDTO));
            gptSessionMessageMap.setType(AIMessageTypeEnum.USER.name());
            gptSessionMessageMap.setCreateTime(currentTime);
            gptSessionMessageMap.setUpdateTime(currentTime);
            gptSessionMessageMapMapper.insert(gptSessionMessageMap);
        }
        // 添加上下文
        LambdaQueryWrapper<ChatTripGptRecords> queryWrapperChatTripGptRecords = new LambdaQueryWrapper<>();
        queryWrapperChatTripGptRecords.eq(ChatTripGptRecords::getChatId, dto.getSessionId())
                .eq(ChatTripGptRecords::getUserId, uid)
                .eq(ChatTripGptRecords::getIsDel, false)
        ;
        queryWrapperChatTripGptRecords.orderByAsc(ChatTripGptRecords::getId);
        List<ChatTripGptRecords> chatTripGptRecordsList = chatTripGptRecordsMapper.selectList(queryWrapperChatTripGptRecords);
        if (!chatTripGptRecordsList.isEmpty()) {
            for (int i = 0; i < chatTripGptRecordsList.size(); i++) {
                ChatTripGptRecords records = chatTripGptRecordsList.get(i);
                if (records.getType() == AIMessageTypeEnum.USER) {
                    ChatMessageReqDTO message = JSON.parseObject(records.getMessage(), ChatMessageReqDTO.class);
                    chatMessages.add(new UserMessage(message.getContent()));
                    userMessageList.add(Message.builder().role(Role.USER.getValue()).content(message.getContent()).build());
                } else if (records.getType() == AIMessageTypeEnum.AI) {
                    StringBuilder stringBuilder = new StringBuilder();
                    for (int j = i; j < chatTripGptRecordsList.size(); j++) {
                        ChatMessageResDTO message = JSON.parseObject(chatTripGptRecordsList.get(j).getMessage(), ChatMessageResDTO.class);
                        if (message.getMessageType() != AIMessageTypeEnum.AI) {
                            i = j - 1;
                            break;
                        }
                        stringBuilder.append(message.getContent());
                        if (message.getTerminateFlag()) {
                            i = j;
                            break;
                        }
                        if (j == chatTripGptRecordsList.size() - 1) {
                            i = j;
                        }
                    }
                    chatMessages.add(new AiMessage(stringBuilder.toString()));
                    userMessageList.add(Message.builder().role(Role.ASSISTANT.getValue()).content(stringBuilder.toString()).build());
                } else if (records.getType() == AIMessageTypeEnum.TOOL_EXECUTION_RESULT) {
                    ChatMessageResDTO message = JSON.parseObject(records.getMessage(), ChatMessageResDTO.class);
                    chatMessages.add(new AiMessage(StrUtil.isBlank(message.getContent()) ? "null" : message.getContent()));
                } else {
                    log.warn("not support chat trip gpt records: {}", records);
                }
                log.info("records end index: {}", i);
            }
        }
        //推理模型（意图识别）
        List<ChatMessage> intentionMessage = getIntentionMessage(content, chatMessages);
        Response<AiMessage> response = intentionLanguageModelMap.get(intentionModelName).generate(intentionMessage);
        try {
            String responseJson = response.content().text();
            JSONObject jsonObject = JSONObject.parseObject(responseJson);
            String intentionName = jsonObject.getString("intentionName");
            intentionEnum = IntentionEnum.getByName(intentionName);
        } catch (Exception e) {
            log.error("intention analysis error，msg：{}", e.getMessage(), e);
        }
        if (intentionEnum.equals(IntentionEnum.FUNCTION_HOTELS_STRATEGY)) {
            llmCommonService.hotelHandler(chatMessages, lang, dto.getContent(), session, dto.getCityName(), dto.getSessionId(), uid);
        } else if (intentionEnum.equals(IntentionEnum.FUNCTION_FLIGHTS_SEARCH_STRATEGY)) {
            llmCommonService.flightSearch(chatMessages, lang, dto.getContent(), session, dto.getSessionId(), uid,
                    StrUtil.isEmpty(dto.getCityName()) ? QuestionWordLangEnum.DEFAULT_CITY.getLangDescription(lang) : dto.getCityName());
        } else if (intentionEnum.equals(IntentionEnum.FUNCTION_FLIGHTS_PASSENGER_STRATEGY)) {
            llmCommonService.dealPassengerInfo(session, dto.getSessionId(), uid, lang, dto.getContent());
        } else if (intentionEnum.equals(IntentionEnum.FUNCTION_FLIGHTS_CONFIRMING_STRATEGY)) {
            llmCommonService.flightBooking(session, dto.getSessionId(), uid, lang, dto.getContent());
        } else if (intentionEnum.equals(IntentionEnum.FUNCTION_TICKETS_STRATEGY)) {
            llmCommonService.ticketsQuery(chatMessages, lang, dto.getContent(), session, dto.getSessionId(), uid);
        } else if (intentionEnum.equals(IntentionEnum.FUNCTION_CAR_RENTAL_STRATEGY)) {
            llmCommonService.carRental(session, dto.getSessionId(), uid, lang, dto.getContent());
        } else if (intentionEnum.equals(IntentionEnum.FUNCTION_VISA_STRATEGY)) {
            llmCommonService.visaInfo(session, dto.getSessionId(), uid, lang, dto.getContent(), chatMessages);
        } else {
            boolean contextFlag = intentionEnum.equals(IntentionEnum.TRAVEL_STRATEGY)
                    || intentionEnum.equals(IntentionEnum.TRAVEL_LOCATION_STRATEGY);
            if (enableRagFlag && contextFlag) {
                List<String> cityExist = HanLPUtil.segmentCity(content);
                //判断是否已上线RAG城市
                if (CollUtil.isNotEmpty(cityExist)) {
                    int count = gptSessionMessageMapMapper.existCityFlag(cityExist.get(0));
                    contextFlag = count > 0;
                }
            }
            String context = "";
            if (enableRagFlag && contextFlag) {
                context = llmCommonService.getLlmContext(dto.getContent(), lang);
                systemPrompt = llmSystemPrompt.replace("Context", context);
                systemPrompt = systemPrompt.replaceAll("Language", lang);
            }
            systemPrompt = systemPrompt + "\n" + "以下是常识内容补充:\n今天日期:"
                    + LocalDateTime.now() + "\n"
                    + "上午时间段:08:00-12:00,下午时间段:12:00-18:00,晚上时间段:18:00-23:59,凌晨时段:00:00-08:59";
            chatMessages.add(new SystemMessage(systemPrompt));
            if (StrUtil.isNotBlank(deepSeekSystemPrompt) && dto.getDeepSeekFlag() != null && dto.getDeepSeekFlag()) {
                deepSeekSystemPrompt = deepSeekSystemPrompt.replaceAll("Language", dto.getLang());
                if (enableRagFlag && contextFlag) {
                    deepSeekSystemPrompt = systemPrompt + " 深度思考过程，不要出现知识库相关字。";
                }
                userMessageList.add(Message.builder().role(Role.SYSTEM.getValue()).content(deepSeekSystemPrompt).build());
            }

            //做攻略改写user prompt
            if (intentionEnum == IntentionEnum.TRAVEL_LOCATION_STRATEGY && scenicEnable) {
                content = llmCommonService.travelStrategy(chatMessages, lang, dto.getContent(), uid);
            }
            //添加用户提问
            chatMessages.add(new UserMessage(content));
            userMessageList.add(Message.builder().role(Role.USER.getValue()).content(content).build());
            String defaultCity = QuestionWordLangEnum.DEFAULT_CITY.getLangDescription(lang);
            String operationId = IdUtil.getSnowflakeNextIdStr();
            //判断是否使用DeepSeek R1 模型进行回复
            Boolean deepSeekFlag = dto.getDeepSeekFlag();
            if (deepSeekFlag != null && deepSeekFlag
                    && (IntentionEnum.TRAVEL_STRATEGY.equals(intentionEnum) || IntentionEnum.TRAVEL_LOCATION_STRATEGY.equals(intentionEnum)
                    || IntentionEnum.DEFAULT_STRATEGY.equals(intentionEnum))) {
                //DeepSeek 回复方式
                try {
                    DeepSeekStreamResponseHandler deepSeekStreamResponseHandler = new DeepSeekStreamResponseHandler(session, aiToolService, chatTripGptRecordsMapper, functionCallService);
                    deepSeekStreamResponseHandler.setUid(uid);
                    deepSeekStreamResponseHandler.setSessionId(dto.getSessionId());
                    deepSeekStreamResponseHandler.setDepartureCityCode(StrUtil.isEmpty(dto.getCityCode()) ? "CN014" : dto.getCityCode());
                    deepSeekStreamResponseHandler.setDepartureCityName(StrUtil.isEmpty(dto.getCityName()) ? defaultCity : dto.getCityName());
                    deepSeekStreamResponseHandler.setUserMessage(dto.getContent());
                    deepSeekStreamResponseHandler.setVersionNum(versionNum);
                    deepSeekStreamResponseHandler.setOperationId(operationId);
                    deepSeekStreamResponseHandler.setLang(lang);
                    deepSeekStreamResponseHandler.setBracketMatchFlag(enableBracketFlag);
                    generationParam.setMessages(userMessageList);
                    generation.streamCall(generationParam, deepSeekStreamResponseHandler);
                } catch (Exception e) {
                    log.error("deepseek 流式回复异常，异常信息：{}", e.getMessage(), e);
                }
            } else {
                //GPT其他 流式处理回复方式
                XiaoQiStreamResponseHandler xiqiStreamResponseHandler = new XiaoQiStreamResponseHandler(session, aiToolService, chatTripGptRecordsMapper, functionCallService);
                xiqiStreamResponseHandler.setUid(uid);
                xiqiStreamResponseHandler.setSessionId(dto.getSessionId());
                xiqiStreamResponseHandler.setDepartureCityCode(StrUtil.isEmpty(dto.getCityCode()) ? "CN014" : dto.getCityCode());
                xiqiStreamResponseHandler.setDepartureCityName(StrUtil.isEmpty(dto.getCityName()) ? defaultCity : dto.getCityName());
                xiqiStreamResponseHandler.setUserMessage(dto.getContent());
                xiqiStreamResponseHandler.setVersionNum(versionNum);
                xiqiStreamResponseHandler.setOperationId(operationId);
                xiqiStreamResponseHandler.setLang(lang);
                xiqiStreamResponseHandler.setBracketMatchFlag(enableBracketFlag);
                String intentionStrategy = intentionEnum.name();
                Map<String, String> llmModelTypeMap = JSON.parseObject(llmModelType, Map.class);
                String modelStrategy = llmModelTypeMap.get(intentionStrategy);
                StreamingChatLanguageModel streamingChatLanguageModel = stringStreamingChatLanguageModelMap.get(modelStrategy);
                streamingChatLanguageModel.generate(chatMessages, toolSpecifications(functionCallingFlag), xiqiStreamResponseHandler);
            }
        }
    }

    private List<ToolSpecification> toolSpecifications(Boolean flag) {
        if (!flag) {
            return ListUtil.empty();
        }
        ToolSpecification hotelTotal = ToolSpecification.builder()
                .name(ToolMethodConstant.BOOK_HOTEL)
                .description(qianFanConfig.getHotelTotalToolDec())
                .parameters(
                        JsonObjectSchema.builder()
                                .addStringProperty("cityName")
                                .addStringProperty("bookingDate")
                                .addStringProperty("location")
                                .addStringProperty("starRating")
                                .build())
                .build();
        ToolSpecification fightBookingSearch = ToolSpecification.builder()
                .name(ToolMethodConstant.FIGHT_BOOKING_SEARCH)
                .description(qianFanConfig.getFlightSearchToolDec())
                .parameters(
                        JsonObjectSchema.builder()
                                .addStringProperty("departureCityName")
                                .addStringProperty("destinationCityName")
                                .addStringProperty("bookingDate")
                                .addStringProperty("timeQuantum")
                                .build())
                .build();
        ToolSpecification tripUserInfo = ToolSpecification.builder()
                .name(ToolMethodConstant.TRIP_USER_INFO)
                .description(qianFanConfig.getUserInfo())
                .build();
        ToolSpecification flightOrderCreate = ToolSpecification.builder()
                .name(ToolMethodConstant.FLIGHT_BOOKING_CREATE_ORDER)
                .description(qianFanConfig.getFlightOrderToolDec())
                .build();
        return ListUtil.of(hotelTotal, fightBookingSearch, tripUserInfo, flightOrderCreate);
    }

    private @NotNull List<ChatMessage> getIntentionMessage(String content, List<ChatMessage> chatMessages) {
        List<ChatMessage> intentionMessage = new ArrayList<>();
        intentionMessage.add(new SystemMessage(intentionSystemPrompt));
        if (CollUtil.isNotEmpty(chatMessages)) {
            List<ChatMessage> userQuestions = new ArrayList<>();
            for (ChatMessage chatMessage : chatMessages) {
                if (ChatMessageType.USER.equals(chatMessage.type())) {
                    userQuestions.add(chatMessage);
                }
            }
            int toIndex = intenionUserMsgCount;
            if (userQuestions.size() < intenionUserMsgCount) {
                toIndex = userQuestions.size();
            }
            intentionMessage.addAll(CollUtil.reverse(userQuestions).subList(0, toIndex));
        }
        intentionMessage.add(new UserMessage(content));
        return intentionMessage;
    }
}
