package com.somytrip.provider.xq.service.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.somytrip.api.service.tourism.AiToolService;
import com.somytrip.bean.dto.HighlightDTO;
import com.somytrip.entity.enums.xq.AIMessageShowTypeEnum;
import com.somytrip.entity.enums.xq.AIMessageTypeEnum;
import com.somytrip.entity.enums.xq.ForwardTypeEnum;
import com.somytrip.entity.itinerary.ItineraryCreateParam;
import com.somytrip.entity.xiaoqi.ChatTripGptRecords;
import com.somytrip.provider.xq.constant.FunCallExceptionEnum;
import com.somytrip.provider.xq.constant.QuestionWordLangEnum;
import com.somytrip.provider.xq.constant.ToolMethodConstant;
import com.somytrip.provider.xq.dto.*;
import com.somytrip.provider.xq.mapper.ChatTripGptRecordsMapper;
import com.somytrip.provider.xq.service.FunctionCallService;
import com.somytrip.provider.xq.util.HanLPUtil;
import com.somytrip.provider.xq.util.MarkdownUtil;
import com.somytrip.provider.xq.util.XqRegxUtil;
import dev.langchain4j.agent.tool.ToolExecutionRequest;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.output.Response;
import jakarta.websocket.Session;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;


/**
 * <AUTHOR>
 */
@Slf4j
public class XiaoQiStreamResponseHandler implements StreamingResponseHandler<AiMessage> {
    private final Session session;
    private final AiToolService aiToolService;
    private final ChatTripGptRecordsMapper chatTripGptRecordsMapper;
    private final List<ItineraryCreateParam.DayItem> dayItems = new ArrayList<>();
    private final FunctionCallService functionCallService;
    private String cityName;
    private String cityCode;
    private Integer dayNum = 0;
    private Integer currentDay;
    /**
     * 是否生成攻略
     */
    private Boolean itineraryFlag = false;
    /**
     * 城市处理标签
     */
    private Boolean cityFlag = false;
    @Setter
    private String sessionId;
    @Setter
    private String uid;
    @Setter
    private String departureCityCode;
    /**
     * 出发城市名称
     */
    @Setter
    private String departureCityName;
    /**
     * 聊天保存用户记录标记
     */
    private Boolean userChatSaveFlag = false;
    @Setter
    private String userMessage;
    /**
     * 回答messag拼接成一段返回处理
     */
    private String resultMsg = null;
    @Setter
    private Integer versionNum;
    @Setter
    private String operationId;
    private Boolean terminateFlag = false;
    private StringBuilder terminateResultMsg = new StringBuilder();
    @Setter
    private String lang;
    @Setter
    private Boolean bracketMatchFlag = false;

    public XiaoQiStreamResponseHandler(Session session, AiToolService aiToolService, ChatTripGptRecordsMapper chatTripGptRecordsMapper, FunctionCallService functionCallService) {
        this.session = session;
        this.aiToolService = aiToolService;
        this.chatTripGptRecordsMapper = chatTripGptRecordsMapper;
        this.functionCallService = functionCallService;
    }

    private static @NotNull List<ItineraryCreateParam.DayItem.ActivityItem> getActivityItems(ItineraryCreateParam.DayItem dayItem, List<HighlightDTO> highlightList) {
        List<ItineraryCreateParam.DayItem.ActivityItem> activities = dayItem.getActivities();
        if (activities == null) {
            activities = new ArrayList<>();
        }
        for (HighlightDTO highlightDTO : highlightList) {
            ItineraryCreateParam.DayItem.ActivityItem activityItem = new ItineraryCreateParam.DayItem.ActivityItem();
            activityItem.setActivityId(highlightDTO.getBusinessId());
            activityItem.setActivityName(highlightDTO.getShowTxt());
            activityItem.setActivityType(1);
            activityItem.setStayTime(highlightDTO.getStayTime());
            activityItem.setLon(highlightDTO.getLon());
            activityItem.setLat(highlightDTO.getLat());
            activities.add(activityItem);
        }
        return activities;
    }

    @Override
    public void onNext(String message) {
        if (terminateFlag) {
            return;
        }
        terminateResultMsg.append(message);
        terminateFlag = functionCallService.getTerminateFlag(operationId);
        if (versionNum == null || versionNum == 0) {
            try {
                StopWatch stopWatch = new StopWatch();
                stopWatch.currentTaskName();
                stopWatch.start();
                if (resultMsg == null) {
                    resultMsg = message;
                } else {
                    resultMsg = resultMsg.concat(message);
                }
                Matcher resultMatcher = XqRegxUtil.RESULT_END_REG.matcher(resultMsg);
                if (!resultMatcher.find()) {
                    return;
                }
                //保存用户会话记录
                if (userChatSaveFlag == false) {
                    saveChatRecord();
                }
                if (!cityFlag) {
                    //城市名称正则匹配
                    List<String> cityList = HanLPUtil.segmentCity(userMessage);
                    if (CollectionUtil.isNotEmpty(cityList)) {
                        int citySize = cityList.size();
                        if (citySize == 1) {
                            cityName = cityList.get(0);
                            cityCode = aiToolService.getCityCodeByName(cityName);
                        } else {
                            departureCityName = cityList.get(0);
                            departureCityCode = aiToolService.getCityCodeByName(departureCityName);
                            cityName = cityList.get(1);
                            cityCode = aiToolService.getCityCodeByName(cityName);
                        }
                    }
                    cityFlag = true;
                }
                //景点正则匹配
                List<String> scenicNames = HanLPUtil.segmentScenic(resultMsg);
                ChatMessageResDTO chatMessageRes = ChatMessageResDTO
                        .builder()
                        .content(resultMsg)
                        .messageType(AIMessageTypeEnum.AI)
                        .operationId(operationId)
                        .build();
                List<HighlightDTO> highlights = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(scenicNames)) {
                    highlights = aiToolService.queryHighlightList(cityCode, scenicNames);
                }
//            攻略天数+景点处理
                Matcher matcherDay = XqRegxUtil.DAY_REGX.matcher(resultMsg);
                while (matcherDay.find()) {
                    String dayText = matcherDay.group(2);
                    if (StrUtil.isBlank(dayText)) {
                        dayText = matcherDay.group(3);
                    }
                    if (StrUtil.isBlank(dayText)) {
                        continue;
                    }
                    if (NumberUtil.isNumber(dayText)) {
                        currentDay = Integer.valueOf(dayText);
                    } else {
                        currentDay = NumberChineseFormatter.chineseToNumber(dayText);
                    }
                    if (currentDay > dayNum) {
                        dayNum = currentDay;
                    }
                    ItineraryCreateParam.DayItem dayItemDetail = new ItineraryCreateParam.DayItem();
                    dayItemDetail.setDayNum(currentDay);
                    dayItemDetail.setCityCode(cityCode);
                    dayItems.add(dayItemDetail);
                }
                List<HighlightDTO> highlightList = highlights;
                if (CollectionUtil.isNotEmpty(highlightList)) {
                    this.itineraryFlag = true;
                    for (ItineraryCreateParam.DayItem dayItem : dayItems) {
                        if (dayItem.getDayNum().equals(currentDay)) {
                            List<ItineraryCreateParam.DayItem.ActivityItem> activities = getActivityItems(dayItem, highlightList);
                            dayItem.setActivities(activities);
                        }
                    }
                }
                //Markdown 转化
                List<StyleCssHandlerDTO> styleCssHandlerList = MarkdownUtil.markdownConvertChar(resultMsg);
                chatMessageRes.setStyleCssHandlerList(MarkdownUtil.hightLightConvert(styleCssHandlerList, highlightList, lang));
                //保存回答记录
                LocalDateTime currentTime = LocalDateTime.now();
                ChatTripGptRecords chatTripGptRecords = ChatTripGptRecords
                        .builder()
                        .chatId(sessionId)
                        .isDel(false)
                        .createTime(currentTime)
                        .type(AIMessageTypeEnum.AI)
                        .userId(Long.valueOf(uid))
                        .updateTime(currentTime)
                        .message(JSON.toJSONString(chatMessageRes))
                        .build();
                chatTripGptRecordsMapper.insert(chatTripGptRecords);
                resultMsg = null;
                if (session.isOpen()) {
                    session.getBasicRemote().sendText(JSON.toJSONString(chatMessageRes));
                }
                stopWatch.stop();
                log.info("sessionId={} save chat trip gpt records total time {} second", sessionId, stopWatch.getTotalTimeSeconds());
            } catch (IOException e) {
                log.error("AI 回答异常，异常信息：{}", e.getMessage(), e);
            }
        } else {
            ChatMessageResDTO chatMessageRes = ChatMessageResDTO.builder()
                    .content(message)
                    .terminateFlag(false)
                    .operationId(operationId)
                    .showStyle(AIMessageShowTypeEnum.markdown.name())
                    .build();
            if (session.isOpen()) {
                try {
                    session.getBasicRemote().sendText(JSON.toJSONString(chatMessageRes));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        if (terminateFlag) {
            //保存用户会话记录
            if (userChatSaveFlag == false) {
                saveChatRecord();
            }
            ChatMessageResDTO chatMessageRes = ChatMessageResDTO
                    .builder()
                    .content(terminateResultMsg.toString())
                    .socketEndFlag(true)
                    .terminateFlag(true)
                    .messageType(AIMessageTypeEnum.AI)
                    .showStyle(AIMessageShowTypeEnum.markdown.name())
                    .operationId(operationId)
                    .build();
            //保存回答记录
            LocalDateTime currentTime = LocalDateTime.now();
            ChatTripGptRecords chatTripGptRecords = ChatTripGptRecords
                    .builder()
                    .chatId(sessionId)
                    .isDel(false)
                    .createTime(currentTime)
                    .type(AIMessageTypeEnum.AI)
                    .userId(Long.valueOf(uid))
                    .updateTime(currentTime)
                    .message(JSON.toJSONString(chatMessageRes))
                    .build();
            chatTripGptRecordsMapper.insert(chatTripGptRecords);

        }
    }

    @Override
    public void onError(Throwable throwable) {
        log.error("response 响应异常，sessionId={}, uid={}, message: {}", sessionId, uid, throwable.getMessage(), throwable);
        if (throwable instanceof RuntimeException) {
            String message = QuestionWordLangEnum.MESSAGE_LIMIT.getLangDescription(lang);
            ChatMessageResDTO chatMessageRes = ChatMessageResDTO
                    .builder()
                    .content(message)
                    .styleCssHandlerList(ListUtil.of(StyleCssHandlerDTO.builder()
                            .content(message)
                            .build()))
                    .messageType(AIMessageTypeEnum.AI)
                    .terminateFlag(true)
                    .build();
            log.info("chatMessageRes: {}", JSON.toJSONString(chatMessageRes));
            try {
                session.getBasicRemote().sendText(JSON.toJSONString(chatMessageRes));
            } catch (IOException e) {
                log.error("error: {}", e.getMessage(), e);
            }
        }
    }

    @Override
    public void onComplete(Response<AiMessage> response) {
        try {
            log.info("complete response: {}", JSON.toJSONString(response.content()));
            log.info("token usage: {}", JSON.toJSONString(response.tokenUsage()));
            AiMessage aiMessage1 = response.content();
            if (response.content().hasToolExecutionRequests() && StrUtil.isBlank(aiMessage1.text())) {
                saveChatRecord();
                AiMessage aiMessage = response.content();
                List<ToolExecutionRequest> toolExecutionRequestList = aiMessage.toolExecutionRequests();
                for (ToolExecutionRequest toolExecutionRequest : toolExecutionRequestList) {
                    if (toolExecutionRequest.name().equals(ToolMethodConstant.BOOK_HOTEL)) {
                        ChatMessageResDTO chatMessageResDTO = functionCallService.dealHotel(toolExecutionRequest,
                                new ToolExtInfo(JSON.toJSONString(new HotelTotalInfoDto(sessionId, uid)), lang));
                        if (chatMessageResDTO != null && session.isOpen()) {
                            session.getBasicRemote().sendText(JSON.toJSONString(chatMessageResDTO));
                        }
                    } else if (toolExecutionRequest.name().equals(ToolMethodConstant.FIGHT_BOOKING_SEARCH)) {
                        ChatMessageResDTO chatMessageResDTO = functionCallService.dealFlightSearch(toolExecutionRequest,
                                new ToolExtInfo(JSON.toJSONString(new HotelTotalInfoDto(sessionId, uid)), lang));
                        if (chatMessageResDTO != null && session.isOpen()) {
                            session.getBasicRemote().sendText(JSON.toJSONString(chatMessageResDTO));
                        }
                        assert chatMessageResDTO != null;
                        ToolResultDTO toolResultDTO = chatMessageResDTO.getToolResult();
                        if (toolResultDTO != null && toolResultDTO.getType() != null
                                && ForwardTypeEnum.FLIGHT_INFO.getType() == toolResultDTO.getForwardType()) {
                            //机票旅客提示联系词追问
                            ChatMessageResDTO chatMessageRes = ChatMessageResDTO.builder()
                                    .messageType(AIMessageTypeEnum.QUESTION_WORD)
                                    .associationalQuestionWord(List.of(QuestionWordLangEnum.PASSENGER_INFO.getLangDescription(lang)))
                                    .build();
                            if (session.isOpen()) {
                                session.getBasicRemote().sendText(JSON.toJSONString(chatMessageRes));
                            }
                            LocalDateTime localDateTime = LocalDateTime.now();
                            ChatTripGptRecords chatTripGptRecords = ChatTripGptRecords
                                    .builder()
                                    .chatId(sessionId)
                                    .isDel(false)
                                    .createTime(localDateTime)
                                    .type(AIMessageTypeEnum.QUESTION_WORD)
                                    .userId(Long.valueOf(uid))
                                    .updateTime(localDateTime)
                                    .message(JSON.toJSONString(chatMessageRes))
                                    .build();
                            chatTripGptRecordsMapper.insert(chatTripGptRecords);
                        }
                    } else if (toolExecutionRequest.name().equals(ToolMethodConstant.TRIP_USER_INFO)) {
                        ChatMessageResDTO chatMessageResDTO = functionCallService.dealUserTripInfo(toolExecutionRequest,
                                new ToolExtInfo(JSON.toJSONString(new ToolExtInfoDto(sessionId, uid)), lang));
                        if (chatMessageResDTO != null && session.isOpen()) {
                            session.getBasicRemote().sendText(JSON.toJSONString(chatMessageResDTO));
                        }
                        assert chatMessageResDTO != null;
                        if (!FunCallExceptionEnum.NO_PASSENGER_INFO.equals(chatMessageResDTO.getFunCallExceptionEnum())) {
                            //机票预定提示联系词追问
                            ChatMessageResDTO chatMessageRes = ChatMessageResDTO.builder()
                                    .messageType(AIMessageTypeEnum.QUESTION_WORD)
                                    .associationalQuestionWord(List.of(QuestionWordLangEnum.FLIGHT_TICKET.getLangDescription(lang)))
                                    .build();
                            if (session.isOpen()) {
                                session.getBasicRemote().sendText(JSON.toJSONString(chatMessageRes));
                            }
                            LocalDateTime localDateTime = LocalDateTime.now();
                            ChatTripGptRecords chatTripGptRecords = ChatTripGptRecords
                                    .builder()
                                    .chatId(sessionId)
                                    .isDel(false)
                                    .createTime(localDateTime)
                                    .type(AIMessageTypeEnum.QUESTION_WORD)
                                    .userId(Long.valueOf(uid))
                                    .updateTime(localDateTime)
                                    .message(JSON.toJSONString(chatMessageRes))
                                    .build();
                            chatTripGptRecordsMapper.insert(chatTripGptRecords);
                        }
                    } else if (toolExecutionRequest.name().equals(ToolMethodConstant.FLIGHT_BOOKING_CREATE_ORDER)) {
                        ChatMessageResDTO chatMessageResDTO = functionCallService.dealFlightBookingOrUserInfo(toolExecutionRequest,
                                new ToolExtInfo(JSON.toJSONString(new ToolExtInfoDto(sessionId, uid)), lang));
                        if (chatMessageResDTO != null && session.isOpen()) {
                            session.getBasicRemote().sendText(JSON.toJSONString(chatMessageResDTO));
                        }
                    }
                }
            } else {
                if (terminateFlag) {
                    return;
                }
                ChatMessageResDTO chatMessageResEnd = ChatMessageResDTO.builder()
                        .content("")
                        .terminateFlag(true)
                        .socketEndFlag(true)
                        .operationId(operationId)
                        .build();
                if (session.isOpen()) {
                    session.getBasicRemote().sendText(JSON.toJSONString(chatMessageResEnd));
                }
                if (versionNum != null && versionNum > 0) {
                    String content = response.content().text();
                    try {
                        StopWatch stopWatch = new StopWatch();
                        stopWatch.currentTaskName();
                        stopWatch.start();
                        //保存用户会话记录
                        if (userChatSaveFlag == false) {
                            saveChatRecord();
                        }
                        if (!cityFlag) {
                            //城市名称正则匹配
                            List<String> cityList = HanLPUtil.segmentCity(userMessage);
                            if (CollectionUtil.isNotEmpty(cityList)) {
                                int citySize = cityList.size();
                                if (citySize == 1) {
                                    cityName = cityList.get(0);
                                    cityCode = aiToolService.getCityCodeByName(cityName);
                                } else {
                                    departureCityName = cityList.get(0);
                                    departureCityCode = aiToolService.getCityCodeByName(departureCityName);
                                    cityName = cityList.get(1);
                                    cityCode = aiToolService.getCityCodeByName(cityName);
                                }
                            }
                            cityFlag = true;
                        }
                        List<HighlightDTO> highlightList = new ArrayList<>();
                        //攻略天数+景点处理
                        Matcher matcherDay = XqRegxUtil.DAY_REGX.matcher(content);
                        String[] dayStr = content.split(XqRegxUtil.DAY_REGX_STR);
                        while (matcherDay.find()) {
                            String dayText = matcherDay.group(2);
                            if (StrUtil.isBlank(dayText)) {
                                dayText = matcherDay.group(3);
                            }
                            if (StrUtil.isBlank(dayText)) {
                                continue;
                            }
                            if (NumberUtil.isNumber(dayText)) {
                                currentDay = Integer.valueOf(dayText);
                            } else {
                                currentDay = NumberChineseFormatter.chineseToNumber(dayText);
                            }
                            if (currentDay > dayNum) {
                                dayNum = currentDay;
                            }
                            if (currentDay >= dayStr.length) {
                                continue;
                            }
                            ItineraryCreateParam.DayItem dayItemDetail = new ItineraryCreateParam.DayItem();
                            dayItemDetail.setDayNum(currentDay);
                            dayItemDetail.setCityCode(cityCode);
                            dayItems.add(dayItemDetail);
                            String dayScenicStr = dayStr[currentDay];
                            //景点正则匹配
                            List<String> scenicNames = null;
                            if (bracketMatchFlag) {
                                scenicNames = HanLPUtil.bracketContent(dayScenicStr);
                            } else {
                                scenicNames = HanLPUtil.segmentScenic(dayScenicStr);
                            }
                            List<HighlightDTO> highlights = new ArrayList<>();
                            if (CollectionUtil.isNotEmpty(scenicNames)) {
                                highlights = aiToolService.queryHighlightList(cityCode, scenicNames);
                            }
                            if (CollectionUtil.isNotEmpty(highlights)) {
                                this.itineraryFlag = true;
                                for (ItineraryCreateParam.DayItem dayItem : dayItems) {
                                    if (dayItem.getDayNum().equals(currentDay)) {
                                        List<ItineraryCreateParam.DayItem.ActivityItem> activities = getActivityItems(dayItem, highlightList);
                                        dayItem.setActivities(activities);
                                    }
                                }
                                highlightList.addAll(highlights);
                            }
                        }
                        ChatMessageResDTO chatMessageRes = ChatMessageResDTO
                                .builder()
                                .content(content)
                                .socketEndFlag(true)
                                .terminateFlag(true)
                                .messageType(AIMessageTypeEnum.AI)
                                .operationId(operationId)
                                .showStyle(AIMessageShowTypeEnum.xiaoqi.name())
                                .build();
                        //Markdown 转化
                        List<StyleCssHandlerDTO> styleCssHandlerList = MarkdownUtil.markdownConvertChar(content);
                        chatMessageRes.setStyleCssHandlerList(MarkdownUtil.hightLightConvertV2(styleCssHandlerList, highlightList, lang, bracketMatchFlag));
                        //保存回答记录
                        LocalDateTime currentTime = LocalDateTime.now();
                        ChatTripGptRecords chatTripGptRecords = ChatTripGptRecords
                                .builder()
                                .chatId(sessionId)
                                .isDel(false)
                                .createTime(currentTime)
                                .type(AIMessageTypeEnum.AI)
                                .userId(Long.valueOf(uid))
                                .updateTime(currentTime)
                                .message(JSON.toJSONString(chatMessageRes))
                                .build();
                        chatTripGptRecordsMapper.insert(chatTripGptRecords);
                        resultMsg = null;
                        if (session.isOpen()) {
                            session.getBasicRemote().sendText(JSON.toJSONString(chatMessageRes));
                        }
                        stopWatch.stop();
                        log.info("sessionId={} save chat trip gpt records total time {} second", sessionId, stopWatch.getTotalTimeSeconds());
                    } catch (IOException e) {
                        log.error("AI 回答异常，异常信息：{}", e.getMessage(), e);
                    }
                }
                if (itineraryFlag) {
                    Runnable runnable = () -> {
                        try {
                            //生成攻略
                            ItineraryInfoDto itineraryInfoDto = new ItineraryInfoDto(sessionId, uid,
                                    dayNum, departureCityCode, ListUtil.of(cityCode), dayItems);
                            ChatMessageResDTO chatMessageResDTO = functionCallService.dealItinerary(new ToolExtInfo(JSON.toJSONString(itineraryInfoDto), lang));
                            if (chatMessageResDTO != null && session.isOpen()) {
                                chatMessageResDTO.setTerminateFlag(true);
                                chatMessageResDTO.setOperationId(operationId);
                                session.getBasicRemote().sendText(JSON.toJSONString(chatMessageResDTO));
                            }
                        } catch (Exception e) {
                            log.error("小奇生产攻略异常，异常信息：{}，sessionId: {}", e.getMessage(), sessionId, e);
                        }
                    };
                    runnable.run();
                }
                //添加推荐视频信息
                if (dayNum > 0 && StrUtil.isNotBlank(cityName)) {
                    //搜索目的地城市景区视频
                    String searchWord = cityName.concat("推荐景点");
                    ChatMessageResDTO recommendContent = functionCallService.recommendContent(searchWord, sessionId, uid);
                    if (session.isOpen() && recommendContent != null) {
                        recommendContent.setOperationId(operationId);
                        session.getBasicRemote().sendText(JSON.toJSONString(recommendContent));
                    }
                }
                if (dayNum > 0) {
                    List<String> associationalQuestionWord = new ArrayList<>();
                    if (StrUtil.isNotBlank(cityName)) {
                        String hotelText = String.format(QuestionWordLangEnum.BOOK_HOTEL.getLangDescription(lang), cityName);
                        associationalQuestionWord.add(hotelText);
                        if (!departureCityName.equals(cityName)) {
                            String flightText = String.format(QuestionWordLangEnum.BOOK_FLIGHT.getLangDescription(lang), departureCityName, cityName);
                            associationalQuestionWord.add(flightText);
                        }
                        String ticketsText = String.format(QuestionWordLangEnum.BOOK_TICKETS.getLangDescription(lang), cityName);
                        associationalQuestionWord.add(ticketsText);
                        ChatMessageResDTO chatMessageRes = ChatMessageResDTO.builder()
                                .messageType(AIMessageTypeEnum.QUESTION_WORD)
                                .associationalQuestionWord(associationalQuestionWord)
                                .build();
                        if (session.isOpen()) {
                            session.getBasicRemote().sendText(JSON.toJSONString(chatMessageRes));
                        }
                        LocalDateTime localDateTime = LocalDateTime.now();
                        ChatTripGptRecords chatTripGptRecords = ChatTripGptRecords
                                .builder()
                                .chatId(sessionId)
                                .isDel(false)
                                .createTime(localDateTime)
                                .type(AIMessageTypeEnum.QUESTION_WORD)
                                .userId(Long.valueOf(uid))
                                .updateTime(localDateTime)
                                .message(JSON.toJSONString(chatMessageRes))
                                .build();
                        chatTripGptRecordsMapper.insert(chatTripGptRecords);
                    }
                }
            }
        } catch (Exception e) {
            String message = QuestionWordLangEnum.RUNTIME_EXCEPTION.getLangDescription(lang);
            ChatMessageResDTO chatMessageRes = ChatMessageResDTO
                    .builder()
                    .content(message)
                    .messageType(AIMessageTypeEnum.AI)
                    .terminateFlag(true)
                    .build();
            if (session.isOpen()) {
                try {
                    session.getBasicRemote().sendText(JSON.toJSONString(chatMessageRes));
                } catch (IOException exception) {
                    log.error("error: {}", exception.getMessage(), exception);
                }
            }
            log.error("function calling 处理异常，异常信息：{}", e.getMessage(), e);
        }
        StreamingResponseHandler.super.onComplete(response);
    }

    /**
     * 保存聊天记录
     */
    private void saveChatRecord() {
        LocalDateTime currentTime = LocalDateTime.now();
        //保存用户提问记录
        ChatMessageReqDTO chatMessageReqDTO = ChatMessageReqDTO
                .builder()
                .sessionId(sessionId)
                .messageType(AIMessageTypeEnum.USER)
                .content(userMessage)
                .build();
        ChatTripGptRecords chatTripGptRecords = ChatTripGptRecords
                .builder()
                .chatId(sessionId)
                .isDel(false)
                .createTime(currentTime)
                .type(AIMessageTypeEnum.USER)
                .userId(Long.valueOf(uid))
                .updateTime(currentTime)
                .message(JSON.toJSONString(chatMessageReqDTO))
                .build();
        chatTripGptRecordsMapper.insert(chatTripGptRecords);
        userChatSaveFlag = true;
    }
}
