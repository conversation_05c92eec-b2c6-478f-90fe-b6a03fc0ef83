package com.somytrip.provider.xq.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.somytrip.bean.dto.HighlightDTO;
import com.somytrip.entity.enums.LanguageEnum;
import com.somytrip.provider.xq.dto.StyleCssHandlerDTO;
import com.vladsch.flexmark.ast.*;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.ast.Document;
import com.vladsch.flexmark.util.ast.Node;
import com.vladsch.flexmark.util.collection.iteration.ReversiblePeekingIterable;

import java.util.ArrayList;
import java.util.List;

public class MarkdownUtil {

    /**
     * Markdown 转换 字符串
     *
     * @param content
     * @return
     */
    public static List<StyleCssHandlerDTO> markdownConvertChar(String content) {
        List<StyleCssHandlerDTO> list = new ArrayList<StyleCssHandlerDTO>();
        Parser parser = Parser.builder().build();
        Document document = parser.parse(content);
        nodehandler(document, list, true, false);
        return list;
    }

    /**
     * 高亮处理+跳转链接
     *
     * @param styleCssHandlerList
     * @param highlightList
     * @return
     */
    public static List<StyleCssHandlerDTO> hightLightConvert(List<StyleCssHandlerDTO> styleCssHandlerList, List<HighlightDTO> highlightList, String lang) {
        List<StyleCssHandlerDTO> styleCssHandlerListResult = new ArrayList<>();
        if (CollectionUtil.isEmpty(styleCssHandlerList) || CollectionUtil.isEmpty(highlightList)) {
            return styleCssHandlerList;
        }
        for (HighlightDTO highlight : highlightList) {
            String showText = highlight.getShowTxt();
            if (LanguageEnum.EN_US.getName().equals(lang)) {
                showText = highlight.getShowTxtEn();
            }
            int showTextLength = showText.length();
            for (StyleCssHandlerDTO styleCssHandler : styleCssHandlerList) {
                String content = styleCssHandler.getContent();
                if (StrUtil.isEmpty(content)) {
                    styleCssHandlerListResult.add(styleCssHandler);
                    continue;
                }
                int index = content.indexOf(showText);
                if (StrUtil.isNotBlank(content) && index != -1) {
                    if (index >= 1 && index + showTextLength < content.length()) {
                        //前段拼接
                        String subContent = content.substring(0, index);
                        StyleCssHandlerDTO beforeStyleCss = new StyleCssHandlerDTO();
                        BeanUtil.copyProperties(styleCssHandler, beforeStyleCss);
                        beforeStyleCss.setContent(subContent);
                        styleCssHandlerListResult.add(beforeStyleCss);
                        //中段拼接
                        String midContent = content.substring(index, index + showTextLength);
                        StyleCssHandlerDTO midStyleCss = new StyleCssHandlerDTO();
                        BeanUtil.copyProperties(styleCssHandler, midStyleCss);
                        midStyleCss.setContent(midContent);
                        midStyleCss.setHighlight(true);
                        StyleCssHandlerDTO.ForwardUrl forwardUrl = new StyleCssHandlerDTO.ForwardUrl();
                        forwardUrl.setForwardType(highlight.getForwardType());
                        forwardUrl.setBusinessId(highlight.getBusinessId());
                        midStyleCss.setForwardUrl(forwardUrl);
                        styleCssHandlerListResult.add(midStyleCss);
                        //后段拼接
                        String afterContent = content.substring(index + showTextLength, content.length());
                        StyleCssHandlerDTO afterStyleCss = new StyleCssHandlerDTO();
                        BeanUtil.copyProperties(styleCssHandler, afterStyleCss);
                        afterStyleCss.setContent(afterContent);
                        styleCssHandlerListResult.add(afterStyleCss);
                    } else if (index == 0 && index + showTextLength == content.length()) {
                        styleCssHandler.setHighlight(true);
                        StyleCssHandlerDTO.ForwardUrl forwardUrl = new StyleCssHandlerDTO.ForwardUrl();
                        forwardUrl.setForwardType(highlight.getForwardType());
                        forwardUrl.setBusinessId(highlight.getBusinessId());
                        styleCssHandler.setForwardUrl(forwardUrl);
                        styleCssHandlerListResult.add(styleCssHandler);
                    } else if (index > 0 && index + showTextLength == content.length()) {
                        //前段拼接
                        String subContent = content.substring(0, index);
                        StyleCssHandlerDTO beforeStyleCss = new StyleCssHandlerDTO();
                        BeanUtil.copyProperties(styleCssHandler, beforeStyleCss);
                        beforeStyleCss.setContent(subContent);
                        styleCssHandlerListResult.add(beforeStyleCss);
                        //后段拼接
                        String afterContent = content.substring(index, index + showTextLength);
                        styleCssHandler.setHighlight(true);
                        StyleCssHandlerDTO.ForwardUrl forwardUrl = new StyleCssHandlerDTO.ForwardUrl();
                        forwardUrl.setForwardType(highlight.getForwardType());
                        forwardUrl.setBusinessId(highlight.getBusinessId());
                        styleCssHandler.setForwardUrl(forwardUrl);
                        styleCssHandler.setContent(afterContent);
                        styleCssHandlerListResult.add(styleCssHandler);
                    } else {
                        //前段拼接
                        String subContent = content.substring(0, showText.length());
                        StyleCssHandlerDTO newStyleCss = new StyleCssHandlerDTO();
                        BeanUtil.copyProperties(styleCssHandler, newStyleCss);
                        newStyleCss.setContent(subContent);
                        newStyleCss.setHighlight(true);
                        StyleCssHandlerDTO.ForwardUrl forwardUrl = new StyleCssHandlerDTO.ForwardUrl();
                        forwardUrl.setForwardType(highlight.getForwardType());
                        forwardUrl.setBusinessId(highlight.getBusinessId());
                        newStyleCss.setForwardUrl(forwardUrl);
                        styleCssHandlerListResult.add(newStyleCss);
                        //后段拼接
                        String afterContent = content.substring(index + showText.length(), content.length());
                        StyleCssHandlerDTO afterStyleCss = new StyleCssHandlerDTO();
                        BeanUtil.copyProperties(styleCssHandler, afterStyleCss);
                        afterStyleCss.setContent(afterContent);
                        styleCssHandlerListResult.add(afterStyleCss);
                    }

                } else {
                    styleCssHandlerListResult.add(styleCssHandler);
                }
            }
        }
        return styleCssHandlerListResult;
    }

    public static List<StyleCssHandlerDTO> hightLightConvertV2(List<StyleCssHandlerDTO> styleCssHandlerList, List<HighlightDTO> highlightList, String lang, Boolean bracketMatchFlag) {

        if (CollectionUtil.isEmpty(styleCssHandlerList) || CollectionUtil.isEmpty(highlightList)) {
            return styleCssHandlerList;
        }
        if (highlightList.size() == 1) {
            return hightLightSplitWorld(styleCssHandlerList, highlightList.get(0), lang, bracketMatchFlag);
        } else {
            List<StyleCssHandlerDTO> tragetStyleCssHandlerList = null;
            for (int i = 0; i < highlightList.size(); i++) {
                if (i == 0) {
                    tragetStyleCssHandlerList = hightLightSplitWorld(styleCssHandlerList, highlightList.get(i), lang, bracketMatchFlag);
                } else {
                    tragetStyleCssHandlerList = hightLightSplitWorld(tragetStyleCssHandlerList, highlightList.get(i), lang, bracketMatchFlag);
                }
            }
            return tragetStyleCssHandlerList;
        }
    }

    private static List<StyleCssHandlerDTO> hightLightSplitWorld(List<StyleCssHandlerDTO> styleCssHandlerList, HighlightDTO highlight, String lang, Boolean bracketMatchFlag) {
        List<StyleCssHandlerDTO> styleCssHandlerListResult = new ArrayList<>();
        String showText = highlight.getShowTxt();
        if (LanguageEnum.EN_US.getName().equals(lang)) {
            showText = highlight.getShowTxtEn();
        }
        if (bracketMatchFlag) {
            showText = "[".concat(showText).concat("]");
        }
        int showTextLength = showText.length();
        for (StyleCssHandlerDTO styleCssHandler : styleCssHandlerList) {
            String content = styleCssHandler.getContent();
            if (StrUtil.isEmpty(content)) {
                styleCssHandlerListResult.add(styleCssHandler);
                continue;
            }
            if (bracketMatchFlag) {
                content = content.replace("【", "[").replace("】", "]");
            }
            int index = content.indexOf(showText);
            if (StrUtil.isNotBlank(content) && index != -1) {
                if (index >= 1 && index + showTextLength < content.length()) {
                    //前段拼接
                    String subContent = content.substring(0, index);
                    StyleCssHandlerDTO beforeStyleCss = new StyleCssHandlerDTO();
                    BeanUtil.copyProperties(styleCssHandler, beforeStyleCss);
                    if (bracketMatchFlag) {
                        subContent = subContent.replace("[", "").replace("]", "");
                    }
                    beforeStyleCss.setContent(subContent);
                    styleCssHandlerListResult.add(beforeStyleCss);
                    //中段拼接
                    String midContent = content.substring(index, index + showTextLength);
                    StyleCssHandlerDTO midStyleCss = new StyleCssHandlerDTO();
                    BeanUtil.copyProperties(styleCssHandler, midStyleCss);
                    if (bracketMatchFlag) {
                        midContent = midContent.replace("[", "").replace("]", "");
                    }
                    midStyleCss.setContent(midContent);
                    midStyleCss.setHighlight(true);
                    StyleCssHandlerDTO.ForwardUrl forwardUrl = new StyleCssHandlerDTO.ForwardUrl();
                    forwardUrl.setForwardType(highlight.getForwardType());
                    forwardUrl.setBusinessId(highlight.getBusinessId());
                    midStyleCss.setForwardUrl(forwardUrl);
                    styleCssHandlerListResult.add(midStyleCss);
                    //后段拼接
                    String afterContent = content.substring(index + showTextLength, content.length());
                    StyleCssHandlerDTO afterStyleCss = new StyleCssHandlerDTO();
                    BeanUtil.copyProperties(styleCssHandler, afterStyleCss);
                    if (bracketMatchFlag) {
                        afterContent = afterContent.replace("[", "").replace("]", "");
                    }
                    afterStyleCss.setContent(afterContent);
                    styleCssHandlerListResult.add(afterStyleCss);
                } else if (index == 0 && index + showTextLength == content.length()) {
                    styleCssHandler.setHighlight(true);
                    StyleCssHandlerDTO.ForwardUrl forwardUrl = new StyleCssHandlerDTO.ForwardUrl();
                    forwardUrl.setForwardType(highlight.getForwardType());
                    forwardUrl.setBusinessId(highlight.getBusinessId());
                    if (bracketMatchFlag) {
                        String contentTxt = styleCssHandler.getContent().replace("[", "").replace("]", "");
                        styleCssHandler.setContent(contentTxt);
                    }
                    styleCssHandler.setForwardUrl(forwardUrl);
                    styleCssHandlerListResult.add(styleCssHandler);
                } else if (index > 0 && index + showTextLength == content.length()) {
                    //前段拼接
                    String subContent = content.substring(0, index);
                    StyleCssHandlerDTO beforeStyleCss = new StyleCssHandlerDTO();
                    BeanUtil.copyProperties(styleCssHandler, beforeStyleCss);
                    if (bracketMatchFlag) {
                        subContent = subContent.replace("[", "").replace("]", "");
                    }
                    beforeStyleCss.setContent(subContent);
                    styleCssHandlerListResult.add(beforeStyleCss);
                    //后段拼接
                    String afterContent = content.substring(index, index + showTextLength);
                    styleCssHandler.setHighlight(true);
                    StyleCssHandlerDTO.ForwardUrl forwardUrl = new StyleCssHandlerDTO.ForwardUrl();
                    forwardUrl.setForwardType(highlight.getForwardType());
                    forwardUrl.setBusinessId(highlight.getBusinessId());
                    styleCssHandler.setForwardUrl(forwardUrl);
                    if (bracketMatchFlag) {
                        afterContent = afterContent.replace("[", "").replace("]", "");
                    }
                    styleCssHandler.setContent(afterContent);
                    styleCssHandlerListResult.add(styleCssHandler);
                } else {
                    //前段拼接
                    String subContent = content.substring(0, showText.length());
                    StyleCssHandlerDTO newStyleCss = new StyleCssHandlerDTO();
                    BeanUtil.copyProperties(styleCssHandler, newStyleCss);
                    if (bracketMatchFlag) {
                        subContent = subContent.replace("[", "").replace("]", "");
                    }
                    newStyleCss.setContent(subContent);
                    newStyleCss.setHighlight(true);
                    StyleCssHandlerDTO.ForwardUrl forwardUrl = new StyleCssHandlerDTO.ForwardUrl();
                    forwardUrl.setForwardType(highlight.getForwardType());
                    forwardUrl.setBusinessId(highlight.getBusinessId());
                    newStyleCss.setForwardUrl(forwardUrl);
                    styleCssHandlerListResult.add(newStyleCss);
                    //后段拼接
                    String afterContent = content.substring(index + showText.length(), content.length());
                    StyleCssHandlerDTO afterStyleCss = new StyleCssHandlerDTO();
                    BeanUtil.copyProperties(styleCssHandler, afterStyleCss);
                    if (bracketMatchFlag) {
                        afterContent = afterContent.replace("[", "").replace("]", "");
                    }
                    afterStyleCss.setContent(afterContent);
                    styleCssHandlerListResult.add(afterStyleCss);
                }

            } else {
                styleCssHandlerListResult.add(styleCssHandler);
            }
        }
        return styleCssHandlerListResult;
    }

    /**
     * markdown 循环处理
     *
     * @param childNode
     * @param list
     */
    private static void nodehandler(Node childNode, List<StyleCssHandlerDTO> list, Boolean lineFlag, Boolean strongFlag) {
        StyleCssHandlerDTO styleCssHandlerDTO = new StyleCssHandlerDTO();
        if (childNode instanceof Document) {
            for (Node child : childNode.getChildren()) {
                nodehandler(child, list, true, false);
            }
        } else if (childNode instanceof BulletList bulletList) {
//            if(lineFlag){
//                StyleCssHandlerDTO styleCssHandlerbegin = new StyleCssHandlerDTO();
//                styleCssHandlerbegin.setLineSegments(1);
//                list.add(styleCssHandlerbegin);
//            }
            //无序列表
            //styleCssHandlerDTO.setContent(bulletList.getOpeningMarker() + "");
            //styleCssHandlerDTO.setIndentedCodeBlock(true);
            ReversiblePeekingIterable<Node> nodes = bulletList.getChildren();
            //if (CollectionUtil.isNotEmpty(nodes)) {
            //list.add(styleCssHandlerDTO);
            //}
            for (Node children : nodes) {
                nodehandler(children, list, false, false);
            }
            return;
        } else if (childNode instanceof Text text) {
            if (strongFlag) {
                styleCssHandlerDTO.setStrongEmphasis(true);
            }
            String txt = text.getChars().toString();
            if (txt.indexOf("\\n") != -1) {
                txt = txt.replace("\\n", StrUtil.AT);
                String[] arrays = txt.split(StrUtil.AT);
                if (txt.indexOf(StrUtil.AT) == 0) {
                    StyleCssHandlerDTO styleCssHandlerbegin = new StyleCssHandlerDTO();
                    styleCssHandlerbegin.setLineSegments(1);
                    list.add(styleCssHandlerbegin);
                }
                for (int i = 0; i < arrays.length; i++) {
                    StyleCssHandlerDTO styleCssHandler = new StyleCssHandlerDTO();
                    styleCssHandler.setContent(arrays[i]);
                    list.add(styleCssHandler);
                    if (i < arrays.length - 1) {
                        StyleCssHandlerDTO styleCssHandlerbegin = new StyleCssHandlerDTO();
                        styleCssHandlerbegin.setLineSegments(1);
                        list.add(styleCssHandlerbegin);
                    }
                }
            } else {
                styleCssHandlerDTO.setContent(text.getChars().toString());
            }
        } else if (childNode instanceof BulletListItem) {
            ReversiblePeekingIterable<Node> nodes = childNode.getChildren();
            for (Node children : nodes) {
                nodehandler(children, list, false, false);
            }
        } else if (childNode instanceof LinkRef linkRef) {
            styleCssHandlerDTO.setContent(linkRef.getReferenceOpeningMarker().toString().concat(linkRef.getReference().toString()).concat(linkRef.getReferenceClosingMarker().toString()));
        } else if (childNode instanceof StrongEmphasis strongEmphasis) {
            if (lineFlag) {
                StyleCssHandlerDTO styleCssHandlerbegin = new StyleCssHandlerDTO();
                styleCssHandlerbegin.setLineSegments(1);
                list.add(styleCssHandlerbegin);
            }
            styleCssHandlerDTO.setContent(strongEmphasis.getText().toString());
            styleCssHandlerDTO.setStrongEmphasis(true);
        } else if (childNode instanceof Emphasis emphasis) {
            styleCssHandlerDTO.setContent(emphasis.getText().toString());
            styleCssHandlerDTO.setStrongEmphasis(true);
        } else if (childNode instanceof IndentedCodeBlock indentedCodeBlock) {
            styleCssHandlerDTO.setContent(indentedCodeBlock.getChars().toString());
            StyleCssHandlerDTO styleCssHandlerbegin = new StyleCssHandlerDTO();
            styleCssHandlerbegin.setLineSegments(1);
            list.add(styleCssHandlerbegin);
            styleCssHandlerDTO.setIndentedCodeBlock(true);
        } else if (childNode instanceof Paragraph paragraph) {
            if (lineFlag) {
                styleCssHandlerDTO.setLineSegments(2);
            }
            Node firstChild = paragraph.getFirstChild();
            ReversiblePeekingIterable<Node> nodes = paragraph.getChildren();
            if (CollectionUtil.isNotEmpty(nodes) && lineFlag && !(firstChild instanceof Text)) {
                list.add(styleCssHandlerDTO);
            }
            for (Node children : nodes) {
                nodehandler(children, list, true, false);
            }
        } else if (childNode instanceof SoftLineBreak softLineBreak) {
            StyleCssHandlerDTO styleCssHandlerbegin = new StyleCssHandlerDTO();
            styleCssHandlerbegin.setLineSegments(1);
            list.add(styleCssHandlerbegin);
            styleCssHandlerDTO.setContent(softLineBreak.getChars().toString());
        } else if (childNode instanceof HardLineBreak hardLineBreak) {
            StyleCssHandlerDTO styleCssHandlerbegin = new StyleCssHandlerDTO();
            styleCssHandlerbegin.setLineSegments(2);
            list.add(styleCssHandlerbegin);
            styleCssHandlerDTO.setContent(hardLineBreak.getChars().toString());
        } else if (childNode instanceof Heading heading) {
            StyleCssHandlerDTO styleCssHandlerbegin = new StyleCssHandlerDTO();
            styleCssHandlerbegin.setLineSegments(1);
            list.add(styleCssHandlerbegin);
            styleCssHandlerDTO.setContent(heading.getText().toString());
            //styleCssHandlerDTO.setHeading(heading.getLevel());
            styleCssHandlerDTO.setStrongEmphasis(true);
            StyleCssHandlerDTO styleCssHandlerend = new StyleCssHandlerDTO();
            styleCssHandlerend.setLineSegments(1);
            list.add(styleCssHandlerend);
            ReversiblePeekingIterable<Node> nodes = childNode.getChildren();
            for (Node children : nodes) {
                nodehandler(children, list, false, true);
            }
            return;
        } else if (childNode instanceof OrderedList orderedList) {
            ReversiblePeekingIterable<Node> nodes = orderedList.getChildren();
            for (Node children : nodes) {
                nodehandler(children, list, false, false);
            }
        } else if (childNode instanceof OrderedListItem orderedListItem) {
            if (lineFlag) {
                StyleCssHandlerDTO styleCssHandlerbegin = new StyleCssHandlerDTO();
                styleCssHandlerbegin.setLineSegments(1);
                list.add(styleCssHandlerbegin);
            }
            styleCssHandlerDTO.setContent(orderedListItem.getOpeningMarker().toString());
            styleCssHandlerDTO.setIndentedCodeBlock(true);
            ReversiblePeekingIterable<Node> nodes = orderedListItem.getChildren();
            if (CollectionUtil.isNotEmpty(nodes)) {
                list.add(styleCssHandlerDTO);
            }
            for (Node children : nodes) {
                nodehandler(children, list, false, false);
            }
            return;
        } else if (childNode instanceof ThematicBreak thematicBreak) {
            ReversiblePeekingIterable<Node> nodes = thematicBreak.getChildren();
            for (Node children : nodes) {
                nodehandler(children, list, false, false);
            }
        }
        if (StrUtil.isNotBlank(styleCssHandlerDTO.getContent()) || styleCssHandlerDTO.isHighlight()
                || styleCssHandlerDTO.isStrongEmphasis() || styleCssHandlerDTO.isIndentedCodeBlock()
                || styleCssHandlerDTO.getLineSegments() > 0) {
            list.add(styleCssHandlerDTO);
        }
    }


}
