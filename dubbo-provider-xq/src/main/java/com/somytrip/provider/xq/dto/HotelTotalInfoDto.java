package com.somytrip.provider.xq.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 酒店Total
 * @author: pigeon
 * @created: 2024-07-27 10:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class HotelTotalInfoDto extends ToolExtInfoDto {
    private Integer priceMin;
    private Integer priceMax;
    private Integer offset;
    private String hotelName;
    private Integer topN;
    private Integer aiSort;
    private String checkOutDate;

    public HotelTotalInfoDto() {
    }

    public HotelTotalInfoDto(String sessionId, String uid) {
        super(sessionId, uid);
    }
}
